import { getRelativePath } from "../getRelativePath.js";
export function parseAnyDef(refs) {
    if (refs.target !== "openAi") {
        return {};
    }
    const anyDefinitionPath = [
        ...refs.basePath,
        refs.definitionPath,
        refs.openAiAnyTypeName,
    ];
    refs.flags.hasReferencedOpenAiAnyType = true;
    return {
        $ref: refs.$refStrategy === "relative"
            ? getRelativePath(anyDefinitionPath, refs.currentPath)
            : anyDefinitionPath.join("/"),
    };
}
