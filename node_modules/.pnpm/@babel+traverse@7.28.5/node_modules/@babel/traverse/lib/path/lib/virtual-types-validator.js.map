{"version": 3, "names": ["_t", "require", "isBinding", "isBlockScoped", "nodeIsBlockScoped", "isExportDeclaration", "isExpression", "nodeIsExpression", "isFlow", "nodeIsFlow", "isForStatement", "isForXStatement", "isIdentifier", "isImportDeclaration", "isImportSpecifier", "isJSXIdentifier", "isJSXMemberExpression", "isMemberExpression", "isRestElement", "nodeIsRestElement", "isReferenced", "nodeIsReferenced", "isScope", "nodeIsScope", "isStatement", "nodeIsStatement", "isVar", "nodeIsVar", "isVariableDeclaration", "react", "isForOfStatement", "isCompatTag", "isReferencedIdentifier", "opts", "node", "parent", "parentPath", "name", "isReferencedMemberExpression", "isBindingIdentifier", "grandparent", "left", "init", "isUser", "_this$node", "loc", "isGenerated", "isPure", "constantsOnly", "scope", "importKind", "exportKind", "isRestProperty", "_this$parentPath", "isObjectPattern", "isSpreadProperty", "_this$parentPath2", "isObjectExpression", "isForAwaitStatement", "await", "exports", "isExistentialTypeParam", "Error", "isNumericLiteralTypeAnnotation"], "sources": ["../../../src/path/lib/virtual-types-validator.ts"], "sourcesContent": ["import type NodePath from \"../index.ts\";\nimport {\n  isBinding,\n  isBlockScoped as nodeIsBlockScoped,\n  isExportDeclaration,\n  isExpression as nodeIsExpression,\n  isFlow as nodeIsFlow,\n  isForStatement,\n  isForXStatement,\n  isIdentifier,\n  isImportDeclaration,\n  isImportSpecifier,\n  isJSXIdentifier,\n  isJSXMemberExpression,\n  isMemberExpression,\n  isRestElement as nodeIsRestElement,\n  isReferenced as nodeIsReferenced,\n  isScope as nodeIsScope,\n  isStatement as nodeIsStatement,\n  isVar as nodeIsVar,\n  isVariableDeclaration,\n  react,\n  isForOfStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nconst { isCompatTag } = react;\nimport type { VirtualTypeAliases } from \"./virtual-types.ts\";\n\ntype Options<Obj> = Partial<{\n  [Prop in Exclude<keyof Obj, \"type\">]: Obj[Prop] extends t.Node\n    ? t.Node\n    : Obj[Prop] extends t.Node[]\n      ? t.Node[]\n      : Obj[Prop];\n}>;\n\nexport interface VirtualTypeNodePathValidators {\n  isBindingIdentifier(\n    this: NodePath,\n  ): this is NodePath<VirtualTypeAliases[\"BindingIdentifier\"]>;\n  isBlockScoped(this: NodePath): boolean;\n  /**\n   * @deprecated\n   */\n  isExistentialTypeParam(\n    this: NodePath,\n  ): this is NodePath<VirtualTypeAliases[\"ExistentialTypeParam\"]>;\n  isExpression(this: NodePath): this is NodePath<t.Expression>;\n  isFlow(this: NodePath): this is NodePath<t.Flow>;\n  isForAwaitStatement(\n    this: NodePath,\n  ): this is NodePath<VirtualTypeAliases[\"ForAwaitStatement\"]>;\n  isGenerated(): this is NodePath<VirtualTypeAliases[\"Generated\"]>;\n  /**\n   * @deprecated\n   */\n  isNumericLiteralTypeAnnotation(this: NodePath): void;\n  isPure(): boolean;\n  isReferenced(): boolean;\n  isReferencedIdentifier<\n    Opts extends Options<VirtualTypeAliases[\"ReferencedIdentifier\"]>,\n  >(\n    this: NodePath,\n    opts?: Opts,\n  ): this is NodePath<VirtualTypeAliases[\"ReferencedIdentifier\"] & Opts>;\n  isReferencedMemberExpression(\n    this: NodePath,\n  ): this is NodePath<VirtualTypeAliases[\"ReferencedMemberExpression\"]>;\n  isRestProperty(this: NodePath): this is NodePath<t.RestProperty>;\n  isScope(this: NodePath): this is NodePath<VirtualTypeAliases[\"Scope\"]>;\n  isSpreadProperty(this: NodePath): this is NodePath<t.SpreadProperty>;\n  isStatement(this: NodePath): this is NodePath<t.Statement>;\n  isUser(): this is NodePath<VirtualTypeAliases[\"User\"]>;\n  isVar(this: NodePath): this is NodePath<VirtualTypeAliases[\"Var\"]>;\n}\n\nexport function isReferencedIdentifier(\n  this: NodePath,\n  opts?: Options<VirtualTypeAliases[\"ReferencedIdentifier\"]>,\n): boolean {\n  const { node, parent } = this;\n  if (isIdentifier(node, opts)) {\n    return nodeIsReferenced(node, parent, this.parentPath.parent);\n  } else if (isJSXIdentifier(node, opts)) {\n    if (!isJSXMemberExpression(parent) && isCompatTag(node.name)) return false;\n    return nodeIsReferenced(node, parent, this.parentPath.parent);\n  } else {\n    // not a JSXIdentifier or an Identifier\n    return false;\n  }\n}\n\nexport function isReferencedMemberExpression(this: NodePath): boolean {\n  const { node, parent } = this;\n  return isMemberExpression(node) && nodeIsReferenced(node, parent);\n}\n\nexport function isBindingIdentifier(this: NodePath): boolean {\n  const { node, parent } = this;\n  const grandparent = this.parentPath.parent;\n  return isIdentifier(node) && isBinding(node, parent, grandparent);\n}\n\nexport function isStatement(this: NodePath): boolean {\n  const { node, parent } = this;\n  if (nodeIsStatement(node)) {\n    if (isVariableDeclaration(node)) {\n      if (isForXStatement(parent, { left: node })) return false;\n      if (isForStatement(parent, { init: node })) return false;\n    }\n\n    return true;\n  } else {\n    return false;\n  }\n}\n\nexport function isExpression(this: NodePath): boolean {\n  if (this.isIdentifier()) {\n    return this.isReferencedIdentifier();\n  } else {\n    return nodeIsExpression(this.node);\n  }\n}\n\nexport function isScope(this: NodePath): boolean {\n  return nodeIsScope(this.node, this.parent);\n}\n\nexport function isReferenced(this: NodePath): boolean {\n  return nodeIsReferenced(this.node, this.parent);\n}\n\nexport function isBlockScoped(this: NodePath): boolean {\n  return nodeIsBlockScoped(this.node);\n}\n\nexport function isVar(this: NodePath): boolean {\n  return nodeIsVar(this.node);\n}\n\nexport function isUser(this: NodePath): boolean {\n  return !!this.node?.loc;\n}\n\nexport function isGenerated(this: NodePath): boolean {\n  return !this.isUser();\n}\n\nexport function isPure(this: NodePath, constantsOnly?: boolean): boolean {\n  return this.scope.isPure(this.node, constantsOnly);\n}\n\nexport function isFlow(this: NodePath): boolean {\n  const { node } = this;\n  if (nodeIsFlow(node)) {\n    return true;\n  } else if (isImportDeclaration(node)) {\n    return node.importKind === \"type\" || node.importKind === \"typeof\";\n  } else if (isExportDeclaration(node)) {\n    return node.exportKind === \"type\";\n  } else if (isImportSpecifier(node)) {\n    return node.importKind === \"type\" || node.importKind === \"typeof\";\n  } else {\n    return false;\n  }\n}\n\n// TODO: 7.0 Backwards Compat\nexport function isRestProperty(this: NodePath): boolean {\n  return nodeIsRestElement(this.node) && this.parentPath?.isObjectPattern();\n}\n\nexport function isSpreadProperty(this: NodePath): boolean {\n  return nodeIsRestElement(this.node) && this.parentPath?.isObjectExpression();\n}\n\nexport function isForAwaitStatement(this: NodePath): boolean {\n  return isForOfStatement(this.node, { await: true });\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.isExistentialTypeParam = function isExistentialTypeParam(\n    this: NodePath,\n  ): void {\n    throw new Error(\n      \"`path.isExistentialTypeParam` has been renamed to `path.isExistsTypeAnnotation()` in Babel 7.\",\n    );\n  };\n\n  // eslint-disable-next-line no-restricted-globals\n  exports.isNumericLiteralTypeAnnotation =\n    function isNumericLiteralTypeAnnotation(this: NodePath): void {\n      throw new Error(\n        \"`path.isNumericLiteralTypeAnnotation()` has been renamed to `path.isNumberLiteralTypeAnnotation()` in Babel 7.\",\n      );\n    };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAsBsB;EArBpBC,SAAS;EACTC,aAAa,EAAIC,iBAAiB;EAClCC,mBAAmB;EACnBC,YAAY,EAAIC,gBAAgB;EAChCC,MAAM,EAAIC,UAAU;EACpBC,cAAc;EACdC,eAAe;EACfC,YAAY;EACZC,mBAAmB;EACnBC,iBAAiB;EACjBC,eAAe;EACfC,qBAAqB;EACrBC,kBAAkB;EAClBC,aAAa,EAAIC,iBAAiB;EAClCC,YAAY,EAAIC,gBAAgB;EAChCC,OAAO,EAAIC,WAAW;EACtBC,WAAW,EAAIC,eAAe;EAC9BC,KAAK,EAAIC,SAAS;EAClBC,qBAAqB;EACrBC,KAAK;EACLC;AAAgB,IAAA9B,EAAA;AAGlB,MAAM;EAAE+B;AAAY,CAAC,GAAGF,KAAK;AAmDtB,SAASG,sBAAsBA,CAEpCC,IAA0D,EACjD;EACT,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAG,IAAI;EAC7B,IAAIvB,YAAY,CAACsB,IAAI,EAAED,IAAI,CAAC,EAAE;IAC5B,OAAOZ,gBAAgB,CAACa,IAAI,EAAEC,MAAM,EAAE,IAAI,CAACC,UAAU,CAACD,MAAM,CAAC;EAC/D,CAAC,MAAM,IAAIpB,eAAe,CAACmB,IAAI,EAAED,IAAI,CAAC,EAAE;IACtC,IAAI,CAACjB,qBAAqB,CAACmB,MAAM,CAAC,IAAIJ,WAAW,CAACG,IAAI,CAACG,IAAI,CAAC,EAAE,OAAO,KAAK;IAC1E,OAAOhB,gBAAgB,CAACa,IAAI,EAAEC,MAAM,EAAE,IAAI,CAACC,UAAU,CAACD,MAAM,CAAC;EAC/D,CAAC,MAAM;IAEL,OAAO,KAAK;EACd;AACF;AAEO,SAASG,4BAA4BA,CAAA,EAA0B;EACpE,MAAM;IAAEJ,IAAI;IAAEC;EAAO,CAAC,GAAG,IAAI;EAC7B,OAAOlB,kBAAkB,CAACiB,IAAI,CAAC,IAAIb,gBAAgB,CAACa,IAAI,EAAEC,MAAM,CAAC;AACnE;AAEO,SAASI,mBAAmBA,CAAA,EAA0B;EAC3D,MAAM;IAAEL,IAAI;IAAEC;EAAO,CAAC,GAAG,IAAI;EAC7B,MAAMK,WAAW,GAAG,IAAI,CAACJ,UAAU,CAACD,MAAM;EAC1C,OAAOvB,YAAY,CAACsB,IAAI,CAAC,IAAIhC,SAAS,CAACgC,IAAI,EAAEC,MAAM,EAAEK,WAAW,CAAC;AACnE;AAEO,SAAShB,WAAWA,CAAA,EAA0B;EACnD,MAAM;IAAEU,IAAI;IAAEC;EAAO,CAAC,GAAG,IAAI;EAC7B,IAAIV,eAAe,CAACS,IAAI,CAAC,EAAE;IACzB,IAAIN,qBAAqB,CAACM,IAAI,CAAC,EAAE;MAC/B,IAAIvB,eAAe,CAACwB,MAAM,EAAE;QAAEM,IAAI,EAAEP;MAAK,CAAC,CAAC,EAAE,OAAO,KAAK;MACzD,IAAIxB,cAAc,CAACyB,MAAM,EAAE;QAAEO,IAAI,EAAER;MAAK,CAAC,CAAC,EAAE,OAAO,KAAK;IAC1D;IAEA,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEO,SAAS5B,YAAYA,CAAA,EAA0B;EACpD,IAAI,IAAI,CAACM,YAAY,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACoB,sBAAsB,CAAC,CAAC;EACtC,CAAC,MAAM;IACL,OAAOzB,gBAAgB,CAAC,IAAI,CAAC2B,IAAI,CAAC;EACpC;AACF;AAEO,SAASZ,OAAOA,CAAA,EAA0B;EAC/C,OAAOC,WAAW,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC;AAC5C;AAEO,SAASf,YAAYA,CAAA,EAA0B;EACpD,OAAOC,gBAAgB,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC;AACjD;AAEO,SAAShC,aAAaA,CAAA,EAA0B;EACrD,OAAOC,iBAAiB,CAAC,IAAI,CAAC8B,IAAI,CAAC;AACrC;AAEO,SAASR,KAAKA,CAAA,EAA0B;EAC7C,OAAOC,SAAS,CAAC,IAAI,CAACO,IAAI,CAAC;AAC7B;AAEO,SAASS,MAAMA,CAAA,EAA0B;EAAA,IAAAC,UAAA;EAC9C,OAAO,CAAC,GAAAA,UAAA,GAAC,IAAI,CAACV,IAAI,aAATU,UAAA,CAAWC,GAAG;AACzB;AAEO,SAASC,WAAWA,CAAA,EAA0B;EACnD,OAAO,CAAC,IAAI,CAACH,MAAM,CAAC,CAAC;AACvB;AAEO,SAASI,MAAMA,CAAiBC,aAAuB,EAAW;EACvE,OAAO,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,IAAI,CAACb,IAAI,EAAEc,aAAa,CAAC;AACpD;AAEO,SAASxC,MAAMA,CAAA,EAA0B;EAC9C,MAAM;IAAE0B;EAAK,CAAC,GAAG,IAAI;EACrB,IAAIzB,UAAU,CAACyB,IAAI,CAAC,EAAE;IACpB,OAAO,IAAI;EACb,CAAC,MAAM,IAAIrB,mBAAmB,CAACqB,IAAI,CAAC,EAAE;IACpC,OAAOA,IAAI,CAACgB,UAAU,KAAK,MAAM,IAAIhB,IAAI,CAACgB,UAAU,KAAK,QAAQ;EACnE,CAAC,MAAM,IAAI7C,mBAAmB,CAAC6B,IAAI,CAAC,EAAE;IACpC,OAAOA,IAAI,CAACiB,UAAU,KAAK,MAAM;EACnC,CAAC,MAAM,IAAIrC,iBAAiB,CAACoB,IAAI,CAAC,EAAE;IAClC,OAAOA,IAAI,CAACgB,UAAU,KAAK,MAAM,IAAIhB,IAAI,CAACgB,UAAU,KAAK,QAAQ;EACnE,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAGO,SAASE,cAAcA,CAAA,EAA0B;EAAA,IAAAC,gBAAA;EACtD,OAAOlC,iBAAiB,CAAC,IAAI,CAACe,IAAI,CAAC,MAAAmB,gBAAA,GAAI,IAAI,CAACjB,UAAU,qBAAfiB,gBAAA,CAAiBC,eAAe,CAAC,CAAC;AAC3E;AAEO,SAASC,gBAAgBA,CAAA,EAA0B;EAAA,IAAAC,iBAAA;EACxD,OAAOrC,iBAAiB,CAAC,IAAI,CAACe,IAAI,CAAC,MAAAsB,iBAAA,GAAI,IAAI,CAACpB,UAAU,qBAAfoB,iBAAA,CAAiBC,kBAAkB,CAAC,CAAC;AAC9E;AAEO,SAASC,mBAAmBA,CAAA,EAA0B;EAC3D,OAAO5B,gBAAgB,CAAC,IAAI,CAACI,IAAI,EAAE;IAAEyB,KAAK,EAAE;EAAK,CAAC,CAAC;AACrD;AAE+C;EAE7CC,OAAO,CAACC,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAExD;IACN,MAAM,IAAIC,KAAK,CACb,+FACF,CAAC;EACH,CAAC;EAGDF,OAAO,CAACG,8BAA8B,GACpC,SAASA,8BAA8BA,CAAA,EAAuB;IAC5D,MAAM,IAAID,KAAK,CACb,gHACF,CAAC;EACH,CAAC;AACL", "ignoreList": []}