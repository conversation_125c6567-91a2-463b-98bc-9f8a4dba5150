{"version": 3, "names": ["_renamer", "require", "_index", "_traverseForScope", "_binding", "_t", "t", "_cache", "globalsBuiltinLower", "globalsBuiltinUpper", "assignmentExpression", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isCallExpression", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMemberExpression", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "expressionStatement", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "buildUndefinedNode", "sequenceExpression", "gatherNodeParts", "node", "parts", "type", "_node$specifiers", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "resetScope", "scope", "references", "Object", "create", "uids", "bindings", "globals", "isAnonymousFunctionExpression", "path", "isFunctionExpression", "isArrowFunctionExpression", "NOT_LOCAL_BINDING", "Symbol", "for", "collectorVisitor", "ForStatement", "declar", "get", "isVar", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "TSImportEqualsDeclaration", "ReferencedIdentifier", "state", "isTSQualifiedName", "right", "parentPath", "isTSImportEqualsDeclaration", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "CatchClause", "Function", "params", "param", "ClassExpression", "TSTypeAnnotation", "skip", "scopeVisitor", "uid", "<PERSON><PERSON>", "constructor", "block", "inited", "labels", "referencesSet", "uidsSet", "data", "crawling", "cached", "scopeCache", "set", "Map", "defineProperties", "enumerable", "configurable", "writable", "_parent", "_path", "shouldSkip", "<PERSON><PERSON><PERSON>", "isScope", "Error", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "hub", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "dump", "sep", "repeat", "console", "log", "violations", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "registerConstantViolation", "ids", "getAssignmentIdentifiers", "_this$getBinding", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "expressions", "tag", "noGlobals", "quasi", "isStringLiteral", "setData", "val", "getData", "removeData", "init", "crawl", "isProgram", "programParent", "traverse", "visitors", "merge", "typeVisitors", "visit", "enter", "call", "ref", "opts", "getPatternParent", "isBlockStatement", "isSwitchStatement", "unique", "pushContainer", "isLoop", "isCatchClause", "isFunction", "ensureBlock", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "isFunctionParent", "isBlockParent", "getAllBindings", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "_this$getBinding2", "getOwnBindingIdentifier", "hasOwnBinding", "noUids", "upToScope", "includes", "contextVariables", "parentHasBinding", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding3", "hoistVariables", "emit", "seen", "Set", "isVariableDeclarator", "has", "add", "firstId", "isForXStatement", "replaceWith", "remove", "expr", "isForStatement", "exports", "default", "prototype", "_renameFromMap", "map", "_generateUid", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "getAllBindingsOfKind", "kinds", "parentBlock"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer.ts\";\nimport type NodePath from \"../path/index.ts\";\nimport traverse from \"../index.ts\";\nimport traverseForScope from \"./traverseForScope.ts\";\nimport Binding from \"./binding.ts\";\nimport type { BindingKind } from \"./binding.ts\";\nimport globalsBuiltinLower from \"@babel/helper-globals/data/builtin-lower.json\" with { type: \"json\" };\nimport globalsBuiltinUpper from \"@babel/helper-globals/data/builtin-upper.json\" with { type: \"json\" };\nimport {\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isCallExpression,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMemberExpression,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  expressionStatement,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n  buildUndefinedNode,\n  sequenceExpression,\n} from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache.ts\";\nimport type { ExplodedVisitor, Visitor } from \"../types.ts\";\n\ntype NodePart = string | number | bigint | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node | null | undefined, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers?.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n    case \"ImportExpression\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\nfunction resetScope(scope: Scope) {\n  if (!process.env.BABEL_8_BREAKING) {\n    // @ts-expect-error(Babel 7 vs Babel 8)\n    scope.references = Object.create(null);\n    // @ts-expect-error(Babel 7 vs Babel 8)\n    scope.uids = Object.create(null);\n  } else if (scope.path.type === \"Program\") {\n    scope.referencesSet = new Set();\n    scope.uidsSet = new Set();\n  }\n\n  scope.bindings = Object.create(null);\n  scope.globals = Object.create(null);\n}\n\nfunction isAnonymousFunctionExpression(\n  path: NodePath,\n): path is NodePath<t.FunctionExpression | t.ArrowFunctionExpression> {\n  return (\n    (path.isFunctionExpression() && !path.node.id) ||\n    path.isArrowFunctionExpression()\n  );\n}\n\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath<t.Node>[];\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var NOT_LOCAL_BINDING = Symbol.for(\n    \"should not be considered a local binding\",\n  );\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  TSImportEqualsDeclaration(path) {\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    if (t.isTSQualifiedName(path.parent) && path.parent.right === path.node) {\n      return;\n    }\n    if (path.parentPath.isTSImportEqualsDeclaration()) return;\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope: Scope = path.scope;\n    if (scope.path === path) scope = scope.parent!;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent!.getBinding(name)!;\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.node.id &&\n      (process.env.BABEL_8_BREAKING ||\n        // @ts-expect-error Fixme: document symbol ast properties\n        !path.node.id[NOT_LOCAL_BINDING])\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.node.id &&\n      (process.env.BABEL_8_BREAKING ||\n        // @ts-expect-error Fixme: document symbol ast properties\n        !path.node.id[NOT_LOCAL_BINDING])\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  TSTypeAnnotation(path) {\n    path.skip();\n  },\n};\n\nlet scopeVisitor: ExplodedVisitor<CollectVisitorState>;\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport { Scope as default };\nclass Scope {\n  uid;\n\n  path!: NodePath;\n  block!: t.Pattern | t.Scopable;\n\n  inited!: boolean;\n\n  labels!: Map<string, NodePath<t.LabeledStatement>>;\n  bindings!: { [name: string]: Binding };\n  /** Only defined in the program scope */\n  referencesSet?: Set<string>;\n  globals!: { [name: string]: t.Identifier | t.JSXIdentifier };\n  /** Only defined in the program scope */\n  uidsSet?: Set<string>;\n  data!: { [key: string | symbol]: unknown };\n  crawling!: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n\n    if (!process.env.BABEL_8_BREAKING) {\n      // Shadow the Babel 8 removal getters\n      Object.defineProperties(this, {\n        references: {\n          enumerable: true,\n          configurable: true,\n          writable: true,\n          value: Object.create(null),\n        },\n        uids: {\n          enumerable: true,\n          configurable: true,\n          writable: true,\n          value: Object.create(null),\n        },\n      });\n    }\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = [...globalsBuiltinLower, ...globalsBuiltinUpper];\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path?.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  get references() {\n    throw new Error(\n      \"Scope#references is not available in Babel 8. Use Scope#referencesSet instead.\",\n    );\n  }\n\n  get uids() {\n    throw new Error(\n      \"Scope#uids is not available in Babel 8. Use Scope#uidsSet instead.\",\n    );\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name).replace(/^_+/, \"\").replace(/\\d+$/g, \"\");\n\n    let uid;\n    let i = 0;\n    do {\n      uid = `_${name}`;\n\n      // Ideally we would just use (i - 1) as the suffix, but that generates\n      // unnecessary changes in every single file generated by Babel :)\n      //\n      // i:       0        1  2  3  4  5  6  7  8  9 10 11 12 13 14 ...\n      // suffix:  (empty)  2  3  4  5  6  7  8  9  0  1 10 11 12 13 ...\n      if (i >= 11) uid += i - 1;\n      else if (i >= 9) uid += i - 9;\n      else if (i >= 1) uid += i + 1;\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    if (process.env.BABEL_8_BREAKING) {\n      program.referencesSet.add(uid);\n      program.uidsSet.add(uid);\n    } else {\n      // @ts-expect-error Babel 7\n      program.references[uid] = true;\n      // @ts-expect-error Babel 7\n      program.uids[uid] = true;\n    }\n\n    return uid;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it won't result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.path.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope | undefined = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath<t.Node>) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(\n          kind === \"using\" || kind === \"await using\" ? \"const\" : kind,\n          declar,\n        );\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return buildUndefinedNode();\n  }\n\n  registerConstantViolation(path: NodePath<t.Node>) {\n    const ids = path.getAssignmentIdentifiers();\n    for (const name of Object.keys(ids)) {\n      this.getBinding(name)?.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath<t.Node>,\n    bindingPath: NodePath<t.Node> = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      if (process.env.BABEL_8_BREAKING) {\n        parent.referencesSet.add(name);\n      } else {\n        // @ts-expect-error Babel 7\n        parent.references[name] = true;\n      }\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          local.reassign(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    if (process.env.BABEL_8_BREAKING) {\n      return this.getProgramParent().uidsSet.has(name);\n    } else {\n      let scope: Scope | undefined = this;\n\n      do {\n        // @ts-expect-error Babel 7\n        if (scope.uids[name]) return true;\n      } while ((scope = scope.parent));\n\n      return false;\n    }\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope | undefined = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    if (process.env.BABEL_8_BREAKING) {\n      return this.getProgramParent().referencesSet.has(name);\n    } else {\n      // @ts-expect-error Babel 7\n      return !!this.getProgramParent().references[name];\n    }\n  }\n\n  isPure(node: t.Node | null | undefined, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      // @ts-expect-error comparing undefined and number\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      // @ts-expect-error comparing undefined and number\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      // @ts-expect-error comparing undefined and number\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", { noGlobals: true }) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isMemberExpression(node)) {\n      return (\n        !node.computed &&\n        isIdentifier(node.object) &&\n        node.object.name === \"Symbol\" &&\n        isIdentifier(node.property) &&\n        node.property.name !== \"for\" &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true })\n      );\n    } else if (isCallExpression(node)) {\n      return (\n        matchesPattern(node.callee, \"Symbol.for\") &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true }) &&\n        node.arguments.length === 1 &&\n        t.isStringLiteral(node.arguments[0])\n      );\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope | undefined = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope | undefined = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    if (process.env.BABEL_8_BREAKING && path.opts?.noScope) {\n      return;\n    }\n\n    resetScope(this);\n    this.data = Object.create(null);\n\n    let scope: Scope | undefined = this;\n    do {\n      if (scope.crawling) return;\n      if (scope.path.isProgram()) {\n        break;\n      }\n    } while ((scope = scope.parent));\n\n    const programParent = scope!;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    scopeVisitor ||= traverse.visitors.merge([\n      {\n        Scope(path) {\n          resetScope(path.scope);\n        },\n      },\n      collectorVisitor,\n    ]);\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\") {\n      const typeVisitors = scopeVisitor[path.type];\n      if (typeVisitors) {\n        for (const visit of typeVisitors.enter!) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    if (process.env.BABEL_8_BREAKING) {\n      traverseForScope(path, scopeVisitor, state);\n    } else {\n      path.traverse(scopeVisitor, state);\n    }\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getAssignmentIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.ArrayPattern | t.Identifier | t.ObjectPattern;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    const { init, unique, kind = \"var\", id } = opts;\n\n    // When injecting a non-const non-initialized binding inside\n    // an IIFE, if the number of call arguments is less than or\n    // equal to the number of function parameters, we can safely\n    // inject the binding into the parameter list.\n    if (\n      !init &&\n      !unique &&\n      (kind === \"var\" || kind === \"let\") &&\n      isAnonymousFunctionExpression(path) &&\n      isCallExpression(path.parent, { callee: path.node }) &&\n      path.parent.arguments.length <= path.node.params.length &&\n      isIdentifier(id)\n    ) {\n      path.pushContainer(\"params\", id);\n      path.scope.registerBinding(\n        \"param\",\n        path.get(\"params\")[path.node.params.length - 1],\n      );\n      return;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      path.ensureBlock();\n      path = path.get(\"body\");\n    }\n\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(id, init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent(): Scope & {\n    referencesSet: Set<string>;\n    uidsSet: Set<string>;\n  } {\n    let scope: Scope | undefined = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope as Scope & {\n          referencesSet: Set<string>;\n          uidsSet: Set<string>;\n        };\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope | undefined = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope | undefined = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope | undefined = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent!.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope | undefined = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope | undefined = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  getBindingIdentifier(name: string): t.Identifier | undefined {\n    return this.getBinding(name)?.identifier;\n  }\n\n  getOwnBindingIdentifier(name: string): t.Identifier | undefined {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?:\n      | boolean\n      | { noGlobals?: boolean; noUids?: boolean; upToScope?: Scope },\n  ) {\n    if (!name) return false;\n    // TODO: Only accept the object form.\n    let noGlobals;\n    let noUids;\n    let upToScope;\n    if (typeof opts === \"object\") {\n      noGlobals = opts.noGlobals;\n      noUids = opts.noUids;\n      upToScope = opts.upToScope;\n    } else if (typeof opts === \"boolean\") {\n      noGlobals = opts;\n    }\n    let scope: Scope | undefined = this;\n    do {\n      if (upToScope === scope) {\n        break;\n      }\n      if (scope.hasOwnBinding(name)) {\n        return true;\n      }\n    } while ((scope = scope.parent));\n\n    if (!noUids && this.hasUid(name)) return true;\n    if (!noGlobals && Scope.globals.includes(name)) return true;\n    if (!noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    if (process.env.BABEL_8_BREAKING) {\n      this.getProgramParent().uidsSet.delete(name);\n    } else {\n      let scope: Scope | undefined = this;\n      do {\n        // @ts-expect-error Babel 7\n        if (scope.uids[name]) {\n          // @ts-expect-error Babel 7\n          scope.uids[name] = false;\n        }\n      } while ((scope = scope.parent));\n    }\n  }\n\n  /**\n   * Hoist all the `var` variable to the beginning of the function/program\n   * scope where their binding will be actually defined. For exmaple,\n   *     { var x = 2 }\n   * will be transformed to\n   *     var x; { x = 2 }\n   *\n   * @param emit A custom function to emit `var` declarations, for example to\n   *   emit them in a different scope.\n   */\n  hoistVariables(\n    emit: (id: t.Identifier, hasInit: boolean) => void = id =>\n      this.push({ id }),\n  ) {\n    this.crawl();\n\n    const seen = new Set();\n    for (const name of Object.keys(this.bindings)) {\n      const binding = this.bindings[name];\n      if (!binding) continue;\n      const { path } = binding;\n      if (!path.isVariableDeclarator()) continue;\n      const { parent, parentPath } = path;\n\n      if (parent.kind !== \"var\" || seen.has(parent)) continue;\n      seen.add(path.parent);\n\n      let firstId;\n      const init = [];\n      for (const decl of parent.declarations) {\n        firstId ??= decl.id;\n        if (decl.init) {\n          init.push(\n            assignmentExpression(\n              \"=\",\n              // var declarator must not be a void pattern\n              decl.id as Exclude<t.VariableDeclarator[\"id\"], t.VoidPattern>,\n              decl.init,\n            ),\n          );\n        }\n\n        const ids = Object.keys(getBindingIdentifiers(decl, false, true, true));\n        for (const name of ids) {\n          emit(identifier(name), decl.init != null);\n        }\n      }\n\n      // for (var i in test)\n      if (parentPath.parentPath.isForXStatement({ left: parent })) {\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        parentPath.replaceWith(firstId!);\n      } else if (init.length === 0) {\n        parentPath.remove();\n      } else {\n        const expr = init.length === 1 ? init[0] : sequenceExpression(init);\n        if (parentPath.parentPath.isForStatement({ init: parent })) {\n          parentPath.replaceWith(expr);\n        } else {\n          parentPath.replaceWith(expressionStatement(expr));\n        }\n      }\n    }\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /** @deprecated Not used in our codebase */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._renameFromMap = function _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  };\n\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.traverse = function <S>(\n    this: Scope,\n    node: any,\n    opts: any,\n    state?: S,\n  ) {\n    traverse(node, opts, this, state, this.path);\n  };\n\n  /**\n   * Generate an `_id1`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._generateUid = function _generateUid(\n    name: string,\n    i: number,\n  ) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  };\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.toArray = function toArray(\n    this: Scope,\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.path.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.path.hub.addHelper(helperName), args);\n  };\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.getAllBindingsOfKind = function getAllBindingsOfKind(\n    ...kinds: string[]\n  ): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope | undefined = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  };\n\n  Object.defineProperties(Scope.prototype, {\n    parentBlock: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.parent;\n      },\n    },\n    hub: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.hub;\n      },\n    },\n  });\n}\n\ntype _Binding = Binding;\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Scope {\n  export type Binding = _Binding;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAIA,IAAAI,EAAA,GAAAJ,OAAA;AAiDsB,IAAAK,CAAA,GAAAD,EAAA;AAEtB,IAAAE,MAAA,GAAAN,OAAA;AAAkD,MArD3CO,mBAAmB,GAAAP,OAAA,CAAM,+CAA+C;EACxEQ,mBAAmB,GAAAR,OAAA,CAAM,+CAA+C;AAAA;EAE7ES,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,gBAAgB;EAChBC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,kBAAkB;EAClBC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,mBAAmB;EACnBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC,mBAAmB;EACnBC,kBAAkB;EAClBC;AAAkB,IAAApD,EAAA;AAQpB,SAASqD,eAAeA,CAACC,IAA+B,EAAEC,KAAiB,EAAE;EAC3E,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAInC,mBAAmB,CAACiC,IAAI,CAAC,IAAIJ,mBAAmB,CAACI,IAAI,CAAC,EAAE;QAAA,IAAAG,gBAAA;QAC1D,IACE,CAACzC,sBAAsB,CAACsC,IAAI,CAAC,IAC3BpC,wBAAwB,CAACoC,IAAI,CAAC,IAC9BjC,mBAAmB,CAACiC,IAAI,CAAC,KAC3BA,IAAI,CAACI,MAAM,EACX;UACAL,eAAe,CAACC,IAAI,CAACI,MAAM,EAAEH,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAACrC,wBAAwB,CAACoC,IAAI,CAAC,IAAIjC,mBAAmB,CAACiC,IAAI,CAAC,MAAAG,gBAAA,GAC5DH,IAAI,CAACK,UAAU,aAAfF,gBAAA,CAAiBG,MAAM,EACvB;UACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,CAACK,UAAU,EAAEN,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACtC,0BAA0B,CAACqC,IAAI,CAAC,IAC/BpC,wBAAwB,CAACoC,IAAI,CAAC,KAChCA,IAAI,CAACQ,WAAW,EAChB;UACAT,eAAe,CAACC,IAAI,CAACQ,WAAW,EAAEP,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI9B,iBAAiB,CAAC6B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACS,KAAK,EAAER,KAAK,CAAC;MACpC,CAAC,MAAM,IACLjC,SAAS,CAACgC,IAAI,CAAC,IACf,CAAC5B,aAAa,CAAC4B,IAAI,CAAC,IACpB,CAACxB,eAAe,CAACwB,IAAI,CAAC,IACtB,CAACrB,iBAAiB,CAACqB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACS,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBZ,eAAe,CAACC,IAAI,CAACY,MAAM,EAAEX,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACS,IAAI,CAACV,IAAI,CAACc,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBf,eAAe,CAACC,IAAI,CAACe,MAAM,EAAEd,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMM,CAAC,IAAIP,IAAI,CAACgB,UAAU,EAAE;QAC/BjB,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACkB,GAAG,EAAEjB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;IACb,KAAK,kBAAkB;MACrBT,KAAK,CAACS,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBT,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACmB,IAAI,EAAElB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACqB,UAAU,EAAEpB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACsB,IAAI,EAAErB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACuB,cAAc,EAAEtB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACwB,eAAe,EAAEvB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACS,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBX,eAAe,CAACC,IAAI,CAACyB,SAAS,EAAExB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;EACJ;AACF;AAEA,SAASyB,UAAUA,CAACC,KAAY,EAAE;EACG;IAEjCA,KAAK,CAACC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAEtCH,KAAK,CAACI,IAAI,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAClC;EAKAH,KAAK,CAACK,QAAQ,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACpCH,KAAK,CAACM,OAAO,GAAGJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACrC;AAEA,SAASI,6BAA6BA,CACpCC,IAAc,EACsD;EACpE,OACGA,IAAI,CAACC,oBAAoB,CAAC,CAAC,IAAI,CAACD,IAAI,CAACnC,IAAI,CAACoB,EAAE,IAC7Ce,IAAI,CAACE,yBAAyB,CAAC,CAAC;AAEpC;AAQmC;EAEjC,IAAIC,iBAAiB,GAAGC,MAAM,CAACC,GAAG,CAChC,0CACF,CAAC;AACH;AAEA,MAAMC,gBAA8C,GAAG;EACrDC,YAAYA,CAACP,IAAI,EAAE;IACjB,MAAMQ,MAAM,GAAGR,IAAI,CAACS,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE;MAClB,MAAM;QAAElB;MAAM,CAAC,GAAGQ,IAAI;MACtB,MAAMW,WAAW,GAAGnB,KAAK,CAACoB,iBAAiB,CAAC,CAAC,IAAIpB,KAAK,CAACqB,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEN,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDO,WAAWA,CAACf,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACgB,aAAa,CAAC,CAAC,EAAE;IAG1B,IAAIhB,IAAI,CAACpE,mBAAmB,CAAC,CAAC,EAAE;IAGhC,IAAIoE,IAAI,CAACvC,mBAAmB,CAAC,CAAC,EAAE;IAGhC,MAAMwD,MAAM,GACVjB,IAAI,CAACR,KAAK,CAACoB,iBAAiB,CAAC,CAAC,IAAIZ,IAAI,CAACR,KAAK,CAACqB,gBAAgB,CAAC,CAAC;IACjEI,MAAM,CAACC,mBAAmB,CAAClB,IAAI,CAAC;EAClC,CAAC;EAEDmB,iBAAiBA,CAACnB,IAAI,EAAE;IAEtB,MAAMiB,MAAM,GAAGjB,IAAI,CAACR,KAAK,CAAC4B,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAAClB,IAAI,CAAC;EAClC,CAAC;EAEDqB,yBAAyBA,CAACrB,IAAI,EAAE;IAC9B,MAAMiB,MAAM,GAAGjB,IAAI,CAACR,KAAK,CAAC4B,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAAClB,IAAI,CAAC;EAClC,CAAC;EAEDsB,oBAAoBA,CAACtB,IAAI,EAAEuB,KAAK,EAAE;IAChC,IAAI/G,CAAC,CAACgH,iBAAiB,CAACxB,IAAI,CAACiB,MAAM,CAAC,IAAIjB,IAAI,CAACiB,MAAM,CAACQ,KAAK,KAAKzB,IAAI,CAACnC,IAAI,EAAE;MACvE;IACF;IACA,IAAImC,IAAI,CAAC0B,UAAU,CAACC,2BAA2B,CAAC,CAAC,EAAE;IACnDJ,KAAK,CAAC9B,UAAU,CAAClB,IAAI,CAACyB,IAAI,CAAC;EAC7B,CAAC;EAED4B,aAAaA,CAAC5B,IAAI,EAAEuB,KAAK,EAAE;IACzB,MAAMvC,IAAI,GAAGgB,IAAI,CAACS,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAIzB,IAAI,CAAC6C,SAAS,CAAC,CAAC,IAAI7C,IAAI,CAACrD,YAAY,CAAC,CAAC,EAAE;MAC3C4F,KAAK,CAACO,kBAAkB,CAACvD,IAAI,CAACyB,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIhB,IAAI,CAAC0B,KAAK,CAAC,CAAC,EAAE;MACrB,MAAM;QAAElB;MAAM,CAAC,GAAGQ,IAAI;MACtB,MAAMW,WAAW,GAAGnB,KAAK,CAACoB,iBAAiB,CAAC,CAAC,IAAIpB,KAAK,CAACqB,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAE9B,IAAI,CAAC;IAC1C;EACF,CAAC;EAED+C,iBAAiB,EAAE;IACjBC,IAAIA,CAAChC,IAAI,EAAE;MACT,MAAM;QAAEnC,IAAI;QAAE2B;MAAM,CAAC,GAAGQ,IAAI;MAE5B,IAAIzE,sBAAsB,CAACsC,IAAI,CAAC,EAAE;MAClC,MAAM2C,MAAM,GAAG3C,IAAI,CAACQ,WAAW;MAC/B,IAAI/C,kBAAkB,CAACkF,MAAM,CAAC,IAAI9E,qBAAqB,CAAC8E,MAAM,CAAC,EAAE;QAC/D,MAAMvB,EAAE,GAAGuB,MAAM,CAACvB,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAMgD,OAAO,GAAGzC,KAAK,CAAC0C,UAAU,CAACjD,EAAE,CAACN,IAAI,CAAC;QACzCsD,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAACnC,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIrD,qBAAqB,CAAC6D,MAAM,CAAC,EAAE;QACxC,KAAK,MAAM4B,IAAI,IAAI5B,MAAM,CAAC6B,YAAY,EAAE;UACtC,KAAK,MAAM1D,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAACvH,qBAAqB,CAACqH,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGzC,KAAK,CAAC0C,UAAU,CAACvD,IAAI,CAAC;YACtCsD,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAACnC,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAEDuC,gBAAgBA,CAACvC,IAAI,EAAE;IACrBA,IAAI,CAACR,KAAK,CAAC4B,cAAc,CAAC,CAAC,CAACF,mBAAmB,CAAClB,IAAI,CAAC;EACvD,CAAC;EAEDwC,oBAAoBA,CAACxC,IAAI,EAAEuB,KAAK,EAAE;IAChCA,KAAK,CAACkB,WAAW,CAAClE,IAAI,CAACyB,IAAI,CAAC;EAC9B,CAAC;EAED0C,gBAAgBA,CAAC1C,IAAI,EAAEuB,KAAK,EAAE;IAC5BA,KAAK,CAACO,kBAAkB,CAACvD,IAAI,CAACyB,IAAI,CAAC;EACrC,CAAC;EAED2C,eAAeA,CAAC3C,IAAI,EAAEuB,KAAK,EAAE;IAC3B,IAAIvB,IAAI,CAACnC,IAAI,CAAC+E,QAAQ,KAAK,QAAQ,EAAE;MACnCrB,KAAK,CAACO,kBAAkB,CAACvD,IAAI,CAACyB,IAAI,CAAC;IACrC;EACF,CAAC;EAED6C,WAAWA,CAAC7C,IAAI,EAAE;IAChB,IAAIR,KAAY,GAAGQ,IAAI,CAACR,KAAK;IAC7B,IAAIA,KAAK,CAACQ,IAAI,KAAKA,IAAI,EAAER,KAAK,GAAGA,KAAK,CAACyB,MAAO;IAE9C,MAAMA,MAAM,GAAGzB,KAAK,CAAC4B,cAAc,CAAC,CAAC;IACrCH,MAAM,CAACC,mBAAmB,CAAClB,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAAC1E,kBAAkB,CAAC,CAAC,IAAI0E,IAAI,CAACnC,IAAI,CAACoB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGe,IAAI,CAACnC,IAAI,CAACoB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpBqB,IAAI,CAACR,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC,GAAGqB,IAAI,CAACR,KAAK,CAACyB,MAAM,CAAEiB,UAAU,CAACvD,IAAI,CAAE;IAClE;EACF,CAAC;EAEDmE,WAAWA,CAAC9C,IAAI,EAAE;IAChBA,IAAI,CAACR,KAAK,CAACsB,eAAe,CAAC,KAAK,EAAEd,IAAI,CAAC;EACzC,CAAC;EAED+C,QAAQA,CAAC/C,IAAI,EAAE;IACb,MAAMgD,MAAM,GAAGhD,IAAI,CAACS,GAAG,CAAC,QAAQ,CAAC;IACjC,KAAK,MAAMwC,KAAK,IAAID,MAAM,EAAE;MAC1BhD,IAAI,CAACR,KAAK,CAACsB,eAAe,CAAC,OAAO,EAAEmC,KAAK,CAAC;IAC5C;IAKA,IACEjD,IAAI,CAACC,oBAAoB,CAAC,CAAC,IAC3BD,IAAI,CAACnC,IAAI,CAACoB,EAAE,IAGV,CAACe,IAAI,CAACnC,IAAI,CAACoB,EAAE,CAACkB,iBAAiB,CAAC,EAClC;MACAH,IAAI,CAACR,KAAK,CAACsB,eAAe,CAAC,OAAO,EAAEd,IAAI,CAACS,GAAG,CAAC,IAAI,CAAC,EAAET,IAAI,CAAC;IAC3D;EACF,CAAC;EAEDkD,eAAeA,CAAClD,IAAI,EAAE;IACpB,IACEA,IAAI,CAACnC,IAAI,CAACoB,EAAE,IAGV,CAACe,IAAI,CAACnC,IAAI,CAACoB,EAAE,CAACkB,iBAAiB,CAAC,EAClC;MACAH,IAAI,CAACR,KAAK,CAACsB,eAAe,CAAC,OAAO,EAAEd,IAAI,CAACS,GAAG,CAAC,IAAI,CAAC,EAAET,IAAI,CAAC;IAC3D;EACF,CAAC;EAEDmD,gBAAgBA,CAACnD,IAAI,EAAE;IACrBA,IAAI,CAACoD,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAED,IAAIC,YAAkD;AAEtD,IAAIC,GAAG,GAAG,CAAC;AAKX,MAAMC,KAAK,CAAC;EAsBVC,WAAWA,CAACxD,IAAsC,EAAE;IAAA,KArBpDsD,GAAG;IAAA,KAEHtD,IAAI;IAAA,KACJyD,KAAK;IAAA,KAELC,MAAM;IAAA,KAENC,MAAM;IAAA,KACN9D,QAAQ;IAAA,KAER+D,aAAa;IAAA,KACb9D,OAAO;IAAA,KAEP+D,OAAO;IAAA,KACPC,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAElG;IAAK,CAAC,GAAGmC,IAAI;IACrB,MAAMgE,MAAM,GAAGC,YAAU,CAACxD,GAAG,CAAC5C,IAAI,CAAC;IAGnC,IAAI,CAAAmG,MAAM,oBAANA,MAAM,CAAEhE,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAOgE,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACrG,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAACyF,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG5F,IAAI;IACjB,IAAI,CAACmC,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAAC2D,MAAM,GAAG,IAAIQ,GAAG,CAAC,CAAC;IACvB,IAAI,CAACT,MAAM,GAAG,KAAK;IAEgB;MAEjChE,MAAM,CAAC0E,gBAAgB,CAAC,IAAI,EAAE;QAC5B3E,UAAU,EAAE;UACV4E,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE,IAAI;UACd/F,KAAK,EAAEkB,MAAM,CAACC,MAAM,CAAC,IAAI;QAC3B,CAAC;QACDC,IAAI,EAAE;UACJyE,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE,IAAI;UACd/F,KAAK,EAAEkB,MAAM,CAACC,MAAM,CAAC,IAAI;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;EAcA,IAAIsB,MAAMA,CAAA,EAAG;IAAA,IAAAuD,OAAA;IACX,IAAIvD,MAAM;MACRjB,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAAA,IAAAyE,KAAA;MAED,MAAMC,UAAU,GAAG1E,IAAI,CAACjB,GAAG,KAAK,KAAK,IAAIiB,IAAI,CAAC2E,OAAO,KAAK,YAAY;MACtE3E,IAAI,GAAGA,IAAI,CAAC0B,UAAU;MACtB,IAAIgD,UAAU,IAAI1E,IAAI,CAACjE,QAAQ,CAAC,CAAC,EAAEiE,IAAI,GAAGA,IAAI,CAAC0B,UAAU;MACzD,KAAA+C,KAAA,GAAIzE,IAAI,aAAJyE,KAAA,CAAMG,OAAO,CAAC,CAAC,EAAE3D,MAAM,GAAGjB,IAAI;IACpC,CAAC,QAAQA,IAAI,IAAI,CAACiB,MAAM;IAExB,QAAAuD,OAAA,GAAOvD,MAAM,qBAANuD,OAAA,CAAQhF,KAAK;EACtB;EAEA,IAAIC,UAAUA,CAAA,EAAG;IACf,MAAM,IAAIoF,KAAK,CACb,gFACF,CAAC;EACH;EAEA,IAAIjF,IAAIA,CAAA,EAAG;IACT,MAAM,IAAIiF,KAAK,CACb,oEACF,CAAC;EACH;EAMAC,6BAA6BA,CAACnG,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAAC8F,qBAAqB,CAACpG,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;EACtB;EAMA8F,qBAAqBA,CAACpG,IAAa,EAAE;IACnC,OAAO3D,UAAU,CAAC,IAAI,CAACgK,WAAW,CAACrG,IAAI,CAAC,CAAC;EAC3C;EAMAqG,WAAWA,CAACrG,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAG3B,YAAY,CAAC2B,IAAI,CAAC,CAACsG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAEjE,IAAI3B,GAAG;IACP,IAAI4B,CAAC,GAAG,CAAC;IACT,GAAG;MACD5B,GAAG,GAAG,IAAI3E,IAAI,EAAE;MAOhB,IAAIuG,CAAC,IAAI,EAAE,EAAE5B,GAAG,IAAI4B,CAAC,GAAG,CAAC,CAAC,KACrB,IAAIA,CAAC,IAAI,CAAC,EAAE5B,GAAG,IAAI4B,CAAC,GAAG,CAAC,CAAC,KACzB,IAAIA,CAAC,IAAI,CAAC,EAAE5B,GAAG,IAAI4B,CAAC,GAAG,CAAC;MAC7BA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACC,QAAQ,CAAC7B,GAAG,CAAC,IAClB,IAAI,CAAC8B,UAAU,CAAC9B,GAAG,CAAC,IACpB,IAAI,CAAC+B,SAAS,CAAC/B,GAAG,CAAC,IACnB,IAAI,CAACgC,YAAY,CAAChC,GAAG,CAAC;IAGxB,MAAMiC,OAAO,GAAG,IAAI,CAAC1E,gBAAgB,CAAC,CAAC;IAIhC;MAEL0E,OAAO,CAAC9F,UAAU,CAAC6D,GAAG,CAAC,GAAG,IAAI;MAE9BiC,OAAO,CAAC3F,IAAI,CAAC0D,GAAG,CAAC,GAAG,IAAI;IAC1B;IAEA,OAAOA,GAAG;EACZ;EAEAkC,sBAAsBA,CAAC3H,IAAY,EAAE4H,WAAoB,EAAE;IACzD,MAAM3H,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAImB,EAAE,GAAGnB,KAAK,CAAC4H,IAAI,CAAC,GAAG,CAAC;IACxBzG,EAAE,GAAGA,EAAE,CAACgG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIQ,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACT,WAAW,CAAC/F,EAAE,CAAC0G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAAC/H,IAAY,EAAE4H,WAAoB,EAAE;IACnE,OAAOzK,UAAU,CAAC,IAAI,CAACwK,sBAAsB,CAAC3H,IAAI,EAAE4H,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAAChI,IAAY,EAAW;IAC9B,IAAIpB,gBAAgB,CAACoB,IAAI,CAAC,IAAIvB,OAAO,CAACuB,IAAI,CAAC,IAAIP,gBAAgB,CAACO,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMoE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACrE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIsD,OAAO,EAAE;QACX,OAAOA,OAAO,CAAC6D,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAACvH,IAAI,CAACc,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMAoH,qBAAqBA,CAAClI,IAAY,EAAEmI,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAAChI,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMoB,EAAE,GAAG,IAAI,CAAC2G,gCAAgC,CAAC/H,IAAI,CAAC;MACtD,IAAI,CAACmI,QAAQ,EAAE;QACb,IAAI,CAACzH,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEAgH,0BAA0BA,CACxB3H,KAAc,EACd4H,IAAiB,EACjBvH,IAAY,EACZM,EAAO,EACP;IAEA,IAAIiH,IAAI,KAAK,OAAO,EAAE;IAItB,IAAI5H,KAAK,CAAC4H,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACd5H,KAAK,CAAC4H,IAAI,KAAK,KAAK,IACpB5H,KAAK,CAAC4H,IAAI,KAAK,OAAO,IACtB5H,KAAK,CAAC4H,IAAI,KAAK,QAAQ,IAEtB5H,KAAK,CAAC4H,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAACnG,IAAI,CAACoG,GAAG,CAACC,UAAU,CAC5BpH,EAAE,EACF,0BAA0BN,IAAI,GAAG,EACjC2H,SACF,CAAC;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAMxE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACsE,OAAO,CAAC;IACxC,IAAIvE,OAAO,EAAE;MACXwE,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAAC7H,IAAI;MACpD,MAAM+H,OAAO,GAAG,IAAIC,gBAAO,CAAC1E,OAAO,EAAEuE,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAItH,KAAwB,GAAG,IAAI;IACnC,GAAG;MACDwH,OAAO,CAACC,GAAG,CAAC,GAAG,EAAEzH,KAAK,CAACiE,KAAK,CAAC1F,IAAI,CAAC;MAClC,KAAK,MAAMY,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAAC9C,KAAK,CAACK,QAAQ,CAAC,EAAE;QAC9C,MAAMoC,OAAO,GAAGzC,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC;QACpCqI,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEtI,IAAI,EAAE;UACtBmH,QAAQ,EAAE7D,OAAO,CAAC6D,QAAQ;UAC1BrG,UAAU,EAAEwC,OAAO,CAACxC,UAAU;UAC9ByH,UAAU,EAAEjF,OAAO,CAACH,kBAAkB,CAAC3D,MAAM;UAC7C+H,IAAI,EAAEjE,OAAO,CAACiE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAAS1G,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAC9B+F,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAEA3B,QAAQA,CAACxG,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAACwI,QAAQ,CAACxI,IAAI,CAAC;EAC9B;EAEAwI,QAAQA,CAACxI,IAAY,EAAE;IACrB,OAAO,IAAI,CAACgF,MAAM,CAAClD,GAAG,CAAC9B,IAAI,CAAC;EAC9B;EAEAyI,aAAaA,CAACpH,IAAkC,EAAE;IAChD,IAAI,CAAC2D,MAAM,CAACO,GAAG,CAAClE,IAAI,CAACnC,IAAI,CAACwJ,KAAK,CAAC1I,IAAI,EAAEqB,IAAI,CAAC;EAC7C;EAEAkB,mBAAmBA,CAAClB,IAAsB,EAAE;IAC1C,IAAIA,IAAI,CAACsH,kBAAkB,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACF,aAAa,CAACpH,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAACtE,qBAAqB,CAAC,CAAC,EAAE;MACvC,IAAI,CAACoF,eAAe,CAAC,SAAS,EAAEd,IAAI,CAACS,GAAG,CAAC,IAAI,CAAC,EAAET,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAACrD,qBAAqB,CAAC,CAAC,EAAE;MACvC,MAAM0F,YAAY,GAAGrC,IAAI,CAACS,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAEyF;MAAK,CAAC,GAAGlG,IAAI,CAACnC,IAAI;MAC1B,KAAK,MAAM2C,MAAM,IAAI6B,YAAY,EAAE;QACjC,IAAI,CAACvB,eAAe,CAClBoF,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,GAAG,OAAO,GAAGA,IAAI,EAC3D1F,MACF,CAAC;MACH;IACF,CAAC,MAAM,IAAIR,IAAI,CAAC1E,kBAAkB,CAAC,CAAC,EAAE;MACpC,IAAI0E,IAAI,CAACnC,IAAI,CAAC0J,OAAO,EAAE;MACvB,IAAI,CAACzG,eAAe,CAAC,KAAK,EAAEd,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAACpE,mBAAmB,CAAC,CAAC,EAAE;MACrC,MAAM4L,iBAAiB,GACrBxH,IAAI,CAACnC,IAAI,CAAC4J,UAAU,KAAK,MAAM,IAAIzH,IAAI,CAACnC,IAAI,CAAC4J,UAAU,KAAK,QAAQ;MACtE,MAAMvJ,UAAU,GAAG8B,IAAI,CAACS,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAMiH,SAAS,IAAIxJ,UAAU,EAAE;QAClC,MAAMyJ,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,CAAC,CAAC,KAC3BF,SAAS,CAAC7J,IAAI,CAAC4J,UAAU,KAAK,MAAM,IACnCC,SAAS,CAAC7J,IAAI,CAAC4J,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAAC3G,eAAe,CAAC6G,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAI1H,IAAI,CAACvC,mBAAmB,CAAC,CAAC,EAAE;MAErC,MAAM+C,MAAM,GAAGR,IAAI,CAACS,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAAClF,kBAAkB,CAAC,CAAC,IAC3BkF,MAAM,CAAC9E,qBAAqB,CAAC,CAAC,IAC9B8E,MAAM,CAAC7D,qBAAqB,CAAC,CAAC,EAC9B;QACA,IAAI,CAACuE,mBAAmB,CAACV,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACM,eAAe,CAAC,SAAS,EAAEd,IAAI,CAAC;IACvC;EACF;EAEAtC,kBAAkBA,CAAA,EAAG;IACnB,OAAOA,kBAAkB,CAAC,CAAC;EAC7B;EAEAmK,yBAAyBA,CAAC7H,IAAsB,EAAE;IAChD,MAAM8H,GAAG,GAAG9H,IAAI,CAAC+H,wBAAwB,CAAC,CAAC;IAC3C,KAAK,MAAMpJ,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAACwF,GAAG,CAAC,EAAE;MAAA,IAAAE,gBAAA;MACnC,CAAAA,gBAAA,OAAI,CAAC9F,UAAU,CAACvD,IAAI,CAAC,aAArBqJ,gBAAA,CAAuBC,QAAQ,CAACjI,IAAI,CAAC;IACvC;EACF;EAEAc,eAAeA,CACboF,IAAqB,EACrBlG,IAAsB,EACtBkI,WAA6B,GAAGlI,IAAI,EACpC;IACA,IAAI,CAACkG,IAAI,EAAE,MAAM,IAAIiC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAInI,IAAI,CAACrD,qBAAqB,CAAC,CAAC,EAAE;MAChC,MAAMyL,WAAW,GAAGpI,IAAI,CAACS,GAAG,CAAC,cAAc,CAAC;MAC5C,KAAK,MAAMD,MAAM,IAAI4H,WAAW,EAAE;QAChC,IAAI,CAACtH,eAAe,CAACoF,IAAI,EAAE1F,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMS,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACtC,MAAMiH,GAAG,GAAG9H,IAAI,CAACqI,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAM1J,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAACwF,GAAG,CAAC,EAAE;MAG5B;QAEL7G,MAAM,CAACxB,UAAU,CAACd,IAAI,CAAC,GAAG,IAAI;MAChC;MAEA,KAAK,MAAMM,EAAE,IAAI6I,GAAG,CAACnJ,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAACgK,aAAa,CAAC3J,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAACtD,UAAU,KAAKiE,EAAE,EAAE;UAE7B,IAAI,CAACgH,0BAA0B,CAAC3H,KAAK,EAAE4H,IAAI,EAAEvH,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACTA,KAAK,CAAC2J,QAAQ,CAACC,WAAW,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAACrI,QAAQ,CAAClB,IAAI,CAAC,GAAG,IAAI4J,gBAAO,CAAC;YAChCvN,UAAU,EAAEiE,EAAE;YACdO,KAAK,EAAE,IAAI;YACXQ,IAAI,EAAEkI,WAAW;YACjBhC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEAsC,SAASA,CAAC3K,IAAoC,EAAE;IAC9C,IAAI,CAACiC,OAAO,CAACjC,IAAI,CAACc,IAAI,CAAC,GAAGd,IAAI;EAChC;EAEA4K,MAAMA,CAAC9J,IAAY,EAAW;IAGrB;MACL,IAAIa,KAAwB,GAAG,IAAI;MAEnC,GAAG;QAED,IAAIA,KAAK,CAACI,IAAI,CAACjB,IAAI,CAAC,EAAE,OAAO,IAAI;MACnC,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACyB,MAAM;MAE9B,OAAO,KAAK;IACd;EACF;EAEAoE,SAASA,CAAC1G,IAAY,EAAW;IAC/B,IAAIa,KAAwB,GAAG,IAAI;IAEnC,GAAG;MACD,IAAIA,KAAK,CAACM,OAAO,CAACnB,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAqE,YAAYA,CAAC3G,IAAY,EAAW;IAG3B;MAEL,OAAO,CAAC,CAAC,IAAI,CAACkC,gBAAgB,CAAC,CAAC,CAACpB,UAAU,CAACd,IAAI,CAAC;IACnD;EACF;EAEA+J,MAAMA,CAAC7K,IAA+B,EAAE8K,aAAuB,EAAW;IACxE,IAAIhN,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMoE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACrE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAI,CAACsD,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAI0G,aAAa,EAAE,OAAO1G,OAAO,CAAC6D,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACLrJ,gBAAgB,CAACoB,IAAI,CAAC,IACtBN,cAAc,CAACM,IAAI,CAAC,IACpBP,gBAAgB,CAACO,IAAI,CAAC,IACtBL,aAAa,CAACK,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzC,OAAO,CAACyC,IAAI,CAAC,EAAE;MAAA,IAAA+K,gBAAA;MACxB,IAAI/K,IAAI,CAACgL,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAAC7K,IAAI,CAACgL,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MAEA,IAAI,EAAAC,gBAAA,GAAA/K,IAAI,CAACiL,UAAU,qBAAfF,gBAAA,CAAiBzK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAACuK,MAAM,CAAC7K,IAAI,CAACkL,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAItN,WAAW,CAACwC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAMmL,MAAM,IAAInL,IAAI,CAACkL,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzN,QAAQ,CAAC2C,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAAC6K,MAAM,CAAC7K,IAAI,CAACmB,IAAI,EAAE2J,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAAC7K,IAAI,CAAC4D,KAAK,EAAEkH,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAI1N,iBAAiB,CAAC4C,IAAI,CAAC,IAAIT,iBAAiB,CAACS,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAMoL,IAAI,IAAIpL,IAAI,CAACqL,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACP,MAAM,CAACO,IAAI,EAAEN,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzM,kBAAkB,CAAC2B,IAAI,CAAC,IAAIV,kBAAkB,CAACU,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAMsL,IAAI,IAAItL,IAAI,CAACgB,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC6J,MAAM,CAACS,IAAI,EAAER,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI5M,QAAQ,CAAC8B,IAAI,CAAC,EAAE;MAAA,IAAAuL,iBAAA;MACzB,IAAIvL,IAAI,CAACwL,QAAQ,IAAI,CAAC,IAAI,CAACX,MAAM,CAAC7K,IAAI,CAACkB,GAAG,EAAE4J,aAAa,CAAC,EAAE,OAAO,KAAK;MAExE,IAAI,EAAAS,iBAAA,GAAAvL,IAAI,CAACiL,UAAU,qBAAfM,iBAAA,CAAiBjL,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhC,UAAU,CAAC0B,IAAI,CAAC,EAAE;MAAA,IAAAyL,iBAAA;MAE3B,IAAIzL,IAAI,CAACwL,QAAQ,IAAI,CAAC,IAAI,CAACX,MAAM,CAAC7K,IAAI,CAACkB,GAAG,EAAE4J,aAAa,CAAC,EAAE,OAAO,KAAK;MAExE,IAAI,EAAAW,iBAAA,GAAAzL,IAAI,CAACiL,UAAU,qBAAfQ,iBAAA,CAAiBnL,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAId,gBAAgB,CAACQ,IAAI,CAAC,IAAIA,IAAI,CAAC0L,MAAM,EAAE;QACzC,IAAI1L,IAAI,CAACW,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAACkK,MAAM,CAAC7K,IAAI,CAACW,KAAK,EAAEmK,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIjM,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAAC6K,MAAM,CAAC7K,IAAI,CAACiB,QAAQ,EAAE6J,aAAa,CAAC;IAClD,CAAC,MAAM,IAAInM,iBAAiB,CAACqB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMqB,UAAU,IAAIrB,IAAI,CAAC2L,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACd,MAAM,CAACxJ,UAAU,EAAEyJ,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIpM,0BAA0B,CAACsB,IAAI,CAAC,EAAE;MAC3C,OACEhB,cAAc,CAACgB,IAAI,CAAC4L,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAACrE,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C,IAAI,CAAChB,MAAM,CAAC7K,IAAI,CAAC8L,KAAK,EAAEhB,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAI7M,kBAAkB,CAAC+B,IAAI,CAAC,EAAE;MACnC,OACE,CAACA,IAAI,CAACwL,QAAQ,IACd1N,YAAY,CAACkC,IAAI,CAACY,MAAM,CAAC,IACzBZ,IAAI,CAACY,MAAM,CAACE,IAAI,KAAK,QAAQ,IAC7BhD,YAAY,CAACkC,IAAI,CAACa,QAAQ,CAAC,IAC3Bb,IAAI,CAACa,QAAQ,CAACC,IAAI,KAAK,KAAK,IAC5B,CAAC,IAAI,CAACyG,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC;IAEnD,CAAC,MAAM,IAAIvO,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MACjC,OACEhB,cAAc,CAACgB,IAAI,CAACe,MAAM,EAAE,YAAY,CAAC,IACzC,CAAC,IAAI,CAACwG,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C7L,IAAI,CAAC+I,SAAS,CAACzI,MAAM,KAAK,CAAC,IAC3B3D,CAAC,CAACoP,eAAe,CAAC/L,IAAI,CAAC+I,SAAS,CAAC,CAAC,CAAC,CAAC;IAExC,CAAC,MAAM;MACL,OAAOxK,SAAS,CAACyB,IAAI,CAAC;IACxB;EACF;EAMAgM,OAAOA,CAAC9K,GAAoB,EAAE+K,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAAChG,IAAI,CAAC/E,GAAG,CAAC,GAAG+K,GAAG;EAC9B;EAMAC,OAAOA,CAAChL,GAAoB,EAAO;IACjC,IAAIS,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,MAAMsE,IAAI,GAAGtE,KAAK,CAACsE,IAAI,CAAC/E,GAAG,CAAC;MAC5B,IAAI+E,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAAStE,KAAK,GAAGA,KAAK,CAACyB,MAAM;EAChC;EAOA+I,UAAUA,CAACjL,GAAW,EAAE;IACtB,IAAIS,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,MAAMsE,IAAI,GAAGtE,KAAK,CAACsE,IAAI,CAAC/E,GAAG,CAAC;MAC5B,IAAI+E,IAAI,IAAI,IAAI,EAAEtE,KAAK,CAACsE,IAAI,CAAC/E,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASS,KAAK,GAAGA,KAAK,CAACyB,MAAM;EAChC;EAEAgJ,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACvG,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAACwG,KAAK,CAAC,CAAC;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAMlK,IAAI,GAAG,IAAI,CAACA,IAAI;IAAC;IAMvBT,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI,CAACuE,IAAI,GAAGpE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIH,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,IAAIA,KAAK,CAACuE,QAAQ,EAAE;MACpB,IAAIvE,KAAK,CAACQ,IAAI,CAACmK,SAAS,CAAC,CAAC,EAAE;QAC1B;MACF;IACF,CAAC,QAAS3K,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAE9B,MAAMmJ,aAAa,GAAG5K,KAAM;IAE5B,MAAM+B,KAA0B,GAAG;MACjC9B,UAAU,EAAE,EAAE;MACdqC,kBAAkB,EAAE,EAAE;MACtBW,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACsB,QAAQ,GAAG,IAAI;IACpBV,YAAY,KAAZA,YAAY,GAAKgH,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACvC;MACEhH,KAAKA,CAACvD,IAAI,EAAE;QACVT,UAAU,CAACS,IAAI,CAACR,KAAK,CAAC;MACxB;IACF,CAAC,EACDc,gBAAgB,CACjB,CAAC;IAGF,IAAIN,IAAI,CAACjC,IAAI,KAAK,SAAS,EAAE;MAC3B,MAAMyM,YAAY,GAAGnH,YAAY,CAACrD,IAAI,CAACjC,IAAI,CAAC;MAC5C,IAAIyM,YAAY,EAAE;QAChB,KAAK,MAAMC,KAAK,IAAID,YAAY,CAACE,KAAK,EAAG;UACvCD,KAAK,CAACE,IAAI,CAACpJ,KAAK,EAAEvB,IAAI,EAAEuB,KAAK,CAAC;QAChC;MACF;IACF;IAGO;MACLvB,IAAI,CAACqK,QAAQ,CAAChH,YAAY,EAAE9B,KAAK,CAAC;IACpC;IACA,IAAI,CAACwC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAM/D,IAAI,IAAIuB,KAAK,CAACkB,WAAW,EAAE;MAEpC,MAAMqF,GAAG,GAAG9H,IAAI,CAAC+H,wBAAwB,CAAC,CAAC;MAC3C,KAAK,MAAMpJ,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAACwF,GAAG,CAAC,EAAE;QACnC,IAAI9H,IAAI,CAACR,KAAK,CAAC0C,UAAU,CAACvD,IAAI,CAAC,EAAE;QACjCyL,aAAa,CAAC5B,SAAS,CAACV,GAAG,CAACnJ,IAAI,CAAC,CAAC;MACpC;MAGAqB,IAAI,CAACR,KAAK,CAACqI,yBAAyB,CAAC7H,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAM4K,GAAG,IAAIrJ,KAAK,CAAC9B,UAAU,EAAE;MAClC,MAAMwC,OAAO,GAAG2I,GAAG,CAACpL,KAAK,CAAC0C,UAAU,CAAC0I,GAAG,CAAC/M,IAAI,CAACc,IAAI,CAAC;MACnD,IAAIsD,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAACyI,GAAG,CAAC;MACxB,CAAC,MAAM;QACLR,aAAa,CAAC5B,SAAS,CAACoC,GAAG,CAAC/M,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAMmC,IAAI,IAAIuB,KAAK,CAACO,kBAAkB,EAAE;MAC3C9B,IAAI,CAACR,KAAK,CAACqI,yBAAyB,CAAC7H,IAAI,CAAC;IAC5C;EACF;EAEAzB,IAAIA,CAACsM,IAMJ,EAAE;IACD,IAAI7K,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAAC6B,SAAS,CAAC,CAAC,EAAE;MACpB7B,IAAI,GAAG,IAAI,CAAC8K,gBAAgB,CAAC,CAAC,CAAC9K,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAAC+K,gBAAgB,CAAC,CAAC,IAAI,CAAC/K,IAAI,CAACmK,SAAS,CAAC,CAAC,EAAE;MACxDnK,IAAI,GAAG,IAAI,CAACoB,cAAc,CAAC,CAAC,CAACpB,IAAI;IACnC;IAEA,IAAIA,IAAI,CAACgL,iBAAiB,CAAC,CAAC,EAAE;MAC5BhL,IAAI,GAAG,CAAC,IAAI,CAACY,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEb,IAAI;IACnE;IAEA,MAAM;MAAEiK,IAAI;MAAEgB,MAAM;MAAE/E,IAAI,GAAG,KAAK;MAAEjH;IAAG,CAAC,GAAG4L,IAAI;IAM/C,IACE,CAACZ,IAAI,IACL,CAACgB,MAAM,KACN/E,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,IAClCnG,6BAA6B,CAACC,IAAI,CAAC,IACnC7E,gBAAgB,CAAC6E,IAAI,CAACiB,MAAM,EAAE;MAAErC,MAAM,EAAEoB,IAAI,CAACnC;IAAK,CAAC,CAAC,IACpDmC,IAAI,CAACiB,MAAM,CAAC2F,SAAS,CAACzI,MAAM,IAAI6B,IAAI,CAACnC,IAAI,CAACmF,MAAM,CAAC7E,MAAM,IACvDxC,YAAY,CAACsD,EAAE,CAAC,EAChB;MACAe,IAAI,CAACkL,aAAa,CAAC,QAAQ,EAAEjM,EAAE,CAAC;MAChCe,IAAI,CAACR,KAAK,CAACsB,eAAe,CACxB,OAAO,EACPd,IAAI,CAACS,GAAG,CAAC,QAAQ,CAAC,CAACT,IAAI,CAACnC,IAAI,CAACmF,MAAM,CAAC7E,MAAM,GAAG,CAAC,CAChD,CAAC;MACD;IACF;IAEA,IAAI6B,IAAI,CAACmL,MAAM,CAAC,CAAC,IAAInL,IAAI,CAACoL,aAAa,CAAC,CAAC,IAAIpL,IAAI,CAACqL,UAAU,CAAC,CAAC,EAAE;MAC9DrL,IAAI,CAACsL,WAAW,CAAC,CAAC;MAClBtL,IAAI,GAAGA,IAAI,CAACS,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAM8K,UAAU,GAAGV,IAAI,CAACW,WAAW,IAAI,IAAI,GAAG,CAAC,GAAGX,IAAI,CAACW,WAAW;IAElE,MAAMC,OAAO,GAAG,eAAevF,IAAI,IAAIqF,UAAU,EAAE;IACnD,IAAIG,UAAU,GAAG,CAACT,MAAM,IAAIjL,IAAI,CAAC+J,OAAO,CAAC0B,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAMlL,MAAM,GAAGvD,mBAAmB,CAACiJ,IAAI,EAAE,EAAE,CAAC;MAE5C1F,MAAM,CAACgL,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAI1L,IAAI,CAAgC2L,gBAAgB,CAClE,MAAM,EACN,CAACnL,MAAM,CACT,CAAC;MACD,IAAI,CAACyK,MAAM,EAAEjL,IAAI,CAAC6J,OAAO,CAAC4B,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAG1O,kBAAkB,CAAC+B,EAAE,EAAEgL,IAAI,CAAC;IAC/C,MAAM4B,GAAG,GAAGH,UAAU,CAAC7N,IAAI,CAACwE,YAAY,CAAC9D,IAAI,CAACqN,UAAU,CAAC;IACzD5L,IAAI,CAACR,KAAK,CAACsB,eAAe,CAACoF,IAAI,EAAEwF,UAAU,CAACjL,GAAG,CAAC,cAAc,CAAC,CAACoL,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMAhL,gBAAgBA,CAAA,EAGd;IACA,IAAIrB,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,IAAIA,KAAK,CAACQ,IAAI,CAACmK,SAAS,CAAC,CAAC,EAAE;QAC1B,OAAO3K,KAAK;MAId;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAC9B,MAAM,IAAI4D,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMAjE,iBAAiBA,CAAA,EAAiB;IAChC,IAAIpB,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,IAAIA,KAAK,CAACQ,IAAI,CAAC8L,gBAAgB,CAAC,CAAC,EAAE;QACjC,OAAOtM,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAI5B,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,IAAIA,KAAK,CAACQ,IAAI,CAAC+L,aAAa,CAAC,CAAC,EAAE;QAC9B,OAAOvM,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAC9B,MAAM,IAAI4D,KAAK,CACb,8EACF,CAAC;EACH;EAOAiG,gBAAgBA,CAAA,EAAG;IACjB,IAAItL,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,IAAI,CAACA,KAAK,CAACQ,IAAI,CAAC6B,SAAS,CAAC,CAAC,EAAE;QAC3B,OAAOrC,KAAK,CAAC4B,cAAc,CAAC,CAAC;MAC/B;IACF,CAAC,QAAS5B,KAAK,GAAGA,KAAK,CAACyB,MAAM,CAAEA,MAAM;IACtC,MAAM,IAAI4D,KAAK,CACb,8EACF,CAAC;EACH;EAMAmH,cAAcA,CAAA,EAA4B;IACxC,MAAMlE,GAAG,GAAGpI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIH,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,KAAK,MAAMT,GAAG,IAAIW,MAAM,CAAC4C,IAAI,CAAC9C,KAAK,CAACK,QAAQ,CAAC,EAAE;QAC7C,IAAId,GAAG,IAAI+I,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAAC/I,GAAG,CAAC,GAAGS,KAAK,CAACK,QAAQ,CAACd,GAAG,CAAC;QAChC;MACF;MACAS,KAAK,GAAGA,KAAK,CAACyB,MAAM;IACtB,CAAC,QAAQzB,KAAK;IAEd,OAAOsI,GAAG;EACZ;EAEAmE,uBAAuBA,CAACtN,IAAY,EAAEd,IAAY,EAAW;IAC3D,OAAO,IAAI,CAACqO,oBAAoB,CAACvN,IAAI,CAAC,KAAKd,IAAI;EACjD;EAEAqE,UAAUA,CAACvD,IAAY,EAAuB;IAC5C,IAAIa,KAAwB,GAAG,IAAI;IACnC,IAAI2M,YAAY;IAEhB,GAAG;MACD,MAAMlK,OAAO,GAAGzC,KAAK,CAAC8I,aAAa,CAAC3J,IAAI,CAAC;MACzC,IAAIsD,OAAO,EAAE;QAAA,IAAAmK,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAcvK,SAAS,CAAC,CAAC,IACzBI,OAAO,CAACiE,IAAI,KAAK,OAAO,IACxBjE,OAAO,CAACiE,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAOjE,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRtD,IAAI,KAAK,WAAW,IACpBa,KAAK,CAACQ,IAAI,CAACqL,UAAU,CAAC,CAAC,IACvB,CAAC7L,KAAK,CAACQ,IAAI,CAACE,yBAAyB,CAAC,CAAC,EACvC;QACA;MACF;MACAiM,YAAY,GAAG3M,KAAK,CAACQ,IAAI;IAC3B,CAAC,QAASR,KAAK,GAAGA,KAAK,CAACyB,MAAM;EAChC;EAEAqH,aAAaA,CAAC3J,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACkB,QAAQ,CAAClB,IAAI,CAAC;EAC5B;EAEAuN,oBAAoBA,CAACvN,IAAY,EAA4B;IAAA,IAAA0N,iBAAA;IAC3D,QAAAA,iBAAA,GAAO,IAAI,CAACnK,UAAU,CAACvD,IAAI,CAAC,qBAArB0N,iBAAA,CAAuBrR,UAAU;EAC1C;EAEAsR,uBAAuBA,CAAC3N,IAAY,EAA4B;IAC9D,MAAMsD,OAAO,GAAG,IAAI,CAACpC,QAAQ,CAAClB,IAAI,CAAC;IACnC,OAAOsD,OAAO,oBAAPA,OAAO,CAAEjH,UAAU;EAC5B;EAEAuR,aAAaA,CAAC5N,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAAC2J,aAAa,CAAC3J,IAAI,CAAC;EACnC;EAQAyG,UAAUA,CACRzG,IAAY,EACZkM,IAEgE,EAChE;IACA,IAAI,CAAClM,IAAI,EAAE,OAAO,KAAK;IAEvB,IAAI+K,SAAS;IACb,IAAI8C,MAAM;IACV,IAAIC,SAAS;IACb,IAAI,OAAO5B,IAAI,KAAK,QAAQ,EAAE;MAC5BnB,SAAS,GAAGmB,IAAI,CAACnB,SAAS;MAC1B8C,MAAM,GAAG3B,IAAI,CAAC2B,MAAM;MACpBC,SAAS,GAAG5B,IAAI,CAAC4B,SAAS;IAC5B,CAAC,MAAM,IAAI,OAAO5B,IAAI,KAAK,SAAS,EAAE;MACpCnB,SAAS,GAAGmB,IAAI;IAClB;IACA,IAAIrL,KAAwB,GAAG,IAAI;IACnC,GAAG;MACD,IAAIiN,SAAS,KAAKjN,KAAK,EAAE;QACvB;MACF;MACA,IAAIA,KAAK,CAAC+M,aAAa,CAAC5N,IAAI,CAAC,EAAE;QAC7B,OAAO,IAAI;MACb;IACF,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAE9B,IAAI,CAACuL,MAAM,IAAI,IAAI,CAAC/D,MAAM,CAAC9J,IAAI,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI,CAAC+K,SAAS,IAAInG,KAAK,CAACzD,OAAO,CAAC4M,QAAQ,CAAC/N,IAAI,CAAC,EAAE,OAAO,IAAI;IAC3D,IAAI,CAAC+K,SAAS,IAAInG,KAAK,CAACoJ,gBAAgB,CAACD,QAAQ,CAAC/N,IAAI,CAAC,EAAE,OAAO,IAAI;IACpE,OAAO,KAAK;EACd;EAEAiO,gBAAgBA,CACdjO,IAAY,EACZkM,IAAgD,EAChD;IAAA,IAAAgC,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAAC5L,MAAM,qBAAX4L,YAAA,CAAazH,UAAU,CAACzG,IAAI,EAAEkM,IAAI,CAAC;EAC5C;EAMAiC,aAAaA,CAACnO,IAAY,EAAEa,KAAY,EAAE;IACxC,MAAMuN,IAAI,GAAG,IAAI,CAAC7K,UAAU,CAACvD,IAAI,CAAC;IAClC,IAAIoO,IAAI,EAAE;MACRA,IAAI,CAACvN,KAAK,CAACwN,gBAAgB,CAACrO,IAAI,CAAC;MACjCoO,IAAI,CAACvN,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC,GAAGoO,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAACrO,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACkB,QAAQ,CAAClB,IAAI,CAAC;EAC5B;EAEAsO,aAAaA,CAACtO,IAAY,EAAE;IAAA,IAAAuO,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAChL,UAAU,CAACvD,IAAI,CAAC,aAArBuO,iBAAA,CAAuB1N,KAAK,CAACwN,gBAAgB,CAACrO,IAAI,CAAC;IAK5C;MACL,IAAIa,KAAwB,GAAG,IAAI;MACnC,GAAG;QAED,IAAIA,KAAK,CAACI,IAAI,CAACjB,IAAI,CAAC,EAAE;UAEpBa,KAAK,CAACI,IAAI,CAACjB,IAAI,CAAC,GAAG,KAAK;QAC1B;MACF,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACyB,MAAM;IAChC;EACF;EAYAkM,cAAcA,CACZC,IAAkD,GAAGnO,EAAE,IACrD,IAAI,CAACV,IAAI,CAAC;IAAEU;EAAG,CAAC,CAAC,EACnB;IACA,IAAI,CAACiL,KAAK,CAAC,CAAC;IAEZ,MAAMmD,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,KAAK,MAAM3O,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAAC,IAAI,CAACzC,QAAQ,CAAC,EAAE;MAC7C,MAAMoC,OAAO,GAAG,IAAI,CAACpC,QAAQ,CAAClB,IAAI,CAAC;MACnC,IAAI,CAACsD,OAAO,EAAE;MACd,MAAM;QAAEjC;MAAK,CAAC,GAAGiC,OAAO;MACxB,IAAI,CAACjC,IAAI,CAACuN,oBAAoB,CAAC,CAAC,EAAE;MAClC,MAAM;QAAEtM,MAAM;QAAES;MAAW,CAAC,GAAG1B,IAAI;MAEnC,IAAIiB,MAAM,CAACiF,IAAI,KAAK,KAAK,IAAImH,IAAI,CAACG,GAAG,CAACvM,MAAM,CAAC,EAAE;MAC/CoM,IAAI,CAACI,GAAG,CAACzN,IAAI,CAACiB,MAAM,CAAC;MAErB,IAAIyM,OAAO;MACX,MAAMzD,IAAI,GAAG,EAAE;MACf,KAAK,MAAM7H,IAAI,IAAInB,MAAM,CAACoB,YAAY,EAAE;QACtCqL,OAAO,WAAPA,OAAO,GAAPA,OAAO,GAAKtL,IAAI,CAACnD,EAAE;QACnB,IAAImD,IAAI,CAAC6H,IAAI,EAAE;UACbA,IAAI,CAAC1L,IAAI,CACP3D,oBAAoB,CAClB,GAAG,EAEHwH,IAAI,CAACnD,EAAE,EACPmD,IAAI,CAAC6H,IACP,CACF,CAAC;QACH;QAEA,MAAMnC,GAAG,GAAGpI,MAAM,CAAC4C,IAAI,CAACvH,qBAAqB,CAACqH,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvE,KAAK,MAAMzD,IAAI,IAAImJ,GAAG,EAAE;UACtBsF,IAAI,CAACpS,UAAU,CAAC2D,IAAI,CAAC,EAAEyD,IAAI,CAAC6H,IAAI,IAAI,IAAI,CAAC;QAC3C;MACF;MAGA,IAAIvI,UAAU,CAACA,UAAU,CAACiM,eAAe,CAAC;QAAE3O,IAAI,EAAEiC;MAAO,CAAC,CAAC,EAAE;QAE3DS,UAAU,CAACkM,WAAW,CAACF,OAAQ,CAAC;MAClC,CAAC,MAAM,IAAIzD,IAAI,CAAC9L,MAAM,KAAK,CAAC,EAAE;QAC5BuD,UAAU,CAACmM,MAAM,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAMC,IAAI,GAAG7D,IAAI,CAAC9L,MAAM,KAAK,CAAC,GAAG8L,IAAI,CAAC,CAAC,CAAC,GAAGtM,kBAAkB,CAACsM,IAAI,CAAC;QACnE,IAAIvI,UAAU,CAACA,UAAU,CAACqM,cAAc,CAAC;UAAE9D,IAAI,EAAEhJ;QAAO,CAAC,CAAC,EAAE;UAC1DS,UAAU,CAACkM,WAAW,CAACE,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLpM,UAAU,CAACkM,WAAW,CAAChR,mBAAmB,CAACkR,IAAI,CAAC,CAAC;QACnD;MACF;IACF;EACF;AACF;AAACE,OAAA,CAAAC,OAAA,GAAA1K,KAAA;AAjgCKA,KAAK,CA+DFzD,OAAO,GAAG,CAAC,GAAGpF,mBAAmB,EAAE,GAAGC,mBAAmB,CAAC;AA/D7D4I,KAAK,CAqEFoJ,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC;AA87B1B;EAG7CpJ,KAAK,CAAC2K,SAAS,CAACC,cAAc,GAAG,SAASA,cAAcA,CACtDC,GAAqC,EACrC5H,OAAwB,EACxBC,OAAwB,EACxBjI,KAAc,EACd;IACA,IAAI4P,GAAG,CAAC5H,OAAO,CAAC,EAAE;MAChB4H,GAAG,CAAC3H,OAAO,CAAC,GAAGjI,KAAK;MACpB4P,GAAG,CAAC5H,OAAO,CAAC,GAAG,IAAI;IACrB;EACF,CAAC;EAcDjD,KAAK,CAAC2K,SAAS,CAAC7D,QAAQ,GAAG,UAEzBxM,IAAS,EACTgN,IAAS,EACTtJ,KAAS,EACT;IACA,IAAA8I,cAAQ,EAACxM,IAAI,EAAEgN,IAAI,EAAE,IAAI,EAAEtJ,KAAK,EAAE,IAAI,CAACvB,IAAI,CAAC;EAC9C,CAAC;EAMDuD,KAAK,CAAC2K,SAAS,CAACG,YAAY,GAAG,SAASA,YAAYA,CAClD1P,IAAY,EACZuG,CAAS,EACT;IACA,IAAIjG,EAAE,GAAGN,IAAI;IACb,IAAIuG,CAAC,GAAG,CAAC,EAAEjG,EAAE,IAAIiG,CAAC;IAClB,OAAO,IAAIjG,EAAE,EAAE;EACjB,CAAC;EAIDsE,KAAK,CAAC2K,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAExCzQ,IAAY,EACZqH,CAAoB,EACpBqJ,mBAAoC,EACpC;IACA,IAAI5S,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMoE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACrE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIsD,OAAO,YAAPA,OAAO,CAAE6D,QAAQ,IAAI7D,OAAO,CAACjC,IAAI,CAACwO,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAO3Q,IAAI;MACb;IACF;IAEA,IAAI5C,iBAAiB,CAAC4C,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,EAAE;MAAEc,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAO9D,cAAc,CACnBiC,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC9B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CACpB,CAAC,EACDA,UAAU,CAAC,MAAM,CACnB,CAAC,EACD,CAAC6C,IAAI,CACP,CAAC;IACH;IAEA,IAAI4Q,UAAU;IACd,MAAMC,IAAI,GAAG,CAAC7Q,IAAI,CAAC;IACnB,IAAIqH,CAAC,KAAK,IAAI,EAAE;MAEduJ,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAOvJ,CAAC,KAAK,QAAQ,EAAE;MAChCwJ,IAAI,CAACnQ,IAAI,CAACxB,cAAc,CAACmI,CAAC,CAAC,CAAC;MAG5BuJ,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC3O,IAAI,CAACoG,GAAG,CAACwI,SAAS,CAACH,UAAU,CAAC,CAAC;MACjDA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAO5T,cAAc,CAAC,IAAI,CAACmF,IAAI,CAACoG,GAAG,CAACwI,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAClE,CAAC;EAMDnL,KAAK,CAAC2K,SAAS,CAACW,oBAAoB,GAAG,SAASA,oBAAoBA,CAClE,GAAGC,KAAe,EACO;IACzB,MAAMhH,GAAG,GAAGpI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAMuG,IAAI,IAAI4I,KAAK,EAAE;MACxB,IAAItP,KAAwB,GAAG,IAAI;MACnC,GAAG;QACD,KAAK,MAAMb,IAAI,IAAIe,MAAM,CAAC4C,IAAI,CAAC9C,KAAK,CAACK,QAAQ,CAAC,EAAE;UAC9C,MAAMoC,OAAO,GAAGzC,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC;UACpC,IAAIsD,OAAO,CAACiE,IAAI,KAAKA,IAAI,EAAE4B,GAAG,CAACnJ,IAAI,CAAC,GAAGsD,OAAO;QAChD;QACAzC,KAAK,GAAGA,KAAK,CAACyB,MAAM;MACtB,CAAC,QAAQzB,KAAK;IAChB;IAEA,OAAOsI,GAAG;EACZ,CAAC;EAEDpI,MAAM,CAAC0E,gBAAgB,CAACb,KAAK,CAAC2K,SAAS,EAAE;IACvCa,WAAW,EAAE;MACXzK,YAAY,EAAE,IAAI;MAClBD,UAAU,EAAE,IAAI;MAChB5D,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACT,IAAI,CAACiB,MAAM;MACzB;IACF,CAAC;IACDmF,GAAG,EAAE;MACH9B,YAAY,EAAE,IAAI;MAClBD,UAAU,EAAE,IAAI;MAChB5D,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACT,IAAI,CAACoG,GAAG;MACtB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}