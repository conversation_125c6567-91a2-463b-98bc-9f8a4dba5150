{"version": 3, "names": ["_t", "require", "_index", "_visitors", "_context", "VISITOR_KEYS", "traverseForScope", "path", "visitors", "state", "exploded", "explode", "enter", "exit", "Error", "_traverse", "parentPath", "parent", "node", "container", "key", "<PERSON><PERSON><PERSON>", "hub", "inPath", "NodePath", "get", "setScope", "call", "visitor", "type", "visit", "shouldSkip", "keys", "length", "prop", "Array", "isArray", "i", "value"], "sources": ["../../src/scope/traverseForScope.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { HubInterface, Visitor } from \"../index.ts\";\nimport { NodePath } from \"../index.ts\";\nimport { explode } from \"../visitors.ts\";\nimport { setScope } from \"../path/context.ts\";\n\nexport default function traverseForScope(\n  path: NodePath,\n  visitors: Visitor,\n  state: any,\n) {\n  const exploded = explode(visitors);\n\n  if (exploded.enter || exploded.exit) {\n    throw new Error(\"Should not be used with enter/exit visitors.\");\n  }\n\n  _traverse(\n    path.parentPath,\n    path.parent,\n    path.node,\n    path.container!,\n    path.key!,\n    path.listKey,\n    path.hub,\n    path,\n  );\n\n  function _traverse(\n    parentPath: NodePath,\n    parent: t.Node,\n    node: t.Node,\n    container: t.Node | t.Node[],\n    key: string | number,\n    listKey: string | null | undefined,\n    hub?: HubInterface,\n    inPath?: NodePath,\n  ) {\n    if (!node) {\n      return;\n    }\n\n    const path =\n      inPath ||\n      NodePath.get({\n        hub,\n        parentPath,\n        parent,\n        container,\n        listKey,\n        key,\n      });\n\n    setScope.call(path);\n\n    const visitor = exploded[node.type];\n    if (visitor) {\n      if (visitor.enter) {\n        for (const visit of visitor.enter) {\n          visit.call(state, path, state);\n        }\n      }\n      if (visitor.exit) {\n        for (const visit of visitor.exit) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    if (path.shouldSkip) {\n      return;\n    }\n\n    const keys = VISITOR_KEYS[node.type];\n    if (!keys?.length) {\n      return;\n    }\n\n    for (const key of keys) {\n      // @ts-expect-error key must present in node\n      const prop = node[key];\n      if (!prop) continue;\n      if (Array.isArray(prop)) {\n        for (let i = 0; i < prop.length; i++) {\n          const value = prop[i];\n          _traverse(path, node, value, prop, i, key);\n        }\n      } else {\n        _traverse(path, node, prop, node, key, null);\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAA8C;EALrCI;AAAY,IAAAL,EAAA;AAON,SAASM,gBAAgBA,CACtCC,IAAc,EACdC,QAAiB,EACjBC,KAAU,EACV;EACA,MAAMC,QAAQ,GAAG,IAAAC,iBAAO,EAACH,QAAQ,CAAC;EAElC,IAAIE,QAAQ,CAACE,KAAK,IAAIF,QAAQ,CAACG,IAAI,EAAE;IACnC,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EAEAC,SAAS,CACPR,IAAI,CAACS,UAAU,EACfT,IAAI,CAACU,MAAM,EACXV,IAAI,CAACW,IAAI,EACTX,IAAI,CAACY,SAAS,EACdZ,IAAI,CAACa,GAAG,EACRb,IAAI,CAACc,OAAO,EACZd,IAAI,CAACe,GAAG,EACRf,IACF,CAAC;EAED,SAASQ,SAASA,CAChBC,UAAoB,EACpBC,MAAc,EACdC,IAAY,EACZC,SAA4B,EAC5BC,GAAoB,EACpBC,OAAkC,EAClCC,GAAkB,EAClBC,MAAiB,EACjB;IACA,IAAI,CAACL,IAAI,EAAE;MACT;IACF;IAEA,MAAMX,IAAI,GACRgB,MAAM,IACNC,eAAQ,CAACC,GAAG,CAAC;MACXH,GAAG;MACHN,UAAU;MACVC,MAAM;MACNE,SAAS;MACTE,OAAO;MACPD;IACF,CAAC,CAAC;IAEJM,iBAAQ,CAACC,IAAI,CAACpB,IAAI,CAAC;IAEnB,MAAMqB,OAAO,GAAGlB,QAAQ,CAACQ,IAAI,CAACW,IAAI,CAAC;IACnC,IAAID,OAAO,EAAE;MACX,IAAIA,OAAO,CAAChB,KAAK,EAAE;QACjB,KAAK,MAAMkB,KAAK,IAAIF,OAAO,CAAChB,KAAK,EAAE;UACjCkB,KAAK,CAACH,IAAI,CAAClB,KAAK,EAAEF,IAAI,EAAEE,KAAK,CAAC;QAChC;MACF;MACA,IAAImB,OAAO,CAACf,IAAI,EAAE;QAChB,KAAK,MAAMiB,KAAK,IAAIF,OAAO,CAACf,IAAI,EAAE;UAChCiB,KAAK,CAACH,IAAI,CAAClB,KAAK,EAAEF,IAAI,EAAEE,KAAK,CAAC;QAChC;MACF;IACF;IACA,IAAIF,IAAI,CAACwB,UAAU,EAAE;MACnB;IACF;IAEA,MAAMC,IAAI,GAAG3B,YAAY,CAACa,IAAI,CAACW,IAAI,CAAC;IACpC,IAAI,EAACG,IAAI,YAAJA,IAAI,CAAEC,MAAM,GAAE;MACjB;IACF;IAEA,KAAK,MAAMb,GAAG,IAAIY,IAAI,EAAE;MAEtB,MAAME,IAAI,GAAGhB,IAAI,CAACE,GAAG,CAAC;MACtB,IAAI,CAACc,IAAI,EAAE;MACX,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACD,MAAM,EAAEI,CAAC,EAAE,EAAE;UACpC,MAAMC,KAAK,GAAGJ,IAAI,CAACG,CAAC,CAAC;UACrBtB,SAAS,CAACR,IAAI,EAAEW,IAAI,EAAEoB,KAAK,EAAEJ,IAAI,EAAEG,CAAC,EAAEjB,GAAG,CAAC;QAC5C;MACF,CAAC,MAAM;QACLL,SAAS,CAACR,IAAI,EAAEW,IAAI,EAAEgB,IAAI,EAAEhB,IAAI,EAAEE,GAAG,EAAE,IAAI,CAAC;MAC9C;IACF;EACF;AACF", "ignoreList": []}