{"name": "@antfu/ni", "type": "module", "version": "25.0.0", "description": "Use the right package manager", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/antfu-collective/ni#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu-collective/ni.git"}, "bugs": {"url": "https://github.com/antfu-collective/ni/issues"}, "exports": {".": "./dist/index.mjs", "./ni": "./dist/ni.mjs", "./nci": "./dist/nci.mjs", "./nr": "./dist/nr.mjs", "./nup": "./dist/nup.mjs", "./nlx": "./dist/nlx.mjs", "./na": "./dist/na.mjs", "./nun": "./dist/nun.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "bin": {"ni": "bin/ni.mjs", "nci": "bin/nci.mjs", "nr": "bin/nr.mjs", "nup": "bin/nup.mjs", "nlx": "bin/nlx.mjs", "na": "bin/na.mjs", "nun": "bin/nun.mjs"}, "files": ["bin", "dist"], "dependencies": {"ansis": "^4.0.0", "fzf": "^0.5.2", "package-manager-detector": "^1.3.0", "tinyexec": "^1.0.1"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@posva/prompts": "^2.4.4", "@types/ini": "^4.1.1", "@types/node": "^22.15.21", "@types/which": "^3.0.4", "bumpp": "^10.1.1", "eslint": "^9.27.0", "ini": "^5.0.0", "lint-staged": "^16.0.0", "simple-git-hooks": "^2.13.0", "taze": "^19.1.0", "terminal-link": "^4.0.0", "tinyglobby": "^0.2.14", "tsx": "^4.19.4", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.1.4", "which": "^5.0.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "scripts": {"dev": "tsx src/commands/ni.ts", "nr": "tsx src/commands/nr.ts", "build": "unbuild", "stub": "unbuild --stub", "release": "bumpp && pnpm publish", "typecheck": "tsc", "lint": "eslint", "test": "vitest"}}