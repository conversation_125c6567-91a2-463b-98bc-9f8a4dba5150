import { r as runCli, p as parseNi } from './shared/ni.b-W1u-ew.mjs';
import 'node:path';
import 'node:process';
import 'readline';
import 'events';
import 'ansis';
import 'package-manager-detector';
import 'tinyexec';
import 'node:fs';
import 'package-manager-detector/constants';
import 'os';
import 'tty';
import 'node:os';
import 'fs';
import 'fs/promises';
import 'path';
import 'package-manager-detector/commands';

runCli(
  (agent, args, hasLock) => parseNi(agent, [...args, "--frozen-if-present"], hasLock),
  { autoInstall: true }
);
