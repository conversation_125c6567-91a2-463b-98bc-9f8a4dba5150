export { C as CLI_TEMP_DIR, U as UnsupportedCommand, o as cmdExists, d as detect, n as exclude, t as formatPackageWithUrl, k as getCliCommand, c as getCommand, g as getConfig, a as getDefaultAgent, b as getGlobalAgent, q as limitText, j as parseNa, p as parseNi, i as parseNlx, e as parseNr, h as parseNun, f as parseNup, m as remove, l as run, r as runCli, s as serializeCommand, w as writeFileSafe } from './shared/ni.b-W1u-ew.mjs';
export * from 'package-manager-detector/commands';
export * from 'package-manager-detector/constants';
import 'node:path';
import 'node:process';
import 'readline';
import 'events';
import 'ansis';
import 'package-manager-detector';
import 'tinyexec';
import 'node:fs';
import 'os';
import 'tty';
import 'node:os';
import 'fs';
import 'fs/promises';
import 'path';
