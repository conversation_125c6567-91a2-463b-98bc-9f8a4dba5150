{"version": 3, "names": ["_core", "require", "_normalizeAndLoadMetadata", "toGetWrapperPayload", "lazy", "source", "metadata", "isSideEffectImport", "reexportAll", "includes", "Array", "isArray", "Error", "wrapReference", "ref", "payload", "t", "callExpression"], "sources": ["../src/lazy-modules.ts"], "sourcesContent": ["// TODO: Move `lazy` implementation logic into the CommonJS plugin, since other\n// modules systems do not support `lazy`.\n\nimport { types as t } from \"@babel/core\";\nimport {\n  type SourceModuleMetadata,\n  isSideEffectImport,\n} from \"./normalize-and-load-metadata.ts\";\n\nexport type Lazy = boolean | string[] | ((source: string) => boolean);\n\nexport function toGetWrapperPayload(lazy: Lazy) {\n  return (source: string, metadata: SourceModuleMetadata): null | \"lazy\" => {\n    if (lazy === false) return null;\n    if (isSideEffectImport(metadata) || metadata.reexportAll) return null;\n    if (lazy === true) {\n      // 'true' means that local relative files are eagerly loaded and\n      // dependency modules are loaded lazily.\n      return source.includes(\".\") ? null : \"lazy\";\n    }\n    if (Array.isArray(lazy)) {\n      return !lazy.includes(source) ? null : \"lazy\";\n    }\n    if (typeof lazy === \"function\") {\n      return lazy(source) ? \"lazy\" : null;\n    }\n    throw new Error(`.lazy must be a boolean, string array, or function`);\n  };\n}\n\nexport function wrapReference(\n  ref: t.Identifier,\n  payload: unknown,\n): t.Expression | null {\n  if (payload === \"lazy\") return t.callExpression(ref, []);\n  return null;\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AAOO,SAASE,mBAAmBA,CAACC,IAAU,EAAE;EAC9C,OAAO,CAACC,MAAc,EAAEC,QAA8B,KAAoB;IACxE,IAAIF,IAAI,KAAK,KAAK,EAAE,OAAO,IAAI;IAC/B,IAAI,IAAAG,4CAAkB,EAACD,QAAQ,CAAC,IAAIA,QAAQ,CAACE,WAAW,EAAE,OAAO,IAAI;IACrE,IAAIJ,IAAI,KAAK,IAAI,EAAE;MAGjB,OAAOC,MAAM,CAACI,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,MAAM;IAC7C;IACA,IAAIC,KAAK,CAACC,OAAO,CAACP,IAAI,CAAC,EAAE;MACvB,OAAO,CAACA,IAAI,CAACK,QAAQ,CAACJ,MAAM,CAAC,GAAG,IAAI,GAAG,MAAM;IAC/C;IACA,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;MAC9B,OAAOA,IAAI,CAACC,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI;IACrC;IACA,MAAM,IAAIO,KAAK,CAAC,oDAAoD,CAAC;EACvE,CAAC;AACH;AAEO,SAASC,aAAaA,CAC3BC,GAAiB,EACjBC,OAAgB,EACK;EACrB,IAAIA,OAAO,KAAK,MAAM,EAAE,OAAOC,WAAC,CAACC,cAAc,CAACH,GAAG,EAAE,EAAE,CAAC;EACxD,OAAO,IAAI;AACb", "ignoreList": []}