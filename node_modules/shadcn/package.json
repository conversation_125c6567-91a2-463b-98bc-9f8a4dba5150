{"name": "shadcn", "version": "3.5.0", "description": "Add components to your apps.", "publishConfig": {"access": "public"}, "license": "MIT", "author": {"name": "shadcn", "url": "https://twitter.com/shadcn"}, "repository": {"type": "git", "url": "https://github.com/shadcn/ui.git", "directory": "packages/shadcn"}, "files": ["dist"], "keywords": ["components", "ui", "tailwind", "radix-ui", "shadcn"], "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./registry": {"types": "./dist/registry/index.d.ts", "default": "./dist/registry/index.js"}, "./schema": {"types": "./dist/schema/index.d.ts", "default": "./dist/schema/index.js"}, "./mcp": {"types": "./dist/mcp/index.d.ts", "default": "./dist/mcp/index.js"}}, "bin": "./dist/index.js", "dependencies": {"@antfu/ni": "^25.0.0", "@babel/core": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/plugin-transform-typescript": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@dotenvx/dotenvx": "^1.48.4", "@modelcontextprotocol/sdk": "^1.17.2", "browserslist": "^4.26.2", "commander": "^14.0.0", "cosmiconfig": "^9.0.0", "dedent": "^1.6.0", "deepmerge": "^4.3.1", "diff": "^8.0.2", "execa": "^9.6.0", "fast-glob": "^3.3.3", "fs-extra": "^11.3.1", "fuzzysort": "^3.1.0", "https-proxy-agent": "^7.0.6", "kleur": "^4.1.5", "msw": "^2.10.4", "node-fetch": "^3.3.2", "ora": "^8.2.0", "postcss": "^8.5.6", "prompts": "^2.4.2", "recast": "^0.23.11", "stringify-object": "^5.0.0", "ts-morph": "^26.0.0", "tsconfig-paths": "^4.2.0", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/fs-extra": "^11.0.4", "@types/prompts": "^2.4.9", "@types/stringify-object": "^4.0.5", "rimraf": "^6.0.1", "tsup": "^8.5.0", "type-fest": "^4.41.0", "typescript": "^5.9.2"}, "scripts": {"dev": "tsup --watch", "build": "tsup", "typecheck": "tsc --noEmit", "clean": "rimraf dist && rimraf components", "start:dev": "cross-env REGISTRY_URL=http://localhost:4000/r node dist/index.js", "start:prod": "cross-env REGISTRY_URL=https://ui.shadcn.com/r node dist/index.js", "start": "node dist/index.js", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "release": "changeset version", "pub:beta": "pnpm build && pnpm publish --no-git-checks --access public --tag beta", "pub:next": "pnpm build && pnpm publish --no-git-checks --access public --tag next", "pub:release": "pnpm build && pnpm publish --access public", "test": "vitest run", "test:dev": "REGISTRY_URL=http://localhost:4000/r vitest run", "mcp:inspect": "pnpm dlx @modelcontextprotocol/inspector node dist/index.js mcp"}}