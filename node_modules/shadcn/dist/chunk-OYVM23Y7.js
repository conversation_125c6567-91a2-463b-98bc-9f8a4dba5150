import {g,q as q$1,p,r,m,h as h$1,o,i,j as j$1,k as k$1,l,u,t,b as b$1}from'./chunk-OG5VNDVA.js';import*as h from'path';import h__default,{basename}from'path';import {z}from'zod';import*as B from'fs/promises';import {tmpdir,homedir}from'os';import {green,cyan,yellow,red}from'kleur/colors';import {loadConfig,createMatchPath}from'tsconfig-paths';import {cosmiconfig}from'cosmiconfig';import Ae from'fast-glob';import Oe from'fs-extra';import {existsSync,statSync,promises}from'fs';import Tr from'ora';import {Project,SyntaxKind,ScriptKind,QuoteKind}from'ts-morph';import {transformFromAstSync}from'@babel/core';import {parse}from'@babel/parser';import zr from'@babel/plugin-transform-typescript';import*as Ke from'recast';import Yr from'prompts';import oe from'deepmerge';import {HttpsProxyAgent}from'https-proxy-agent';import Ps from'node-fetch';import {createHash}from'crypto';import $s from'stringify-object';import Zs from'fuzzysort';var ce=process.env.REGISTRY_URL??"https://ui.shadcn.com/r",Pe="new-york-v4",Et=[{name:"neutral",label:"Neutral"},{name:"gray",label:"Gray"},{name:"zinc",label:"Zinc"},{name:"stone",label:"Stone"},{name:"slate",label:"Slate"}],K={"@shadcn":`${ce}/styles/{style}/{name}.json`};var sn=[{name:"toast",deprecatedBy:"sonner",message:"The toast component is deprecated. Use the sonner component instead."},{name:"toaster",deprecatedBy:"sonner",message:"The toaster component is deprecated. Use the sonner component instead."}];function pe(e){return e.replace(/\${(\w+)}/g,(t,r)=>process.env[r]||"")}function fe(e){let t=[],r=/\${(\w+)}/g,s;for(;(s=r.exec(e))!==null;)t.push(s[1]);return t}var F={NOT_FOUND:"NOT_FOUND",UNAUTHORIZED:"UNAUTHORIZED",FORBIDDEN:"FORBIDDEN",FETCH_ERROR:"FETCH_ERROR",NOT_CONFIGURED:"NOT_CONFIGURED",INVALID_CONFIG:"INVALID_CONFIG",MISSING_ENV_VARS:"MISSING_ENV_VARS",LOCAL_FILE_ERROR:"LOCAL_FILE_ERROR",PARSE_ERROR:"PARSE_ERROR",VALIDATION_ERROR:"VALIDATION_ERROR",UNKNOWN_ERROR:"UNKNOWN_ERROR"},b=class extends Error{code;statusCode;context;suggestion;timestamp;cause;constructor(t,r={}){super(t),this.name="RegistryError",this.code=r.code||F.UNKNOWN_ERROR,this.statusCode=r.statusCode,this.cause=r.cause,this.context=r.context,this.suggestion=r.suggestion,this.timestamp=new Date,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor);}toJSON(){return {name:this.name,message:this.message,code:this.code,statusCode:this.statusCode,context:this.context,suggestion:this.suggestion,timestamp:this.timestamp,stack:this.stack}}},q=class extends b{constructor(r,s){let n=`The item at ${r} was not found. It may not exist at the registry.`;super(n,{code:F.NOT_FOUND,statusCode:404,cause:s,context:{url:r},suggestion:"Check if the item name is correct and the registry URL is accessible."});this.url=r;this.name="RegistryNotFoundError";}},me=class extends b{constructor(r,s){let n=`You are not authorized to access the item at ${r}. If this is a remote registry, you may need to authenticate.`;super(n,{code:F.UNAUTHORIZED,statusCode:401,cause:s,context:{url:r},suggestion:"Check your authentication credentials and environment variables."});this.url=r;this.name="RegistryUnauthorizedError";}},de=class extends b{constructor(r,s){let n=`You are not authorized to access the item at ${r}. If this is a remote registry, you may need to authenticate.`;super(n,{code:F.FORBIDDEN,statusCode:403,cause:s,context:{url:r},suggestion:"Check your authentication credentials and environment variables."});this.url=r;this.name="RegistryForbiddenError";}},ge=class extends b{constructor(r,s,n,i){let o=s?`Failed to fetch from registry (${s}): ${r}`:`Failed to fetch from registry: ${r}`,a=typeof i=="string"&&i?`${o} - ${i}`:o,c="Check your network connection and try again.";s===404?c="The requested resource was not found. Check the URL or item name.":s===500?c="The registry server encountered an error. Try again later.":s&&s>=400&&s<500&&(c="There was a client error. Check your request parameters.");super(a,{code:F.FETCH_ERROR,statusCode:s,cause:i,context:{url:r,responseBody:n},suggestion:c});this.url=r;this.responseBody=n;this.name="RegistryFetchError";}},W=class extends b{constructor(r){let s=r?`Unknown registry "${r}". Make sure it is defined in components.json as follows:
{
  "registries": {
    "${r}": "[URL_TO_REGISTRY]"
  }
}`:'Unknown registry. Make sure it is defined in components.json under "registries".';super(s,{code:F.NOT_CONFIGURED,context:{registryName:r},suggestion:"Add the registry configuration to your components.json file. Consult the registry documentation for the correct format."});this.registryName=r;this.name="RegistryNotConfiguredError";}},X=class extends b{constructor(r,s){super(`Failed to read local registry file: ${r}`,{code:F.LOCAL_FILE_ERROR,cause:s,context:{filePath:r},suggestion:"Check if the file exists and you have read permissions."});this.filePath=r;this.name="RegistryLocalFileError";}},$=class extends b{constructor(r,s){let n=`Failed to parse registry item: ${r}`;s instanceof z.ZodError&&(n=`Failed to parse registry item: ${r}
${s.errors.map(i=>`  - ${i.path.join(".")}: ${i.message}`).join(`
`)}`);super(n,{code:F.PARSE_ERROR,cause:s,context:{item:r},suggestion:"The registry item may be corrupted or have an invalid format. Please make sure it returns a valid JSON object. See https://ui.shadcn.com/schema/registry-item.json."});this.item=r;this.parseError=s,this.name="RegistryParseError";}parseError},ue=class extends b{constructor(r,s){let n=`Registry "${r}" requires the following environment variables:

`+s.map(i=>`  \u2022 ${i}`).join(`
`);super(n,{code:F.MISSING_ENV_VARS,context:{registryName:r,missingVars:s},suggestion:"Set the required environment variables to your .env or .env.local file."});this.registryName=r;this.missingVars=s;this.name="RegistryMissingEnvironmentVariablesError";}},he=class extends b{constructor(r){let s=`Invalid registry namespace: "${r}". Registry names must start with @ (e.g., @shadcn, @v0).`;super(s,{code:F.VALIDATION_ERROR,context:{name:r},suggestion:"Use a valid registry name starting with @ or provide a direct URL to the registry."});this.name=r;this.name="RegistryInvalidNamespaceError";}};var Te=class extends b{constructor(r,s){let n=`Invalid components.json configuration in ${r}.`;s instanceof z.ZodError&&(n=`Invalid components.json configuration in ${r}:
${s.errors.map(i=>`  - ${i.path.join(".")}: ${i.message}`).join(`
`)}`);super(n,{code:F.INVALID_CONFIG,cause:s,context:{cwd:r},suggestion:"Check your components.json file for syntax errors or invalid configuration. Run 'npx shadcn@latest init' to regenerate a valid configuration."});this.cwd=r;this.name="ConfigParseError";}},ye=class extends b{parseError;constructor(t){let r="Failed to parse registries index";if(t instanceof z.ZodError){let s=t.errors.filter(n=>n.path.length>0).map(n=>`"${n.path[0]}"`).filter((n,i,o)=>o.indexOf(n)===i);s.length>0?r=`Failed to parse registries index. Invalid registry namespace(s): ${s.join(", ")}
${t.errors.map(n=>`  - ${n.path.join(".")}: ${n.message}`).join(`
`)}`:r=`Failed to parse registries index:
${t.errors.map(n=>`  - ${n.path.join(".")}: ${n.message}`).join(`
`)}`;}super(r,{code:F.PARSE_ERROR,cause:t,context:{parseError:t},suggestion:"The registries index may be corrupted or have invalid registry namespace format. Registry names must start with @ (e.g., @shadcn, @example)."}),this.parseError=t,this.name="RegistriesIndexParseError";}};var or=/^(@[a-zA-Z0-9](?:[a-zA-Z0-9-_]*[a-zA-Z0-9])?)\/(.+)$/;function ee(e){if(!e.startsWith("@"))return {registry:null,item:e};let t=e.match(or);return t?{registry:t[1],item:t[2]}:{registry:null,item:e}}var O={"next-app":{name:"next-app",label:"Next.js",links:{installation:"https://ui.shadcn.com/docs/installation/next",tailwind:"https://tailwindcss.com/docs/guides/nextjs"}},"next-pages":{name:"next-pages",label:"Next.js",links:{installation:"https://ui.shadcn.com/docs/installation/next",tailwind:"https://tailwindcss.com/docs/guides/nextjs"}},remix:{name:"remix",label:"Remix",links:{installation:"https://ui.shadcn.com/docs/installation/remix",tailwind:"https://tailwindcss.com/docs/guides/remix"}},"react-router":{name:"react-router",label:"React Router",links:{installation:"https://ui.shadcn.com/docs/installation/react-router",tailwind:"https://tailwindcss.com/docs/installation/framework-guides/react-router"}},vite:{name:"vite",label:"Vite",links:{installation:"https://ui.shadcn.com/docs/installation/vite",tailwind:"https://tailwindcss.com/docs/guides/vite"}},astro:{name:"astro",label:"Astro",links:{installation:"https://ui.shadcn.com/docs/installation/astro",tailwind:"https://tailwindcss.com/docs/guides/astro"}},laravel:{name:"laravel",label:"Laravel",links:{installation:"https://ui.shadcn.com/docs/installation/laravel",tailwind:"https://tailwindcss.com/docs/guides/laravel"}},"tanstack-start":{name:"tanstack-start",label:"TanStack Start",links:{installation:"https://ui.shadcn.com/docs/installation/tanstack",tailwind:"https://tailwindcss.com/docs/installation/using-postcss"}},gatsby:{name:"gatsby",label:"Gatsby",links:{installation:"https://ui.shadcn.com/docs/installation/gatsby",tailwind:"https://tailwindcss.com/docs/guides/gatsby"}},expo:{name:"expo",label:"Expo",links:{installation:"https://ui.shadcn.com/docs/installation/expo",tailwind:"https://www.nativewind.dev/docs/getting-started/installation"}},manual:{name:"manual",label:"Manual",links:{installation:"https://ui.shadcn.com/docs/installation/manual",tailwind:"https://tailwindcss.com/docs/installation"}}};var k={error:red,warn:yellow,info:cyan,success:green};async function A(e,t){return createMatchPath(t.absoluteBaseUrl,t.paths)(e,void 0,()=>true,[".ts",".tsx",".jsx",".js",".css"])}var In="@/components",Sn="@/lib/utils",bn="app/globals.css",Pn="tailwind.config.js";var ke=cosmiconfig("components",{searchPlaces:["components.json"]});async function je(e){let t=await ur(e);return t?(t.iconLibrary||(t.iconLibrary=t.style==="new-york"?"radix":"lucide"),await Qe(e,t)):null}async function Qe(e,t){t.registries={...K,...t.registries||{}};let r=await loadConfig(e);if(r.resultType==="failed")throw new Error(`Failed to load ${t.tsx?"tsconfig":"jsconfig"}.json. ${r.message??""}`.trim());return q$1.parse({...t,resolvedPaths:{cwd:e,tailwindConfig:t.tailwind.config?h__default.resolve(e,t.tailwind.config):"",tailwindCss:h__default.resolve(e,t.tailwind.css),utils:await A(t.aliases.utils,r),components:await A(t.aliases.components,r),ui:t.aliases.ui?await A(t.aliases.ui,r):h__default.resolve(await A(t.aliases.components,r)??e,"ui"),lib:t.aliases.lib?await A(t.aliases.lib,r):h__default.resolve(await A(t.aliases.utils,r)??e,".."),hooks:t.aliases.hooks?await A(t.aliases.hooks,r):h__default.resolve(await A(t.aliases.components,r)??e,"..","hooks")}})}async function ur(e){try{let t=await ke.search(e);if(!t)return null;let r=p.parse(t.config);if(r.registries){for(let s of Object.keys(r.registries))if(s in K)throw new Error(`"${s}" is a built-in registry and cannot be overridden.`)}return r}catch(t){let r=`${e}/components.json`;throw t instanceof Error&&t.message.includes("reserved registry")?t:new Error(`Invalid configuration found in ${k.info(r)}.`)}}async function Tn(e){let t={};for(let s of Object.keys(e.aliases)){if(!yr(s,e))continue;let n=e.resolvedPaths[s],i=await hr(e.resolvedPaths.cwd,n);if(!i){t[s]=e;continue}t[s]=await je(i);}let r$1=r.safeParse(t);return r$1.success?r$1.data:null}async function hr(e,t){let r=xr(e,t),s=h__default.relative(r,t),i=(await Ae.glob("**/package.json",{cwd:r,deep:3,ignore:["**/node_modules/**","**/dist/**","**/build/**","**/public/**"]})).map(o=>h__default.dirname(o)).find(o=>s.startsWith(o));return i?h__default.join(r,i):null}function yr(e,t){return Object.keys(t.resolvedPaths).filter(r=>r!=="utils").includes(e)}function xr(e,t){let r=e.split(h__default.sep),s=t.split(h__default.sep),n=[];for(let i=0;i<Math.min(r.length,s.length)&&r[i]===s[i];i++)n.push(r[i]);return n.join(h__default.sep)}async function vt(e,t){return (await V(e))?.tailwindVersion==="v4"?"new-york-v4":t}function Rt(e){let t={resolvedPaths:{cwd:process.cwd(),tailwindConfig:"",tailwindCss:"",utils:"",components:"",ui:"",lib:"",hooks:""},style:"",tailwind:{config:"",css:"",baseColor:"",cssVariables:false},rsc:false,tsx:true,aliases:{components:"",utils:""},registries:{...K}};return e?{...t,...e,resolvedPaths:{...t.resolvedPaths,...e.resolvedPaths||{}},tailwind:{...t.tailwind,...e.tailwind||{}},aliases:{...t.aliases,...e.aliases||{}},registries:{...t.registries,...e.registries||{}}}:t}function Xe(e="",t=true){let r=h__default.join(e,"package.json");return Oe.readJSONSync(r,{throws:t})}var Fe=["**/node_modules/**",".next","public","dist","build"];z.object({compilerOptions:z.object({paths:z.record(z.string().or(z.array(z.string())))})});async function V(e){let[t,r,s,n,i,o,a,c]=await Promise.all([Ae.glob("**/{next,vite,astro,app}.config.*|gatsby-config.*|composer.json|react-router.config.*",{cwd:e,deep:3,ignore:Fe}),Oe.pathExists(h__default.resolve(e,"src")),br(e),Ir(e),Cr(e),Ct(e),Sr(e),Xe(e,false)]),p=await Oe.pathExists(h__default.resolve(e,`${r?"src/":""}app`)),l={framework:O.manual,isSrcDir:r,isRSC:false,isTsx:s,tailwindConfigFile:n,tailwindCssFile:i,tailwindVersion:o,frameworkVersion:null,aliasPrefix:a};if(t.find(f=>f.startsWith("next.config."))?.length)return l.framework=p?O["next-app"]:O["next-pages"],l.isRSC=p,l.frameworkVersion=await Rr(l.framework,c),l;if(t.find(f=>f.startsWith("astro.config."))?.length)return l.framework=O.astro,l;if(t.find(f=>f.startsWith("gatsby-config."))?.length)return l.framework=O.gatsby,l;if(t.find(f=>f.startsWith("composer.json"))?.length)return l.framework=O.laravel,l;if(Object.keys(c?.dependencies??{}).find(f=>f.startsWith("@remix-run/")))return l.framework=O.remix,l;if([...Object.keys(c?.dependencies??{}),...Object.keys(c?.devDependencies??{})].find(f=>f.startsWith("@tanstack/react-start")))return l.framework=O["tanstack-start"],l;if(t.find(f=>f.startsWith("react-router.config."))?.length)return l.framework=O["react-router"],l;if(t.find(f=>f.startsWith("vite.config."))?.length)return l.framework=O.vite,l;let d=t.find(f=>f.startsWith("app.config"));return d?.length&&(await Oe.readFile(h__default.resolve(e,d),"utf8")).includes("defineConfig")?(l.framework=O.vite,l):(c?.dependencies?.expo&&(l.framework=O.expo),l)}async function Rr(e,t){if(!t||!["next-app","next-pages"].includes(e.name))return null;let r=t.dependencies?.next||t.devDependencies?.next;if(!r)return null;let s=r.match(/^[\^~]?(\d+\.\d+\.\d+)/);if(s)return s[1];let n=r.match(/(\d+\.\d+\.\d+)/);return n?n[1]:r}async function Ct(e){let[t,r]=await Promise.all([Xe(e,false),je(e)]);return r?.tailwind?.config===""?"v4":!t?.dependencies?.tailwindcss&&!t?.devDependencies?.tailwindcss?null:/^(?:\^|~)?3(?:\.\d+)*(?:-.*)?$/.test(t?.dependencies?.tailwindcss||t?.devDependencies?.tailwindcss||"")?"v3":"v4"}async function Cr(e){let[t,r]=await Promise.all([Ae.glob(["**/*.css","**/*.scss"],{cwd:e,deep:5,ignore:Fe}),Ct(e)]);if(!t.length)return null;for(let n of t){let i=await Oe.readFile(h__default.resolve(e,n),"utf8");if(i.includes('@import "tailwindcss"')||i.includes("@import 'tailwindcss'")||i.includes("@tailwind base"))return n}return null}async function Ir(e){let t=await Ae.glob("tailwind.config.*",{cwd:e,deep:3,ignore:Fe});return t.length?t[0]:null}async function Sr(e){let t=await loadConfig(e);if(t?.resultType==="failed"||!Object.entries(t?.paths).length)return null;for(let[r,s]of Object.entries(t.paths))if(s.includes("./*")||s.includes("./src/*")||s.includes("./app/*")||s.includes("./resources/js/*"))return r.replace(/\/\*$/,"")??null;return Object.keys(t?.paths)?.[0].replace(/\/\*$/,"")??null}async function br(e){return (await Ae.glob("tsconfig.*",{cwd:e,deep:1,ignore:Fe})).length>0}async function Mn(e,t=null){let[r,s]=await Promise.all([je(e),t?Promise.resolve(t):V(e)]);if(r)return r;if(!s||!s.tailwindCssFile||s.tailwindVersion==="v3"&&!s.tailwindConfigFile)return null;let n={$schema:"https://ui.shadcn.com/schema.json",rsc:s.isRSC,tsx:s.isTsx,style:"new-york",tailwind:{config:s.tailwindConfigFile??"",baseColor:"zinc",css:s.tailwindCssFile,cssVariables:true,prefix:""},iconLibrary:"lucide",aliases:{components:`${s.aliasPrefix}/components`,ui:`${s.aliasPrefix}/components/ui`,hooks:`${s.aliasPrefix}/hooks`,lib:`${s.aliasPrefix}/lib`,utils:`${s.aliasPrefix}/lib/utils`}};return await Qe(e,n)}async function Le(e){if(!e.resolvedPaths?.cwd)return "v3";let t=await V(e.resolvedPaths.cwd);return t?.tailwindVersion?t.tailwindVersion:null}function It(e,t,r={}){let{ignoreImports:s=false}=r,n=e.replace(/\r\n/g,`
`).trim(),i=t.replace(/\r\n/g,`
`).trim();if(n===i)return  true;if(!s)return  false;let o=/^(import\s+(?:type\s+)?(?:\*\s+as\s+\w+|\{[^}]*\}|\w+)?(?:\s*,\s*(?:\{[^}]*\}|\w+))?\s+from\s+["'])([^"']+)(["'])/gm,a=l=>l.replace(o,(d,f,x,y)=>{if(x.startsWith("."))return `${f}${x}${y}`;let w=x.split("/"),m=w[w.length-1];return `${f}@normalized/${m}${y}`}),c=a(n),p=a(i);return c===p}function J(e){let t=h__default.basename(e);return /^\.env(\.|$)/.test(t)}function bt(e){let t=[".env.local",".env",".env.development.local",".env.development"];for(let r of t){let s=h__default.join(e,r);if(existsSync(s))return s}return null}function re(e){let t=e.split(`
`),r={};for(let s of t){let n=s.trim();if(!n||n.startsWith("#"))continue;let i=n.indexOf("=");if(i===-1)continue;let o=n.substring(0,i).trim(),a=n.substring(i+1).trim();o&&(r[o]=a.replace(/^["']|["']$/g,""));}return r}function Pt(e,t){let r=re(e),s=re(t),n=[];for(let i of Object.keys(s))i in r||n.push(i);return n}function Tt(e,t){let r=re(e),s=re(t),n=e.trimEnd();n&&!n.endsWith(`
`)&&(n+=`
`);let i=[];for(let[o,a]of Object.entries(s))o in r||i.push(`${o}=${a}`);return i.length>0?(n&&(n+=`
`),n+=i.join(`
`),n+`
`):n&&!n.endsWith(`
`)?n+`
`:n}var v={error(...e){console.log(k.error(e.join(" ")));},warn(...e){console.log(k.warn(e.join(" ")));},info(...e){console.log(k.info(e.join(" ")));},success(...e){console.log(k.success(e.join(" ")));},log(...e){console.log(e.join(" "));},break(){console.log("");}};function Y(e,t){return Tr({text:e,isSilent:t?.silent})}var Ne=async({sourceFile:e,config:t,baseColor:r})=>(t.tailwind?.cssVariables||!r?.inlineColors||e.getDescendantsOfKind(SyntaxKind.StringLiteral).forEach(s=>{let n=s.getLiteralText(),i=$r(n,r.inlineColors).trim();i!==n&&s.setLiteralValue(i);}),e);function et(e){if(!e.includes("/")&&!e.includes(":"))return [null,e,null];let t=[],[r,s]=e.split("/");if(!r.includes(":"))return [null,r,s];let n=r.split(":"),i=n.pop(),o=n.join(":");return t.push(o??null,i??null,s??null),t}var jr=["bg-","text-","border-","ring-offset-","ring-"];function $r(e,t){e.includes(" border ")&&(e=e.replace(" border "," border border-border "));let r=e.split(" "),s=new Set,n=new Set;for(let i of r){let[o,a,c]=et(i),p=jr.find(d=>a?.startsWith(d));if(!p){s.has(i)||s.add(i);continue}let l=a?.replace(p,"");if(l&&l in t.light){s.add([o,`${p}${t.light[l]}`].filter(Boolean).join(":")+(c?`/${c}`:"")),n.add(["dark",o,`${p}${t.dark[l]}`].filter(Boolean).join(":")+(c?`/${c}`:""));continue}s.has(i)||s.add(i);}return [...Array.from(s),...Array.from(n)].join(" ").trim()}var De={lucide:{name:"lucide-react",package:"lucide-react",import:"lucide-react"},radix:{name:"@radix-ui/react-icons",package:"@radix-ui/react-icons",import:"@radix-ui/react-icons"}};var kt="lucide",ze=async({sourceFile:e,config:t})=>{if(!t.iconLibrary||!(t.iconLibrary in De))return e;let r=await jt(),s=kt,n=t.iconLibrary;if(s===n)return e;let i=[];for(let o of e.getImportDeclarations()??[])if(o.getModuleSpecifier()?.getText()===`"${De[kt].import}"`){for(let a of o.getNamedImports()??[]){let c=a.getName(),p=r[c]?.[n];!p||i.includes(p)||(i.push(p),a.remove(),e.getDescendantsOfKind(SyntaxKind.JsxSelfClosingElement).filter(l=>l.getTagNameNode()?.getText()===c).forEach(l=>l.getTagNameNode()?.replaceWithText(p)));}o.getNamedImports()?.length===0&&o.remove();}if(i.length>0){let o=e.addImportDeclaration({moduleSpecifier:De[n]?.import,namedImports:i.map(a=>({name:a}))});Ar(e)||o.replaceWithText(o.getText().replace(";",""));}return e};function Ar(e){return e.getImportDeclarations()?.[0]?.getText().endsWith(";")??false}var _e=async({sourceFile:e,config:t,isRemote:r})=>{let n=`@${t.aliases?.utils?.split("/")[0]?.slice(1)}/lib/utils`;if(![".tsx",".ts",".jsx",".js"].includes(e.getExtension()))return e;for(let i of e.getImportStringLiterals()){let o=Lr(i.getLiteralValue(),t,r);if(i.setLiteralValue(o),n===o||o==="@/lib/utils"){if(!i.getFirstAncestorByKind(SyntaxKind.ImportDeclaration)?.getNamedImports().some(p=>p.getName()==="cn"))continue;i.setLiteralValue(n===o?o.replace(n,t.aliases.utils):t.aliases.utils);}}return e};function Lr(e,t,r=false){if(!e.startsWith("@/")&&!r)return e;if(r&&e.startsWith("@/")&&(e=e.replace(/^@\//,"@/registry/new-york/")),!e.startsWith("@/registry/")){let s=t.aliases.components.split("/")[0];return e.replace(/^@\//,`${s}/`)}return e.match(/^@\/registry\/(.+)\/ui/)?e.replace(/^@\/registry\/(.+)\/ui/,t.aliases.ui??`${t.aliases.components}/ui`):t.aliases.components&&e.match(/^@\/registry\/(.+)\/components/)?e.replace(/^@\/registry\/(.+)\/components/,t.aliases.components):t.aliases.lib&&e.match(/^@\/registry\/(.+)\/lib/)?e.replace(/^@\/registry\/(.+)\/lib/,t.aliases.lib):t.aliases.hooks&&e.match(/^@\/registry\/(.+)\/hooks/)?e.replace(/^@\/registry\/(.+)\/hooks/,t.aliases.hooks):e.replace(/^@\/registry\/[^/]+/,t.aliases.components)}var _r={sourceType:"module",allowImportExportEverywhere:true,allowReturnOutsideFunction:true,startLine:1,tokens:true,plugins:["asyncGenerators","bigInt","classPrivateMethods","classPrivateProperties","classProperties","classStaticBlock","decimal","decorators-legacy","doExpressions","dynamicImport","exportDefaultFrom","exportNamespaceFrom","functionBind","functionSent","importAssertions","importMeta","nullishCoalescingOperator","numericSeparator","objectRestSpread","optionalCatchBinding","optionalChaining",["pipelineOperator",{proposal:"minimal"}],["recordAndTuple",{syntaxType:"hash"}],"throwExpressions","topLevelAwait","v8intrinsic","typescript","jsx"]},$t=async({sourceFile:e,config:t})=>{let r=e.getFullText();if(t.tsx)return r;let s=Ke.parse(r,{parser:{parse:i=>parse(i,_r)}}),n=transformFromAstSync(s,r,{cloneInputAst:false,code:false,ast:true,plugins:[zr],configFile:false});if(!n||!n.ast)throw new Error("Failed to transform JSX");return Ke.print(n.ast).code};var Vr=/^["']use client["']$/g,Ve=async({sourceFile:e,config:t})=>{if(t.rsc)return e;let r=e.getFirstChildByKind(SyntaxKind.ExpressionStatement);return r&&Vr.test(r.getText())&&r.remove(),e};var We=async({sourceFile:e,config:t})=>{if(!t.tailwind?.prefix)return e;let r=await Le(t);return e.getDescendantsOfKind(SyntaxKind.CallExpression).filter(s=>s.getExpression().getText()==="cva").forEach(s=>{if(s.getArguments()[0]?.isKind(SyntaxKind.StringLiteral)){let n=s.getArguments()[0];n&&n.replaceWithText(`"${U(n.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);}s.getArguments()[1]?.isKind(SyntaxKind.ObjectLiteralExpression)&&s.getArguments()[1]?.getDescendantsOfKind(SyntaxKind.PropertyAssignment).find(n=>n.getName()==="variants")?.getDescendantsOfKind(SyntaxKind.PropertyAssignment).forEach(n=>{n.getDescendantsOfKind(SyntaxKind.PropertyAssignment).forEach(i=>{let o=i.getInitializerIfKind(SyntaxKind.StringLiteral);o&&o?.replaceWithText(`"${U(o.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);});});}),e.getDescendantsOfKind(SyntaxKind.JsxAttribute).forEach(s=>{if(s.getNameNode().getText()==="className"){if(s.getInitializer()?.isKind(SyntaxKind.StringLiteral)){let n=s.getInitializer();n&&n.replaceWithText(`"${U(n.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);}if(s.getInitializer()?.isKind(SyntaxKind.JsxExpression)){let n=s.getInitializer()?.getDescendantsOfKind(SyntaxKind.CallExpression).find(i=>i.getExpression().getText()==="cn");n&&n.getArguments().forEach(i=>{(i.isKind(SyntaxKind.ConditionalExpression)||i.isKind(SyntaxKind.BinaryExpression))&&i.getChildrenOfKind(SyntaxKind.StringLiteral).forEach(o=>{o.replaceWithText(`"${U(o.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);}),i.isKind(SyntaxKind.StringLiteral)&&i.replaceWithText(`"${U(i.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);});}}s.getNameNode().getText()==="classNames"&&s.getInitializer()?.isKind(SyntaxKind.JsxExpression)&&s.getDescendantsOfKind(SyntaxKind.PropertyAssignment).forEach(n=>{if(n.getInitializer()?.isKind(SyntaxKind.CallExpression)){let i=n.getInitializerIfKind(SyntaxKind.CallExpression);i&&i.getArguments().forEach(o=>{o.isKind(SyntaxKind.ConditionalExpression)&&o.getChildrenOfKind(SyntaxKind.StringLiteral).forEach(a=>{a.replaceWithText(`"${U(a.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);}),o.isKind(SyntaxKind.StringLiteral)&&o.replaceWithText(`"${U(o.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);});}if(n.getInitializer()?.isKind(SyntaxKind.StringLiteral)&&n.getNameNode().getText()!=="variant"){let i=n.getInitializer();i&&i.replaceWithText(`"${U(i.getText()?.replace(/"|'/g,""),t.tailwind.prefix,r)}"`);}});}),e};function U(e,t="",r){return r==="v3"?e.split(" ").map(s=>{let[n,i,o]=et(s);return n?o?`${n}:${t}${i}/${o}`:`${n}:${t}${i}`:o?`${t}${i}/${o}`:`${t}${i}`}).join(" "):e.split(" ").map(s=>s.indexOf(`${t}:`)===0?s:`${t}:${s.trim()}`).join(" ")}var Hr=new Project({compilerOptions:{}});async function Gr(e){let t=await promises.mkdtemp(h__default.join(tmpdir(),"shadcn-"));return h__default.join(t,e)}async function At(e,t=[_e,Ve,Ne,We,ze]){let r=await Gr(e.filename),s=Hr.createSourceFile(r,e.raw,{scriptKind:ScriptKind.TSX});for(let n of t)await n({sourceFile:s,...e});return e.transformJsx?await $t({sourceFile:s,...e}):s.getText()}var Ft=async({sourceFile:e})=>(e.getFunctions().forEach(t=>{t.getName()==="middleware"&&t.rename("proxy");}),e.getVariableDeclarations().forEach(t=>{t.getName()==="middleware"&&t.rename("proxy");}),e.getExportDeclarations().forEach(t=>{t.getNamedExports().forEach(s=>{s.getName()==="middleware"&&s.setName("proxy"),s.getAliasNode()?.getText()==="middleware"&&s.setAlias("proxy");});}),e);async function so(e,t,r){if(!e?.length)return {filesCreated:[],filesUpdated:[],filesSkipped:[]};r={overwrite:false,force:false,silent:false,isRemote:false,isWorkspace:false,...r};let s=Y("Updating files.",{silent:r.silent})?.start(),[n,i]=await Promise.all([V(t.resolvedPaths.cwd),t.tailwind.baseColor?Ue(t.tailwind.baseColor):Promise.resolve(void 0)]),o=[],a=[],c=[],p=[],l=null;for(let y=0;y<e.length;y++){let w=e[y];if(!w.content)continue;let m=tt(w,t,{isSrcDir:n?.isSrcDir,framework:n?.framework.name,commonRoot:rt(e.map(C=>C.path),w.path),path:r.path,fileIndex:y});if(!m)continue;let u=basename(w.path),E=h__default.dirname(m);if(t.tsx||(m=m.replace(/\.tsx?$/,C=>C===".tsx"?".jsx":".js")),J(m)&&!existsSync(m)){let C=bt(E);C&&(m=C);}let I=existsSync(m);if(I&&statSync(m).isDirectory())throw new Error(`Cannot write to ${m}: path exists and is a directory. Please provide a file path instead.`);let R=J(m)?w.content:await At({filename:w.path,raw:w.content,config:t,baseColor:i,transformJsx:!t.tsx,isRemote:r.isRemote},[_e,Ve,Ne,We,ze,...Lt(m,n,t)?[Ft]:[]]);if(I&&!J(m)){let C=await promises.readFile(m,"utf-8");if(It(C,R,{ignoreImports:r.isWorkspace})){c.push(h__default.relative(t.resolvedPaths.cwd,m));continue}}if(I&&!r.overwrite&&!J(m)){s.stop(),r.rootSpinner&&r.rootSpinner.stop();let{overwrite:C}=await Yr({type:"confirm",name:"overwrite",message:`The file ${k.info(u)} already exists. Would you like to overwrite?`,initial:false});if(!C){c.push(h__default.relative(t.resolvedPaths.cwd,m)),r.rootSpinner&&r.rootSpinner.start();continue}s?.start(),r.rootSpinner&&r.rootSpinner.start();}if(Lt(m,n,t)&&(m=m.replace(/middleware\.(ts|js)$/,"proxy.$1")),existsSync(E)||await promises.mkdir(E,{recursive:true}),J(m)&&I){let C=await promises.readFile(m,"utf-8"),T=Tt(C,R);if(p=Pt(C,R),l=h__default.relative(t.resolvedPaths.cwd,m),!p.length){c.push(h__default.relative(t.resolvedPaths.cwd,m));continue}await promises.writeFile(m,T,"utf-8"),a.push(h__default.relative(t.resolvedPaths.cwd,m));continue}await promises.writeFile(m,R,"utf-8"),I?a.push(h__default.relative(t.resolvedPaths.cwd,m)):(o.push(h__default.relative(t.resolvedPaths.cwd,m)),J(m)&&(p=Object.keys(re(R)),l=h__default.relative(t.resolvedPaths.cwd,m)));}let d=[...o,...a,...c],f=await ss(d,t);if(a.push(...f),a=a.filter(y=>!o.includes(y)),!(o.length||a.length)&&!c.length&&s?.info("No files updated."),o=Array.from(new Set(o)),a=Array.from(new Set(a)),c=Array.from(new Set(c)),o.length){if(s?.succeed(`Created ${o.length} ${o.length===1?"file":"files"}:`),!r.silent)for(let y of o)v.log(`  - ${y}`);}else s?.stop();if(a.length&&(Y(`Updated ${a.length} ${a.length===1?"file":"files"}:`,{silent:r.silent})?.info(),!r.silent))for(let y of a)v.log(`  - ${y}`);if(c.length&&(Y(`Skipped ${c.length} ${a.length===1?"file":"files"}: (files might be identical, use --overwrite to overwrite)`,{silent:r.silent})?.info(),!r.silent))for(let y of c)v.log(`  - ${y}`);if(p.length&&l&&(Y(`Added the following variables to ${k.info(l)}:`)?.info(),!r.silent))for(let y of p)v.log(`  ${k.success("+")} ${y}`);return r.silent||v.break(),{filesCreated:o,filesUpdated:a,filesSkipped:c}}function tt(e,t,r){if(r.path){let i=h__default.isAbsolute(r.path)?r.path:h__default.join(t.resolvedPaths.cwd,r.path);if(/\.[^/\\]+$/.test(i)){if(r.fileIndex===0)return i}else {let a=h__default.basename(e.path);return h__default.join(i,a)}}if(e.target){if(e.target.startsWith("~/"))return h__default.join(t.resolvedPaths.cwd,e.target.replace("~/",""));let i=e.target;return e.type==="registry:page"&&(i=rs(i,r.framework),!i)?"":r.isSrcDir?h__default.join(t.resolvedPaths.cwd,"src",i.replace("src/","")):h__default.join(t.resolvedPaths.cwd,i.replace("src/",""))}let s=es(e,t),n=ts(e.path,s);return h__default.join(s,n)}function es(e,t){return e.type==="registry:ui"?t.resolvedPaths.ui:e.type==="registry:lib"?t.resolvedPaths.lib:e.type==="registry:block"||e.type==="registry:component"?t.resolvedPaths.components:e.type==="registry:hook"?t.resolvedPaths.hooks:t.resolvedPaths.components}function rt(e,t){let r=e.map(o=>o.replace(/^\//,"")),s=t.replace(/^\//,""),n=s.split("/").slice(0,-1).join("/");if(!n)return "";let i=n.split("/");for(let o=i.length;o>0;o--){let a=i.slice(0,o).join("/");if(r.some(p=>p!==s&&p.startsWith(a+"/")))return "/"+a}return "/"+n}function ts(e,t){let r=e.replace(/^\/|\/$/g,""),s=t.replace(/^\/|\/$/g,""),n=r.split("/"),i=s.split("/"),o=i[i.length-1],a=n.findIndex(c=>c===o);return a===-1?n[n.length-1]:n.slice(a+1).join("/")}function rs(e,t){if(!t)return "";if(t==="next-app")return e;if(t==="next-pages"){let r=e.replace(/^app\//,"pages/");return r=r.replace(/\/page(\.[jt]sx?)$/,"$1"),r}if(t==="react-router"){let r=e.replace(/^app\//,"app/routes/");return r=r.replace(/\/page(\.[jt]sx?)$/,"$1"),r}if(t==="laravel"){let r=e.replace(/^app\//,"resources/js/pages/");return r=r.replace(/\/page(\.[jt]sx?)$/,"$1"),r}return ""}async function ss(e,t){let r=new Project({compilerOptions:{}}),s=await V(t.resolvedPaths.cwd),n=loadConfig(t.resolvedPaths.cwd),i=[];if(!s||n.resultType==="failed")return [];for(let o of e){let a=h__default.resolve(t.resolvedPaths.cwd,o);if(!existsSync(a))continue;let c=await promises.readFile(a,"utf-8"),p=await promises.mkdtemp(h__default.join(tmpdir(),"shadcn-")),l=r.createSourceFile(h__default.join(p,basename(a)),c,{scriptKind:ScriptKind.TSX});if(![".tsx",".ts",".jsx",".js"].includes(l.getExtension()))continue;let d=l.getImportDeclarations();for(let f of d){let x=f.getModuleSpecifierValue();if(s?.aliasPrefix&&!x.startsWith(`${s.aliasPrefix}/`))continue;let y=await A(x,n);if(!y)continue;let w=ns(y,e,t);if(!w)continue;let m=is(w,t,s);!m||m===x||(f.setModuleSpecifier(m),await promises.writeFile(a,l.getFullText(),"utf-8"),i.push(o));}}return i}function ns(e,t,r,s=[".tsx",".ts",".js",".jsx",".css"]){let n=h__default.normalize(r.resolvedPaths.cwd),i=t.map(m=>m.split(h__default.sep).join(h__default.posix.sep)),o=new Set(i),a=h__default.extname(e),c=a!=="",p=c?e.slice(0,-a.length):e,d=h__default.relative(n,p).split(h__default.sep).join(h__default.posix.sep),f=c?[a]:s,x=new Set;for(let m of f){let u=p+m,E=h__default.posix.normalize(h__default.relative(n,u));(o.has(E)||existsSync(u))&&x.add(E);let I=h__default.join(p,`index${m}`),R=h__default.posix.normalize(h__default.relative(n,I));(o.has(R)||existsSync(I))&&x.add(R);}let y=h__default.basename(p);for(let m of i)f.some(u=>m.endsWith(`/${y}${u}`))&&x.add(m);return x.size===0?null:Array.from(x).sort((m,u)=>{let E=h__default.posix.extname(m),I=h__default.posix.extname(u),R=f.indexOf(E)-f.indexOf(I);if(R!==0)return R;let C=d&&m.startsWith(d)?-1:1,T=d&&u.startsWith(d)?-1:1;return C-T})[0]}function is(e,t,r){let s=h__default.normalize(h__default.join(t.resolvedPaths.cwd,e)),n=Object.entries(t.resolvedPaths).filter(([,y])=>y&&s.startsWith(h__default.normalize(y+h__default.sep))).sort((y,w)=>w[1].length-y[1].length);if(n.length===0)return null;let[i,o]=n[0],a=h__default.relative(o,s);a=a.split(h__default.sep).join("/");let c=h__default.posix.extname(a),l=[".ts",".tsx",".js",".jsx"].includes(c)?"":c,d=a.slice(0,a.length-c.length);d.endsWith("/index")&&(d=d.slice(0,-6));let f=i==="cwd"?r.aliasPrefix:t.aliases[i];if(!f)return null;let x=d===""?"":`/${d}`;return x=x.replace("/src",""),`${f}${x}${l}`}function Lt(e,t,r){let s=e===h__default.join(r.resolvedPaths.cwd,"middleware.ts")||e===h__default.join(r.resolvedPaths.cwd,"middleware.js"),n=t?.framework.name==="next-app"||t?.framework.name==="next-pages";if(!s||!n||!t?.frameworkVersion)return  false;let i=parseInt(t.frameworkVersion.split(".")[0]);return !isNaN(i)&&i>=16}var zt=[".tsx",".ts",".jsx",".js",".css"],_t=["lib/utils.ts"],ps=[/^(react|react-dom|next)(\/.*)?$/,/^(node|jsr|npm):.*$/],fs=new Project({compilerOptions:{}});function ms(e){if(ps.some(t=>t.test(e)))return null;if(!e.startsWith("@")&&e.includes("/")&&(e=e.split("/")[0]),e.startsWith("@")){let t=e.split("/");t.length>2&&(e=t.slice(0,2).join("/"));}return e}async function ds(e,t,r,s=new Set){let n=h.resolve(t.resolvedPaths.cwd,e),i=h.relative(t.resolvedPaths.cwd,n);if(_t.includes(i))return {dependencies:[],files:[]};let o=h.extname(e);if(!zt.includes(o))return {dependencies:[],files:[]};if(s.has(i))return {dependencies:[],files:[]};if(s.add(i),!(await B.stat(n)).isFile())return {dependencies:[],files:[]};let c=await B.readFile(n,"utf-8"),p=await gs(h.basename(n)),l=fs.createSourceFile(p,c,{scriptKind:ScriptKind.TSX}),d=await loadConfig(t.resolvedPaths.cwd);if(d.resultType==="failed")return {dependencies:[],files:[]};let f=[],x=new Set,y=Kt(e),w={path:i,type:y,target:""};f.push(w);let m=l.getImportDeclarations();for(let E of m){let I=E.getModuleSpecifierValue(),R=I.startsWith(".");if(!I.startsWith(`${r.aliasPrefix}/`)&&!R){let z=ms(I);z&&x.add(z);continue}let T=await A(I,d);if(R&&(T=h.resolve(h.dirname(n),I)),!T)continue;if(!h.extname(T))for(let z of zt){let ct=`${T}${z}`;try{await B.access(ct),T=ct;break}catch{continue}}let D=h.relative(t.resolvedPaths.cwd,T);if(s.has(D)||_t.includes(D))continue;let le=Kt(I),Ie={path:D,type:le,target:""};(le==="registry:page"||le==="registry:file")&&(Ie.target=I),f.push(Ie);let Se=await ds(D,t,r,s);if(Se.files)for(let z of Se.files)s.has(z.path)||(s.add(z.path),f.push(z));Se.dependencies&&Se.dependencies.forEach(z=>x.add(z));}let u=Array.from(new Map(f.map(E=>[E.path,E])).values());return {dependencies:Array.from(x),files:u}}async function gs(e){let t=await B.mkdtemp(h.join(tmpdir(),"shadcn-"));return h.join(t,e)}function Kt(e){return e.includes("/ui/")?"registry:ui":e.includes("/lib/")?"registry:lib":e.includes("/hooks/")?"registry:hook":(e.includes("/components/"),"registry:component")}function N(e){try{return new URL(e),!0}catch{return  false}}function Me(e){return e.endsWith(".json")&&!N(e)}function yo(e){return !e||e.type!=="registry:item"&&e.type!=="registry:file"?false:(e.files??[]).every(r=>!!r.target&&(r.type==="registry:file"||r.type==="registry:item"))}async function Vt(e,t){if(!us(t))return z.array(b$1).parse(e.flat().filter(Boolean));let r=await V(t.resolvedPaths.cwd),s=new Map,n=z.array(b$1).parse(e.flat().filter(Boolean));return n.forEach(i=>{let o=tt(i,t,{isSrcDir:r?.isSrcDir,framework:r?.framework.name,commonRoot:rt(n.map(a=>a.path),i.path)});o&&s.set(o,i);}),Array.from(s.values())}function us(e){return !!(e?.resolvedPaths?.cwd&&(e?.resolvedPaths?.ui||e?.resolvedPaths?.lib||e?.resolvedPaths?.components||e?.resolvedPaths?.hooks))}function ys(e){return e.style?e.style==="new-york"&&e.tailwind?.config===""?Pe:e.style:Pe}function ne(e){let t=Rt({style:Pe,registries:K});return e?q$1.parse(oe(t,{...e,style:ys(e),registries:{...K,...e.registries}})):t}var Be={headers:{}};function xe(e){Be.headers={...Be.headers,...e};}function Wt(e){return Be.headers[e]||{}}function we(){Be.headers={};}function xs(e){let t=new Set;return typeof e=="string"?fe(e).forEach(r=>t.add(r)):(fe(e.url).forEach(r=>t.add(r)),e.params&&Object.values(e.params).forEach(r=>{fe(r).forEach(s=>t.add(s));}),e.headers&&Object.values(e.headers).forEach(r=>{fe(r).forEach(s=>t.add(s));})),Array.from(t)}function Ut(e,t){let s=xs(t).filter(n=>!process.env[n]);if(s.length>0)throw new ue(e,s)}function $o(e,t){for(let r of e)ie(r,ne(t));we();}var Mt="{name}",He="{style}",Bt=/\${(\w+)}/g,Ht="?",ws="&";function ie(e,t){let{registry:r,item:s}=ee(e);if(!r)return null;let i=(t?.registries||{})[r];if(!i)throw new W(r);return Ut(r,i),{url:Es(s,i,t),headers:vs(i)}}function Es(e,t,r){if(typeof t=="string"){let n=t.replace(Mt,e);return r?.style&&n.includes(He)&&(n=n.replace(He,r.style)),pe(n)}let s=t.url.replace(Mt,e);return r?.style&&s.includes(He)&&(s=s.replace(He,r.style)),s=pe(s),t.params?Rs(s,t.params):s}function vs(e){if(typeof e=="string"||!e.headers)return {};let t={};for(let[r,s]of Object.entries(e.headers)){let n=pe(s);Cs(s,n)&&(t[r]=n);}return t}function Rs(e,t){let r=new URLSearchParams;for(let[i,o]of Object.entries(t)){let a=pe(o);a&&r.append(i,a);}let s=r.toString();if(!s)return e;let n=e.includes(Ht)?ws:Ht;return `${e}${n}${s}`}function Cs(e,t){let r=t.trim();if(!r)return  false;if(e.includes("${")&&e.match(Bt)){let n=e.replace(Bt,"").trim();return r!==n}return  true}function Ge(e){if(N(e)){let t=new URL(e);return t.pathname.match(/\/chat\/b\//)&&!t.pathname.endsWith("/json")&&(t.pathname=`${t.pathname}/json`),t.toString()}return `${ce}/${e}`}var Ts=process.env.https_proxy?new HttpsProxyAgent(process.env.https_proxy):void 0,st=new Map;async function j(e,t={}){t={useCache:true,...t};try{return await Promise.all(e.map(async s=>{let n=Ge(s);if(t.useCache&&st.has(n))return st.get(n);let i=(async()=>{let o=Wt(n),a=await Ps(n,{agent:Ts,headers:{...o}});if(!a.ok){let c;if(a.headers.get("content-type")?.includes("application/json")){let p=await a.json(),l=z.object({detail:z.string().optional(),title:z.string().optional(),message:z.string().optional(),error:z.string().optional()}).safeParse(p);l.success&&(c=l.data.detail||l.data.message,l.data.error&&(c=`[${l.data.error}] ${c}`));}throw a.status===401?new me(n,c):a.status===404?new q(n,c):a.status===403?new de(n,c):new ge(n,a.status,c)}return a.json()})();return t.useCache&&st.set(n,i),i}))}catch(r){throw r}}async function qt(e){try{let t=e;e.startsWith("~/")&&(t=h__default.join(homedir(),e.slice(2)));let r=h__default.resolve(t),s=await promises.readFile(r,"utf8"),n=JSON.parse(s);try{return g.parse(n)}catch(i){throw new $(e,i)}}catch(t){throw t instanceof Error&&(t.message.includes("ENOENT")||t.message.includes("no such file"))?new X(e,t):t instanceof $?t:new X(e,t)}}async function ca(e,t,r){if(!e||(r={silent:false,tailwindVersion:"v3",...r},r.tailwindVersion==="v4"))return;let s=h__default.relative(t.resolvedPaths.cwd,t.resolvedPaths.tailwindConfig),n=Y(`Updating ${k.info(s)}`,{silent:r.silent}).start(),i=await promises.readFile(t.resolvedPaths.tailwindConfig,"utf8"),o=await Fs(i,e,t);await promises.writeFile(t.resolvedPaths.tailwindConfig,o,"utf8"),n?.succeed();}async function Fs(e,t,r){let s=await Yt(e,r),n=s.getDescendantsOfKind(SyntaxKind.ObjectLiteralExpression).find(o=>o.getProperties().some(a=>a.isKind(SyntaxKind.PropertyAssignment)&&a.getName()==="content"));if(!n)return e;let i=zs(n);return Ls(n,{name:"darkMode",value:"class"},{quoteChar:i}),t.plugins?.forEach(o=>{Ds(n,o);}),t.theme&&await Ns(n,t.theme),s.getFullText()}function Ls(e,t,{quoteChar:r}){let s=e.getProperty("darkMode");if(!s){let n={name:t.name,initializer:`[${r}${t.value}${r}]`};return t.name==="darkMode"?(e.insertPropertyAssignment(0,n),e):(e.addPropertyAssignment(n),e)}if(s.isKind(SyntaxKind.PropertyAssignment)){let n=s.getInitializer(),i=`${r}${t.value}${r}`;if(n?.isKind(SyntaxKind.StringLiteral)){let o=n.getText();return n.replaceWithText(`[${o}, ${i}]`),e}if(n?.isKind(SyntaxKind.ArrayLiteralExpression)){if(n.getElements().map(o=>o.getText()).includes(i))return e;n.addElement(i);}return e}return e}async function Ns(e,t){e.getProperty("theme")||e.addPropertyAssignment({name:"theme",initializer:"{}"}),it(e);let s=(e.getPropertyOrThrow("theme")?.asKindOrThrow(SyntaxKind.PropertyAssignment)).getInitializer();if(s?.isKind(SyntaxKind.ObjectLiteralExpression)){let n=s.getText(),i=await _s(n),o=oe(i,t,{arrayMerge:(c,p)=>p}),a=$s(o).replace(/\'\.\.\.(.*)\'/g,"...$1").replace(/\'\"/g,"'").replace(/\"\'/g,"'").replace(/\'\[/g,"[").replace(/\]\'/g,"]").replace(/\'\\\'/g,"'").replace(/\\\'/g,"'").replace(/\\\'\'/g,"'").replace(/\'\'/g,"'");s.replaceWithText(a);}ot(e);}function Ds(e,t){let r=e.getProperty("plugins");if(!r)return e.addPropertyAssignment({name:"plugins",initializer:`[${t}]`}),e;if(r.isKind(SyntaxKind.PropertyAssignment)){let s=r.getInitializer();if(s?.isKind(SyntaxKind.ArrayLiteralExpression)){if(s.getElements().map(n=>n.getText().replace(/["']/g,"")).includes(t.replace(/["']/g,"")))return e;s.addElement(t);}return e}return e}async function Yt(e,t){let r=await promises.mkdtemp(h__default.join(tmpdir(),"shadcn-")),s=t?.resolvedPaths?.tailwindConfig||"tailwind.config.ts",n=h__default.join(r,`shadcn-${h__default.basename(s)}`);return new Project({compilerOptions:{}}).createSourceFile(n,e,{scriptKind:h__default.extname(s)===".ts"?ScriptKind.TS:ScriptKind.JS})}function zs(e){return e.getFirstDescendantByKind(SyntaxKind.StringLiteral)?.getQuoteKind()===QuoteKind.Single?"'":'"'}function it(e){let t=e.getProperties();for(let r=0;r<t.length;r++){let s=t[r];if(s.isKind(SyntaxKind.SpreadAssignment)){let n=s.asKindOrThrow(SyntaxKind.SpreadAssignment),i=n.getExpression().getText();e.insertPropertyAssignment(r,{name:`"___${i.replace(/^\.\.\./,"")}"`,initializer:`"...${i.replace(/^\.\.\./,"")}"`}),n.remove();}else if(s.isKind(SyntaxKind.PropertyAssignment)){let i=s.asKindOrThrow(SyntaxKind.PropertyAssignment).getInitializer();i&&i.isKind(SyntaxKind.ObjectLiteralExpression)?it(i.asKindOrThrow(SyntaxKind.ObjectLiteralExpression)):i&&i.isKind(SyntaxKind.ArrayLiteralExpression)&&Zt(i.asKindOrThrow(SyntaxKind.ArrayLiteralExpression));}}}function Zt(e){let t=e.getElements();for(let r=0;r<t.length;r++){let s=t[r];if(s.isKind(SyntaxKind.ObjectLiteralExpression))it(s.asKindOrThrow(SyntaxKind.ObjectLiteralExpression));else if(s.isKind(SyntaxKind.ArrayLiteralExpression))Zt(s.asKindOrThrow(SyntaxKind.ArrayLiteralExpression));else if(s.isKind(SyntaxKind.SpreadElement)){let n=s.getText();e.removeElement(r),e.insertElement(r,`"${n}"`);}}}function ot(e){let t=e.getProperties();for(let r=0;r<t.length;r++){let s=t[r];if(s.isKind(SyntaxKind.PropertyAssignment)){let n=s,i=n.getInitializer();if(i&&i.isKind(SyntaxKind.StringLiteral)){let o=i.asKindOrThrow(SyntaxKind.StringLiteral).getLiteralValue();o.startsWith("...")&&(e.insertSpreadAssignment(r,{expression:o.slice(3)}),n.remove());}else i?.isKind(SyntaxKind.ObjectLiteralExpression)?ot(i):i&&i.isKind(SyntaxKind.ArrayLiteralExpression)&&Qt(i.asKindOrThrow(SyntaxKind.ArrayLiteralExpression));}}}function Qt(e){let t=e.getElements();for(let r=0;r<t.length;r++){let s=t[r];if(s.isKind(SyntaxKind.ObjectLiteralExpression))ot(s.asKindOrThrow(SyntaxKind.ObjectLiteralExpression));else if(s.isKind(SyntaxKind.ArrayLiteralExpression))Qt(s.asKindOrThrow(SyntaxKind.ArrayLiteralExpression));else if(s.isKind(SyntaxKind.StringLiteral)){let n=s.getText(),i=/(?:^['"])(\.\.\..*)(?:['"]$)/g;i.test(n)&&(e.removeElement(r),e.insertElement(r,n.replace(i,"$1")));}}}async function _s(e){let r=(await Yt(`const theme = ${e}`,null)).getStatements()[0];if(r?.getKind()===SyntaxKind.VariableStatement){let n=(r.getDeclarationList()?.getDeclarations()[0]).getInitializer();if(n?.isKind(SyntaxKind.ObjectLiteralExpression))return await qe(n)}throw new Error("Invalid input: not an object literal")}function qe(e){let t={};for(let r of e.getProperties())if(r.isKind(SyntaxKind.PropertyAssignment)){let s=r.getName().replace(/\'/g,"");r.getInitializer()?.isKind(SyntaxKind.ObjectLiteralExpression)?t[s]=qe(r.getInitializer()):r.getInitializer()?.isKind(SyntaxKind.ArrayLiteralExpression)?t[s]=Xt(r.getInitializer()):t[s]=at(r.getInitializer());}return t}function Xt(e){let t=[];for(let r of e.getElements())r.isKind(SyntaxKind.ObjectLiteralExpression)?t.push(qe(r.asKindOrThrow(SyntaxKind.ObjectLiteralExpression))):r.isKind(SyntaxKind.ArrayLiteralExpression)?t.push(Xt(r.asKindOrThrow(SyntaxKind.ArrayLiteralExpression))):t.push(at(r));return t}function at(e){switch(e.getKind()){case SyntaxKind.StringLiteral:return e.getText();case SyntaxKind.NumericLiteral:return Number(e.getText());case SyntaxKind.TrueKeyword:return  true;case SyntaxKind.FalseKeyword:return  false;case SyntaxKind.NullKeyword:return null;case SyntaxKind.ArrayLiteralExpression:return e.getElements().map(at);case SyntaxKind.ObjectLiteralExpression:return qe(e);default:return e.getText()}}function er(e){let t={};for(let r of Object.keys(e)){let s=r.split("-"),n=s[0],i=s.slice(1).join("-");i===""?typeof t[n]=="object"?t[n].DEFAULT=`hsl(var(--${r}))`:t[n]=`hsl(var(--${r}))`:(typeof t[n]!="object"&&(t[n]={DEFAULT:`hsl(var(--${n}))`}),t[n][i]=`hsl(var(--${r}))`);}for(let[r,s]of Object.entries(t))typeof s=="object"&&s.DEFAULT===`hsl(var(--${r}))`&&!(r in e)&&delete s.DEFAULT;return t}function Re(e,t){let r={},s=[...e];if(!t?.registries)return xe({}),s;for(let n=0;n<s.length;n++){let i=ie(s[n],t);i&&(s[n]=i.url,Object.keys(i.headers).length>0&&(r[i.url]=i.headers));}return xe(r),s}async function Z(e,t,r={}){return await Promise.all(e.map(async n=>{if(Me(n))return qt(n);if(N(n)){let[a]=await j([n],r);try{return g.parse(a)}catch(c){throw new $(n,c)}}if(n.startsWith("@")&&t?.registries){let a=Re([n],t),[c]=await j(a,r);try{return g.parse(c)}catch(p){throw new $(n,p)}}let i=`styles/${t?.style??"new-york-v4"}/${n}.json`,[o]=await j([i],r);try{return g.parse(o)}catch(a){throw new $(n,a)}}))}g.extend({_source:z.string().optional()});async function sr(e,t,r={}){r={useCache:true,...r};let s=[],n=[],i=[],o=Array.from(new Set(e)),a=await Z(o,t,r),c=new Map;for(let u=0;u<a.length;u++)a[u]&&c.set(o[u],a[u]);for(let[u,E]of Array.from(c.entries())){let I={...E,_source:u};if(s.push(I),E.registryDependencies){let R=E.registryDependencies;if(t?.registries)R=Re(E.registryDependencies,t);else {let G=E.registryDependencies.filter(D=>D.startsWith("@"));if(G.length>0){let{registry:D}=ee(G[0]);throw new W(D)}}let{items:C,registryNames:T}=await Ce(R,t,r,new Set(o));n.push(...C),i.push(...T);}}if(s.push(...n),i.length>0){let u=Array.from(new Set(i)),E=u.filter(R=>!R.startsWith("@")),I=u.filter(R=>R.startsWith("@"));if(I.length>0){let R=await Z(I,t,r);for(let C=0;C<R.length;C++){let G={...R[C],_source:I[C]};s.push(G);}}if(E.length>0){let R=await nr();if(!R&&s.length===0)return null;if(R){E.includes("index")&&E.unshift("index");let C=[];for(let le of E){let Ie=await Vs(le,t,r);C.push(...Ie);}let T=Array.from(new Set(C)),G=await j(T,r),D=z.array(g).parse(G);s.push(...D);}}}if(!s.length)return null;if((o.includes("index")||i.includes("index"))&&t.tailwind.baseColor){let u=await Ws(t.tailwind.baseColor,t);u&&s.unshift(u);}let p=new Map;s.forEach(u=>{let E=u._source||u.name;p.set(u,E);}),s=Ms(s,p),s.sort((u,E)=>u.type==="registry:theme"&&E.type!=="registry:theme"?-1:u.type!=="registry:theme"&&E.type==="registry:theme"?1:0);let l={};s.forEach(u=>{l=oe(l,u.tailwind??{});});let d={};s.forEach(u=>{d=oe(d,u.cssVars??{});});let f={};s.forEach(u=>{f=oe(f,u.css??{});});let x="";s.forEach(u=>{u.docs&&(x+=`${u.docs}
`);});let y={};s.forEach(u=>{y=oe(y,u.envVars??{});});let w=await Vt(s.map(u=>u.files??[]),t),m$1=m.parse({dependencies:oe.all(s.map(u=>u.dependencies??[])),devDependencies:oe.all(s.map(u=>u.devDependencies??[])),files:w,tailwind:l,cssVars:d,css:f,docs:x});return Object.keys(y).length>0&&(m$1.envVars=y),m$1}async function Ce(e,t,r={},s=new Set){let n=[],i=[];for(let o of e)if(!s.has(o)){if(s.add(o),N(o)||Me(o)){let[a]=await Z([o],t,r);if(a&&(n.push(a),a.registryDependencies)){let c=t?.registries?Re(a.registryDependencies,t):a.registryDependencies,p=await Ce(c,t,r,s);n.push(...p.items),i.push(...p.registryNames);}}else if(o.startsWith("@")&&t?.registries){let{registry:a}=ee(o);if(a&&!(a in t.registries))throw new W(a);let[c]=await Z([o],t,r);if(c&&(n.push(c),c.registryDependencies)){let p=t?.registries?Re(c.registryDependencies,t):c.registryDependencies,l=await Ce(p,t,r,s);n.push(...l.items),i.push(...l.registryNames);}}else if(i.push(o),t)try{let[a]=await Z([o],t,r);if(a&&a.registryDependencies){let c=t?.registries?Re(a.registryDependencies,t):a.registryDependencies,p=await Ce(c,t,r,s);n.push(...p.items),i.push(...p.registryNames);}}catch{}}return {items:n,registryNames:i}}async function Vs(e,t,r={}){if(N(e))return [e];let{registryNames:s}=await Ce([e],t,r,new Set),n=t.resolvedPaths?.cwd?await vt(t.resolvedPaths.cwd,t.style):t.style,i=s.map(o=>Ge(N(o)?o:`styles/${n}/${o}.json`));return Array.from(new Set(i))}async function Ws(e,t){let[r,s]=await Promise.all([Ue(e),Le(t)]);if(!r)return null;let n={name:e,type:"registry:theme",tailwind:{config:{theme:{extend:{borderRadius:{lg:"var(--radius)",md:"calc(var(--radius) - 2px)",sm:"calc(var(--radius) - 4px)"},colors:{}}}}},cssVars:{theme:{},light:{radius:"0.5rem"},dark:{}}};return t.tailwind.cssVariables&&(n.tailwind.config.theme.extend.colors={...n.tailwind.config.theme.extend.colors,...er(r.cssVars.dark??{})},n.cssVars={theme:{...r.cssVars.theme,...n.cssVars.theme},light:{...r.cssVars.light,...n.cssVars.light},dark:{...r.cssVars.dark,...n.cssVars.dark}},s==="v4"&&r.cssVarsV4&&(n.cssVars={theme:{...r.cssVarsV4.theme,...n.cssVars.theme},light:{radius:"0.625rem",...r.cssVarsV4.light},dark:{...r.cssVarsV4.dark}})),n}function H(e,t){let r=t||e.name,s=createHash("sha256").update(r).digest("hex").substring(0,8);return `${e.name}::${s}`}function Us(e){if(N(e)){let s=new URL(e).pathname,n=s.match(/\/([^/]+)\.json$/),i=n?n[1]:h__default.basename(s,".json");return {name:i,hash:H({name:i},e)}}if(Me(e)){let r=e.match(/\/([^/]+)\.json$/),s=r?r[1]:h__default.basename(e,".json");return {name:s,hash:H({name:s},e)}}let{item:t}=ee(e);return {name:t,hash:H({name:t},e)}}function Ms(e,t){let r=new Map,s=new Map,n=new Map,i=new Map;e.forEach(p=>{let l=t.get(p)||p.name,d=H(p,l);r.set(d,p),s.set(d,p),n.set(d,0),i.set(d,[]);});let o=new Map;e.forEach(p=>{let l=t.get(p)||p.name,d=H(p,l);o.has(p.name)||o.set(p.name,[]),o.get(p.name).push(d),l!==p.name&&(o.has(l)||o.set(l,[]),o.get(l).push(d));}),e.forEach(p=>{let l=t.get(p)||p.name,d=H(p,l);p.registryDependencies&&p.registryDependencies.forEach(f=>{let x,y=o.get(f)||[];if(y.length===1)x=y[0];else if(y.length>1)x=y[0];else {let{name:w}=Us(f),m=o.get(w)||[];m.length>0&&(x=m[0]);}x&&r.has(x)&&(i.get(x).push(d),n.set(d,n.get(d)+1));});});let a=[],c=[];for(n.forEach((p,l)=>{p===0&&a.push(l);});a.length>0;){let p=a.shift(),l=r.get(p);c.push(l),i.get(p).forEach(d=>{let f=n.get(d)-1;n.set(d,f),f===0&&a.push(d);});}if(c.length!==e.length){console.warn("Circular dependency detected in registry items");let p=new Set(c.map(l=>{let d=t.get(l)||l.name;return H(l,d)}));e.forEach(l=>{let d=t.get(l)||l.name,f=H(l,d);p.has(f)||c.push(l);});}return c}function ae(e){if(v.break(),v.error("Something went wrong. Please check the error below for more details."),v.error("If the problem persists, please open an issue on GitHub."),v.error(""),typeof e=="string"&&(v.error(e),v.break(),process.exit(1)),e instanceof b&&(e.message&&(v.error(e.cause?"Error:":"Message:"),v.error(e.message)),e.cause&&(v.error(`
Message:`),v.error(e.cause)),e.suggestion&&(v.error(`
Suggestion:`),v.error(e.suggestion)),v.break(),process.exit(1)),e instanceof z.ZodError){v.error("Validation failed:");for(let[t,r]of Object.entries(e.flatten().fieldErrors))v.error(`- ${k.info(t)}: ${r}`);v.break(),process.exit(1);}e instanceof Error&&(v.error(e.message),v.break(),process.exit(1)),v.break(),process.exit(1);}async function lt(e,t){let{config:r,useCache:s}=t||{};if(N(e)){let[a]=await j([e],{useCache:s});try{return h$1.parse(a)}catch(c){throw new $(e,c)}}if(!e.startsWith("@"))throw new he(e);let n=e;n.endsWith("/registry")||(n=`${n}/registry`);let i=ie(n,ne(r));if(!i?.url)throw new q(n);i.headers&&Object.keys(i.headers).length>0&&xe({[i.url]:i.headers});let[o]=await j([i.url],{useCache:s});try{return h$1.parse(o)}catch(a){throw new $(n,a)}}async function Gs(e,t){let{config:r,useCache:s=false}=t||{};return we(),Z(e,ne(r),{useCache:s})}async function qs(e,t){let{config:r,useCache:s=false}=t||{};return we(),sr(e,ne(r),{useCache:s})}async function Ja(e,t){let{useCache:r=true}=t||{};r||ke.clearCaches();let s=await ke.search(e);if(!s)return {registries:K};let n=z.object({registries:o.optional()}).safeParse(s.config);if(!n.success)throw new Te(e,n.error);return {registries:{...K,...n.data.registries||{}}}}async function nr(){try{let[e]=await j(["index.json"]);return i.parse(e)}catch(e){v.error(`
`),ae(e);}}async function Ya(){try{let[e]=await j(["styles/index.json"]);return j$1.parse(e)}catch(e){return v.error(`
`),ae(e),[]}}async function jt(){try{let[e]=await j(["icons/index.json"]);return k$1.parse(e)}catch(e){return ae(e),{}}}async function Za(){return Et}async function Ue(e){try{let[t]=await j([`colors/${e}.json`]);return l.parse(t)}catch(t){ae(t);}}async function Js(e,t){let r=[];for(let s of t){let n=e.find(i=>i.name===s);if(n&&(r.push(n),n.registryDependencies)){let i=await Js(e,n.registryDependencies);r.push(...i);}}return r.filter((s,n,i)=>i.findIndex(o=>o.name===s.name)===n)}async function Qa(e,t){try{let r=t.map(n=>`styles/${e}/${n.name}.json`);return (await j(r)).map(n=>g.parse(n))}catch(r){return ae(r),[]}}async function Xa(e,t,r){if(r)return r;if(t.type==="registry:ui")return e.resolvedPaths.ui??e.resolvedPaths.components;let[s,n]=t.type?.split(":")??[];return s in e.resolvedPaths?h__default.join(e.resolvedPaths[s],n):null}async function Ys(e){e={useCache:true,...e};let t=`${ce}/registries.json`,[r]=await j([t],{useCache:e.useCache});try{return u.parse(r)}catch(s){throw s instanceof z.ZodError?new ye(s):s}}async function Qs(e,t$1){let{query:r,limit:s,offset:n,config:i,useCache:o}=t$1||{},a=[];for(let f of e){let y=((await lt(f,{config:i,useCache:o})).items||[]).map(w=>({name:w.name,type:w.type,description:w.description,registry:f,addCommandArgument:rn(w.name,f)}));a=a.concat(y);}r&&(a=en(a,{query:r,limit:a.length,keys:["name","description"]}));let c=n||0,p=s||a.length,l=a.length,d={pagination:{total:l,offset:c,limit:p,hasMore:c+p<l},items:a.slice(c,c+p)};return t.parse(d)}var Xs=z.object({name:z.string(),type:z.string().optional(),description:z.string().optional(),registry:z.string().optional(),addCommandArgument:z.string().optional()}).passthrough();function en(e,t){t={limit:100,threshold:-1e4,...t};let s=Zs.go(t.query,e,{keys:t.keys,threshold:t.threshold,limit:t.limit}).map(n=>n.obj);return z.array(Xs).parse(s)}function tn(e){try{return new URL(e),!0}catch{return  false}}function rn(e,t){if(!tn(t))return `${t}/${e}`;let r=t.indexOf("://")+3,s=t.indexOf("/",r);if(s===-1){let f=t.indexOf("?",r);if(f!==-1){let x=t.substring(0,f),w=t.substring(f).replace(/\bregistry\b/g,e);return x+w}return t}let n=t.substring(0,s),i=t.substring(s),o=i.indexOf("?")!==-1?i.indexOf("?"):i.length,a=i.substring(0,o),c=i.substring(o),p=a.lastIndexOf("registry"),l=a;p!==-1&&(l=a.substring(0,p)+e+a.substring(p+8));let d=c.replace(/\bregistry\b/g,e);return n+l+d}
export{qs as $,X as A,$ as B,ue as C,he as D,ye as E,ee as F,bt as G,Pt as H,Tt as I,De as J,At as K,so as L,ds as M,yo as N,ne as O,we as P,$o as Q,ie as R,j as S,ca as T,Yt as U,zs as V,Z as W,sr as X,ae as Y,lt as Z,Gs as _,Et as a,Ja as aa,K as b,nr as ba,sn as c,Ya as ca,k as d,jt as da,In as e,Za as ea,Sn as f,Ue as fa,bn as g,Js as ga,Pn as h,Qa as ha,je as i,Xa as ia,Qe as j,Ys as ja,Tn as k,Qs as ka,hr as l,xr as m,Rt as n,Xe as o,V as p,Mn as q,Le as r,v as s,Y as t,b as u,q as v,me as w,de as x,ge as y,W as z};//# sourceMappingURL=chunk-OYVM23Y7.js.map
//# sourceMappingURL=chunk-OYVM23Y7.js.map