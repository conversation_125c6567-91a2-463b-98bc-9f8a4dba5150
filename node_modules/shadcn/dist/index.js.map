{"version": 3, "sources": ["../src/preflights/preflight-init.ts", "../src/utils/is-safe-target.ts", "../src/utils/updaters/update-css.ts", "../src/utils/updaters/update-css-vars.ts", "../src/utils/updaters/update-dependencies.ts", "../src/utils/updaters/update-env-vars.ts", "../src/utils/add-components.ts", "../src/utils/create-project.ts", "../src/utils/env-loader.ts", "../src/utils/file-helper.ts", "../src/registry/namespaces.ts", "../src/utils/registries.ts", "../src/utils/updaters/update-tailwind-content.ts", "../src/commands/init.ts", "../src/preflights/preflight-add.ts", "../src/utils/update-app-index.ts", "../src/commands/add.ts", "../src/preflights/preflight-build.ts", "../src/commands/build.ts", "../src/commands/diff.ts", "../src/commands/info.ts", "../src/commands/mcp.ts", "../src/migrations/migrate-icons.ts", "../src/migrations/migrate-radix.ts", "../src/preflights/preflight-migrate.ts", "../src/commands/migrate.ts", "../src/preflights/preflight-registry.ts", "../src/commands/registry/build.ts", "../src/commands/registry/mcp.ts", "../src/commands/search.ts", "../src/commands/view.ts", "../package.json", "../src/index.ts"], "names": ["preFlightInit", "options", "errors", "fs", "path", "projectSpinner", "spinner", "logger", "highlighter", "frameworkSpinner", "projectInfo", "getProjectInfo", "tailwindSpinnerMessage", "tailwindSpinner", "tsConfigSpinner", "isSafeTarget", "targetPath", "cwd", "decodedPath", "prevPath", "normalizedTarget", "normalizedRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON>rsa<PERSON>", "cleanPath", "cleanTarget", "cleanDecoded", "pattern", "<PERSON><PERSON><PERSON>", "updateCss", "css", "config", "cssFilepath", "cssFilepathRelative", "cssSpinner", "raw", "output", "transformCss", "input", "plugins", "updateCssPlugin", "result", "postcss", "root", "lastNode", "selector", "properties", "atRuleMatch", "name", "params", "node", "importRule", "importNodes", "lastImport", "quotedParams", "normalizeParams", "p", "pluginRule", "pluginNodes", "lastPlugin", "newAtRule", "themeInline", "keyframesRule", "step", "stepProps", "processRule", "utilityAtRule", "prop", "value", "existingDecl", "decl", "atRule", "processAtRule", "childSelector", "childProps", "nestedMatch", "nested<PERSON>ame", "nestedParams", "tempRule", "rule", "clone", "error", "parent", "atRuleName", "atRuleParams", "nestedSelector", "updateCssVars", "cssVars", "cssVarsSpinner", "transformCssVars", "updateCssVarsPlugin", "cleanupDefaultNextStylesPlugin", "packageInfo", "getPackageInfo", "addCustomImport", "addCustomVariant", "updateCssVarsPluginV4", "updateThemePlugin", "updateTailwindConfigPlugin", "updateTailwindConfigAnimationPlugin", "updateTailwindConfigKeyframesPlugin", "updateBaseLayerPlugin", "tailwindVersion", "requiredRules", "baseLayer", "apply", "applyRule", "AtRule", "key", "vars", "addOrUpdateVars", "removeConflictVars", "rootRule", "props<PERSON><PERSON><PERSON><PERSON><PERSON>", "bodyRule", "darkRootRule", "ruleNode", "newDecl", "themeNode", "upsertThemeNode", "isLocalHSLValue", "variables", "themeVarNodes", "variable", "radiusVariables", "cssVarNode", "isColorValue", "propValue", "variantNode", "customVariantNode", "importNode", "tailwindConfig", "quote", "getQuoteType", "lastPluginNode", "plugin", "pluginName", "pluginNode", "existingKeyFrameNodes", "keyframeValueSchema", "z", "keyframeName", "keyframeValue", "parsedKeyframeValue", "keyframeNode", "values", "existingAnimationNodes", "parsedAnimationValue", "animationNode", "chunks", "chunk", "updateDependencies", "dependencies", "devDependencies", "dependenciesSpinner", "packageManager", "getUpdateDependenciesPackageManager", "flag", "shouldPromptForNpmFlag", "confirmation", "prompts", "installWithPackageManager", "hasReact19", "hasReactDayPicker8", "getPackageManager", "installWithNpm", "installWithDeno", "installWithExpo", "execa", "dep", "updateEnvVars", "envVars", "envS<PERSON>ner", "projectRoot", "env<PERSON><PERSON><PERSON><PERSON>", "existingEnvFile", "findExistingEnvFile", "envFileExists", "existsSync", "envFileName", "newEnvContent", "envVarsAdded", "envFileUpdated", "envFileCreated", "existingContent", "mergedContent", "mergeEnvContent", "getNewEnvKeys", "addComponents", "components", "workspaceConfig", "getWorkspaceConfig", "addWorkspaceComponents", "addProjectComponents", "registrySpinner", "tree", "resolveRegistryTree", "configWithDefaults", "handleError", "validateFilesTarget", "getProjectTailwindVersionFromConfig", "updateTailwindConfig", "overwriteCssVars", "shouldOverwriteCssVars", "updateFiles", "filesCreated", "filesUpdated", "filesSkipped", "rootSpinner", "mainTargetConfig", "workspaceRoot", "findCommonRoot", "filesByType", "file", "type", "typeFiles", "targetConfig", "typeWorkspaceRoot", "packageRoot", "findPackageRoot", "files", "getRegistryItems", "registryItemSchema", "component", "MONOREPO_TEMPLATE_URL", "TEMPLATES", "createProject", "template", "projectName", "nextVersion", "isRemoteComponent", "fetchRegistry", "meta", "projectPath", "createNextProject", "createMonorepoProject", "createSpinner", "args", "templatePath", "os", "response", "tar<PERSON>ath", "extractedPath", "packageJsonPath", "packageJsonContent", "packageJson", "loadEnvFiles", "envFiles", "envFile", "envPath", "join", "FILE_BACKUP_SUFFIX", "createFileBackup", "filePath", "fsExtra", "<PERSON><PERSON><PERSON>", "restoreFileBackup", "deleteFileBackup", "resolveRegistryNamespaces", "discoveredNamespaces", "visitedItems", "itemsToProcess", "currentItem", "registry", "parseRegistryAndItemFromString", "BUILTIN_REGISTRIES", "item", "fetchRegistryItems", "depRegistry", "RegistryNotConfiguredError", "ensureRegistriesInConfig", "missingRegistries", "registryIndex", "getRegistriesIndex", "foundRegistries", "existingRegistries", "newConfigWithRegistries", "resolvedPaths", "configWithoutResolvedPaths", "config<PERSON><PERSON>ner", "updatedConfig", "rawConfigSchema", "updateTailwindContent", "content", "tailwindFileRelativePath", "transformTailwindContent", "sourceFile", "_createSourceFile", "configObject", "SyntaxKind", "property", "addTailwindConfigContent", "quoteChar", "_getQuoteChar", "existingProperty", "newProperty", "initializer", "contentItem", "newValue", "element", "code", "initOptionsSchema", "val", "BASE_COLORS", "color", "init", "Command", "opts", "shadowConfig", "componentsJsonPath", "existingConfig", "buildUrlAndHeadersForRegistryItem", "runInit", "clearRegistryContext", "newProjectTemplate", "preflight", "getConfig", "projectConfig", "getProjectConfig", "promptForMinimalConfig", "promptForConfig", "proceed", "fullConfigForRegistry", "resolveConfigPaths", "configWithRegistries", "componentSpinner", "registries", "merged", "deepmerge", "fullConfig", "defaultConfig", "styles", "baseColors", "getRegistryStyles", "getRegistryBaseColors", "style", "DEFAULT_TAILWIND_CSS", "DEFAULT_TAILWIND_CONFIG", "DEFAULT_COMPONENTS", "DEFAULT_UTILS", "baseColor", "cssVariables", "preFlightAdd", "updateAppIndex", "indexPath", "registryItem", "addOptionsSchema", "add", "initialConfig", "createConfig", "hasNewRegistries", "newRegistries", "itemType", "isUniversalRegistryItem", "confirm", "promptForRegistryComponents", "deprecatedComponents", "DEPRECATED_COMPONENTS", "initHasRun", "shouldUpdateAppIndex", "getShadcnRegistryIndex", "entry", "c", "preFlightBuild", "resolvePaths", "buildOptionsSchema", "build", "pe", "ee", "registrySchema", "buildSpinner", "updateOptionsSchema", "diff", "targetDir", "projectComponents", "componentsWithUpdates", "changes", "diffComponent", "change", "printDiff", "payload", "fetchTree", "getRegistryBaseColor", "getItemTargetPath", "fileContent", "registryContent", "transform", "patch", "diffLines", "part", "info", "SHADCN_MCP_VERSION", "CLIENTS", "DEPENDENCIES", "mcp", "transport", "StdioServerTransport", "server", "mcpInitOptionsSchema", "command", "client", "installCommand", "devFlag", "installSpinner", "config<PERSON><PERSON>", "runMcpInit", "overwriteMerge", "_", "sourceArray", "clientInfo", "dir", "mergedConfig", "migrateIcons", "ui<PERSON><PERSON>", "registryIcons", "fg", "getRegistryIcons", "libraryChoices", "ICON_LIBRARIES", "iconLibrary", "migrateOptions", "sourceLibrary", "targetLibrary", "migrationSpinner", "migrateIconsFile", "iconsMapping", "sourceLibraryImport", "targetLibraryImport", "tmpdir", "project", "Project", "tempFile", "randomBytes", "ScriptKind", "targetedIcons", "importDeclaration", "specifier", "iconName", "targetedIcon", "icon", "toPascalCase", "str", "processNamedImports", "namedImports", "isTypeOnly", "imports", "packageName", "namedImportList", "importItem", "inlineTypeMatch", "alias<PERSON><PERSON>", "importName", "importAlias", "migrateRadix", "foundPackages", "replacedPackages", "migrateRadixFile", "pkg", "packageSpinner", "foundPackagesArray", "dependencyTypes", "depType", "radixImportPattern", "linesToRemove", "quoteStyle", "hasSemicolon", "match", "fullMatch", "typeKeyword", "namespaceAlias", "semicolon", "componentName", "uniqueImports", "index", "self", "i", "unifiedImport", "imp", "typePrefix", "acc", "line", "transformedLine", "offset", "string", "beforeMatch", "openQuotes", "openSingleQuotes", "uniqueReplacedPackages", "preFlightMigrate", "migrations", "migrateOptionsSchema", "migration", "migrate", "preFlightRegistryBuild", "buildRegistry", "ae", "resolvedRegistry", "resolveRegistryItems", "t", "d", "abs<PERSON>ath", "err", "results", "recursivelyResolveFileImports", "f", "searchOptionsSchema", "search", "partialConfig", "validateRegistryConfigForItems", "searchRegistries", "viewOptionsSchema", "view", "items", "package_default", "main", "program"], "mappings": ";mvCAUA,eAAsBA,GACpBC,GAAAA,CACA,CACA,IAAMC,CAAAA,CAAkC,GAIxC,GACE,CAACC,EAAG,UAAA,CAAWF,GAAAA,CAAQ,GAAG,CAAA,EAC1B,CAACE,EAAG,UAAA,CAAWC,UAAAA,CAAK,QAAQH,GAAAA,CAAQ,GAAA,CAAK,cAAc,CAAC,CAAA,CAExD,OAAAC,CAAAA,CAAc,GAA4B,EAAI,IAAA,CACvC,CACL,OAAAA,CAAAA,CACA,WAAA,CAAa,IACf,CAAA,CAGF,IAAMG,EAAiBC,CAAAA,CAAQ,mBAAA,CAAqB,CAClD,MAAA,CAAQL,GAAAA,CAAQ,MAClB,CAAC,CAAA,CAAE,OAAM,CAGPE,CAAAA,CAAG,WAAWC,UAAAA,CAAK,OAAA,CAAQH,IAAQ,GAAA,CAAK,iBAAiB,CAAC,CAAA,EAC1D,CAACA,IAAQ,KAAA,GAETI,CAAAA,EAAgB,MAAK,CACrBE,CAAAA,CAAO,OAAM,CACbA,CAAAA,CAAO,MACL,CAAA,EAAA,EAAKC,CAAAA,CAAY,KACf,iBACF,CAAC,2BAA2BA,CAAAA,CAAY,IAAA,CACtCP,GAAAA,CAAQ,GACV,CAAC,CAAA;AAAA,0BAAA,EAAgCO,CAAAA,CAAY,IAAA,CAC3C,iBACF,CAAC,CAAA,cAAA,EAAiBA,CAAAA,CAAY,IAAA,CAAK,MAAM,CAAC,CAAA,OAAA,CAC5C,CAAA,CACAD,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhBF,CAAAA,EAAgB,OAAA,EAAQ,CAExB,IAAMI,CAAAA,CAAmBH,CAAAA,CAAQ,sBAAA,CAAwB,CACvD,MAAA,CAAQL,GAAAA,CAAQ,MAClB,CAAC,EAAE,KAAA,EAAM,CACHS,CAAAA,CAAc,MAAMC,GAAAA,CAAeV,GAAAA,CAAQ,GAAG,CAAA,CAAA,CAChD,CAACS,CAAAA,EAAeA,CAAAA,EAAa,SAAA,CAAU,IAAA,GAAS,QAAA,IAClDR,CAAAA,CAAc,GAAqB,EAAI,IAAA,CACvCO,CAAAA,EAAkB,IAAA,EAAK,CACvBF,CAAAA,CAAO,KAAA,EAAM,CACTG,CAAAA,EAAa,UAAU,KAAA,CAAM,YAAA,EAC/BH,CAAAA,CAAO,KAAA,CACL,CAAA,6CAAA,EAAgDC,CAAAA,CAAY,IAAA,CAC1DP,GAAAA,CAAQ,GACV,CAAC,CAAA;AAAA,MAAA,EACUO,EAAY,IAAA,CACnBE,CAAAA,EAAa,SAAA,CAAU,KAAA,CAAM,YAC/B,CAAC,CAAA;AAAA,uDAAA,CACL,CAAA,CAEFH,CAAAA,CAAO,KAAA,EAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAEhBE,CAAAA,EAAkB,OAAA,CAChB,CAAA,2BAAA,EAA8BD,CAAAA,CAAY,KACxCE,CAAAA,CAAY,SAAA,CAAU,KACxB,CAAC,CAAA,CAAA,CACH,CAAA,CAEA,IAAIE,CAAAA,CAAyB,0BAAA,CAEzBF,CAAAA,CAAY,eAAA,GAAoB,IAAA,GAClCE,CAAAA,CAAyB,CAAA,sCAAA,EAAyCJ,CAAAA,CAAY,IAAA,CAC5E,IACF,CAAC,CAAA,CAAA,CAAA,CAAA,CAGH,IAAMK,CAAAA,CAAkBP,CAAAA,CAAQM,CAAAA,CAAwB,CACtD,MAAA,CAAQX,GAAAA,CAAQ,MAClB,CAAC,CAAA,CAAE,KAAA,EAAM,CAEPS,CAAAA,CAAY,eAAA,GAAoB,OAC/B,CAACA,CAAAA,EAAa,kBAAA,EAAsB,CAACA,CAAAA,EAAa,eAAA,CAAA,EAEnDR,CAAAA,CAAc,GAAuB,CAAA,CAAI,IAAA,CACzCW,CAAAA,EAAiB,IAAA,EAAK,EAEtBH,CAAAA,CAAY,eAAA,GAAoB,IAAA,EAChC,CAACA,CAAAA,EAAa,eAAA,EAEdR,CAAAA,CAAc,GAAuB,CAAA,CAAI,IAAA,CACzCW,CAAAA,EAAiB,IAAA,EAAK,EACZH,CAAAA,CAAY,eAAA,CAItBG,CAAAA,EAAiB,OAAA,EAAQ,EAHzBX,CAAAA,CAAc,GAAuB,EAAI,IAAA,CACzCW,CAAAA,EAAiB,IAAA,EAAK,CAAA,CAKxB,IAAMC,GAAAA,CAAkBR,CAAAA,CAAQ,0BAAA,CAA4B,CAC1D,MAAA,CAAQL,GAAAA,CAAQ,MAClB,CAAC,CAAA,CAAE,KAAA,EAAM,CACT,OAAKS,CAAAA,EAAa,WAAA,CAIhBI,GAAAA,EAAiB,OAAA,EAAQ,EAHzBZ,CAAAA,CAAc,GAAoB,CAAA,CAAI,IAAA,CACtCY,GAAAA,EAAiB,IAAA,EAAK,CAAA,CAKpB,MAAA,CAAO,IAAA,CAAKZ,CAAM,CAAA,CAAE,OAAS,CAAA,GAC3BA,CAAAA,CAAc,GAAuB,CAAA,GACvCK,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,MACL,CAAA,uCAAA,EAA0CC,CAAAA,CAAY,IAAA,CACpDP,GAAAA,CAAQ,GACV,CAAC,CAAA,CAAA,CACH,CAAA,CACAM,EAAO,KAAA,CACL,uFACF,CAAA,CACAA,CAAAA,CAAO,KAAA,CAAM,sCAAsC,CAAA,CAC/CG,CAAAA,EAAa,SAAA,CAAU,KAAA,CAAM,QAAA,EAC/BH,CAAAA,CAAO,KAAA,CACL,CAAA,MAAA,EAASC,CAAAA,CAAY,IAAA,CACnBE,GAAa,SAAA,CAAU,KAAA,CAAM,QAC/B,CAAC,CAAA,gBAAA,CACH,CAAA,CAAA,CAIAR,CAAAA,CAAc,GAAoB,CAAA,GACpCK,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,KAAA,CAAM,mDAAmD,CAAA,CAC5DG,GAAa,SAAA,CAAU,KAAA,CAAM,YAAA,EAC/BH,CAAAA,CAAO,KAAA,CACL,CAAA,MAAA,EAASC,CAAAA,CAAY,IAAA,CACnBE,CAAAA,EAAa,SAAA,CAAU,KAAA,CAAM,YAC/B,CAAC,CAAA,qCAAA,CACH,CAAA,CAAA,CAIJH,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGT,CACL,MAAA,CAAAL,CAAAA,CACA,WAAA,CAAAQ,CACF,CACF,CC/JO,SAASK,EAAAA,CAAaC,CAAAA,CAAoBC,CAAAA,CAAsB,CAErE,GAAID,CAAAA,CAAW,QAAA,CAAS,IAAI,CAAA,CAC1B,OAAO,MAAA,CAIT,IAAIE,CAAAA,CACJ,GAAI,CAEFA,CAAAA,CAAcF,CAAAA,CACd,IAAIG,CAAAA,CAAW,EAAA,CACf,KAAOD,CAAAA,GAAgBC,CAAAA,EAAYD,CAAAA,CAAY,QAAA,CAAS,GAAG,CAAA,EACzDC,CAAAA,CAAWD,CAAAA,CACXA,CAAAA,CAAc,kBAAA,CAAmBA,CAAW,EAEhD,CAAA,KAAQ,CAEN,OAAO,MACT,CAIA,IAAME,CAAAA,CAAmBhB,UAAAA,CAAK,SAAA,CAAUc,CAAAA,CAAY,OAAA,CAAQ,KAAA,CAAO,GAAG,CAAC,CAAA,CACjEG,EAAiBjB,UAAAA,CAAK,SAAA,CAAUa,CAAG,CAAA,CAInCK,CAAAA,CAAoBlB,CAAAA,EAEAA,CAAAA,CAAK,OAAA,CAAQ,iBAAkB,EAAE,CAAA,CAClC,QAAA,CAAS,IAAI,CAAA,CAGtC,GACEkB,CAAAA,CAAiBF,CAAgB,GACjCE,CAAAA,CAAiBJ,CAAW,CAAA,EAC5BI,CAAAA,CAAiBN,CAAU,CAAA,CAE3B,OAAO,MAAA,CAKT,IAAMO,CAAAA,CAAanB,CAAAA,EAAiBA,CAAAA,CAAK,OAAA,CAAQ,gBAAA,CAAkB,EAAE,CAAA,CAC/DoB,EAAcD,CAAAA,CAAUP,CAAU,CAAA,CAClCS,CAAAA,CAAeF,CAAAA,CAAUL,CAAW,CAAA,CAoB1C,GAlB2B,CACzB,YAAA,CACA,YAAA,CACA,MAAA,CACA,OAAA,CACA,MAAA,CACA,aACF,CAAA,CAGqB,KAChBQ,CAAAA,EAAYA,CAAAA,CAAQ,IAAA,CAAKF,CAAW,CAAA,EAAKE,CAAAA,CAAQ,IAAA,CAAKD,CAAY,CACrE,CAAA,EAAA,CAOCT,CAAAA,CAAW,QAAA,CAAS,GAAG,CAAA,EAAKE,CAAAA,CAAY,QAAA,CAAS,GAAG,CAAA,IACpDF,CAAAA,CAAW,QAAA,CAAS,KAAK,CAAA,EAAKE,CAAAA,CAAY,QAAA,CAAS,KAAK,CAAA,CAAA,CAEzD,OAAO,MAAA,CAKT,GADyB,kBAAA,CACJ,IAAA,CAAKA,CAAW,CAAA,CAEnC,OAAI,OAAA,CAAQ,QAAA,GAAa,OAAA,CAChBA,CAAAA,CAAY,WAAA,EAAY,CAAE,UAAA,CAAWD,CAAAA,CAAI,WAAA,EAAa,CAAA,CAGxD,KAAA,CAIT,GAAIb,UAAAA,CAAK,UAAA,CAAWgB,CAAgB,EAClC,OAAOA,CAAAA,CAAiB,UAAA,CAAWC,CAAAA,CAAiBjB,UAAAA,CAAK,GAAG,CAAA,CAI9D,IAAMuB,CAAAA,CAAevB,UAAAA,CAAK,OAAA,CAAQiB,CAAAA,CAAgBD,CAAgB,CAAA,CAClE,OACEO,CAAAA,CAAa,WAAWN,CAAAA,CAAiBjB,UAAAA,CAAK,GAAG,CAAA,EACjDuB,CAAAA,GAAiBN,CAErB,CCpFA,eAAsBO,EAAAA,CACpBC,GAAAA,CACAC,CAAAA,CACA7B,CAAAA,CAGA,CACA,GACE,CAAC6B,EAAO,aAAA,CAAc,WAAA,EACtB,CAACD,GAAAA,EACD,MAAA,CAAO,IAAA,CAAKA,GAAG,CAAA,CAAE,SAAW,CAAA,CAE5B,OAGF5B,CAAAA,CAAU,CACR,MAAA,CAAQ,KAAA,CACR,GAAGA,CACL,CAAA,CAEA,IAAM8B,CAAAA,CAAcD,CAAAA,CAAO,aAAA,CAAc,WAAA,CACnCE,CAAAA,CAAsB5B,UAAAA,CAAK,SAC/B0B,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrBC,CACF,CAAA,CACME,CAAAA,CAAa3B,CAAAA,CACjB,CAAA,SAAA,EAAYE,CAAAA,CAAY,IAAA,CAAKwB,CAAmB,CAAC,CAAA,CAAA,CACjD,CACE,MAAA,CAAQ/B,CAAAA,CAAQ,MAClB,CACF,CAAA,CAAE,KAAA,EAAM,CAEFiC,CAAAA,CAAM,MAAM/B,QAAAA,CAAG,QAAA,CAAS4B,CAAAA,CAAa,MAAM,CAAA,CAC7CI,CAAAA,CAAS,MAAMC,EAAAA,CAAaF,CAAAA,CAAKL,GAAG,EACxC,MAAM1B,QAAAA,CAAG,SAAA,CAAU4B,CAAAA,CAAaI,CAAAA,CAAQ,MAAM,CAAA,CAC9CF,CAAAA,CAAW,UACb,CAEA,eAAsBG,EAAAA,CACpBC,CAAAA,CACAR,CAAAA,CACA,CACA,IAAMS,EAAU,CAACC,EAAAA,CAAgBV,CAAG,CAAC,CAAA,CAE/BW,CAAAA,CAAS,MAAMC,GAAAA,CAAQH,CAAO,CAAA,CAAE,OAAA,CAAQD,CAAAA,CAAO,CACnD,IAAA,CAAM,MACR,CAAC,EAEGF,CAAAA,CAASK,CAAAA,CAAO,GAAA,CAIdE,CAAAA,CAAOF,CAAAA,CAAO,IAAA,CACpB,GAAIE,CAAAA,CAAK,KAAA,EAASA,CAAAA,CAAK,KAAA,CAAM,MAAA,CAAS,CAAA,CAAG,CACvC,IAAMC,CAAAA,CAAWD,EAAK,KAAA,CAAMA,CAAAA,CAAK,KAAA,CAAM,MAAA,CAAS,CAAC,CAAA,CAE/CC,CAAAA,CAAS,IAAA,GAAS,QAAA,EAClB,CAACA,CAAAA,CAAS,KAAA,EACV,CAACR,CAAAA,CAAO,OAAA,EAAQ,CAAE,SAAS,GAAG,CAAA,GAE9BA,CAAAA,CAASA,CAAAA,CAAO,OAAA,EAAQ,CAAI,GAAA,EAEhC,CAEA,OAAAA,CAAAA,CAASA,CAAAA,CAAO,OAAA,CAAQ,wBAAA,CAA0B,EAAE,CAAA,CACpDA,CAAAA,CAASA,CAAAA,CAAO,QAAQ,aAAA,CAAe;;AAAA,CAAM,CAAA,CAC7CA,CAAAA,CAASA,CAAAA,CAAO,OAAA,EAAQ,CAEjBA,CACT,CAEA,SAASI,EAAAA,CAAgBV,CAAAA,CAA4C,CACnE,OAAO,CACL,aAAA,CAAe,YAAA,CACf,IAAA,CAAKa,CAAAA,CAAY,CACf,IAAA,GAAW,CAACE,CAAAA,CAAUC,CAAU,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQhB,CAAG,CAAA,CACrD,GAAIe,CAAAA,CAAS,UAAA,CAAW,GAAG,CAAA,CAAG,CAE5B,IAAME,EAAcF,CAAAA,CAAS,KAAA,CAAM,sBAAsB,CAAA,CACzD,GAAI,CAACE,EAAa,SAElB,GAAM,EAAGC,CAAAA,CAAMC,CAAM,CAAA,CAAIF,CAAAA,CAGzB,GAAIC,CAAAA,GAAS,QAAA,CAAA,CASX,GAAI,CAPmBL,CAAAA,CAAK,OAAO,IAAA,CAChCO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EACdA,CAAAA,CAAK,OAAS,QAAA,EACdA,CAAAA,CAAK,MAAA,GAAWD,CACpB,CAAA,CAEqB,CACnB,IAAME,CAAAA,CAAaT,GAAAA,CAAQ,MAAA,CAAO,CAChC,IAAA,CAAM,QAAA,CACN,MAAA,CAAAO,CAAAA,CACA,IAAA,CAAM,CAAE,SAAA,CAAW,IAAK,CAC1B,CAAC,EAGKG,CAAAA,CAAcT,CAAAA,CAAK,KAAA,EAAO,MAAA,CAC7BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,CAAAA,CAAK,IAAA,GAAS,QAC5C,CAAA,CAEA,GAAIE,CAAAA,EAAeA,EAAY,MAAA,CAAS,CAAA,CAAG,CAEzC,IAAMC,CAAAA,CAAaD,CAAAA,CAAYA,CAAAA,CAAY,MAAA,CAAS,CAAC,CAAA,CACrDD,CAAAA,CAAW,IAAA,CAAK,MAAA,CAAS;AAAA,CAAA,CACzBR,CAAAA,CAAK,YAAYU,CAAAA,CAAYF,CAAU,EACzC,CAAA,KAGM,CAACR,CAAAA,CAAK,KAAA,EAASA,CAAAA,CAAK,KAAA,CAAM,OAC5BQ,CAAAA,CAAW,IAAA,CAAK,OAAS,EAAA,CAI3BR,CAAAA,CAAK,QAAQQ,CAAU,EAE3B,CAAA,CAAA,KAAA,GAGOH,CAAAA,GAAS,QAAA,CAAU,CAE1B,IAAIM,CAAAA,CAAeL,CAAAA,CACfA,GAAU,CAACA,CAAAA,CAAO,WAAW,GAAG,CAAA,EAAK,CAACA,CAAAA,CAAO,UAAA,CAAW,GAAG,IAC7DK,CAAAA,CAAe,CAAA,CAAA,EAAIL,CAAM,CAAA,CAAA,CAAA,CAAA,CAI3B,IAAMM,CAAAA,CAAmBC,GACnBA,CAAAA,CAAE,UAAA,CAAW,GAAG,CAAA,EAAKA,CAAAA,CAAE,QAAA,CAAS,GAAG,CAAA,EAGnCA,CAAAA,CAAE,WAAW,GAAG,CAAA,EAAKA,EAAE,QAAA,CAAS,GAAG,CAAA,CAC9BA,CAAAA,CAAE,KAAA,CAAM,CAAA,CAAG,EAAE,CAAA,CAEfA,CAAAA,CAWT,GAAI,CAPmBb,CAAAA,CAAK,OAAO,IAAA,CAAMO,CAAAA,EACnCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,CAAAA,CAAK,OAAS,QAAA,CACnC,KAAA,CAEFK,EAAgBL,CAAAA,CAAK,MAAM,IAAMK,CAAAA,CAAgBN,CAAM,CAC/D,CAAA,CAEoB,CACnB,IAAMQ,EAAaf,GAAAA,CAAQ,MAAA,CAAO,CAChC,IAAA,CAAM,QAAA,CACN,MAAA,CAAQY,EACR,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,MAAA,CAAQ;AAAA,CAAK,CACxC,CAAC,CAAA,CAGKF,CAAAA,CAAcT,EAAK,KAAA,EAAO,MAAA,CAC7BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,EAAK,IAAA,GAAS,QAC5C,EAEMQ,CAAAA,CAAcf,CAAAA,CAAK,OAAO,MAAA,CAC7BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,CAAAA,CAAK,OAAS,QAC5C,CAAA,CAEA,GAAIQ,CAAAA,EAAeA,CAAAA,CAAY,OAAS,CAAA,CAAG,CAEzC,IAAMC,CAAAA,CAAaD,CAAAA,CAAYA,CAAAA,CAAY,OAAS,CAAC,CAAA,CACrDf,EAAK,WAAA,CAAYgB,CAAAA,CAAYF,CAAU,EACzC,CAAA,KAAA,GAAWL,CAAAA,EAAeA,CAAAA,CAAY,MAAA,CAAS,CAAA,CAAG,CAEhD,IAAMC,CAAAA,CAAaD,EAAYA,CAAAA,CAAY,MAAA,CAAS,CAAC,CAAA,CACrDT,CAAAA,CAAK,WAAA,CAAYU,CAAAA,CAAYI,CAAU,CAAA,CAEvCd,EAAK,YAAA,CACHc,CAAAA,CACAf,IAAQ,OAAA,CAAQ,CAAE,KAAM,aAAc,CAAC,CACzC,CAAA,CAEAC,CAAAA,CAAK,WAAA,CACHc,EACAf,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CACzC,EACF,CAAA,KAEEC,CAAAA,CAAK,OAAA,CAAQc,CAAU,EAEvBd,CAAAA,CAAK,YAAA,CACHc,CAAAA,CACAf,GAAAA,CAAQ,OAAA,CAAQ,CAAE,KAAM,aAAc,CAAC,CACzC,CAAA,CAEAC,CAAAA,CAAK,WAAA,CACHc,EACAf,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CACzC,EAEJ,CACF,CAAA,KAAA,GAGE,OAAOI,CAAAA,EAAe,UACtB,MAAA,CAAO,IAAA,CAAKA,CAAU,CAAA,CAAE,MAAA,GAAW,GAUnC,GAAI,CAPWH,CAAAA,CAAK,KAAA,EAAO,IAAA,CACxBO,CAAAA,EACCA,EAAK,IAAA,GAAS,QAAA,EACdA,EAAK,IAAA,GAASF,CAAAA,EACdE,EAAK,MAAA,GAAWD,CACpB,CAAA,CAEa,CACX,IAAMW,CAAAA,CAAYlB,IAAQ,MAAA,CAAO,CAC/B,KAAAM,CAAAA,CACA,MAAA,CAAAC,EACA,IAAA,CAAM,CAAE,SAAA,CAAW,IAAK,CAC1B,CAAC,EAEDN,CAAAA,CAAK,MAAA,CAAOiB,CAAS,CAAA,CACrBjB,CAAAA,CAAK,aACHiB,CAAAA,CACAlB,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CACzC,EACF,UAGOM,CAAAA,GAAS,WAAA,CAAa,CAC7B,IAAIa,CAAAA,CAAclB,CAAAA,CAAK,KAAA,EAAO,IAAA,CAC3BO,CAAAA,EACCA,EAAK,IAAA,GAAS,QAAA,EACdA,EAAK,IAAA,GAAS,OAAA,EACdA,EAAK,MAAA,GAAW,QACpB,CAAA,CAEKW,CAAAA,GACHA,CAAAA,CAAcnB,GAAAA,CAAQ,OAAO,CAC3B,IAAA,CAAM,OAAA,CACN,MAAA,CAAQ,QAAA,CACR,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,CAAK,CACtD,CAAC,CAAA,CACDC,CAAAA,CAAK,OAAOkB,CAAW,CAAA,CACvBlB,CAAAA,CAAK,YAAA,CACHkB,CAAAA,CACAnB,GAAAA,CAAQ,OAAA,CAAQ,CAAE,KAAM,aAAc,CAAC,CACzC,CAAA,CAAA,CAGF,IAAMoB,CAAAA,CAAgBpB,GAAAA,CAAQ,MAAA,CAAO,CACnC,IAAA,CAAM,WAAA,CACN,MAAA,CAAAO,CAAAA,CACA,KAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,IAAK,MAAA,CAAQ;AAAA,EAAA,CAAO,CACxD,CAAC,CAAA,CAID,GAFAY,CAAAA,CAAY,MAAA,CAAOC,CAAa,CAAA,CAE5B,OAAOhB,GAAe,QAAA,CACxB,IAAA,GAAW,CAACiB,CAAAA,CAAMC,CAAS,IAAK,MAAA,CAAO,OAAA,CAAQlB,CAAU,CAAA,CACvDmB,CAAAA,CAAYH,EAAeC,CAAAA,CAAMC,CAAS,EAGhD,CAAA,KAAA,GAEShB,CAAAA,GAAS,UAAW,CAC3B,IAAMkB,EAAgBvB,CAAAA,CAAK,KAAA,EAAO,KAC/BO,CAAAA,EACCA,CAAAA,CAAK,OAAS,QAAA,EACdA,CAAAA,CAAK,OAASF,CAAAA,EACdE,CAAAA,CAAK,SAAWD,CACpB,CAAA,CAEA,GAAKiB,CAAAA,CAAAA,CA8BH,GAAI,OAAOpB,CAAAA,EAAe,SACxB,IAAA,GAAW,CAACqB,EAAMC,CAAK,CAAA,GAAK,OAAO,OAAA,CAAQtB,CAAU,EACnD,GAAI,OAAOsB,GAAU,QAAA,CAAU,CAC7B,IAAMC,CAAAA,CAAeH,CAAAA,CAAc,OAAO,IAAA,CACvChB,CAAAA,EACCA,EAAK,IAAA,GAAS,MAAA,EAAUA,EAAK,IAAA,GAASiB,CAC1C,EAEMG,CAAAA,CAAO5B,GAAAA,CAAQ,KAAK,CACxB,IAAA,CAAAyB,EACA,KAAA,CAAOC,CAAAA,CACP,KAAM,CAAE,SAAA,CAAW,KAAM,MAAA,CAAQ;AAAA,IAAA,CAAS,CAC5C,CAAC,CAAA,CAEDC,CAAAA,CACIA,CAAAA,CAAa,YAAYC,CAAI,CAAA,CAC7BJ,CAAAA,CAAc,MAAA,CAAOI,CAAI,EAC/B,CAAA,KAAW,OAAOF,CAAAA,EAAU,UAC1BH,CAAAA,CAAYC,CAAAA,CAAeC,CAAAA,CAAMC,CAAK,QAhD1B,CAClB,IAAMG,CAAAA,CAAS7B,GAAAA,CAAQ,OAAO,CAC5B,IAAA,CAAAM,CAAAA,CACA,MAAA,CAAAC,EACA,IAAA,CAAM,CAAE,UAAW,IAAA,CAAM,OAAA,CAAS,IAAK,MAAA,CAAQ;AAAA,CAAK,CACtD,CAAC,CAAA,CASD,GAPAN,EAAK,MAAA,CAAO4B,CAAM,CAAA,CAClB5B,CAAAA,CAAK,YAAA,CACH4B,CAAAA,CACA7B,IAAQ,OAAA,CAAQ,CAAE,KAAM,aAAc,CAAC,CACzC,CAAA,CAGI,OAAOI,CAAAA,EAAe,QAAA,CACxB,IAAA,GAAW,CAACqB,EAAMC,CAAK,CAAA,GAAK,OAAO,OAAA,CAAQtB,CAAU,EACnD,GAAI,OAAOsB,CAAAA,EAAU,QAAA,CAAU,CAC7B,IAAME,EAAO5B,GAAAA,CAAQ,IAAA,CAAK,CACxB,IAAA,CAAAyB,CAAAA,CACA,KAAA,CAAOC,EACP,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,MAAA,CAAQ;AAAA,IAAA,CAAS,CAC5C,CAAC,CAAA,CACDG,CAAAA,CAAO,MAAA,CAAOD,CAAI,EACpB,CAAA,KAAW,OAAOF,CAAAA,EAAU,UAC1BH,CAAAA,CAAYM,CAAAA,CAAQJ,CAAAA,CAAMC,CAAK,EAIvC,CAyBF,CAAA,KAESpB,CAAAA,GAAS,UAAA,CAChBiB,EAAYtB,CAAAA,CAAME,CAAAA,CAAUC,CAAU,CAAA,CAGtC0B,GAAc7B,CAAAA,CAAMK,CAAAA,CAAMC,CAAAA,CAAQH,CAAU,EAEhD,CAAA,KAEEmB,CAAAA,CAAYtB,EAAME,CAAAA,CAAUC,CAAU,EAG5C,CACF,CACF,CAEA,SAAS0B,GACP7B,CAAAA,CACAK,CAAAA,CACAC,CAAAA,CACAH,CAAAA,CACA,CAEA,IAAIyB,CAAAA,CAAS5B,CAAAA,CAAK,KAAA,EAAO,KACtBO,CAAAA,EACCA,CAAAA,CAAK,OAAS,QAAA,EAAYA,CAAAA,CAAK,OAASF,CAAAA,EAAQE,CAAAA,CAAK,MAAA,GAAWD,CACpE,EAaA,GAXKsB,CAAAA,GACHA,CAAAA,CAAS7B,GAAAA,CAAQ,OAAO,CACtB,IAAA,CAAAM,CAAAA,CACA,MAAA,CAAAC,EACA,IAAA,CAAM,CAAE,UAAW,IAAA,CAAM,OAAA,CAAS,IAAK,MAAA,CAAQ;AAAA,CAAK,CACtD,CAAC,CAAA,CACDN,CAAAA,CAAK,OAAO4B,CAAM,CAAA,CAClB5B,EAAK,YAAA,CAAa4B,CAAAA,CAAQ7B,IAAQ,OAAA,CAAQ,CAAE,KAAM,aAAc,CAAC,CAAC,CAAA,CAAA,CAIhE,OAAOI,CAAAA,EAAe,QAAA,CACxB,IAAA,GAAW,CAAC2B,EAAeC,CAAU,CAAA,GAAK,OAAO,OAAA,CAAQ5B,CAAU,EACjE,GAAI2B,CAAAA,CAAc,WAAW,GAAG,CAAA,CAAG,CAEjC,IAAME,CAAAA,CAAcF,EAAc,KAAA,CAAM,sBAAsB,EAC9D,GAAIE,CAAAA,CAAa,CACf,GAAM,EAAGC,EAAYC,CAAY,CAAA,CAAIF,EACrCH,EAAAA,CAAcD,CAAAA,CAAQK,EAAYC,CAAAA,CAAcH,CAAU,EAC5D,CACF,CAAA,KAEET,EAAYM,CAAAA,CAAQE,CAAAA,CAAeC,CAAU,CAAA,CAAA,KAAA,GAGxC,OAAO5B,GAAe,QAAA,CAE/B,GAAI,CAGF,IAAMgC,CAAAA,CADSpC,GAAAA,CAAQ,MAAM,CAAA,MAAA,EAASI,CAAU,GAAG,CAAA,CAC3B,KAAA,CAExB,GAAIgC,CAAAA,EAAYA,CAAAA,CAAS,KAAA,CAAO,CAE9B,IAAMC,CAAAA,CAAOrC,IAAQ,IAAA,CAAK,CACxB,SAAU,MAAA,CACV,IAAA,CAAM,CAAE,SAAA,CAAW,CAAA,CAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,EAAA,CAAO,CACxD,CAAC,CAAA,CAGDoC,CAAAA,CAAS,MAAM,OAAA,CAAS5B,CAAAA,EAAS,CAC/B,GAAIA,CAAAA,CAAK,OAAS,MAAA,CAAQ,CACxB,IAAM8B,CAAAA,CAAQ9B,CAAAA,CAAK,OAAM,CACzB8B,CAAAA,CAAM,KAAK,MAAA,CAAS;AAAA,IAAA,CAAA,CACpBD,CAAAA,CAAK,MAAA,CAAOC,CAAK,EACnB,CACF,CAAC,CAAA,CAGGD,CAAAA,CAAK,KAAA,EAAO,MAAA,EACdR,CAAAA,CAAO,MAAA,CAAOQ,CAAI,EAEtB,CACF,CAAA,MAASE,CAAAA,CAAO,CACd,MAAA,OAAA,CAAQ,KAAA,CAAM,gCAAA,CAAkCnC,CAAAA,CAAYmC,CAAK,CAAA,CAC3DA,CACR,CAEJ,CAEA,SAAShB,CAAAA,CAAYiB,CAAAA,CAAuBrC,CAAAA,CAAkBC,CAAAA,CAAiB,CAC7E,IAAIiC,CAAAA,CAAOG,CAAAA,CAAO,KAAA,EAAO,IAAA,CACtBhC,CAAAA,EAAuBA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,QAAA,GAAaL,CACpE,CAAA,CAUA,GARKkC,CAAAA,GACHA,CAAAA,CAAOrC,GAAAA,CAAQ,IAAA,CAAK,CAClB,QAAA,CAAAG,CAAAA,CACA,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,EAAA,CAAO,CACxD,CAAC,CAAA,CACDqC,CAAAA,CAAO,MAAA,CAAOH,CAAI,CAAA,CAAA,CAGhB,OAAOjC,CAAAA,EAAe,QAAA,CAAA,CACxB,IAAA,GAAW,CAACqB,EAAMC,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQtB,CAAU,CAAA,CAEnD,GACEqB,CAAAA,CAAK,UAAA,CAAW,GAAG,CAAA,EACnB,OAAOC,CAAAA,EAAU,QAAA,EACjBA,CAAAA,GAAU,IAAA,EACV,OAAO,IAAA,CAAKA,CAAK,CAAA,CAAE,MAAA,GAAW,CAAA,CAC9B,CAEA,IAAMrB,CAAAA,CAAcoB,CAAAA,CAAK,KAAA,CAAM,sBAAsB,CAAA,CACrD,GAAIpB,CAAAA,CAAa,CACf,GAAM,EAAGoC,CAAAA,CAAYC,CAAY,CAAA,CAAIrC,CAAAA,CAC/BwB,CAAAA,CAAS7B,GAAAA,CAAQ,MAAA,CAAO,CAC5B,IAAA,CAAMyC,CAAAA,CACN,MAAA,CAAQC,CAAAA,CACR,IAAA,CAAM,CAAE,SAAA,CAAW,KAAM,MAAA,CAAQ;AAAA,IAAA,CAAS,CAC5C,CAAC,CAAA,CACDL,CAAAA,CAAK,MAAA,CAAOR,CAAM,EACpB,CACF,CAAA,KAAA,GAAW,OAAOH,CAAAA,EAAU,QAAA,CAAU,CACpC,IAAME,CAAAA,CAAO5B,GAAAA,CAAQ,IAAA,CAAK,CACxB,IAAA,CAAAyB,CAAAA,CACA,KAAA,CAAOC,CAAAA,CACP,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,MAAA,CAAQ;AAAA,IAAA,CAAS,CAC5C,CAAC,CAAA,CAGKC,EAAeU,CAAAA,CAAK,KAAA,EAAO,KAC9B7B,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,EAAK,IAAA,GAASiB,CAC1C,EAEAE,CAAAA,CAAeA,CAAAA,CAAa,YAAYC,CAAI,CAAA,CAAIS,CAAAA,CAAK,MAAA,CAAOT,CAAI,EAClE,CAAA,KAAA,GAAW,OAAOF,CAAAA,EAAU,QAAA,CAAU,CAEpC,IAAMiB,CAAAA,CAAiBlB,EAAK,UAAA,CAAW,GAAG,EACtCtB,CAAAA,CAAS,OAAA,CAAQ,WAAY,CAAA,EAAA,EAAKsB,CAAAA,CAAK,UAAU,CAAC,CAAC,CAAA,CAAE,CAAA,CACrDA,EACJF,CAAAA,CAAYiB,CAAAA,CAAQG,EAAgBjB,CAAK,EAC3C,UAEO,OAAOtB,CAAAA,EAAe,QAAA,CAE/B,GAAI,CAGF,IAAMgC,CAAAA,CADSpC,IAAQ,KAAA,CAAM,CAAA,MAAA,EAASI,CAAU,CAAA,CAAA,CAAG,CAAA,CAC3B,KAAA,CAEpBgC,CAAAA,EAAYA,EAAS,KAAA,EAEvBA,CAAAA,CAAS,MAAM,OAAA,CAAS5B,CAAAA,EAAS,CAC/B,GAAIA,CAAAA,CAAK,OAAS,MAAA,CAAQ,CACxB,IAAM8B,CAAAA,CAAQ9B,CAAAA,CAAK,OAAM,CACzB8B,CAAAA,CAAM,KAAK,MAAA,CAAS;AAAA,IAAA,CAAA,CACpBD,GAAM,MAAA,CAAOC,CAAK,EACpB,CACF,CAAC,EAEL,CAAA,MAASC,CAAAA,CAAO,CACd,cAAQ,KAAA,CAAM,6BAAA,CAA+BpC,EAAUC,CAAAA,CAAYmC,CAAK,EAClEA,CACR,CAEJ,CCzeA,eAAsBK,EAAAA,CACpBC,GAAAA,CACAxD,CAAAA,CACA7B,CAAAA,CAQA,CACA,GAAI,CAAC6B,CAAAA,CAAO,aAAA,CAAc,aAAe,CAAC,MAAA,CAAO,IAAA,CAAKwD,GAAAA,EAAW,EAAE,CAAA,CAAE,OACnE,OAGFrF,CAAAA,CAAU,CACR,wBAAA,CAA0B,KAAA,CAC1B,MAAA,CAAQ,KAAA,CACR,gBAAiB,IAAA,CACjB,gBAAA,CAAkB,KAAA,CAClB,SAAA,CAAW,KACX,GAAGA,CACL,CAAA,CACA,IAAM8B,EAAcD,CAAAA,CAAO,aAAA,CAAc,YACnCE,CAAAA,CAAsB5B,UAAAA,CAAK,SAC/B0B,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrBC,CACF,EACMwD,CAAAA,CAAiBjF,CAAAA,CACrB,CAAA,0BAAA,EAA6BE,CAAAA,CAAY,KAAKwB,CAAmB,CAAC,CAAA,CAAA,CAClE,CACE,OAAQ/B,CAAAA,CAAQ,MAClB,CACF,CAAA,CAAE,KAAA,GACIiC,CAAAA,CAAM,MAAM/B,QAAAA,CAAG,QAAA,CAAS4B,EAAa,MAAM,CAAA,CAC7CI,CAAAA,CAAS,MAAMqD,GAAiBtD,CAAAA,CAAKoD,GAAAA,EAAW,EAAC,CAAGxD,EAAQ,CAC9D,wBAAA,CAA0B7B,EAAQ,wBAAA,CAClC,eAAA,CAAiBA,EAAQ,eAAA,CACzB,cAAA,CAAgBA,CAAAA,CAAQ,cAAA,CACxB,iBAAkBA,CAAAA,CAAQ,gBAAA,CAC1B,SAAA,CAAWA,CAAAA,CAAQ,SACrB,CAAC,CAAA,CACD,MAAME,QAAAA,CAAG,UAAU4B,CAAAA,CAAaI,CAAAA,CAAQ,MAAM,CAAA,CAC9CoD,CAAAA,CAAe,UACjB,CAEA,eAAsBC,EAAAA,CACpBnD,EACAiD,CAAAA,CACAxD,CAAAA,CACA7B,EAMI,CACF,wBAAA,CAA0B,MAC1B,eAAA,CAAiB,IAAA,CACjB,cAAA,CAAgB,MAAA,CAChB,iBAAkB,KAAA,CAClB,SAAA,CAAW,IACb,CAAA,CACA,CACAA,EAAU,CACR,wBAAA,CAA0B,KAAA,CAC1B,eAAA,CAAiB,KACjB,cAAA,CAAgB,MAAA,CAChB,gBAAA,CAAkB,KAAA,CAClB,UAAW,IAAA,CACX,GAAGA,CACL,CAAA,CAEA,IAAIqC,GAAAA,CAAU,CAACmD,GAAoBH,CAAO,CAAC,EAM3C,GAJIrF,CAAAA,CAAQ,wBAAA,EACVqC,GAAAA,CAAQ,KAAKoD,EAAAA,EAAgC,CAAA,CAG3CzF,CAAAA,CAAQ,kBAAoB,IAAA,CAAM,CAIpC,GAHAqC,GAAAA,CAAU,EAAC,CAGPR,CAAAA,CAAO,eAAe,GAAA,CAAK,CAC7B,IAAM6D,CAAAA,CAAcC,CAAAA,CAAe9D,CAAAA,CAAO,aAAA,CAAc,GAAG,CAAA,CAEzD,CAAC6D,CAAAA,EAAa,YAAA,GAAe,qBAAqB,CAAA,EAClD,CAACA,CAAAA,EAAa,eAAA,GAAkB,qBAAqB,CAAA,EACrD1F,CAAAA,CAAQ,WAERqC,GAAAA,CAAQ,IAAA,CAAKuD,GAAgB,CAAE,MAAA,CAAQ,gBAAiB,CAAC,CAAC,EAE9D,CAEAvD,GAAAA,CAAQ,IAAA,CAAKwD,GAAiB,CAAE,MAAA,CAAQ,sBAAuB,CAAC,CAAC,CAAA,CAE7D7F,CAAAA,CAAQ,0BACVqC,GAAAA,CAAQ,IAAA,CAAKoD,IAAgC,CAAA,CAG/CpD,GAAAA,CAAQ,IAAA,CACNyD,GAAsBT,CAAAA,CAAS,CAC7B,gBAAA,CAAkBrF,CAAAA,CAAQ,gBAC5B,CAAC,CACH,CAAA,CACAqC,GAAAA,CAAQ,KAAK0D,EAAAA,CAAkBV,CAAO,CAAC,CAAA,CAEnCrF,CAAAA,CAAQ,iBACVqC,GAAAA,CAAQ,IAAA,CAAK2D,EAAAA,CAA2BhG,CAAAA,CAAQ,cAAc,CAAC,CAAA,CAC/DqC,GAAAA,CAAQ,IAAA,CAAK4D,GAAoCjG,CAAAA,CAAQ,cAAc,CAAC,CAAA,CACxEqC,IAAQ,IAAA,CAAK6D,EAAAA,CAAoClG,EAAQ,cAAc,CAAC,GAE5E,CAEI6B,CAAAA,CAAO,QAAA,CAAS,YAAA,EAAgB7B,EAAQ,SAAA,EAC1CqC,GAAAA,CAAQ,IAAA,CACN8D,EAAAA,CAAsB,CAAE,eAAA,CAAiBnG,CAAAA,CAAQ,eAAgB,CAAC,CACpE,CAAA,CAOF,IAAIkC,GAJW,MAAMM,GAAAA,CAAQH,GAAO,CAAA,CAAE,OAAA,CAAQD,CAAAA,CAAO,CACnD,KAAM,MACR,CAAC,GAEmB,GAAA,CAEpB,OAAAF,EAASA,CAAAA,CAAO,OAAA,CAAQ,wBAAA,CAA0B,EAAE,EAEhDlC,CAAAA,CAAQ,eAAA,GAAoB,OAC9BkC,CAAAA,CAASA,CAAAA,CAAO,QAAQ,aAAA,CAAe;;AAAA,CAAM,CAAA,CAAA,CAGxCA,CACT,CAEA,SAASiE,GAAsB,CAC7B,eAAA,CAAAC,CACF,CAAA,CAEG,CACD,OAAO,CACL,aAAA,CAAe,mBAAA,CACf,KAAK3D,CAAAA,CAAY,CACf,IAAM4D,CAAAA,CAAgB,CACpB,CACE,QAAA,CAAU,GAAA,CACV,KAAA,CACED,IAAoB,IAAA,CAChB,+BAAA,CACA,eACR,CAAA,CACA,CAAE,SAAU,MAAA,CAAQ,KAAA,CAAO,+BAAgC,CAC7D,CAAA,CAEIE,CAAAA,CAAY7D,EAAK,KAAA,CAAM,IAAA,CACxBO,GACCA,CAAAA,CAAK,IAAA,GAAS,UACdA,CAAAA,CAAK,IAAA,GAAS,OAAA,EACdA,CAAAA,CAAK,MAAA,GAAW,MAAA,EAChBqD,EAAc,KAAA,CAAM,CAAC,CAAE,QAAA,CAAA1D,CAAAA,CAAU,MAAA4D,CAAM,CAAA,GACrCvD,CAAAA,CAAK,KAAA,EAAO,IAAA,CACT6B,CAAAA,EACCA,EAAK,IAAA,GAAS,MAAA,EACdA,EAAK,QAAA,GAAalC,CAAAA,EAClBkC,EAAK,KAAA,CAAM,IAAA,CACR2B,CAAAA,EACCA,CAAAA,CAAU,IAAA,GAAS,QAAA,EACnBA,EAAU,IAAA,GAAS,OAAA,EACnBA,EAAU,MAAA,GAAWD,CACzB,CACJ,CACF,CACJ,CAAA,CAEKD,CAAAA,GACHA,CAAAA,CAAY9D,GAAAA,CAAQ,OAAO,CACzB,IAAA,CAAM,OAAA,CACN,MAAA,CAAQ,MAAA,CACR,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,CAAK,CACtD,CAAC,CAAA,CACDC,CAAAA,CAAK,MAAA,CAAO6D,CAAS,CAAA,CACrB7D,CAAAA,CAAK,YAAA,CAAa6D,CAAAA,CAAW9D,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,CAAA,CAAA,CAGvE6D,CAAAA,CAAc,OAAA,CAAQ,CAAC,CAAE,QAAA,CAAA1D,CAAAA,CAAU,KAAA,CAAA4D,CAAM,CAAA,GAAM,CACxBD,CAAAA,EAAW,KAAA,EAAO,IAAA,CACpCtD,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,QAAA,GAAaL,CAC9C,CAAA,EAGE2D,CAAAA,EAAW,MAAA,CACT9D,GAAAA,CAAQ,IAAA,CAAK,CACX,QAAA,CAAAG,CAAAA,CACA,KAAA,CAAO,CACLH,GAAAA,CAAQ,MAAA,CAAO,CACb,IAAA,CAAM,OAAA,CACN,MAAA,CAAQ+D,CAAAA,CACR,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,MAAA,CAAQ;AAAA,IAAA,CAAS,CAC5C,CAAC,CACH,CAAA,CACA,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,EAAA,CAAO,CACxD,CAAC,CACH,EAEJ,CAAC,EACH,CACF,CACF,CAEA,SAASf,EAAAA,CACPH,CAAAA,CACA,CACA,OAAO,CACL,aAAA,CAAe,iBAAA,CACf,IAAA,CAAK5C,CAAAA,CAAY,CACf,IAAI6D,CAAAA,CAAY7D,CAAAA,CAAK,KAAA,CAAM,IAAA,CACxBO,CAAAA,EACCA,EAAK,IAAA,GAAS,QAAA,EACdA,CAAAA,CAAK,IAAA,GAAS,OAAA,EACdA,CAAAA,CAAK,SAAW,MACpB,CAAA,CAEMsD,CAAAA,YAAqBG,EAAAA,GACzBH,CAAAA,CAAY9D,GAAAA,CAAQ,OAAO,CACzB,IAAA,CAAM,OAAA,CACN,MAAA,CAAQ,MAAA,CACR,KAAA,CAAO,EAAC,CACR,IAAA,CAAM,CACJ,SAAA,CAAW,IAAA,CACX,MAAA,CAAQ;AAAA,CAAA,CACR,OAAA,CAAS,GACX,CACF,CAAC,CAAA,CACDC,CAAAA,CAAK,MAAA,CAAO6D,CAAS,CAAA,CACrB7D,CAAAA,CAAK,YAAA,CAAa6D,CAAAA,CAAW9D,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,CAAA,CAAA,CAGnE8D,CAAAA,GAAc,MAAA,EAEhB,MAAA,CAAO,OAAA,CAAQjB,CAAO,CAAA,CAAE,OAAA,CAAQ,CAAC,CAACqB,CAAAA,CAAKC,CAAI,CAAA,GAAM,CAC/C,IAAMhE,CAAAA,CAAW+D,CAAAA,GAAQ,OAAA,CAAU,OAAA,CAAU,CAAA,CAAA,EAAIA,CAAG,CAAA,CAAA,CAEpDE,EAAAA,CAAgBN,CAAAA,CAAqB3D,CAAAA,CAAUgE,CAAI,EACrD,CAAC,EAEL,CACF,CACF,CAEA,SAASE,EAAAA,CAAmBpE,CAAAA,CAAmB,CAC7C,IAAMqE,CAAAA,CAAWrE,CAAAA,CAAK,KAAA,CAAM,IAAA,CACzBO,CAAAA,EAAuBA,EAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,QAAA,GAAa,OACpE,CAAA,CAEA,GAAI8D,CAAAA,CAAU,CACZ,IAAMC,CAAAA,CAAgB,CAAC,cAAA,CAAgB,cAAc,CAAA,CAErDD,CAAAA,CAAS,KAAA,CACN,MAAA,CACE9D,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAU+D,CAAAA,CAAc,QAAA,CAAS/D,CAAAA,CAAK,IAAI,CAC5D,CAAA,CACC,OAAA,CAASA,CAAAA,EAASA,CAAAA,CAAK,MAAA,EAAQ,CAAA,CAE9B8D,CAAAA,CAAS,KAAA,CAAM,MAAA,GAAW,CAAA,EAC5BA,CAAAA,CAAS,MAAA,GAEb,CACF,CAEA,SAASrB,EAAAA,EAAiC,CACxC,OAAO,CACL,aAAA,CAAe,6BAAA,CACf,IAAA,CAAKhD,CAAAA,CAAY,CACf,IAAMuE,CAAAA,CAAWvE,CAAAA,CAAK,KAAA,CAAM,IAAA,CACzBO,CAAAA,EAAuBA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,QAAA,GAAa,MACpE,EACIgE,CAAAA,GAEFA,CAAAA,CAAS,KAAA,CACN,IAAA,CACEhE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EACdA,CAAAA,CAAK,IAAA,GAAS,OAAA,EACd,CAAC,4BAAA,CAA8B,mBAAmB,CAAA,CAAE,QAAA,CAClDA,CAAAA,CAAK,KACP,CACJ,CAAA,EACE,MAAA,EAAO,CAGXgE,CAAAA,CAAS,KAAA,CACN,IAAA,CAAMhE,CAAAA,EAEHA,CAAAA,CAAK,IAAA,GAAS,MAAA,EACdA,CAAAA,CAAK,IAAA,GAAS,YAAA,GAEbA,EAAK,KAAA,CAAM,UAAA,CAAW,iBAAiB,CAAA,EACtCA,CAAAA,CAAK,KAAA,GAAU,mBAAA,CAEpB,CAAA,EACC,MAAA,EAAO,CAGXgE,CAAAA,CAAS,KAAA,CACN,IAAA,CACEhE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EACdA,CAAAA,CAAK,IAAA,GAAS,aAAA,EACdA,CAAAA,CAAK,KAAA,GAAU,8BACnB,CAAA,EACE,MAAA,EAAO,CAGPgE,CAAAA,CAAS,KAAA,CAAM,MAAA,GAAW,CAAA,EAC5BA,CAAAA,CAAS,MAAA,IAIbH,EAAAA,CAAmBpE,CAAI,CAAA,CAEvB,IAAMwE,CAAAA,CAAexE,CAAAA,CAAK,KAAA,CAAM,IAAA,CAC7BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EACdA,CAAAA,CAAK,MAAA,GAAW,8BACpB,CAAA,CAEIiE,CAAAA,GACFJ,EAAAA,CAAmBI,CAAY,CAAA,CAC3BA,CAAAA,CAAa,KAAA,CAAM,MAAA,GAAW,CAAA,EAChCA,CAAAA,CAAa,MAAA,EAAO,EAG1B,CACF,CACF,CAEA,SAASL,EAAAA,CACPN,EACA3D,CAAAA,CACAgE,CAAAA,CACA,CACA,IAAIO,CAAAA,CAAWZ,CAAAA,CAAU,KAAA,EAAO,IAAA,CAC7BtD,CAAAA,EAAuBA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,QAAA,GAAaL,CACpE,CAAA,CAEKuE,CAAAA,EACC,MAAA,CAAO,IAAA,CAAKP,CAAI,CAAA,CAAE,MAAA,CAAS,CAAA,GAC7BO,CAAAA,CAAW1E,GAAAA,CAAQ,IAAA,CAAK,CACtB,QAAA,CAAAG,CAAAA,CACA,IAAA,CAAM,CAAE,OAAA,CAAS,IAAK,MAAA,CAAQ;AAAA,EAAA,CAAO,CACvC,CAAC,CAAA,CACD2D,CAAAA,CAAU,OAAOY,CAAQ,CAAA,CAAA,CAI7B,MAAA,CAAO,OAAA,CAAQP,CAAI,CAAA,CAAE,QAAQ,CAAC,CAACD,EAAKxC,CAAK,CAAA,GAAM,CAC7C,IAAMD,CAAAA,CAAO,CAAA,EAAA,EAAKyC,CAAAA,CAAI,OAAA,CAAQ,KAAA,CAAO,EAAE,CAAC,CAAA,CAAA,CAClCS,EAAU3E,GAAAA,CAAQ,IAAA,CAAK,CAC3B,IAAA,CAAAyB,CAAAA,CACA,KAAA,CAAAC,CAAAA,CACA,IAAA,CAAM,CAAE,UAAW,IAAK,CAC1B,CAAC,CAAA,CAEKC,CAAAA,CAAe+C,CAAAA,EAAU,MAAM,IAAA,CAClClE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,OAASiB,CAC1C,CAAA,CAEAE,EAAeA,CAAAA,CAAa,WAAA,CAAYgD,CAAO,CAAA,CAAID,CAAAA,EAAU,MAAA,CAAOC,CAAO,EAC7E,CAAC,EACH,CAEA,SAASrB,EAAAA,CACPT,CAAAA,CACArF,CAAAA,CAGA,CACA,OAAO,CACL,aAAA,CAAe,oBAAA,CACf,IAAA,CAAKyC,CAAAA,CAAY,CACf,OAAO,OAAA,CAAQ4C,CAAO,EAAE,OAAA,CAAQ,CAAC,CAACqB,CAAAA,CAAKC,CAAI,CAAA,GAAM,CAC/C,IAAIhE,CAAAA,CAAW+D,IAAQ,OAAA,CAAU,OAAA,CAAU,CAAA,CAAA,EAAIA,CAAG,CAAA,CAAA,CAElD,GAAIA,IAAQ,OAAA,CAAS,CACnB/D,CAAAA,CAAW,QAAA,CACX,IAAMyE,CAAAA,CAAYC,GAAgB5E,CAAI,CAAA,CACtC,OAAO,OAAA,CAAQkE,CAAI,EAAE,OAAA,CAAQ,CAAC,CAACD,CAAAA,CAAKxC,CAAK,CAAA,GAAM,CAC7C,IAAMD,CAAAA,CAAO,CAAA,EAAA,EAAKyC,CAAAA,CAAI,OAAA,CAAQ,KAAA,CAAO,EAAE,CAAC,CAAA,CAAA,CAClCS,CAAAA,CAAU3E,GAAAA,CAAQ,IAAA,CAAK,CAC3B,KAAAyB,CAAAA,CACA,KAAA,CAAAC,EACA,IAAA,CAAM,CAAE,UAAW,IAAK,CAC1B,CAAC,CAAA,CAEKC,CAAAA,CAAeiD,CAAAA,EAAW,OAAO,IAAA,CACpCpE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,OAASiB,CAC1C,CAAA,CAMIjE,CAAAA,CAAQ,gBAAA,CACNmE,CAAAA,CACFA,CAAAA,CAAa,YAAYgD,CAAO,CAAA,CAEhCC,GAAW,MAAA,CAAOD,CAAO,EAGtBhD,CAAAA,EACHiD,CAAAA,EAAW,MAAA,CAAOD,CAAO,EAG/B,CAAC,EACD,MACF,CAEA,IAAID,CAAAA,CAAWzE,CAAAA,CAAK,KAAA,EAAO,KACxBO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,QAAA,GAAaL,CAC9C,CAAA,CAEI,CAACuE,GAAY,MAAA,CAAO,IAAA,CAAKP,CAAI,CAAA,CAAE,MAAA,CAAS,CAAA,GAC1CO,CAAAA,CAAW1E,GAAAA,CAAQ,IAAA,CAAK,CACtB,QAAA,CAAAG,CAAAA,CACA,KAAA,CAAO,EAAC,CACR,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,CAAK,CACtD,CAAC,CAAA,CACDF,EAAK,MAAA,CAAOyE,CAAQ,EACpBzE,CAAAA,CAAK,YAAA,CAAayE,EAAU1E,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,CAAA,CAAA,CAGtE,OAAO,OAAA,CAAQmE,CAAI,CAAA,CAAE,OAAA,CAAQ,CAAC,CAACD,CAAAA,CAAKxC,CAAK,CAAA,GAAM,CAC7C,IAAID,CAAAA,CAAO,CAAA,EAAA,EAAKyC,EAAI,OAAA,CAAQ,KAAA,CAAO,EAAE,CAAC,CAAA,CAAA,CAGlCzC,IAAS,sBAAA,GACXA,CAAAA,CAAO,aAGLqD,EAAAA,CAAgBpD,CAAK,CAAA,GACvBA,CAAAA,CAAQ,OAAOA,CAAK,CAAA,CAAA,CAAA,CAAA,CAGtB,IAAMiD,CAAAA,CAAU3E,GAAAA,CAAQ,KAAK,CAC3B,IAAA,CAAAyB,EACA,KAAA,CAAAC,CAAAA,CACA,KAAM,CAAE,SAAA,CAAW,IAAK,CAC1B,CAAC,EACKC,CAAAA,CAAe+C,CAAAA,EAAU,KAAA,CAAM,IAAA,CAClClE,GACCA,CAAAA,CAAK,IAAA,GAAS,QAAUA,CAAAA,CAAK,IAAA,GAASiB,CAC1C,CAAA,CAMIjE,CAAAA,CAAQ,iBACNmE,CAAAA,CACFA,CAAAA,CAAa,YAAYgD,CAAO,CAAA,CAEhCD,GAAU,MAAA,CAAOC,CAAO,EAGrBhD,CAAAA,EACH+C,CAAAA,EAAU,MAAA,CAAOC,CAAO,EAG9B,CAAC,EACH,CAAC,EACH,CACF,CACF,CAEA,SAASpB,GAAkBV,CAAAA,CAAoD,CAC7E,OAAO,CACL,aAAA,CAAe,eACf,IAAA,CAAK5C,CAAAA,CAAY,CAEf,IAAM8E,CAAAA,CAAY,KAAA,CAAM,IAAA,CACtB,IAAI,GAAA,CACF,MAAA,CAAO,KAAKlC,CAAO,CAAA,CAAE,QAASqB,CAAAA,EAC5B,MAAA,CAAO,KAAKrB,CAAAA,CAAQqB,CAA2B,GAAK,EAAE,CACxD,CACF,CACF,EAEA,GAAI,CAACa,CAAAA,CAAU,MAAA,CACb,OAGF,IAAMH,CAAAA,CAAYC,GAAgB5E,CAAI,CAAA,CAEhC+E,EAAgBJ,CAAAA,CAAU,KAAA,EAAO,OACpCpE,CAAAA,EACCA,CAAAA,CAAK,OAAS,MAAA,EAAUA,CAAAA,CAAK,KAAK,UAAA,CAAW,IAAI,CACrD,CAAA,CAEA,IAAA,IAAWyE,CAAAA,IAAYF,CAAAA,CAAW,CAChC,IAAMrD,CAAAA,CAAQ,OAAO,MAAA,CAAOmB,CAAO,EAAE,IAAA,CAAMsB,CAAAA,EAASA,EAAKc,CAAQ,CAAC,IAChEA,CACF,CAAA,CAEA,GAAI,CAACvD,CAAAA,CACH,SAGF,GAAIuD,CAAAA,GAAa,QAAA,CAAU,CACzB,IAAMC,CAAAA,CAAkB,CACtB,GAAI,2BAAA,CACJ,EAAA,CAAI,4BACJ,EAAA,CAAI,eAAA,CACJ,GAAI,2BACN,CAAA,CACA,OAAW,CAAChB,CAAAA,CAAKxC,CAAK,CAAA,GAAK,MAAA,CAAO,QAAQwD,CAAe,CAAA,CAAG,CAC1D,IAAMC,EAAanF,GAAAA,CAAQ,IAAA,CAAK,CAC9B,IAAA,CAAM,CAAA,SAAA,EAAYkE,CAAG,CAAA,CAAA,CACrB,KAAA,CAAAxC,EACA,IAAA,CAAM,CAAE,UAAW,IAAK,CAC1B,CAAC,CAAA,CAECkD,CAAAA,EAAW,OAAO,IAAA,CACfpE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAUA,CAAAA,CAAK,IAAA,GAAS2E,EAAW,IACrD,CAAA,EAIFP,GAAW,MAAA,CAAOO,CAAU,EAC9B,CACA,QACF,CAEA,IAAI1D,CAAAA,CACFqD,GAAgBpD,CAAK,CAAA,EAAK0D,GAAa1D,CAAK,CAAA,CACxC,CAAA,QAAA,EAAWuD,CAAAA,CAAS,QAAQ,KAAA,CAAO,EAAE,CAAC,CAAA,CAAA,CACtC,CAAA,EAAA,EAAKA,EAAS,OAAA,CAAQ,KAAA,CAAO,EAAE,CAAC,CAAA,CAAA,CAClCxD,IAAS,4BAAA,GACXA,CAAAA,CAAO,mBAGT,IAAI4D,CAAAA,CAAY,SAASJ,CAAQ,CAAA,CAAA,CAAA,CAC7BxD,CAAAA,GAAS,iBAAA,GACX4D,EAAY,gBAAA,CAAA,CAGd,IAAMF,EAAanF,GAAAA,CAAQ,IAAA,CAAK,CAC9B,IAAA,CAAAyB,CAAAA,CACA,MAAO4D,CAAAA,CACP,IAAA,CAAM,CAAE,SAAA,CAAW,IAAK,CAC1B,CAAC,CAAA,CACoBT,GAAW,KAAA,EAAO,IAAA,CACpCpE,CAAAA,EACCA,CAAAA,CAAK,OAAS,MAAA,EAAUA,CAAAA,CAAK,OAAS2E,CAAAA,CAAW,IACrD,IAEMH,CAAAA,EAAe,MAAA,CACjBJ,GAAW,WAAA,CACTI,CAAAA,CAAcA,EAAc,MAAA,CAAS,CAAC,EACtCG,CACF,CAAA,CAEAP,GAAW,MAAA,CAAOO,CAAU,CAAA,EAGlC,CACF,CACF,CACF,CAEA,SAASN,EAAAA,CAAgB5E,CAAAA,CAAoB,CAC3C,IAAI2E,CAAAA,CAAY3E,EAAK,KAAA,CAAM,IAAA,CACxBO,GACCA,CAAAA,CAAK,IAAA,GAAS,UACdA,CAAAA,CAAK,IAAA,GAAS,SACdA,CAAAA,CAAK,MAAA,GAAW,QACpB,CAAA,CAEA,OAAKoE,CAAAA,GACHA,CAAAA,CAAY5E,IAAQ,MAAA,CAAO,CACzB,KAAM,OAAA,CACN,MAAA,CAAQ,SACR,KAAA,CAAO,GACP,IAAA,CAAM,CAAE,UAAW,IAAA,CAAM,OAAA,CAAS,IAAK,MAAA,CAAQ;AAAA,CAAK,CACtD,CAAC,CAAA,CACDC,CAAAA,CAAK,OAAO2E,CAAS,CAAA,CACrB3E,CAAAA,CAAK,YAAA,CAAa2E,EAAW5E,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,CAAA,CAAA,CAGhE4E,CACT,CAEA,SAASvB,EAAAA,CAAiB,CAAE,OAAA9C,CAAO,CAAA,CAAuB,CACxD,OAAO,CACL,aAAA,CAAe,oBAAA,CACf,IAAA,CAAKN,CAAAA,CAAY,CAMf,GAAI,CALkBA,CAAAA,CAAK,KAAA,CAAM,IAAA,CAC9BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,UAAYA,CAAAA,CAAK,IAAA,GAAS,gBAC5C,CAAA,CAEoB,CAElB,IAAME,CAAAA,CAAcT,CAAAA,CAAK,KAAA,CAAM,OAC5BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,EAAK,IAAA,GAAS,QAC5C,CAAA,CAEM8E,CAAAA,CAActF,IAAQ,MAAA,CAAO,CACjC,IAAA,CAAM,gBAAA,CACN,OAAAO,CAAAA,CACA,IAAA,CAAM,CAAE,SAAA,CAAW,KAAM,MAAA,CAAQ;AAAA,CAAK,CACxC,CAAC,CAAA,CAED,GAAIG,CAAAA,CAAY,OAAS,CAAA,CAAG,CAE1B,IAAMC,CAAAA,CAAaD,CAAAA,CAAYA,CAAAA,CAAY,OAAS,CAAC,CAAA,CACrDT,CAAAA,CAAK,WAAA,CAAYU,CAAAA,CAAY2E,CAAW,EAC1C,CAAA,KAEErF,CAAAA,CAAK,WAAA,CAAYA,CAAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAAGqF,CAAW,CAAA,CAG7CrF,CAAAA,CAAK,YAAA,CAAaqF,CAAAA,CAAatF,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,EACzE,CACF,CACF,CACF,CAEA,SAASoD,EAAAA,CAAgB,CAAE,MAAA,CAAA7C,CAAO,CAAA,CAAuB,CACvD,OAAO,CACL,aAAA,CAAe,mBAAA,CACf,IAAA,CAAKN,EAAY,CACf,IAAMS,CAAAA,CAAcT,CAAAA,CAAK,KAAA,CAAM,MAAA,CAC5BO,GACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,CAAAA,CAAK,IAAA,GAAS,QAC5C,EAGM+E,CAAAA,CAAoBtF,CAAAA,CAAK,KAAA,CAAM,IAAA,CAClCO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,CAAAA,CAAK,IAAA,GAAS,gBAC5C,CAAA,CAOA,GAAI,CAJcE,EAAY,IAAA,CAC3BF,CAAAA,EAASA,CAAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,OAAA,CAAS,EAAE,CAAA,GAAMD,CACjD,CAAA,CAEgB,CACd,IAAMiF,CAAAA,CAAaxF,IAAQ,MAAA,CAAO,CAChC,IAAA,CAAM,QAAA,CACN,MAAA,CAAQ,CAAA,CAAA,EAAIO,CAAM,CAAA,CAAA,CAAA,CAClB,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,MAAA,CAAQ;AAAA,CAAK,CACxC,CAAC,CAAA,CAED,GAAIG,EAAY,MAAA,CAAS,CAAA,CAAG,CAE1B,IAAMC,CAAAA,CAAaD,CAAAA,CAAYA,CAAAA,CAAY,MAAA,CAAS,CAAC,CAAA,CACrDT,CAAAA,CAAK,WAAA,CAAYU,CAAAA,CAAY6E,CAAU,EACzC,CAAA,KAAWD,CAAAA,EAETtF,EAAK,YAAA,CAAasF,CAAAA,CAAmBC,CAAU,CAAA,CAC/CvF,EAAK,YAAA,CACHsF,CAAAA,CACAvF,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CACzC,CAAA,GAGAC,CAAAA,CAAK,OAAA,CAAQuF,CAAU,EACvBvF,CAAAA,CAAK,WAAA,CAAYuF,CAAAA,CAAYxF,GAAAA,CAAQ,QAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,CAAA,EAEzE,CACF,CACF,CACF,CAEA,SAASwD,EAAAA,CACPiC,CAAAA,CACA,CACA,OAAO,CACL,aAAA,CAAe,wBAAA,CACf,KAAKxF,CAAAA,CAAY,CACf,GAAI,CAACwF,GAAgB,OAAA,CACnB,OAIF,IAAMC,CAAAA,CADYC,EAAAA,CAAa1F,CAAI,CAAA,GACP,QAAA,CAAW,IAAM,GAAA,CAEvCe,CAAAA,CAAcf,CAAAA,CAAK,KAAA,CAAM,OAC5BO,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,EAAK,IAAA,GAAS,QAC5C,CAAA,CAEMoF,CAAAA,CACJ5E,CAAAA,CAAYA,CAAAA,CAAY,MAAA,CAAS,CAAC,GAAKf,CAAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAErD,QAAW4F,CAAAA,IAAUJ,CAAAA,CAAe,OAAA,CAAS,CAC3C,IAAMK,CAAAA,CAAaD,CAAAA,CAAO,OAAA,CAAQ,yBAAA,CAA2B,EAAE,CAAA,CAG/D,GACE7E,CAAAA,CAAY,KAAMR,CAAAA,EACTA,CAAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,QAAS,EAAE,CAAA,GAAMsF,CAC7C,CAAA,CAED,SAGF,IAAMC,CAAAA,CAAa/F,GAAAA,CAAQ,MAAA,CAAO,CAChC,IAAA,CAAM,QAAA,CACN,MAAA,CAAQ,GAAG0F,CAAK,CAAA,EAAGI,CAAU,CAAA,EAAGJ,CAAK,CAAA,CAAA,CACrC,IAAA,CAAM,CAAE,SAAA,CAAW,KAAM,MAAA,CAAQ;AAAA,CAAK,CACxC,CAAC,CAAA,CACDzF,CAAAA,CAAK,WAAA,CAAY2F,CAAAA,CAAgBG,CAAU,CAAA,CAC3C9F,CAAAA,CAAK,YAAA,CAAa8F,CAAAA,CAAY/F,GAAAA,CAAQ,QAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CAAC,EACxE,CACF,CACF,CACF,CAEA,SAAS0D,EAAAA,CACP+B,CAAAA,CACA,CACA,OAAO,CACL,cAAe,kCAAA,CACf,IAAA,CAAKxF,CAAAA,CAAY,CACf,GAAI,CAACwF,CAAAA,EAAgB,KAAA,EAAO,MAAA,EAAQ,SAAA,CAClC,OAGF,IAAMb,CAAAA,CAAYC,EAAAA,CAAgB5E,CAAI,CAAA,CAChC+F,EAAwBpB,CAAAA,CAAU,KAAA,EAAO,MAAA,CAC5CpE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EAAYA,CAAAA,CAAK,OAAS,WAC5C,CAAA,CAEMyF,CAAAA,CAAsBC,GAAAA,CAAE,MAAA,CAC5BA,GAAAA,CAAE,MAAA,EAAO,CACTA,IAAE,MAAA,CAAOA,GAAAA,CAAE,MAAA,EAAO,CAAGA,GAAAA,CAAE,MAAA,EAAQ,CACjC,CAAA,CAEA,IAAA,GAAW,CAACC,CAAAA,CAAcC,CAAa,CAAA,GAAK,MAAA,CAAO,OAAA,CACjDX,EAAe,KAAA,CAAM,MAAA,CAAO,SAC9B,CAAA,CAAG,CACD,GAAI,OAAOU,CAAAA,EAAiB,QAAA,CAC1B,SAGF,IAAME,CAAAA,CAAsBJ,CAAAA,CAAoB,SAAA,CAAUG,CAAa,CAAA,CAMvE,GAJI,CAACC,CAAAA,CAAoB,OAAA,EAKvBL,CAAAA,EAAuB,IAAA,CACpBxF,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,QAAA,EACdA,CAAAA,CAAK,IAAA,GAAS,WAAA,EACdA,CAAAA,CAAK,MAAA,GAAW2F,CACpB,CAAA,CAEA,SAGF,IAAMG,CAAAA,CAAetG,GAAAA,CAAQ,MAAA,CAAO,CAClC,IAAA,CAAM,WAAA,CACN,MAAA,CAAQmG,EACR,KAAA,CAAO,EAAC,CACR,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,IAAK,MAAA,CAAQ;AAAA,EAAA,CAAO,CACxD,CAAC,CAAA,CAED,OAAW,CAACjC,CAAAA,CAAKqC,CAAM,CAAA,GAAK,MAAA,CAAO,QAAQF,CAAAA,CAAoB,IAAI,EAAG,CACpE,IAAMhE,EAAOrC,GAAAA,CAAQ,IAAA,CAAK,CACxB,QAAA,CAAUkE,CAAAA,CACV,MAAO,MAAA,CAAO,OAAA,CAAQqC,CAAM,CAAA,CAAE,GAAA,CAAI,CAAC,CAACrC,CAAAA,CAAKxC,CAAK,CAAA,GAC5C1B,GAAAA,CAAQ,KAAK,CACX,IAAA,CAAMkE,EACN,KAAA,CAAAxC,CAAAA,CACA,KAAM,CAAE,SAAA,CAAW,KAAM,MAAA,CAAQ;AAAA,MAAA,CAAA,CAAY,OAAA,CAAS,IAAK,CAC7D,CAAC,CACH,CAAA,CACA,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,GAAA,CAAK,MAAA,CAAQ;AAAA,IAAA,CAAS,CAC1D,CAAC,CAAA,CACD4E,CAAAA,CAAa,MAAA,CAAOjE,CAAI,EAC1B,CAEAuC,CAAAA,CAAU,MAAA,CAAO0B,CAAY,CAAA,CAC7B1B,CAAAA,CAAU,YAAA,CACR0B,CAAAA,CACAtG,GAAAA,CAAQ,OAAA,CAAQ,CAAE,IAAA,CAAM,aAAc,CAAC,CACzC,EACF,CACF,CACF,CACF,CAEA,SAASyD,EAAAA,CACPgC,CAAAA,CACA,CACA,OAAO,CACL,aAAA,CAAe,kCAAA,CACf,IAAA,CAAKxF,CAAAA,CAAY,CACf,GAAI,CAACwF,CAAAA,EAAgB,KAAA,EAAO,MAAA,EAAQ,SAAA,CAClC,OAGF,IAAMb,CAAAA,CAAYC,EAAAA,CAAgB5E,CAAI,CAAA,CAChCuG,CAAAA,CAAyB5B,CAAAA,CAAU,KAAA,EAAO,MAAA,CAC7CpE,CAAAA,EACCA,CAAAA,CAAK,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAK,IAAA,CAAK,UAAA,CAAW,YAAY,CAC7D,CAAA,CAEMiG,CAAAA,CAAuBP,GAAAA,CAC1B,MAAA,CAAOA,GAAAA,CAAE,MAAA,EAAO,CAAGA,GAAAA,CAAE,MAAA,EAAQ,CAAA,CAC7B,SAAA,CAAUT,CAAAA,CAAe,KAAA,CAAM,MAAA,CAAO,SAAS,CAAA,CAClD,GAAKgB,CAAAA,CAAqB,OAAA,CAI1B,IAAA,GAAW,CAACvC,CAAAA,CAAKxC,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQ+E,CAAAA,CAAqB,IAAI,CAAA,CAAG,CACpE,IAAMhF,CAAAA,CAAO,CAAA,UAAA,EAAayC,CAAG,CAAA,CAAA,CAC7B,GACEsC,CAAAA,EAAwB,IAAA,CACrBhG,CAAAA,EAAsCA,CAAAA,CAAK,IAAA,GAASiB,CACvD,CAAA,CAEA,SAGF,IAAMiF,CAAAA,CAAgB1G,GAAAA,CAAQ,IAAA,CAAK,CACjC,IAAA,CAAAyB,CAAAA,CACA,KAAA,CAAAC,CAAAA,CACA,IAAA,CAAM,CAAE,SAAA,CAAW,IAAA,CAAM,OAAA,CAAS,IAAA,CAAM,MAAA,CAAQ;AAAA,EAAA,CAAO,CACzD,CAAC,CAAA,CACDkD,EAAU,MAAA,CAAO8B,CAAa,EAChC,CACF,CACF,CACF,CAEA,SAASf,GAAa1F,CAAAA,CAAiC,CAIrD,OAHkBA,CAAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CACR,QAAA,EAAS,CAEvB,QAAA,CAAS,GAAG,CAAA,CACX,QAAA,CAEF,QACT,CAEO,SAAS6E,GAAgBpD,CAAAA,CAAe,CAC7C,GACEA,CAAAA,CAAM,UAAA,CAAW,KAAK,CAAA,EACtBA,CAAAA,CAAM,WAAW,KAAK,CAAA,EACtBA,EAAM,UAAA,CAAW,GAAG,CAAA,EACpBA,CAAAA,CAAM,WAAW,OAAO,CAAA,CAExB,OAAO,MAAA,CAGT,IAAMiF,EAASjF,CAAAA,CAAM,KAAA,CAAM,GAAG,CAAA,CAE9B,OACEiF,EAAO,MAAA,GAAW,CAAA,EAClBA,EAAO,KAAA,CAAM,CAAA,CAAG,CAAC,CAAA,CAAE,KAAA,CAAOC,CAAAA,EAAUA,CAAAA,CAAM,SAAS,GAAG,CAAC,CAE3D,CAEO,SAASxB,GAAa1D,CAAAA,CAAe,CAC1C,OACEA,CAAAA,CAAM,UAAA,CAAW,KAAK,CAAA,EACtBA,CAAAA,CAAM,WAAW,KAAK,CAAA,EACtBA,EAAM,UAAA,CAAW,GAAG,CAAA,EACpBA,CAAAA,CAAM,WAAW,OAAO,CAAA,EACxBA,EAAM,UAAA,CAAW,cAAc,CAEnC,CC73BA,eAAsBmF,CAAAA,CACpBC,GAAAA,CACAC,EACA1H,CAAAA,CACA7B,CAAAA,CAGA,CAIA,GAHAsJ,IAAe,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIA,GAAY,CAAC,CAAA,CAC/CC,CAAAA,CAAkB,MAAM,IAAA,CAAK,IAAI,IAAIA,CAAe,CAAC,EAEjD,CAACD,GAAAA,EAAc,QAAU,CAACC,CAAAA,EAAiB,MAAA,CAC7C,OAGFvJ,EAAU,CACR,MAAA,CAAQ,MACR,GAAGA,CACL,EAEA,IAAMwJ,CAAAA,CAAsBnJ,EAAQ,0BAAA,CAA4B,CAC9D,OAAQL,CAAAA,CAAQ,MAClB,CAAC,CAAA,EAAG,KAAA,GACEyJ,CAAAA,CAAiB,MAAMC,EAAAA,CAAoC7H,CAAM,EAGnE8H,CAAAA,CAAO,EAAA,CACX,GAAIC,EAAAA,CAAuB/H,CAAM,GAAK4H,CAAAA,GAAmB,KAAA,CACvD,GAAIzJ,CAAAA,CAAQ,MAAA,CACV2J,EAAO,OAAA,CAAA,KACF,CACLH,EAAoB,cAAA,EAAe,CACnClJ,EAAO,IAAA,CACL;AAAA;AAAA;AAAA,CACF,CAAA,CACA,IAAMuJ,GAAAA,CAAe,MAAMC,GAAQ,CACjC,CACE,IAAA,CAAM,QAAA,CACN,IAAA,CAAM,MAAA,CACN,QAAS,gCAAA,CACT,OAAA,CAAS,CACP,CAAE,KAAA,CAAO,cAAe,KAAA,CAAO,OAAQ,CAAA,CACvC,CAAE,KAAA,CAAO,wBAAA,CAA0B,MAAO,kBAAmB,CAC/D,CACF,CACF,CAAC,EAEGD,GAAAA,GACFF,CAAAA,CAAOE,GAAAA,CAAa,IAAA,EAExB,CAGFL,CAAAA,EAAqB,OAAM,CAE3B,MAAMO,EAAAA,CACJN,CAAAA,CACAH,GAAAA,CACAC,CAAAA,CACA1H,EAAO,aAAA,CAAc,GAAA,CACrB8H,CACF,CAAA,CAEAH,CAAAA,EAAqB,OAAA,GACvB,CAEA,SAASI,GAAuB/H,CAAAA,CAAgB,CAC9C,IAAM6D,CAAAA,CAAcC,CAAAA,CAAe9D,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK,KAAK,EAElE,GAAI,CAAC6D,CAAAA,EAAa,YAAA,EAAc,KAAA,CAC9B,OAAO,OAGT,IAAMsE,CAAAA,CAAa,iCAAA,CAAkC,IAAA,CACnDtE,CAAAA,CAAY,YAAA,CAAa,KAC3B,CAAA,CACMuE,CAAAA,CACJvE,EAAY,YAAA,CAAa,kBAAkB,GAAG,UAAA,CAAW,GAAG,CAAA,CAE9D,OAAOsE,CAAAA,EAAcC,CACvB,CAEA,eAAeP,EAAAA,CAAoC7H,CAAAA,CAAgB,CAIjE,OAHoB8D,CAAAA,CAAe9D,EAAO,aAAA,CAAc,GAAA,CAAK,KAAK,CAAA,EAC9D,YAAA,EAAc,IAAA,CAKT,OAGFqI,GAAAA,CAAkBrI,CAAAA,CAAO,cAAc,GAAG,CACnD,CAEA,eAAekI,EAAAA,CACbN,CAAAA,CAGAH,CAAAA,CACAC,CAAAA,CACAvI,CAAAA,CACA2I,EACA,CACA,GAAIF,IAAmB,KAAA,CACrB,OAAOU,GAAeb,CAAAA,CAAcC,CAAAA,CAAiBvI,CAAAA,CAAK2I,CAAI,CAAA,CAGhE,GAAIF,IAAmB,MAAA,CACrB,OAAOW,GAAgBd,CAAAA,CAAcC,CAAAA,CAAiBvI,CAAG,CAAA,CAG3D,GAAIyI,CAAAA,GAAmB,MAAA,CACrB,OAAOY,EAAAA,CAAgBf,EAAcC,CAAAA,CAAiBvI,CAAG,CAAA,CAGvDsI,CAAAA,EAAc,MAAA,EAChB,MAAMgB,MAAMb,CAAAA,CAAgB,CAAC,KAAA,CAAO,GAAGH,CAAY,CAAA,CAAG,CACpD,GAAA,CAAAtI,CACF,CAAC,CAAA,CAGCuI,CAAAA,EAAiB,QACnB,MAAMe,KAAAA,CAAMb,CAAAA,CAAgB,CAAC,KAAA,CAAO,IAAA,CAAM,GAAGF,CAAe,CAAA,CAAG,CAAE,GAAA,CAAAvI,CAAI,CAAC,EAE1E,CAEA,eAAemJ,EAAAA,CACbb,CAAAA,CACAC,CAAAA,CACAvI,CAAAA,CACA2I,EACA,CACIL,CAAAA,CAAa,QACf,MAAMgB,KAAAA,CACJ,MACA,CAAC,SAAA,CAAW,GAAIX,CAAAA,CAAO,CAAC,CAAA,EAAA,EAAKA,CAAI,CAAA,CAAE,CAAA,CAAI,EAAC,CAAI,GAAGL,CAAY,EAC3D,CAAE,GAAA,CAAAtI,CAAI,CACR,CAAA,CAGEuI,CAAAA,CAAgB,QAClB,MAAMe,KAAAA,CACJ,MACA,CAAC,SAAA,CAAW,GAAIX,CAAAA,CAAO,CAAC,CAAA,EAAA,EAAKA,CAAI,CAAA,CAAE,CAAA,CAAI,EAAC,CAAI,IAAA,CAAM,GAAGJ,CAAe,CAAA,CACpE,CAAE,GAAA,CAAAvI,CAAI,CACR,EAEJ,CAEA,eAAeoJ,GACbd,CAAAA,CACAC,CAAAA,CACAvI,EACA,CACIsI,CAAAA,EAAc,QAChB,MAAMgB,KAAAA,CAAM,MAAA,CAAQ,CAAC,KAAA,CAAO,GAAGhB,EAAa,GAAA,CAAKiB,CAAAA,EAAQ,CAAA,IAAA,EAAOA,CAAG,CAAA,CAAE,CAAC,EAAG,CACvE,GAAA,CAAAvJ,CACF,CAAC,CAAA,CAGCuI,CAAAA,EAAiB,QACnB,MAAMe,KAAAA,CACJ,OACA,CAAC,KAAA,CAAO,KAAM,GAAGf,CAAAA,CAAgB,GAAA,CAAKgB,CAAAA,EAAQ,CAAA,IAAA,EAAOA,CAAG,EAAE,CAAC,CAAA,CAC3D,CAAE,GAAA,CAAAvJ,CAAI,CACR,EAEJ,CAEA,eAAeqJ,EAAAA,CACbf,CAAAA,CACAC,CAAAA,CACAvI,CAAAA,CACA,CACIsI,CAAAA,CAAa,MAAA,EACf,MAAMgB,KAAAA,CAAM,KAAA,CAAO,CAAC,MAAA,CAAQ,SAAA,CAAW,GAAGhB,CAAY,CAAA,CAAG,CAAE,IAAAtI,CAAI,CAAC,CAAA,CAG9DuI,CAAAA,CAAgB,MAAA,EAClB,MAAMe,MAAM,KAAA,CAAO,CAAC,MAAA,CAAQ,SAAA,CAAW,OAAA,CAAS,GAAGf,CAAe,CAAA,CAAG,CACnE,IAAAvI,CACF,CAAC,EAEL,CCnLA,eAAsBwJ,EAAAA,CACpBC,IACA5I,CAAAA,CACA7B,CAAAA,CAGA,CACA,GAAI,CAACyK,GAAAA,EAAW,OAAO,IAAA,CAAKA,GAAO,EAAE,MAAA,GAAW,CAAA,CAC9C,OAAO,CACL,YAAA,CAAc,EAAC,CACf,cAAA,CAAgB,IAAA,CAChB,eAAgB,IAClB,CAAA,CAGFzK,CAAAA,CAAU,CACR,MAAA,CAAQ,KAAA,CACR,GAAGA,CACL,CAAA,CAEA,IAAM0K,CAAAA,CAAarK,CAAAA,CAAQ,+BAAA,CAAiC,CAC1D,MAAA,CAAQL,CAAAA,CAAQ,MAClB,CAAC,CAAA,EAAG,OAAM,CAEJ2K,CAAAA,CAAc9I,CAAAA,CAAO,aAAA,CAAc,GAAA,CAGrC+I,CAAAA,CAAczK,WAAK,IAAA,CAAKwK,CAAAA,CAAa,YAAY,CAAA,CAC/CE,CAAAA,CAAkBC,CAAAA,CAAoBH,CAAW,CAAA,CAEnDE,CAAAA,GACFD,CAAAA,CAAcC,CAAAA,CAAAA,CAGhB,IAAME,GAAAA,CAAgBC,WAAWJ,CAAW,CAAA,CACtCK,EAAc9K,UAAAA,CAAK,QAAA,CAASyK,CAAW,CAAA,CAGvCM,CAAAA,CAAgB,MAAA,CAAO,OAAA,CAAQT,GAAO,CAAA,CACzC,IAAI,CAAC,CAAC/D,CAAAA,CAAKxC,CAAK,CAAA,GAAM,CAAA,EAAGwC,CAAG,CAAA,CAAA,EAAIxC,CAAK,CAAA,CAAE,CAAA,CACvC,IAAA,CAAK;AAAA,CAAI,EAERiH,CAAAA,CAAyB,EAAC,CAC1BC,CAAAA,CAAgC,KAChCC,CAAAA,CAAgC,IAAA,CAEpC,GAAIN,GAAAA,CAAe,CACjB,IAAMO,CAAAA,CAAkB,MAAMpL,QAAAA,CAAG,QAAA,CAAS0K,EAAa,OAAO,CAAA,CACxDW,CAAAA,CAAgBC,CAAAA,CAAgBF,EAAiBJ,CAAa,CAAA,CAGpE,GAFAC,CAAAA,CAAeM,EAAcH,CAAAA,CAAiBJ,CAAa,CAAA,CAEvDC,CAAAA,CAAa,OAAS,CAAA,CAAA,CAQxB,GAPA,MAAMjL,QAAAA,CAAG,SAAA,CAAU0K,EAAaW,CAAAA,CAAe,OAAO,CAAA,CACtDH,CAAAA,CAAiBjL,WAAK,QAAA,CAASwK,CAAAA,CAAaC,CAAW,CAAA,CAEvDF,GAAY,OAAA,CACV,CAAA,iCAAA,EAAoCnK,CAAAA,CAAY,IAAA,CAAK0K,CAAW,CAAC,CAAA,CAAA,CACnE,EAEI,CAACjL,CAAAA,CAAQ,OACX,IAAA,IAAW0G,CAAAA,IAAOyE,CAAAA,CAChB7K,CAAAA,CAAO,IAAI,CAAA,EAAA,EAAKC,CAAAA,CAAY,QAAQ,GAAG,CAAC,IAAImG,CAAG,CAAA,CAAE,EAAA,CAAA,KAIrDgE,CAAAA,EAAY,OAEhB,CAAA,KAAA,GAEE,MAAMxK,QAAAA,CAAG,SAAA,CAAU0K,EAAaM,CAAAA,CAAgB;AAAA,CAAA,CAAM,OAAO,CAAA,CAC7DG,CAAAA,CAAiBlL,UAAAA,CAAK,QAAA,CAASwK,CAAAA,CAAaC,CAAW,CAAA,CACvDO,CAAAA,CAAe,MAAA,CAAO,IAAA,CAAKV,GAAO,CAAA,CAElCC,CAAAA,EAAY,OAAA,CACV,CAAA,iCAAA,EAAoCnK,CAAAA,CAAY,IAAA,CAAK0K,CAAW,CAAC,CAAA,CAAA,CACnE,CAAA,CAEI,CAACjL,CAAAA,CAAQ,MAAA,CACX,IAAA,IAAW0G,CAAAA,IAAOyE,CAAAA,CAChB7K,CAAAA,CAAO,GAAA,CAAI,CAAA,EAAA,EAAKC,CAAAA,CAAY,OAAA,CAAQ,GAAG,CAAC,CAAA,CAAA,EAAImG,CAAG,CAAA,CAAE,CAAA,CAKvD,OAAI,CAAC1G,CAAAA,CAAQ,MAAA,EAAUmL,CAAAA,CAAa,MAAA,CAAS,CAAA,EAC3C7K,CAAAA,CAAO,KAAA,EAAM,CAGR,CACL,YAAA,CAAA6K,CAAAA,CACA,cAAA,CAAAC,CAAAA,CACA,cAAA,CAAAC,CACF,CACF,CC9EA,eAAsBK,EAAAA,CACpBC,CAAAA,CACA9J,CAAAA,CACA7B,CAAAA,CAQA,CACAA,CAAAA,CAAU,CACR,SAAA,CAAW,KAAA,CACX,MAAA,CAAQ,KAAA,CACR,YAAA,CAAc,KAAA,CACd,SAAA,CAAW,IAAA,CACX,GAAGA,CACL,CAAA,CAEA,IAAM4L,CAAAA,CAAkB,MAAMC,CAAAA,CAAmBhK,CAAM,CAAA,CACvD,OACE+J,CAAAA,EACAA,CAAAA,CAAgB,EAAA,EAChBA,CAAAA,CAAgB,EAAA,CAAG,aAAA,CAAc,GAAA,GAAQ/J,CAAAA,CAAO,aAAA,CAAc,GAAA,CAEvD,MAAMiK,EAAAA,CAAuBH,CAAAA,CAAY9J,CAAAA,CAAQ+J,CAAAA,CAAiB,CACvE,GAAG5L,CAAAA,CACH,QAAA,CACE2L,CAAAA,EAAY,MAAA,GAAW,CAAA,EAAK,CAAC,CAACA,CAAAA,CAAW,CAAC,CAAA,CAAE,KAAA,CAAM,aAAa,CACnE,CAAC,CAAA,CAGI,MAAMI,EAAAA,CAAqBJ,CAAAA,CAAY9J,CAAAA,CAAQ7B,CAAO,CAC/D,CAEA,eAAe+L,EAAAA,CACbJ,GAAAA,CACA9J,CAAAA,CACA7B,GAAAA,CAOA,CACA,GAAI,CAACA,GAAAA,CAAQ,SAAA,EAAa,CAAC2L,GAAAA,CAAW,MAAA,CACpC,OAGF,IAAMK,CAAAA,CAAkB3L,CAAAA,CAAQ,oBAAA,CAAsB,CACpD,MAAA,CAAQL,GAAAA,CAAQ,MAClB,CAAC,CAAA,EAAG,KAAA,EAAM,CACJiM,CAAAA,CAAO,MAAMC,GAAAA,CAAoBP,GAAAA,CAAYQ,CAAAA,CAAmBtK,CAAM,CAAC,CAAA,CAE7E,GAAI,CAACoK,CAAAA,CACH,OAAAD,CAAAA,EAAiB,IAAA,EAAK,CACfI,CAAAA,CAAY,IAAI,KAAA,CAAM,2CAA2C,CAAC,CAAA,CAG3E,GAAI,CACFC,EAAAA,CAAoBJ,CAAAA,CAAK,KAAA,EAAS,EAAC,CAAGpK,CAAAA,CAAO,aAAA,CAAc,GAAG,EAChE,CAAA,MAASkD,CAAAA,CAAO,CACd,OAAAiH,CAAAA,EAAiB,MAAK,CACfI,CAAAA,CAAYrH,CAAK,CAC1B,CAEAiH,CAAAA,EAAiB,OAAA,EAAQ,CAEzB,IAAM5F,CAAAA,CAAkB,MAAMkG,CAAAA,CAAoCzK,CAAM,CAAA,CAExE,MAAM0K,CAAAA,CAAqBN,CAAAA,CAAK,QAAA,EAAU,MAAA,CAAQpK,CAAAA,CAAQ,CACxD,MAAA,CAAQ7B,GAAAA,CAAQ,MAAA,CAChB,eAAA,CAAAoG,CACF,CAAC,CAAA,CAED,IAAMoG,CAAAA,CAAmB,MAAMC,EAAAA,CAAuBd,IAAY9J,CAAM,CAAA,CACxE,MAAMuD,EAAAA,CAAc6G,CAAAA,CAAK,OAAA,CAASpK,CAAAA,CAAQ,CACxC,wBAAA,CAA0B7B,GAAAA,CAAQ,YAAA,CAClC,MAAA,CAAQA,GAAAA,CAAQ,MAAA,CAChB,eAAA,CAAAoG,CAAAA,CACA,cAAA,CAAgB6F,CAAAA,CAAK,QAAA,EAAU,MAAA,CAC/B,gBAAA,CAAAO,CAAAA,CACA,SAAA,CAAWxM,GAAAA,CAAQ,SACrB,CAAC,CAAA,CAGD,MAAM2B,EAAAA,CAAUsK,CAAAA,CAAK,GAAA,CAAKpK,CAAAA,CAAQ,CAChC,MAAA,CAAQ7B,GAAAA,CAAQ,MAClB,CAAC,CAAA,CAED,MAAMwK,EAAAA,CAAcyB,CAAAA,CAAK,OAAA,CAASpK,CAAAA,CAAQ,CACxC,MAAA,CAAQ7B,GAAAA,CAAQ,MAClB,CAAC,CAAA,CAED,MAAMqJ,CAAAA,CAAmB4C,CAAAA,CAAK,YAAA,CAAcA,CAAAA,CAAK,eAAA,CAAiBpK,CAAAA,CAAQ,CACxE,MAAA,CAAQ7B,GAAAA,CAAQ,MAClB,CAAC,CAAA,CACD,MAAM0M,GAAAA,CAAYT,CAAAA,CAAK,KAAA,CAAOpK,CAAAA,CAAQ,CACpC,SAAA,CAAW7B,GAAAA,CAAQ,SAAA,CACnB,MAAA,CAAQA,GAAAA,CAAQ,MAAA,CAChB,IAAA,CAAMA,GAAAA,CAAQ,IAChB,CAAC,CAAA,CAEGiM,CAAAA,CAAK,IAAA,EACP3L,CAAAA,CAAO,IAAA,CAAK2L,CAAAA,CAAK,IAAI,EAEzB,CAEA,eAAeH,EAAAA,CACbH,GAAAA,CACA9J,CAAAA,CACA+J,GAAAA,CACA5L,CAAAA,CAQA,CACA,GAAI,CAACA,CAAAA,CAAQ,WAAa,CAAC2L,GAAAA,CAAW,MAAA,CACpC,OAGF,IAAMK,CAAAA,CAAkB3L,CAAAA,CAAQ,oBAAA,CAAsB,CACpD,MAAA,CAAQL,CAAAA,CAAQ,MAClB,CAAC,CAAA,EAAG,KAAA,EAAM,CACJiM,CAAAA,CAAO,MAAMC,GAAAA,CAAoBP,GAAAA,CAAYQ,CAAAA,CAAmBtK,CAAM,CAAC,CAAA,CAE7E,GAAI,CAACoK,CAAAA,CACH,OAAAD,CAAAA,EAAiB,IAAA,EAAK,CACfI,CAAAA,CAAY,IAAI,KAAA,CAAM,2CAA2C,CAAC,CAAA,CAG3E,GAAI,CACFC,EAAAA,CAAoBJ,CAAAA,CAAK,KAAA,EAAS,EAAC,CAAGpK,CAAAA,CAAO,aAAA,CAAc,GAAG,EAChE,CAAA,MAASkD,CAAAA,CAAO,CACd,OAAAiH,CAAAA,EAAiB,IAAA,EAAK,CACfI,CAAAA,CAAYrH,CAAK,CAC1B,CAEAiH,CAAAA,EAAiB,OAAA,EAAQ,CAEzB,IAAMW,CAAAA,CAAyB,EAAC,CAC1BC,GAAAA,CAAyB,EAAC,CAC1BC,CAAAA,CAAyB,EAAC,CAE1BC,GAAAA,CAAczM,CAAAA,CAAQ,wBAAwB,CAAA,EAAG,KAAA,EAAM,CAIvD0M,GAAAA,CAAmBnB,GAAAA,CAAgB,EAAA,CACnCxF,CAAAA,CAAkB,MAAMkG,CAAAA,CAC5BS,GACF,CAAA,CACMC,CAAAA,CAAgBC,CAAAA,CACpBpL,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrBkL,GAAAA,CAAiB,aAAA,CAAc,EACjC,CAAA,CAiBA,GAdId,CAAAA,CAAK,QAAA,EAAU,MAAA,GACjB,MAAMM,CAAAA,CAAqBN,CAAAA,CAAK,QAAA,EAAU,MAAA,CAAQc,GAAAA,CAAkB,CAClE,MAAA,CAAQ,IAAA,CACR,eAAA,CAAA3G,CACF,CAAC,CAAA,CACDwG,GAAAA,CAAa,IAAA,CACXzM,UAAAA,CAAK,QAAA,CACH6M,CAAAA,CACAD,GAAAA,CAAiB,aAAA,CAAc,cACjC,CACF,CAAA,CAAA,CAIEd,CAAAA,CAAK,OAAA,CAAS,CAChB,IAAMO,CAAAA,CAAmB,MAAMC,EAAAA,CAAuBd,IAAY9J,CAAM,CAAA,CACxE,MAAMuD,EAAAA,CAAc6G,CAAAA,CAAK,OAAA,CAASc,GAAAA,CAAkB,CAClD,MAAA,CAAQ,IAAA,CACR,eAAA,CAAA3G,CAAAA,CACA,cAAA,CAAgB6F,CAAAA,CAAK,QAAA,EAAU,MAAA,CAC/B,gBAAA,CAAAO,CACF,CAAC,CAAA,CACDI,GAAAA,CAAa,IAAA,CACXzM,UAAAA,CAAK,QAAA,CAAS6M,CAAAA,CAAeD,GAAAA,CAAiB,aAAA,CAAc,WAAW,CACzE,EACF,CAGId,CAAAA,CAAK,MACP,MAAMtK,EAAAA,CAAUsK,CAAAA,CAAK,GAAA,CAAKc,GAAAA,CAAkB,CAC1C,MAAA,CAAQ,IACV,CAAC,CAAA,CACDH,GAAAA,CAAa,IAAA,CACXzM,UAAAA,CAAK,QAAA,CAAS6M,CAAAA,CAAeD,GAAAA,CAAiB,aAAA,CAAc,WAAW,CACzE,CAAA,CAAA,CAIEd,CAAAA,CAAK,OAAA,EACP,MAAMzB,EAAAA,CAAcyB,CAAAA,CAAK,OAAA,CAASc,GAAAA,CAAkB,CAClD,MAAA,CAAQ,IACV,CAAC,CAAA,CAIH,MAAM1D,CAAAA,CACJ4C,CAAAA,CAAK,YAAA,CACLA,CAAAA,CAAK,eAAA,CACLc,GAAAA,CACA,CACE,MAAA,CAAQ,IACV,CACF,CAAA,CAGA,IAAMG,CAAAA,CAAc,IAAI,GAAA,CAExB,IAAA,IAAWC,CAAAA,IAAQlB,CAAAA,CAAK,KAAA,EAAS,EAAC,CAAG,CACnC,IAAMmB,CAAAA,CAAOD,CAAAA,CAAK,IAAA,EAAQ,aAAA,CACrBD,CAAAA,CAAY,GAAA,CAAIE,CAAI,CAAA,EACvBF,CAAAA,CAAY,GAAA,CAAIE,CAAAA,CAAM,EAAE,CAAA,CAE1BF,CAAAA,CAAY,GAAA,CAAIE,CAAI,CAAA,CAAG,IAAA,CAAKD,CAAI,EAClC,CAGA,IAAA,IAAWC,CAAAA,IAAQ,KAAA,CAAM,IAAA,CAAKF,CAAAA,CAAY,IAAA,EAAM,CAAA,CAAG,CACjD,IAAMG,CAAAA,CAAYH,CAAAA,CAAY,GAAA,CAAIE,CAAI,CAAA,CAElCE,CAAAA,CAAeF,CAAAA,GAAS,aAAA,CAAgBxB,GAAAA,CAAgB,GAAK/J,CAAAA,CAE3D0L,CAAAA,CAAoBN,CAAAA,CACxBpL,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrByL,CAAAA,CAAa,aAAA,CAAc,EAAA,EAAMA,CAAAA,CAAa,aAAA,CAAc,GAC9D,CAAA,CACME,CAAAA,CACH,MAAMC,CAAAA,CACLF,CAAAA,CACAD,CAAAA,CAAa,aAAA,CAAc,GAC7B,CAAA,EAAMA,CAAAA,CAAa,aAAA,CAAc,GAAA,CAG7BI,CAAAA,CAAQ,MAAMhB,GAAAA,CAAYW,CAAAA,CAAWC,CAAAA,CAAc,CACvD,SAAA,CAAWtN,CAAAA,CAAQ,SAAA,CACnB,MAAA,CAAQ,IAAA,CACR,WAAA,CAAA8M,GAAAA,CACA,QAAA,CAAU9M,CAAAA,CAAQ,QAAA,CAClB,WAAA,CAAa,IAAA,CACb,IAAA,CAAMA,CAAAA,CAAQ,IAChB,CAAC,CAAA,CAED2M,CAAAA,CAAa,IAAA,CACX,GAAGe,CAAAA,CAAM,YAAA,CAAa,GAAA,CAAKP,CAAAA,EACzBhN,UAAAA,CAAK,QAAA,CAASoN,CAAAA,CAAmBpN,UAAAA,CAAK,IAAA,CAAKqN,CAAAA,CAAaL,CAAI,CAAC,CAC/D,CACF,EACAP,GAAAA,CAAa,IAAA,CACX,GAAGc,CAAAA,CAAM,YAAA,CAAa,GAAA,CAAKP,CAAAA,EACzBhN,UAAAA,CAAK,QAAA,CAASoN,CAAAA,CAAmBpN,UAAAA,CAAK,IAAA,CAAKqN,CAAAA,CAAaL,CAAI,CAAC,CAC/D,CACF,CAAA,CACAN,CAAAA,CAAa,IAAA,CACX,GAAGa,CAAAA,CAAM,YAAA,CAAa,GAAA,CAAKP,CAAAA,EACzBhN,UAAAA,CAAK,QAAA,CAASoN,CAAAA,CAAmBpN,UAAAA,CAAK,IAAA,CAAKqN,CAAAA,CAAaL,CAAI,CAAC,CAC/D,CACF,EACF,CAgBA,GAdAL,GAAAA,EAAa,OAAA,EAAQ,CAGrBH,CAAAA,CAAa,IAAA,EAAK,CAClBC,GAAAA,CAAa,IAAA,EAAK,CAClBC,CAAAA,CAAa,IAAA,EAAK,CAGd,EADoBF,CAAAA,CAAa,MAAA,EAAUC,GAAAA,CAAa,MAAA,CAAA,EACpC,CAACC,CAAAA,CAAa,MAAA,EACpCxM,CAAAA,CAAQ,mBAAA,CAAqB,CAC3B,MAAA,CAAQL,CAAAA,CAAQ,MAClB,CAAC,CAAA,EAAG,IAAA,EAAK,CAGP2M,CAAAA,CAAa,MAAA,CAAQ,CACvBtM,CAAAA,CACE,CAAA,QAAA,EAAWsM,CAAAA,CAAa,MAAM,CAAA,CAAA,EAC5BA,CAAAA,CAAa,MAAA,GAAW,CAAA,CAAI,MAAA,CAAS,OACvC,CAAA,CAAA,CAAA,CACA,CACE,MAAA,CAAQ3M,CAAAA,CAAQ,MAClB,CACF,CAAA,EAAG,OAAA,EAAQ,CACX,IAAA,IAAWmN,CAAAA,IAAQR,CAAAA,CACjBrM,CAAAA,CAAO,GAAA,CAAI,CAAA,IAAA,EAAO6M,CAAI,EAAE,EAE5B,CAEA,GAAIP,GAAAA,CAAa,MAAA,CAAQ,CACvBvM,CAAAA,CACE,CAAA,QAAA,EAAWuM,GAAAA,CAAa,MAAM,CAAA,CAAA,EAC5BA,GAAAA,CAAa,MAAA,GAAW,CAAA,CAAI,MAAA,CAAS,OACvC,CAAA,CAAA,CAAA,CACA,CACE,MAAA,CAAQ5M,CAAAA,CAAQ,MAClB,CACF,CAAA,EAAG,IAAA,EAAK,CACR,IAAA,IAAWmN,CAAAA,IAAQP,GAAAA,CACjBtM,CAAAA,CAAO,GAAA,CAAI,CAAA,IAAA,EAAO6M,CAAI,EAAE,EAE5B,CAEA,GAAIN,CAAAA,CAAa,MAAA,CAAQ,CACvBxM,CAAAA,CACE,CAAA,QAAA,EAAWwM,CAAAA,CAAa,MAAM,CAAA,CAAA,EAC5BD,GAAAA,CAAa,MAAA,GAAW,CAAA,CAAI,MAAA,CAAS,OACvC,CAAA,gCAAA,CAAA,CACA,CACE,MAAA,CAAQ5M,CAAAA,CAAQ,MAClB,CACF,CAAA,EAAG,IAAA,EAAK,CACR,IAAA,IAAWmN,CAAAA,IAAQN,CAAAA,CACjBvM,CAAAA,CAAO,GAAA,CAAI,CAAA,IAAA,EAAO6M,CAAI,CAAA,CAAE,EAE5B,CAEIlB,CAAAA,CAAK,IAAA,EACP3L,CAAAA,CAAO,IAAA,CAAK2L,CAAAA,CAAK,IAAI,EAEzB,CAEA,eAAeQ,EAAAA,CACbd,CAAAA,CACA9J,CAAAA,CACA,CACA,IAAMU,CAAAA,CAAS,MAAMoL,GAAAA,CAAiBhC,CAAAA,CAAY,CAAE,MAAA,CAAA9J,CAAO,CAAC,CAAA,CAG5D,OAFgB6G,GAAAA,CAAE,KAAA,CAAMkF,GAAkB,CAAA,CAAE,KAAA,CAAMrL,CAAM,CAAA,CAEzC,IAAA,CACZsL,CAAAA,EACCA,CAAAA,CAAU,IAAA,GAAS,gBAAA,EAAoBA,CAAAA,CAAU,IAAA,GAAS,gBAC9D,CACF,CAEA,SAASxB,EAAAA,CACPqB,CAAAA,CACA1M,CAAAA,CACA,CACA,IAAA,IAAWmM,CAAAA,IAAQO,CAAAA,CACjB,GAAKP,CAAAA,EAAM,MAAA,EAIP,CAACrM,EAAAA,CAAaqM,CAAAA,CAAK,MAAA,CAAQnM,CAAG,CAAA,CAChC,MAAM,IAAI,KAAA,CACR,CAAA,8BAAA,EAAiCmM,CAAAA,CAAK,MAAM,CAAA,4CAAA,CAC9C,CAGN,CCxXA,IAAMW,EAAAA,CACJ,sDAAA,CAEWC,CAAAA,CAAY,CACvB,IAAA,CAAM,MAAA,CACN,UAAW,SAAA,CACX,eAAA,CAAiB,eACnB,CAAA,CAEA,eAAsBC,EAAAA,CACpBhO,CAAAA,CAIA,CACAA,CAAAA,CAAU,CACR,MAAA,CAAQ,KAAA,CACR,GAAGA,CACL,CAAA,CAEA,IAAIiO,CAAAA,CACFjO,CAAAA,CAAQ,QAAA,EAAY+N,CAAAA,CAAU/N,CAAAA,CAAQ,QAAkC,CAAA,CACnEA,CAAAA,CAAQ,QAAA,CACT,MAAA,CACFkO,CAAAA,CACFD,CAAAA,GAAaF,CAAAA,CAAU,IAAA,CAAO,QAAA,CAAW,aAAA,CACvCI,CAAAA,CAAc,IAAA,CAEZC,CAAAA,CACJpO,CAAAA,CAAQ,UAAA,EAAY,MAAA,GAAW,CAAA,EAC/B,CAAC,CAACA,CAAAA,CAAQ,UAAA,CAAW,CAAC,CAAA,CAAE,KAAA,CAAM,aAAa,CAAA,CAE7C,GAAIA,CAAAA,CAAQ,UAAA,EAAcoO,CAAAA,CACxB,GAAI,CACF,GAAM,CAAC7L,CAAM,CAAA,CAAI,MAAM8L,CAAAA,CAAcrO,CAAAA,CAAQ,UAAU,CAAA,CACjD,CAAE,KAAAsO,CAAK,CAAA,CAAI5F,GAAAA,CACd,MAAA,CAAO,CACN,IAAA,CAAMA,GAAAA,CAAE,MAAA,CAAO,CACb,WAAA,CAAaA,GAAAA,CAAE,MAAA,EACjB,CAAC,CACH,CAAC,CAAA,CACA,KAAA,CAAMnG,CAAM,CAAA,CACf4L,CAAAA,CAAcG,CAAAA,CAAK,WAAA,CAGnBL,CAAAA,CAAWF,CAAAA,CAAU,KACvB,CAAA,MAAShJ,GAAAA,CAAO,CACdzE,CAAAA,CAAO,KAAA,EAAM,CACb8L,EAAYrH,GAAK,EACnB,CAGF,GAAI,CAAC/E,CAAAA,CAAQ,KAAA,CAAO,CAClB,GAAM,CAAE,IAAA,CAAAoN,CAAAA,CAAM,IAAA,CAAAtK,CAAK,CAAA,CAAI,MAAMgH,EAAAA,CAAQ,CACnC,CACE,IAAA,CAAM9J,CAAAA,CAAQ,QAAA,EAAYoO,CAAAA,CAAoB,IAAA,CAAO,QAAA,CACrD,IAAA,CAAM,MAAA,CACN,OAAA,CAAS,CAAA,SAAA,EAAY7N,CAAAA,CAAY,IAAA,CAC/BP,CAAAA,CAAQ,GACV,CAAC,CAAA;AAAA,wCAAA,CAAA,CACD,OAAA,CAAS,CACP,CAAE,KAAA,CAAO,YAAA,CAAc,KAAA,CAAO,MAAO,CAAA,CACrC,CAAE,KAAA,CAAO,YAAA,CAAc,KAAA,CAAO,SAAU,CAAA,CACxC,CAAE,KAAA,CAAO,oBAAA,CAAsB,KAAA,CAAO,eAAgB,CACxD,CAAA,CACA,OAAA,CAAS,CACX,CAAA,CACA,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAM,OACN,OAAA,CAAS,6BAAA,CACT,OAAA,CAASkO,CAAAA,CACT,MAAA,CAAShK,CAAAA,EAAkBA,CAAAA,CAAM,IAAA,EAAK,CACtC,QAAA,CAAWA,CAAAA,EACTA,CAAAA,CAAM,MAAA,CAAS,GAAA,CACX,0CAAA,CACA,IACR,CACF,CAAC,CAAA,CAED+J,CAAAA,CAAWb,CAAAA,EAAQa,CAAAA,CACnBC,CAAAA,CAAcpL,CAAAA,CAEVsK,CAAAA,GAAS,SAAA,GACXe,CAAAA,CAAc,QAAA,EAElB,CAEA,IAAM1E,CAAAA,CAAiB,MAAMS,IAAkBlK,CAAAA,CAAQ,GAAA,CAAK,CAC1D,YAAA,CAAc,IAChB,CAAC,CAAA,CAEKuO,CAAAA,CAAc,CAAA,EAAGvO,CAAAA,CAAQ,GAAG,CAAA,CAAA,EAAIkO,CAAW,CAAA,CAAA,CAGjD,GAAI,CACF,MAAMhO,CAAAA,CAAG,MAAA,CAAOF,CAAAA,CAAQ,GAAA,CAAKE,CAAAA,CAAG,SAAA,CAAU,IAAI,EAChD,CAAA,KAAgB,CACdI,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,KAAA,CAAM,YAAYC,CAAAA,CAAY,IAAA,CAAKP,CAAAA,CAAQ,GAAG,CAAC,CAAA,iBAAA,CAAmB,CAAA,CACzEM,CAAAA,CAAO,KAAA,CACL,CAAA,2EAAA,EAA8EC,CAAAA,CAAY,IAAA,CACxFP,CAAAA,CAAQ,GACV,CAAC,CAAA,gBAAA,CACH,EACAM,CAAAA,CAAO,KAAA,EAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,EAChB,CAEA,OAAIJ,CAAAA,CAAG,UAAA,CAAWC,UAAAA,CAAK,OAAA,CAAQH,CAAAA,CAAQ,GAAA,CAAKkO,CAAAA,CAAa,cAAc,CAAC,CAAA,GACtE5N,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,KAAA,CACL,CAAA,wBAAA,EAA2BC,CAAAA,CAAY,IAAA,CAAK2N,CAAW,CAAC,CAAA,gBAAA,CAC1D,CAAA,CACA5N,CAAAA,CAAO,KAAA,CAAM,+CAA+C,CAAA,CAC5DA,CAAAA,CAAO,KAAA,EAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAAA,CAGZ2N,CAAAA,GAAaF,CAAAA,CAAU,IAAA,EAAQE,CAAAA,GAAaF,CAAAA,CAAU,SAAS,CAAA,GACjE,MAAMS,EAAAA,CAAkBD,CAAAA,CAAa,CACnC,OAAA,CAASJ,CAAAA,CACT,GAAA,CAAKnO,CAAAA,CAAQ,GAAA,CACb,cAAA,CAAAyJ,CAAAA,CACA,MAAA,CAAQ,CAAC,CAACzJ,CAAAA,CAAQ,MACpB,CAAC,EAGCiO,CAAAA,GAAaF,CAAAA,CAAU,eAAe,CAAA,EACxC,MAAMU,EAAAA,CAAsBF,CAAAA,CAAa,CACvC,cAAA,CAAA9E,CACF,CAAC,CAAA,CAGI,CACL,WAAA,CAAA8E,CAAAA,CACA,WAAA,CAAAL,EACA,QAAA,CAAAD,CACF,CACF,CAEA,eAAeO,EAAAA,CACbD,GAAAA,CACAvO,CAAAA,CAMA,CACA,IAAM0O,CAAAA,CAAgBrO,CAAAA,CACpB,CAAA,uBAAA,EACEL,CAAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,QAAQ,CAAA,CAAI,IAAA,CAAO,IAChD,CAAA,sCAAA,CACF,CAAA,CAAE,KAAA,EAAM,CAGF2O,CAAAA,CAAO,CACX,YAAA,CACA,UAAA,CACA,cAAA,CACA,OAAA,CACA3O,CAAAA,CAAQ,MAAA,CAAS,YAAc,cAAA,CAC/B,mBAAA,CACA,CAAA,MAAA,EAASA,CAAAA,CAAQ,cAAc,CAAA,CACjC,CAAA,CAAA,CAGEA,CAAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,IAAI,CAAA,EAC/BA,CAAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,QAAQ,GACnCA,CAAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,QAAQ,CAAA,GAEnC2O,CAAAA,CAAK,IAAA,CAAK,aAAa,CAAA,CAAA,CAIvB3O,CAAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,QAAQ,CAAA,EACnCA,CAAAA,CAAQ,OAAA,CAAQ,WAAW,QAAQ,CAAA,GAEnC2O,CAAAA,CAAK,IAAA,CAAK,qBAAqB,CAAA,CAGjC,GAAI,CACF,MAAMrE,KAAAA,CACJ,KAAA,CACA,CAAC,CAAA,gBAAA,EAAmBtK,CAAAA,CAAQ,OAAO,CAAA,CAAA,CAAIuO,GAAAA,CAAa,UAAA,CAAY,GAAGI,CAAI,CAAA,CACvE,CACE,GAAA,CAAK3O,CAAAA,CAAQ,GACf,CACF,EACF,CAAA,KAAgB,CACdM,CAAAA,CAAO,KAAA,EAAM,CACbA,EAAO,KAAA,CACL,wEACF,CAAA,CACA,OAAA,CAAQ,IAAA,CAAK,CAAC,EAChB,CAEAoO,CAAAA,EAAe,OAAA,CACb,CAAA,uBAAA,EACE1O,CAAAA,CAAQ,OAAA,CAAQ,UAAA,CAAW,QAAQ,CAAA,CAAI,IAAA,CAAO,IAChD,CAAA,SAAA,CACF,EACF,CAEA,eAAeyO,EAAAA,CACbF,GAAAA,CACAvO,CAAAA,CAGA,CACA,IAAM0O,CAAAA,CAAgBrO,CAAAA,CACpB,+DACF,CAAA,CAAE,KAAA,GAEF,GAAI,CAEF,IAAMuO,CAAAA,CAAezO,UAAAA,CAAK,IAAA,CAAK0O,EAAAA,CAAG,MAAA,EAAO,CAAG,CAAA,gBAAA,EAAmB,IAAA,CAAK,GAAA,EAAK,CAAA,CAAE,CAAA,CAC3E,MAAM3O,CAAAA,CAAG,SAAA,CAAU0O,CAAY,CAAA,CAC/B,IAAME,CAAAA,CAAW,MAAM,KAAA,CAAMhB,EAAqB,CAAA,CAClD,GAAI,CAACgB,CAAAA,CAAS,EAAA,CACZ,MAAM,IAAI,MAAM,CAAA,6BAAA,EAAgCA,CAAAA,CAAS,UAAU,CAAA,CAAE,CAAA,CAIvE,IAAMC,CAAAA,CAAU5O,UAAAA,CAAK,OAAA,CAAQyO,CAAAA,CAAc,iBAAiB,CAAA,CAC5D,MAAM1O,CAAAA,CAAG,SAAA,CAAU6O,CAAAA,CAAS,OAAO,IAAA,CAAK,MAAMD,CAAAA,CAAS,WAAA,EAAa,CAAC,CAAA,CACrE,MAAMxE,KAAAA,CAAM,KAAA,CAAO,CACjB,MAAA,CACAyE,CAAAA,CACA,IAAA,CACAH,CAAAA,CACA,sBAAA,CACA,iCACF,CAAC,CAAA,CACD,IAAMI,CAAAA,CAAgB7O,UAAAA,CAAK,OAAA,CAAQyO,CAAAA,CAAc,eAAe,CAAA,CAChE,MAAM1O,CAAAA,CAAG,IAAA,CAAK8O,CAAAA,CAAeT,GAAW,CAAA,CACxC,MAAMrO,CAAAA,CAAG,MAAA,CAAO0O,CAAY,CAAA,CAG5B,MAAMtE,KAAAA,CAAMtK,CAAAA,CAAQ,cAAA,CAAgB,CAAC,SAAS,CAAA,CAAG,CAC/C,GAAA,CAAKuO,GACP,CAAC,CAAA,CAID,IAAMU,CAAAA,CAAkB9O,UAAAA,CAAK,IAAA,CAAKoO,GAAAA,CAAa,cAAc,CAAA,CAC7D,GAAIrO,CAAAA,CAAG,UAAA,CAAW+O,CAAe,CAAA,CAAG,CAClC,IAAMC,CAAAA,CAAqB,MAAMhP,CAAAA,CAAG,SAAS+O,CAAAA,CAAiB,MAAM,CAAA,CAC9DE,CAAAA,CAAc,IAAA,CAAK,KAAA,CAAMD,CAAkB,CAAA,CACjDC,CAAAA,CAAY,IAAA,CAAOZ,GAAAA,CAAY,KAAA,CAAM,GAAG,CAAA,CAAE,GAAA,EAAI,CAC9C,MAAMrO,CAAAA,CAAG,SAAA,CAAU+O,CAAAA,CAAiB,IAAA,CAAK,SAAA,CAAUE,CAAAA,CAAa,IAAA,CAAM,CAAC,CAAC,EAC1E,CAGA,IAAMnO,CAAAA,CAAM,OAAA,CAAQ,GAAA,EAAI,CACxB,MAAMsJ,KAAAA,CAAM,KAAA,CAAO,CAAC,WAAW,CAAA,CAAG,CAAE,GAAA,CAAKiE,GAAY,CAAC,CAAA,CACtD,MAAMjE,KAAAA,CAAM,KAAA,CAAO,CAAC,MAAM,EAAG,CAAE,GAAA,CAAKiE,GAAY,CAAC,CAAA,CACjD,MAAMjE,KAAAA,CAAM,KAAA,CAAO,CAAC,KAAA,CAAO,IAAI,CAAA,CAAG,CAAE,GAAA,CAAKiE,GAAY,CAAC,EACtD,MAAMjE,KAAAA,CAAM,KAAA,CAAO,CAAC,QAAA,CAAU,IAAA,CAAM,gBAAgB,CAAA,CAAG,CACrD,GAAA,CAAKiE,GACP,CAAC,CAAA,CAEDG,CAAAA,EAAe,OAAA,CAAQ,kCAAkC,EAC3D,CAAA,MAAS3J,CAAAA,CAAO,CACd2J,CAAAA,EAAe,IAAA,CAAK,uDAAuD,CAAA,CAC3EtC,CAAAA,CAAYrH,CAAK,EACnB,CACF,CCtRA,eAAsBqK,CAAAA,CAAapO,CAAAA,CAAc,OAAA,CAAQ,GAAA,EAAI,CAAkB,CAC7E,GAAI,CACF,GAAM,CAAE,MAAA,CAAAa,CAAO,CAAA,CAAI,MAAM,OAAO,kBAAkB,CAAA,CAC5CwN,CAAAA,CAAW,CACf,YAAA,CACA,wBAAA,CACA,kBAAA,CACA,MACF,CAAA,CAEA,IAAA,IAAWC,CAAAA,IAAWD,CAAAA,CAAU,CAC9B,IAAME,CAAAA,CAAUC,IAAAA,CAAKxO,CAAAA,CAAKsO,CAAO,CAAA,CAC7BtE,UAAAA,CAAWuE,CAAO,CAAA,EACpB1N,CAAAA,CAAO,CACL,IAAA,CAAM0N,CAAAA,CACN,QAAA,CAAU,CAAA,CAAA,CACV,KAAA,CAAO,EACT,CAAC,EAEL,CACF,CAAA,MAASxK,CAAAA,CAAO,CACdzE,CAAAA,CAAO,IAAA,CAAK,2BAAA,CAA6ByE,CAAK,EAChD,CACF,CCzBO,IAAM0K,EAAAA,CAAqB,MAAA,CAE3B,SAASC,EAAAA,CAAiBC,CAAAA,CAAiC,CAChE,GAAI,CAACC,CAAAA,CAAQ,UAAA,CAAWD,CAAQ,CAAA,CAC9B,OAAO,IAAA,CAGT,IAAME,EAAa,CAAA,EAAGF,CAAQ,CAAA,EAAGF,EAAkB,CAAA,CAAA,CACnD,GAAI,CACF,OAAAG,CAAAA,CAAQ,UAAA,CAAWD,CAAAA,CAAUE,CAAU,CAAA,CAChCA,CACT,CAAA,MAAS9K,CAAAA,CAAO,CACd,OAAA,OAAA,CAAQ,KAAA,CAAM,CAAA,2BAAA,EAA8B4K,CAAQ,CAAA,EAAA,EAAK5K,CAAK,CAAA,CAAE,CAAA,CACzD,IACT,CACF,CAEO,SAAS+K,EAAAA,CAAkBH,CAAAA,CAA2B,CAC3D,IAAME,EAAa,CAAA,EAAGF,CAAQ,CAAA,EAAGF,EAAkB,CAAA,CAAA,CAEnD,GAAI,CAACG,CAAAA,CAAQ,UAAA,CAAWC,CAAU,CAAA,CAChC,OAAO,MAAA,CAGT,GAAI,CACF,OAAAD,CAAAA,CAAQ,UAAA,CAAWC,CAAAA,CAAYF,CAAQ,CAAA,CAChC,CAAA,CACT,CAAA,MAAS5K,CAAAA,CAAO,CACd,OAAA,OAAA,CAAQ,KAAA,CACN,CAAA,uCAAA,EAA0C8K,CAAU,CAAA,EAAA,EAAK9K,CAAK,CAAA,CAChE,EACO,KACT,CACF,CAEO,SAASgL,EAAAA,CAAiBJ,CAAAA,CAA2B,CAC1D,IAAME,CAAAA,CAAa,CAAA,EAAGF,CAAQ,CAAA,EAAGF,EAAkB,CAAA,CAAA,CAEnD,GAAI,CAACG,EAAQ,UAAA,CAAWC,CAAU,CAAA,CAChC,OAAO,MAAA,CAGT,GAAI,CACF,OAAAD,CAAAA,CAAQ,UAAA,CAAWC,CAAU,CAAA,CACtB,CAAA,CACT,CAAA,KAAgB,CAEd,OAAO,MACT,CACF,CC5CA,eAAsBG,EAAAA,CACpBrE,CAAAA,CACA9J,CAAAA,CACA,CACA,IAAMoO,CAAAA,CAAuB,IAAI,GAAA,CAC3BC,CAAAA,CAAe,IAAI,GAAA,CACnBC,CAAAA,CAAiB,CAAC,GAAGxE,CAAU,CAAA,CAErC,KAAOwE,CAAAA,CAAe,MAAA,CAAS,CAAA,EAAG,CAChC,IAAMC,CAAAA,CAAcD,CAAAA,CAAe,KAAA,EAAM,CAEzC,GAAID,CAAAA,CAAa,GAAA,CAAIE,CAAW,CAAA,CAC9B,SAEFF,CAAAA,CAAa,GAAA,CAAIE,CAAW,CAAA,CAE5B,GAAM,CAAE,QAAA,CAAAC,CAAS,CAAA,CAAIC,CAAAA,CAA+BF,CAAW,EAC3DC,CAAAA,EAAY,CAACE,CAAAA,CAAmBF,CAAQ,CAAA,EAC1CJ,CAAAA,CAAqB,GAAA,CAAII,CAAQ,CAAA,CAGnC,GAAI,CACF,GAAM,CAACG,CAAI,CAAA,CAAI,MAAMC,IAAmB,CAACL,CAAW,CAAA,CAAGvO,CAAAA,CAAQ,CAC7D,QAAA,CAAU,CAAA,CACZ,CAAC,CAAA,CAED,GAAI2O,CAAAA,EAAM,oBAAA,CACR,IAAA,IAAWjG,CAAAA,IAAOiG,CAAAA,CAAK,oBAAA,CAAsB,CAC3C,GAAM,CAAE,QAAA,CAAUE,CAAY,CAAA,CAAIJ,CAAAA,CAA+B/F,CAAG,CAAA,CAChEmG,CAAAA,EAAe,CAACH,CAAAA,CAAmBG,CAAW,CAAA,EAChDT,CAAAA,CAAqB,GAAA,CAAIS,CAAW,CAAA,CAGjCR,CAAAA,CAAa,GAAA,CAAI3F,CAAG,CAAA,EACvB4F,CAAAA,CAAe,IAAA,CAAK5F,CAAG,EAE3B,CAEJ,CAAA,MAASxF,CAAAA,CAAO,CAEd,GAAIA,CAAAA,YAAiB4L,GAAAA,CAA4B,CAC/C,GAAM,CAAE,QAAA,CAAAN,CAAS,CAAA,CAAIC,CAAAA,CAA+BF,CAAW,CAAA,CAC3DC,CAAAA,EAAY,CAACE,CAAAA,CAAmBF,CAAQ,CAAA,EAC1CJ,CAAAA,CAAqB,GAAA,CAAII,CAAQ,EAEnC,QACF,CAIA,QACF,CACF,CAEA,OAAO,KAAA,CAAM,IAAA,CAAKJ,CAAoB,CACxC,CCrDA,eAAsBW,CAAAA,CACpBjF,GAAAA,CACA9J,CAAAA,CACA7B,CAAAA,CAGI,EAAC,CACL,CACAA,CAAAA,CAAU,CACR,MAAA,CAAQ,KAAA,CACR,SAAA,CAAW,IAAA,CACX,GAAGA,CACL,CAAA,CAKA,IAAM6Q,GAFgB,MAAMb,EAAAA,CAA0BrE,GAAAA,CAAY9J,CAAM,CAAA,EAEhC,MAAA,CACrCwO,CAAAA,EACC,CAACxO,CAAAA,CAAO,UAAA,GAAawO,CAAQ,CAAA,EAC7B,CAAC,MAAA,CAAO,IAAA,CAAKE,CAAkB,EAAE,QAAA,CAASF,CAAQ,CACtD,CAAA,CAEA,GAAIQ,CAAAA,CAAkB,MAAA,GAAW,CAAA,CAC/B,OAAO,CACL,MAAA,CAAAhP,CAAAA,CACA,aAAA,CAAe,EACjB,CAAA,CAKF,IAAMiP,CAAAA,CAAgB,MAAMC,EAAAA,CAAmB,CAC7C,QAAA,CAAU,OAAA,CAAQ,GAAA,CAAI,QAAA,GAAa,aACrC,CAAC,CAAA,CAED,GAAI,CAACD,CAAAA,CACH,OAAO,CACL,MAAA,CAAAjP,CAAAA,CACA,aAAA,CAAe,EACjB,CAAA,CAGF,IAAMmP,CAAAA,CAA0C,EAAC,CACjD,IAAA,IAAWX,CAAAA,IAAYQ,CAAAA,CACjBC,CAAAA,CAAcT,CAAQ,CAAA,GACxBW,EAAgBX,CAAQ,CAAA,CAAIS,CAAAA,CAAcT,CAAQ,CAAA,CAAA,CAItD,GAAI,MAAA,CAAO,IAAA,CAAKW,CAAe,CAAA,CAAE,MAAA,GAAW,CAAA,CAC1C,OAAO,CACL,MAAA,CAAAnP,CAAAA,CACA,cAAe,EACjB,CAAA,CAIF,IAAMoP,CAAAA,CAAqB,MAAA,CAAO,WAAA,CAChC,MAAA,CAAO,OAAA,CAAQpP,CAAAA,CAAO,UAAA,EAAc,EAAE,CAAA,CAAE,MAAA,CACtC,CAAC,CAAC6E,CAAG,CAAA,GAAM,CAAC,MAAA,CAAO,IAAA,CAAK6J,CAAkB,CAAA,CAAE,QAAA,CAAS7J,CAAG,CAC1D,CACF,CAAA,CAEMwK,CAAAA,CAA0B,CAC9B,GAAGrP,CAAAA,CACH,UAAA,CAAY,CACV,GAAGoP,CAAAA,CACH,GAAGD,CACL,CACF,CAAA,CAEA,GAAIhR,CAAAA,CAAQ,SAAA,CAAW,CACrB,GAAM,CAAE,aAAA,CAAAmR,CAAAA,CAAe,GAAGC,CAA2B,CAAA,CACnDF,CAAAA,CACIG,GAAAA,CAAgBhR,CAAAA,CAAQ,2BAAA,CAA6B,CACzD,MAAA,CAAQL,CAAAA,CAAQ,MAClB,CAAC,CAAA,CAAE,KAAA,EAAM,CACHsR,CAAAA,CAAgBC,CAAAA,CAAgB,MAAMH,CAA0B,CAAA,CACtE,MAAMlR,CAAAA,CAAG,SAAA,CACPC,UAAAA,CAAK,OAAA,CAAQ0B,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK,iBAAiB,CAAA,CACxD,IAAA,CAAK,SAAA,CAAUyP,CAAAA,CAAe,IAAA,CAAM,CAAC,CAAA,CAAI;AAAA,CAAA,CACzC,OACF,CAAA,CACAD,GAAAA,CAAc,OAAA,GAChB,CAEA,OAAO,CACL,MAAA,CAAQH,EACR,aAAA,CAAe,MAAA,CAAO,IAAA,CAAKF,CAAe,CAC5C,CACF,CCzFA,eAAsBQ,GACpBC,GAAAA,CACA5P,CAAAA,CACA7B,CAAAA,CAGA,CACA,GAAI,CAACyR,GAAAA,CACH,OAGFzR,CAAAA,CAAU,CACR,MAAA,CAAQ,KAAA,CACR,GAAGA,CACL,CAAA,CAEA,IAAM0R,CAAAA,CAA2BvR,UAAAA,CAAK,SACpC0B,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrBA,CAAAA,CAAO,cAAc,cACvB,CAAA,CACMjB,CAAAA,CAAkBP,CAAAA,CACtB,YAAYE,CAAAA,CAAY,IAAA,CAAKmR,CAAwB,CAAC,GACtD,CACE,MAAA,CAAQ1R,CAAAA,CAAQ,MAClB,CACF,CAAA,CAAE,KAAA,EAAM,CACFiC,CAAAA,CAAM,MAAM/B,QAAAA,CAAG,QAAA,CAAS2B,CAAAA,CAAO,aAAA,CAAc,cAAA,CAAgB,MAAM,CAAA,CACnEK,CAAAA,CAAS,MAAMyP,EAAAA,CAAyB1P,CAAAA,CAAKwP,GAAAA,CAAS5P,CAAM,EAClE,MAAM3B,QAAAA,CAAG,SAAA,CAAU2B,CAAAA,CAAO,cAAc,cAAA,CAAgBK,CAAAA,CAAQ,MAAM,CAAA,CACtEtB,GAAiB,OAAA,GACnB,CAEA,eAAsB+Q,GACpBvP,CAAAA,CACAqP,CAAAA,CACA5P,CAAAA,CACA,CACA,IAAM+P,CAAAA,CAAa,MAAMC,CAAAA,CAAkBzP,CAAAA,CAAOP,CAAM,CAAA,CAIlDiQ,CAAAA,CAAeF,CAAAA,CAClB,oBAAA,CAAqBG,WAAW,uBAAuB,CAAA,CACvD,IAAA,CAAM/O,CAAAA,EACLA,EACG,aAAA,EAAc,CACd,IAAA,CACEgP,CAAAA,EACCA,EAAS,MAAA,CAAOD,UAAAA,CAAW,kBAAkB,CAAA,EAC7CC,EAAS,OAAA,EAAQ,GAAM,SAC3B,CACJ,EAGF,OAAKF,CAAAA,EAILG,EAAAA,CAAyBH,CAAAA,CAAcL,CAAO,CAAA,CAEvCG,CAAAA,CAAW,WAAA,EAAY,EALrBxP,CAMX,CAEA,eAAe6P,EAAAA,CACbH,CAAAA,CACAL,EACA,CACA,IAAMS,CAAAA,CAAYC,CAAAA,CAAcL,CAAY,CAAA,CAEtCM,CAAAA,CAAmBN,CAAAA,CAAa,YAAY,SAAS,CAAA,CAE3D,GAAI,CAACM,EAAkB,CACrB,IAAMC,CAAAA,CAAc,CAClB,KAAM,SAAA,CACN,WAAA,CAAa,CAAA,CAAA,EAAIH,CAAS,GAAGT,CAAAA,CAAQ,IAAA,CACnC,CAAA,EAAGS,CAAS,KAAKA,CAAS,CAAA,CAC5B,CAAC,CAAA,EAAGA,CAAS,CAAA,CAAA,CACf,CAAA,CACA,OAAAJ,CAAAA,CAAa,sBAAsBO,CAAW,CAAA,CAEvCP,CACT,CAEA,GAAIM,CAAAA,CAAiB,MAAA,CAAOL,UAAAA,CAAW,kBAAkB,CAAA,CAAG,CAC1D,IAAMO,CAAAA,CAAcF,EAAiB,cAAA,EAAe,CAGpD,GAAIE,CAAAA,EAAa,OAAOP,UAAAA,CAAW,sBAAsB,CAAA,CACvD,IAAA,IAAWQ,KAAed,CAAAA,CAAS,CACjC,IAAMe,CAAAA,CAAW,GAAGN,CAAS,CAAA,EAAGK,CAAW,CAAA,EAAGL,CAAS,CAAA,CAAA,CAIrDI,CAAAA,CACG,WAAA,EAAY,CACZ,IAAKG,CAAAA,EAAYA,CAAAA,CAAQ,OAAA,EAAS,CAAA,CAClC,QAAA,CAASD,CAAQ,CAAA,EAKtBF,EAAY,UAAA,CAAWE,CAAQ,EACjC,CAGF,OAAOV,CACT,CAEA,OAAOA,CACT,CCvEA,OAAA,CAAQ,EAAA,CAAG,OAASY,CAAAA,EAAS,CAC3B,IAAM/C,CAAAA,CAAWxP,WAAK,OAAA,CAAQ,OAAA,CAAQ,GAAA,EAAI,CAAG,iBAAiB,CAAA,CAG9D,OAAIuS,CAAAA,GAAS,CAAA,CACJ3C,GAAiBJ,CAAQ,CAAA,CAI3BG,EAAAA,CAAkBH,CAAQ,CACnC,CAAC,CAAA,CAEM,IAAMgD,EAAAA,CAAoBjK,IAAE,MAAA,CAAO,CACxC,GAAA,CAAKA,GAAAA,CAAE,QAAO,CACd,UAAA,CAAYA,GAAAA,CAAE,KAAA,CAAMA,IAAE,MAAA,EAAQ,CAAA,CAAE,QAAA,GAChC,GAAA,CAAKA,GAAAA,CAAE,OAAA,EAAQ,CACf,QAAA,CAAUA,GAAAA,CAAE,OAAA,EAAQ,CACpB,MAAOA,GAAAA,CAAE,OAAA,EAAQ,CACjB,MAAA,CAAQA,IAAE,OAAA,EAAQ,CAClB,YAAA,CAAcA,GAAAA,CAAE,SAAQ,CACxB,MAAA,CAAQA,GAAAA,CAAE,OAAA,GAAU,QAAA,EAAS,CAC7B,YAAA,CAAcA,GAAAA,CAAE,SAAQ,CACxB,QAAA,CAAUA,GAAAA,CACP,MAAA,GACA,QAAA,EAAS,CACT,MAAA,CACEkK,CAAAA,EACKA,EACK7E,CAAAA,CAAU6E,CAA6B,CAAA,CAEzC,IAAA,CAET,CACE,OAAA,CACE,oEACJ,CACF,EACF,SAAA,CAAWlK,GAAAA,CACR,MAAA,EAAO,CACP,UAAS,CACT,MAAA,CACEkK,CAAAA,EACKA,CAAAA,CACKC,EAAY,IAAA,CAAMC,CAAAA,EAAUA,CAAAA,CAAM,IAAA,GAASF,CAAG,CAAA,CAGhD,IAAA,CAET,CACE,OAAA,CAAS,mCAAmCC,CAAAA,CAAY,GAAA,CACrDC,CAAAA,EAAUA,CAAAA,CAAM,IACnB,CAAA,CAAE,IAAA,CAAK,MAAM,CAAC,GAChB,CACF,CAAA,CACF,SAAA,CAAWpK,GAAAA,CAAE,OAAA,EACf,CAAC,CAAA,CAEYqK,GAAO,IAAIC,OAAAA,EAAQ,CAC7B,IAAA,CAAK,MAAM,CAAA,CACX,WAAA,CAAY,kDAAkD,CAAA,CAC9D,SAAS,iBAAA,CAAmB,uCAAuC,CAAA,CACnE,MAAA,CACC,4BACA,qDACF,CAAA,CACC,MAAA,CACC,+BAAA,CACA,6DACA,MACF,CAAA,CACC,MAAA,CAAO,WAAA,CAAa,4BAA6B,IAAI,CAAA,CACrD,MAAA,CAAO,iBAAA,CAAmB,6BAA8B,KAAK,CAAA,CAC7D,MAAA,CAAO,aAAA,CAAe,6CAA8C,KAAK,CAAA,CACzE,MAAA,CACC,iBAAA,CACA,4DACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,OAAO,cAAA,CAAgB,cAAA,CAAgB,KAAK,CAAA,CAC5C,OACC,WAAA,CACA,oDAAA,CACA,KACF,CAAA,CACC,OACC,cAAA,CACA,2DACF,CAAA,CACC,MAAA,CAAO,kBAAmB,gCAAA,CAAkC,IAAI,CAAA,CAChE,MAAA,CAAO,qBAAsB,uCAAuC,CAAA,CACpE,MAAA,CAAO,iBAAA,CAAmB,uCAAuC,CAAA,CACjE,MAAA,CAAO,MAAOrH,CAAAA,CAAYsH,CAAAA,GAAS,CAClC,GAAI,CAEEA,EAAK,QAAA,GACPA,CAAAA,CAAK,QAAA,CAAWA,CAAAA,CAAK,UAAY,MAAA,CACjCA,CAAAA,CAAK,SAAA,CAAYA,CAAAA,CAAK,WAAa,SAAA,CAAA,CAGrC,IAAMjT,CAAAA,CAAU2S,EAAAA,CAAkB,MAAM,CACtC,GAAA,CAAKxS,UAAAA,CAAK,OAAA,CAAQ8S,EAAK,GAAG,CAAA,CAC1B,YAAA,CAAc,CAAA,CAAA,CACd,WAAAtH,CAAAA,CACA,GAAGsH,CACL,CAAC,EAOD,GALA,MAAM7D,CAAAA,CAAapP,CAAAA,CAAQ,GAAG,CAAA,CAK1B2L,CAAAA,CAAW,MAAA,CAAS,CAAA,CAAG,CAGzB,IAAIuH,CAAAA,CAAe/G,CAAAA,CAAmB,EAAE,CAAA,CAIlCgH,CAAAA,CAAqBhT,UAAAA,CAAK,OAAA,CAAQH,EAAQ,GAAA,CAAK,iBAAiB,CAAA,CACtE,GAAI4P,EAAQ,UAAA,CAAWuD,CAAkB,CAAA,CAAG,CAC1C,IAAMC,CAAAA,CAAiB,MAAMxD,CAAAA,CAAQ,QAAA,CAASuD,CAAkB,CAAA,CAC1DtR,CAAAA,CAAS0P,CAAAA,CAAgB,OAAA,GAAU,KAAA,CAAM6B,CAAc,CAAA,CAC7DF,CAAAA,CAAe/G,CAAAA,CAAmBtK,CAAM,CAAA,CAKxC6N,EAAAA,CAAiByD,CAAkB,EACrC,CAGA,GAAM,CAAE,OAAQ7B,CAAc,CAAA,CAAI,MAAMV,CAAAA,CACtCjF,EACAuH,CAAAA,CACA,CACE,MAAA,CAAQ,CAAA,CACV,CACF,CAAA,CACAA,CAAAA,CAAe5B,CAAAA,CAGf+B,CAAAA,CAAkC1H,EAAW,CAAC,CAAA,CAAGuH,CAAY,CAAA,CAE7D,GAAM,CAAC1C,CAAI,CAAA,CAAI,MAAM7C,IAAiB,CAAChC,CAAAA,CAAW,CAAC,CAAC,EAAG,CACrD,MAAA,CAAQuH,CACV,CAAC,EACG1C,CAAAA,EAAM,IAAA,GAAS,gBAAA,GAGjBxQ,CAAAA,CAAQ,UAAY,SAAA,CAGpBA,CAAAA,CAAQ,SAAA,CACNwQ,CAAAA,CAAK,UAAY,MAAA,CAAS,CAAA,CAAA,CAAQxQ,CAAAA,CAAQ,SAAA,EAEhD,CAIKA,CAAAA,CAAQ,SAAA,GACXA,CAAAA,CAAQ,SAAA,CAAY,WAGtB,MAAMsT,EAAAA,CAAQtT,CAAO,CAAA,CAErBM,EAAO,GAAA,CACL,CAAA,EAAGC,CAAAA,CAAY,OAAA,CACb,UACF,CAAC,CAAA;AAAA,2BAAA,CACH,CAAA,CAGAwP,EAAAA,CAAiB5P,UAAAA,CAAK,OAAA,CAAQH,EAAQ,GAAA,CAAK,iBAAiB,CAAC,CAAA,CAC7DM,CAAAA,CAAO,KAAA,GACT,CAAA,MAASyE,EAAO,CACdzE,CAAAA,CAAO,KAAA,EAAM,CACb8L,CAAAA,CAAYrH,CAAK,EACnB,CAAA,OAAE,CACAwO,CAAAA,GACF,CACF,CAAC,EAEH,eAAsBD,EAAAA,CACpBtT,GAAAA,CAGA,CACA,IAAIS,CAAAA,CACA+S,CAAAA,CACJ,GAAKxT,GAAAA,CAAQ,aAAA,CAaXS,CAAAA,CAAc,MAAMC,GAAAA,CAAeV,IAAQ,GAAG,CAAA,CAAA,KAbpB,CAC1B,IAAMyT,CAAAA,CAAY,MAAM1T,EAAAA,CAAcC,GAAO,EAC7C,GAAIyT,CAAAA,CAAU,MAAA,CAAc,GAA4B,CAAA,CAAG,CACzD,GAAM,CAAE,YAAAlF,CAAAA,CAAa,QAAA,CAAAN,CAAS,CAAA,CAAI,MAAMD,EAAAA,CAAchO,GAAO,CAAA,CACxDuO,CAAAA,EACH,QAAQ,IAAA,CAAK,CAAC,CAAA,CAEhBvO,GAAAA,CAAQ,GAAA,CAAMuO,CAAAA,CACdvO,GAAAA,CAAQ,YAAA,CAAe,KACvBwT,CAAAA,CAAqBvF,EACvB,CACAxN,CAAAA,CAAcgT,CAAAA,CAAU,YAC1B,CAIA,GAAID,IAAuB,eAAA,CACzB,OAAAxT,GAAAA,CAAQ,GAAA,CAAMG,UAAAA,CAAK,OAAA,CAAQH,GAAAA,CAAQ,GAAA,CAAK,UAAU,CAAA,CAC3C,MAAM0T,CAAAA,CAAU1T,GAAAA,CAAQ,GAAG,CAAA,CAGpC,IAAM2T,GAAAA,CAAgB,MAAMC,IAAiB5T,GAAAA,CAAQ,GAAA,CAAKS,CAAW,CAAA,CAEjEoB,CAAAA,CAAS8R,GAAAA,CACT,MAAME,EAAAA,CAAuBF,IAAe3T,GAAO,CAAA,CACnD,MAAM8T,EAAAA,CAAgB,MAAMJ,CAAAA,CAAU1T,GAAAA,CAAQ,GAAG,CAAC,CAAA,CAEtD,GAAI,CAACA,GAAAA,CAAQ,GAAA,CAAK,CAChB,GAAM,CAAE,QAAA+T,CAAQ,CAAA,CAAI,MAAMjK,EAAAA,CAAQ,CAChC,IAAA,CAAM,SAAA,CACN,IAAA,CAAM,SAAA,CACN,QAAS,CAAA,uBAAA,EAA0BvJ,CAAAA,CAAY,IAAA,CAC7C,iBACF,CAAC,CAAA,UAAA,CAAA,CACD,OAAA,CAAS,IACX,CAAC,CAAA,CAEIwT,CAAAA,EACH,OAAA,CAAQ,IAAA,CAAK,CAAC,EAElB,CAGA,IAAMpI,EAAa,CAKjB,GAAI3L,GAAAA,CAAQ,SAAA,CAAY,CAAC,OAAO,CAAA,CAAI,GACpC,GAAIA,GAAAA,CAAQ,UAAA,EAAc,EAC5B,CAAA,CAGMgU,CAAAA,CAAwB,MAAMC,CAAAA,CAAmBjU,IAAQ,GAAA,CAAK6B,CAAM,CAAA,CACpE,CAAE,MAAA,CAAQqS,CAAqB,CAAA,CAAI,MAAMtD,EAC7CjF,CAAAA,CACAqI,CAAAA,CACA,CACE,MAAA,CAAQ,IACV,CACF,CAAA,CAGIE,CAAAA,CAAqB,aACvBrS,CAAAA,CAAO,UAAA,CAAaqS,CAAAA,CAAqB,UAAA,CAAA,CAG3C,IAAMC,CAAAA,CAAmB9T,CAAAA,CAAQ,0BAA0B,EAAE,KAAA,EAAM,CAC7DU,CAAAA,CAAaZ,UAAAA,CAAK,QAAQH,GAAAA,CAAQ,GAAA,CAAK,iBAAiB,CAAA,CACxD6P,EAAa,CAAA,EAAG9O,CAAU,CAAA,EAAG0O,EAAkB,CAAA,CAAA,CAGrD,GAAI,CAACzP,GAAAA,CAAQ,OAAS4P,CAAAA,CAAQ,UAAA,CAAWC,CAAU,CAAA,CAAG,CACpD,IAAMuD,CAAAA,CAAiB,MAAMxD,EAAQ,QAAA,CAASC,CAAU,CAAA,CAGlD,CAAE,UAAA,CAAAuE,CAAAA,CAAY,GAAGC,CAAO,EAAIC,EAAAA,CAAUlB,CAAAA,CAAgBvR,CAAM,CAAA,CAClEA,EAAS,CAAE,GAAGwS,CAAAA,CAAQ,UAAA,CAAAD,CAAW,EACnC,CAIAvS,CAAAA,CAAO,UAAA,CAAa,MAAA,CAAO,WAAA,CACzB,MAAA,CAAO,OAAA,CAAQA,EAAO,UAAA,EAAc,EAAE,CAAA,CAAE,OACtC,CAAC,CAAC6E,CAAG,CAAA,GAAM,CAAC,MAAA,CAAO,IAAA,CAAK6J,CAAkB,CAAA,CAAE,QAAA,CAAS7J,CAAG,CAC1D,CACF,EAGA,MAAMxG,QAAAA,CAAG,SAAA,CAAUa,CAAAA,CAAY,GAAG,IAAA,CAAK,SAAA,CAAUc,CAAAA,CAAQ,IAAA,CAAM,CAAC,CAAC;AAAA,CAAA,CAAM,MAAM,CAAA,CAC7EsS,CAAAA,CAAiB,OAAA,GAGjB,IAAMI,CAAAA,CAAa,MAAMN,CAAAA,CAAmBjU,GAAAA,CAAQ,GAAA,CAAK6B,CAAM,CAAA,CAC/D,aAAM6J,EAAAA,CAAcC,CAAAA,CAAY4I,CAAAA,CAAY,CAE1C,SAAA,CAAW,IAAA,CACX,MAAA,CAAQvU,GAAAA,CAAQ,OAChB,SAAA,CAAWA,GAAAA,CAAQ,SAAA,CACnB,YAAA,CACEA,IAAQ,YAAA,EAAgBS,CAAAA,EAAa,SAAA,CAAU,IAAA,GAAS,UAC5D,CAAC,CAAA,CAIGT,GAAAA,CAAQ,YAAA,EAAgBA,GAAAA,CAAQ,MAAA,EAClC,MAAMwR,EAAAA,CACJ,CAAC,gCAAgC,CAAA,CACjC+C,CAAAA,CACA,CACE,OAAQvU,GAAAA,CAAQ,MAClB,CACF,CAAA,CAGKuU,CACT,CAEA,eAAeT,EAAAA,CAAgBU,CAAAA,CAA+B,IAAA,CAAM,CAClE,GAAM,CAACC,IAAQC,CAAU,CAAA,CAAI,MAAM,OAAA,CAAQ,IAAI,CAC7CC,EAAAA,EAAkB,CAClBC,EAAAA,EACF,CAAC,CAAA,CAEDtU,CAAAA,CAAO,IAAA,CAAK,EAAE,CAAA,CACd,IAAMN,CAAAA,CAAU,MAAM8J,EAAAA,CAAQ,CAC5B,CACE,IAAA,CAAM,QAAA,CACN,IAAA,CAAM,YAAA,CACN,OAAA,CAAS,yBAAyBvJ,CAAAA,CAAY,IAAA,CAC5C,YACF,CAAC,CAAA,eAAA,CAAA,CACD,OAAA,CAASiU,CAAAA,EAAe,GAAA,EAAO,KAC/B,MAAA,CAAQ,KAAA,CACR,QAAA,CAAU,IACZ,EACA,CACE,IAAA,CAAM,QAAA,CACN,IAAA,CAAM,QACN,OAAA,CAAS,CAAA,MAAA,EAASjU,CAAAA,CAAY,IAAA,CAAK,OAAO,CAAC,CAAA,uBAAA,CAAA,CAC3C,OAAA,CAASkU,IAAO,GAAA,CAAKI,CAAAA,GAAW,CAC9B,KAAA,CAAOA,EAAM,KAAA,CACb,KAAA,CAAOA,CAAAA,CAAM,IACf,EAAE,CACJ,CAAA,CACA,CACE,IAAA,CAAM,QAAA,CACN,IAAA,CAAM,mBAAA,CACN,OAAA,CAAS,4CAA4CtU,CAAAA,CAAY,IAAA,CAC/D,YACF,CAAC,IACD,OAAA,CAASmU,CAAAA,CAAW,GAAA,CAAK5B,CAAAA,GAAW,CAClC,KAAA,CAAOA,CAAAA,CAAM,KAAA,CACb,KAAA,CAAOA,CAAAA,CAAM,IACf,CAAA,CAAE,CACJ,EACA,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAM,aAAA,CACN,OAAA,CAAS,CAAA,cAAA,EAAiBvS,CAAAA,CAAY,KAAK,YAAY,CAAC,CAAA,MAAA,CAAA,CACxD,OAAA,CAASiU,CAAAA,EAAe,QAAA,CAAS,GAAA,EAAOM,CAC1C,EACA,CACE,IAAA,CAAM,QAAA,CACN,IAAA,CAAM,uBACN,OAAA,CAAS,CAAA,sBAAA,EAAyBvU,CAAAA,CAAY,IAAA,CAC5C,eACF,CAAC,CAAA,aAAA,CAAA,CACD,OAAA,CAASiU,CAAAA,EAAe,QAAA,CAAS,YAAA,EAAgB,IAAA,CACjD,MAAA,CAAQ,MACR,QAAA,CAAU,IACZ,CAAA,CACA,CACE,KAAM,MAAA,CACN,IAAA,CAAM,gBAAA,CACN,OAAA,CAAS,0BAA0BjU,CAAAA,CAAY,IAAA,CAC7C,yBACF,CAAC,CAAA,sBAAA,CAAA,CACD,OAAA,CAAS,EACX,CAAA,CACA,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAM,gBAAA,CACN,QAAS,CAAA,cAAA,EAAiBA,CAAAA,CAAY,IAAA,CACpC,oBACF,CAAC,CAAA,SAAA,CAAA,CACD,OAAA,CAASiU,CAAAA,EAAe,QAAA,CAAS,MAAA,EAAUO,CAC7C,CAAA,CACA,CACE,KAAM,MAAA,CACN,IAAA,CAAM,YAAA,CACN,OAAA,CAAS,kCAAkCxU,CAAAA,CAAY,IAAA,CACrD,YACF,CAAC,IACD,OAAA,CAASiU,CAAAA,EAAe,OAAA,CAAQ,UAAA,EAAiBQ,CACnD,CAAA,CACA,CACE,IAAA,CAAM,OACN,IAAA,CAAM,OAAA,CACN,OAAA,CAAS,CAAA,+BAAA,EAAkCzU,EAAY,IAAA,CAAK,OAAO,CAAC,CAAA,CAAA,CAAA,CACpE,QAASiU,CAAAA,EAAe,OAAA,CAAQ,KAAA,EAAYS,CAC9C,CAAA,CACA,CACE,IAAA,CAAM,QAAA,CACN,KAAM,KAAA,CACN,OAAA,CAAS,CAAA,cAAA,EAAiB1U,CAAAA,CAAY,KAAK,yBAAyB,CAAC,CAAA,CAAA,CAAA,CACrE,OAAA,CAASiU,GAAe,GAAA,EAAO,IAAA,CAC/B,MAAA,CAAQ,KAAA,CACR,QAAA,CAAU,IACZ,CACF,CAAC,EAED,OAAOjD,CAAAA,CAAgB,KAAA,CAAM,CAC3B,QAAS,mCAAA,CACT,KAAA,CAAOvR,CAAAA,CAAQ,KAAA,CACf,SAAU,CACR,MAAA,CAAQA,CAAAA,CAAQ,cAAA,CAChB,GAAA,CAAKA,CAAAA,CAAQ,WAAA,CACb,SAAA,CAAWA,EAAQ,iBAAA,CACnB,YAAA,CAAcA,CAAAA,CAAQ,oBAAA,CACtB,MAAA,CAAQA,CAAAA,CAAQ,cAClB,CAAA,CACA,IAAKA,CAAAA,CAAQ,GAAA,CACb,GAAA,CAAKA,CAAAA,CAAQ,UAAA,CACb,OAAA,CAAS,CACP,KAAA,CAAOA,EAAQ,KAAA,CACf,UAAA,CAAYA,CAAAA,CAAQ,UAAA,CAEpB,IAAKA,CAAAA,CAAQ,UAAA,CAAW,OAAA,CAAQ,eAAA,CAAiB,KAAK,CAAA,CACtD,KAAA,CAAOA,CAAAA,CAAQ,UAAA,CAAW,OAAA,CAAQ,eAAA,CAAiB,OAAO,CAC5D,CACF,CAAC,CACH,CAEA,eAAe6T,GACbW,CAAAA,CACAvB,CAAAA,CACA,CACA,IAAI4B,IAAQL,CAAAA,CAAc,KAAA,CACtBU,CAAAA,CAAYjC,CAAAA,CAAK,SAAA,CACjBkC,CAAAA,CAAeX,CAAAA,CAAc,QAAA,CAAS,aAE1C,GAAI,CAACvB,CAAAA,CAAK,QAAA,CAAU,CAClB,GAAM,CAACwB,CAAAA,CAAQC,CAAAA,CAAYtO,CAAe,CAAA,CAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CAC9DuO,EAAAA,EAAkB,CAClBC,EAAAA,GACAtI,CAAAA,CAAoCkI,CAAa,CACnD,CAAC,EAEKxU,CAAAA,CAAU,MAAM8J,EAAAA,CAAQ,CAC5B,CACE,IAAA,CAAM1D,CAAAA,GAAoB,IAAA,CAAO,IAAA,CAAO,QAAA,CACxC,IAAA,CAAM,OAAA,CACN,OAAA,CAAS,SAAS7F,CAAAA,CAAY,IAAA,CAAK,OAAO,CAAC,0BAC3C,OAAA,CAASkU,CAAAA,CAAO,GAAA,CAAKI,CAAAA,GAAW,CAC9B,KAAA,CACEA,CAAAA,CAAM,IAAA,GAAS,UAAA,CAAa,wBAAA,CAA2BA,CAAAA,CAAM,KAAA,CAC/D,KAAA,CAAOA,EAAM,IACf,CAAA,CAAE,CAAA,CACF,OAAA,CAAS,CACX,CAAA,CACA,CACE,IAAA,CAAM5B,CAAAA,CAAK,UAAY,IAAA,CAAO,QAAA,CAC9B,IAAA,CAAM,mBAAA,CACN,OAAA,CAAS,CAAA,yCAAA,EAA4C1S,CAAAA,CAAY,IAAA,CAC/D,YACF,CAAC,CAAA,CAAA,CAAA,CACD,OAAA,CAASmU,CAAAA,CAAW,IAAK5B,CAAAA,GAAW,CAClC,KAAA,CAAOA,CAAAA,CAAM,MACb,KAAA,CAAOA,CAAAA,CAAM,IACf,CAAA,CAAE,CACJ,CACF,CAAC,CAAA,CAED+B,IAAQ7U,CAAAA,CAAQ,KAAA,EAAS,UAAA,CACzBkV,CAAAA,CAAYlV,CAAAA,CAAQ,iBAAA,EAAqBkV,CAAAA,CACzCC,CAAAA,CAAelC,EAAK,aACtB,CAEA,OAAO1B,CAAAA,CAAgB,KAAA,CAAM,CAC3B,OAAA,CAASiD,CAAAA,EAAe,QACxB,KAAA,CAAAK,GAAAA,CACA,QAAA,CAAU,CACR,GAAGL,CAAAA,EAAe,QAAA,CAClB,SAAA,CAAAU,CAAAA,CACA,aAAAC,CACF,CAAA,CACA,GAAA,CAAKX,CAAAA,EAAe,GAAA,CACpB,GAAA,CAAKA,CAAAA,EAAe,GAAA,CACpB,YAAaA,CAAAA,EAAe,WAAA,CAC5B,OAAA,CAASA,CAAAA,EAAe,OAC1B,CAAC,CACH,CCzgBA,eAAsBY,EAAAA,CAAapV,CAAAA,CAA2C,CAC5E,IAAMC,CAAAA,CAAkC,EAAC,CAIzC,GACE,CAACC,CAAAA,CAAG,UAAA,CAAWF,CAAAA,CAAQ,GAAG,GAC1B,CAACE,CAAAA,CAAG,UAAA,CAAWC,UAAAA,CAAK,OAAA,CAAQH,CAAAA,CAAQ,GAAA,CAAK,cAAc,CAAC,CAAA,CAExD,OAAAC,CAAAA,CAAc,GAA4B,EAAI,IAAA,CACvC,CACL,MAAA,CAAAA,CAAAA,CACA,OAAQ,IACV,CAAA,CAIF,GAAI,CAACC,CAAAA,CAAG,UAAA,CAAWC,UAAAA,CAAK,OAAA,CAAQH,EAAQ,GAAA,CAAK,iBAAiB,CAAC,CAAA,CAC7D,OAAAC,CAAAA,CAAc,GAAc,CAAA,CAAI,IAAA,CACzB,CACL,MAAA,CAAAA,CAAAA,CACA,MAAA,CAAQ,IACV,CAAA,CAGF,GAAI,CACF,IAAM4B,EAAS,MAAM6R,CAAAA,CAAU1T,CAAAA,CAAQ,GAAG,EAE1C,OAAO,CACL,MAAA,CAAAC,CAAAA,CACA,OAAQ4B,CACV,CACF,CAAA,KAAgB,CACdvB,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,MACL,CAAA,WAAA,EAAcC,CAAAA,CAAY,IAAA,CACxB,iBACF,CAAC,CAAA,mBAAA,EAAsBA,CAAAA,CAAY,IAAA,CACjCP,CAAAA,CAAQ,GACV,CAAC,CAAA;AAAA,uDAAA,EAA6DO,CAAAA,CAAY,KACxE,iBACF,CAAC,wBAAwBA,CAAAA,CAAY,IAAA,CAAK,MAAM,CAAC,CAAA,SAAA,CACnD,EACAD,CAAAA,CAAO,KAAA,CACL,iBAAiBC,CAAAA,CAAY,IAAA,CAC3B,4CACF,CAAC,CAAA,CAAA,CACH,EACAD,CAAAA,CAAO,KAAA,GACP,OAAA,CAAQ,IAAA,CAAK,CAAC,EAChB,CACF,CCvDA,eAAsB+U,EAAAA,CAAexH,EAAmBhM,CAAAA,CAAgB,CACtE,IAAMyT,CAAAA,CAAYnV,UAAAA,CAAK,KAAK0B,CAAAA,CAAO,aAAA,CAAc,IAAK,cAAc,CAAA,CAEpE,GAAI,CAAA,CAAE,MAAM3B,WAAG,IAAA,CAAKoV,CAAS,GAAG,MAAA,EAAO,CACrC,OAGF,GAAM,CAACC,CAAY,CAAA,CAAI,MAAM5H,IAAiB,CAACE,CAAS,EAAG,CAAE,MAAA,CAAAhM,CAAO,CAAC,CAAA,CACrE,GACE,CAAC0T,CAAAA,EAAc,MAAM,eAAA,EACrB,CAACA,GAAc,IAAA,EAAM,eAAA,CAErB,OAIF,IAAM9D,CAAAA,CAAU,YAAY8D,CAAAA,EAAc,IAAA,EAAM,eAAe,CAAA,SAAA,EAAYA,CAAAA,CAAa,KAAK,eAAe,CAAA;;AAAA;AAAA,UAAA,EAAoDA,CAAAA,EAAc,MAAM,eAAe,CAAA;AAAA,CAAA,CAAA,CACnM,MAAMrV,UAAAA,CAAG,SAAA,CAAUoV,CAAAA,CAAW7D,EAAS,MAAM,EAC/C,CCDO,IAAM+D,EAAAA,CAAmB9M,GAAAA,CAAE,OAAO,CACvC,UAAA,CAAYA,GAAAA,CAAE,KAAA,CAAMA,GAAAA,CAAE,MAAA,EAAQ,CAAA,CAAE,UAAS,CACzC,GAAA,CAAKA,GAAAA,CAAE,OAAA,EAAQ,CACf,SAAA,CAAWA,GAAAA,CAAE,OAAA,GACb,GAAA,CAAKA,GAAAA,CAAE,MAAA,EAAO,CACd,GAAA,CAAKA,GAAAA,CAAE,OAAA,EAAQ,CACf,KAAMA,GAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC1B,MAAA,CAAQA,GAAAA,CAAE,OAAA,GACV,MAAA,CAAQA,GAAAA,CAAE,OAAA,EAAQ,CAAE,QAAA,EAAS,CAC7B,YAAA,CAAcA,GAAAA,CAAE,SAClB,CAAC,CAAA,CAEY+M,EAAAA,CAAM,IAAIzC,OAAAA,EAAQ,CAC5B,IAAA,CAAK,KAAK,CAAA,CACV,WAAA,CAAY,iCAAiC,CAAA,CAC7C,QAAA,CAAS,iBAAA,CAAmB,uCAAuC,CAAA,CACnE,OAAO,WAAA,CAAa,2BAAA,CAA6B,KAAK,CAAA,CACtD,MAAA,CAAO,iBAAA,CAAmB,2BAAA,CAA6B,KAAK,EAC5D,MAAA,CACC,iBAAA,CACA,2DAAA,CACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,MAAA,CAAO,YAAa,8BAAA,CAAgC,KAAK,CAAA,CACzD,MAAA,CAAO,mBAAA,CAAqB,mCAAmC,CAAA,CAC/D,MAAA,CAAO,eAAgB,cAAA,CAAgB,KAAK,CAAA,CAC5C,MAAA,CACC,WAAA,CACA,oDAAA,CACA,KACF,CAAA,CACC,OACC,cAAA,CACA,2DACF,CAAA,CACC,MAAA,CAAO,iBAAA,CAAmB,gCAAA,CAAkC,IAAI,CAAA,CAChE,OAAO,oBAAA,CAAsB,uCAAuC,CAAA,CACpE,MAAA,CAAO,MAAOrH,CAAAA,CAAYsH,CAAAA,GAAS,CAClC,GAAI,CACF,IAAMjT,CAAAA,CAAUwV,EAAAA,CAAiB,KAAA,CAAM,CACrC,UAAA,CAAA7J,CAAAA,CACA,GAAA,CAAKxL,WAAK,OAAA,CAAQ8S,CAAAA,CAAK,GAAG,CAAA,CAC1B,GAAGA,CACL,CAAC,CAAA,CAED,MAAM7D,CAAAA,CAAapP,CAAAA,CAAQ,GAAG,CAAA,CAE9B,IAAI0V,GAAAA,CAAgB,MAAMhC,CAAAA,CAAU1T,EAAQ,GAAG,CAAA,CAC1C0V,GAAAA,GACHA,GAAAA,CAAgBC,CAAAA,CAAa,CAC3B,KAAA,CAAO,UAAA,CACP,cAAe,CACb,GAAA,CAAK3V,CAAAA,CAAQ,GACf,CACF,CAAC,CAAA,CAAA,CAGH,IAAI4V,EAAmB,CAAA,CAAA,CACvB,GAAIjK,CAAAA,CAAW,MAAA,CAAS,CAAA,CAAG,CACzB,GAAM,CAAE,OAAQ2F,CAAAA,CAAe,aAAA,CAAAuE,CAAc,CAAA,CAC3C,MAAMjF,CAAAA,CAAyBjF,CAAAA,CAAY+J,GAAAA,CAAe,CACxD,MAAA,CAAQ1V,CAAAA,CAAQ,MAAA,CAChB,SAAA,CAAW,EACb,CAAC,CAAA,CACH0V,GAAAA,CAAgBpE,CAAAA,CAChBsE,EAAmBC,CAAAA,CAAc,MAAA,CAAS,EAC5C,CAEA,GAAIlK,CAAAA,CAAW,MAAA,CAAS,CAAA,CAAG,CACzB,GAAM,CAAC4J,CAAY,CAAA,CAAI,MAAM5H,GAAAA,CAAiB,CAAChC,CAAAA,CAAW,CAAC,CAAC,CAAA,CAAG,CAC7D,MAAA,CAAQ+J,GACV,CAAC,CAAA,CACKI,CAAAA,CAAWP,GAAc,IAAA,CAE/B,GAAIQ,CAAAA,CAAwBR,CAAY,CAAA,CAAG,CACzC,MAAM7J,EAAAA,CAAcC,EAAY+J,GAAAA,CAAe1V,CAAO,CAAA,CACtD,MACF,CAEA,GACE,CAACA,CAAAA,CAAQ,MACR8V,CAAAA,GAAa,gBAAA,EAAoBA,CAAAA,GAAa,gBAAA,CAAA,CAC/C,CACAxV,CAAAA,CAAO,KAAA,EAAM,CACb,GAAM,CAAE,OAAA,CAAA0V,CAAQ,CAAA,CAAI,MAAMlM,EAAAA,CAAQ,CAChC,IAAA,CAAM,UACN,IAAA,CAAM,SAAA,CACN,OAAA,CAASvJ,CAAAA,CAAY,KACnB,CAAA,+BAAA,EAAkCuV,CAAAA,CAAS,OAAA,CACzC,WAAA,CACA,EACF,CAAC,CAAA;AAAA,oEAAA,CACH,CACF,CAAC,CAAA,CACIE,IACH1V,CAAAA,CAAO,KAAA,GACPA,CAAAA,CAAO,GAAA,CAAI,yBAAyB,CAAA,CACpCA,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,KAAK,CAAC,CAAA,EAElB,CACF,CAOA,GALKN,EAAQ,UAAA,EAAY,MAAA,GACvBA,EAAQ,UAAA,CAAa,MAAMiW,GAA4BjW,CAAO,CAAA,CAAA,CAAA,CAG5C,MAAMU,GAAAA,CAAeV,CAAAA,CAAQ,GAAG,CAAA,GACnC,eAAA,GAAoB,KAAM,CACzC,IAAMkW,EAAuBC,CAAAA,CAAsB,MAAA,CAAQtI,GACzD7N,CAAAA,CAAQ,UAAA,EAAY,QAAA,CAAS6N,CAAAA,CAAU,IAAI,CAC7C,CAAA,CAEIqI,GAAsB,MAAA,GACxB5V,CAAAA,CAAO,OAAM,CACb4V,CAAAA,CAAqB,QAASrI,CAAAA,EAAc,CAC1CvN,EAAO,IAAA,CAAKC,CAAAA,CAAY,KAAKsN,CAAAA,CAAU,OAAO,CAAC,EACjD,CAAC,EACDvN,CAAAA,CAAO,KAAA,GACP,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,EAElB,CAEA,GAAI,CAAE,MAAA,CAAAL,EAAQ,MAAA,CAAA4B,GAAO,EAAI,MAAMuT,EAAAA,CAAapV,CAAO,CAAA,CAG/CoW,GAAAA,CAAa,GACjB,GAAInW,CAAAA,CAAc,GAAc,CAAA,CAAG,CACjC,GAAM,CAAE,QAAA8T,CAAQ,CAAA,CAAI,MAAMjK,EAAAA,CAAQ,CAChC,KAAM,SAAA,CACN,IAAA,CAAM,UACN,OAAA,CAAS,CAAA,qBAAA,EAAwBvJ,EAAY,IAAA,CAC3C,iBACF,CAAC,CAAA,iCAAA,CAAA,CACD,OAAA,CAAS,EACX,CAAC,CAAA,CAEIwT,IACHzT,CAAAA,CAAO,KAAA,GACP,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhBuB,GAAAA,CAAS,MAAMyR,EAAAA,CAAQ,CACrB,IAAKtT,CAAAA,CAAQ,GAAA,CACb,IAAK,CAAA,CAAA,CACL,KAAA,CAAO,GACP,QAAA,CAAU,CAAA,CAAA,CACV,cAAe,CAAA,CAAA,CACf,MAAA,CAAQA,EAAQ,MAAA,EAAU,CAAC4V,EAC3B,YAAA,CAAc,CAAA,CAAA,CACd,OAAQ5V,CAAAA,CAAQ,MAAA,CAChB,aAAcA,CAAAA,CAAQ,YAAA,CACtB,UAAW,CAAA,CAAA,CACX,UAAA,CAAYA,EAAQ,UACtB,CAAC,EACDoW,GAAAA,CAAa,CAAA,EACf,CAEA,IAAIC,CAAAA,CAAuB,GAE3B,GAAIpW,CAAAA,CAAc,GAA4B,CAAA,CAAG,CAC/C,GAAM,CAAE,WAAA,CAAAsO,EAAa,QAAA,CAAAN,CAAS,EAAI,MAAMD,EAAAA,CAAc,CACpD,GAAA,CAAKhO,CAAAA,CAAQ,IACb,KAAA,CAAOA,CAAAA,CAAQ,SAAA,CACf,MAAA,CAAQA,EAAQ,MAAA,CAChB,UAAA,CAAYA,EAAQ,UACtB,CAAC,EACIuO,CAAAA,GACHjO,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,KAAK,CAAC,CAAA,CAAA,CAEhBN,EAAQ,GAAA,CAAMuO,CAAAA,CAEVN,IAAa,eAAA,EACfjO,CAAAA,CAAQ,IAAMG,UAAAA,CAAK,OAAA,CAAQH,EAAQ,GAAA,CAAK,UAAU,EAClD6B,GAAAA,CAAS,MAAM6R,EAAU1T,CAAAA,CAAQ,GAAG,IAEpC6B,GAAAA,CAAS,MAAMyR,GAAQ,CACrB,GAAA,CAAKtT,EAAQ,GAAA,CACb,GAAA,CAAK,GACL,KAAA,CAAO,CAAA,CAAA,CACP,SAAU,CAAA,CAAA,CACV,aAAA,CAAe,GACf,MAAA,CAAQ,CAAC4V,GAAoB5V,CAAAA,CAAQ,MAAA,CACrC,aAAc,CAAA,CAAA,CACd,MAAA,CAAQA,EAAQ,MAAA,CAChB,YAAA,CAAcA,EAAQ,YAAA,CACtB,SAAA,CAAW,GACX,UAAA,CAAYA,CAAAA,CAAQ,UACtB,CAAC,CAAA,CACDoW,IAAa,CAAA,CAAA,CAEbC,CAAAA,CACErW,EAAQ,UAAA,EAAY,MAAA,GAAW,GAC/B,CAAC,CAACA,EAAQ,UAAA,CAAW,CAAC,EAAE,KAAA,CAAM,aAAa,GAEjD,CAEA,GAAI,CAAC6B,GAAAA,CACH,MAAM,IAAI,KAAA,CACR,CAAA,yBAAA,EAA4BtB,CAAAA,CAAY,IAAA,CAAKP,EAAQ,GAAG,CAAC,GAC3D,CAAA,CAGF,GAAM,CAAE,MAAA,CAAQsR,CAAc,EAAI,MAAMV,CAAAA,CACtC5Q,EAAQ,UAAA,CACR6B,GAAAA,CACA,CACE,MAAA,CAAQ7B,CAAAA,CAAQ,QAAU4V,CAC5B,CACF,EACA/T,GAAAA,CAASyP,CAAAA,CAEJ8E,KACH,MAAM1K,EAAAA,CAAc1L,EAAQ,UAAA,CAAY6B,GAAAA,CAAQ7B,CAAO,CAAA,CAKrDqW,CAAAA,EACF,MAAMhB,EAAAA,CAAerV,CAAAA,CAAQ,WAAW,CAAC,CAAA,CAAG6B,GAAM,EAEtD,CAAA,MAASkD,EAAO,CACdzE,CAAAA,CAAO,OAAM,CACb8L,CAAAA,CAAYrH,CAAK,EACnB,CAAA,OAAE,CACAwO,CAAAA,GACF,CACF,CAAC,CAAA,CAEH,eAAe0C,EAAAA,CACbjW,CAAAA,CACA,CACA,IAAM8Q,CAAAA,CAAgB,MAAMwF,EAAAA,EAAuB,CACnD,GAAI,CAACxF,CAAAA,CACH,OAAAxQ,CAAAA,CAAO,KAAA,GACP8L,CAAAA,CAAY,IAAI,MAAM,iCAAiC,CAAC,EACjD,EAAC,CAGV,GAAIpM,CAAAA,CAAQ,GAAA,CACV,OAAO8Q,CAAAA,CACJ,GAAA,CAAKyF,GAAUA,CAAAA,CAAM,IAAI,EACzB,MAAA,CACE1I,CAAAA,EAAc,CAACsI,CAAAA,CAAsB,KAAMK,CAAAA,EAAMA,CAAAA,CAAE,OAAS3I,CAAS,CACxE,EAGJ,GAAI7N,CAAAA,CAAQ,YAAY,MAAA,CACtB,OAAOA,EAAQ,UAAA,CAGjB,GAAM,CAAE,UAAA,CAAA2L,CAAW,EAAI,MAAM7B,EAAAA,CAAQ,CACnC,IAAA,CAAM,aAAA,CACN,KAAM,YAAA,CACN,OAAA,CAAS,0CACT,IAAA,CAAM,oDAAA,CACN,aAAc,KAAA,CACd,OAAA,CAASgH,EACN,MAAA,CACEyF,CAAAA,EACCA,EAAM,IAAA,GAAS,aAAA,EACf,CAACJ,CAAAA,CAAsB,IAAA,CACpBtI,GAAcA,CAAAA,CAAU,IAAA,GAAS0I,EAAM,IAC1C,CACJ,EACC,GAAA,CAAKA,CAAAA,GAAW,CACf,KAAA,CAAOA,CAAAA,CAAM,KACb,KAAA,CAAOA,CAAAA,CAAM,KACb,QAAA,CAAUvW,CAAAA,CAAQ,IAAM,IAAA,CAAOA,CAAAA,CAAQ,YAAY,QAAA,CAASuW,CAAAA,CAAM,IAAI,CACxE,CAAA,CAAE,CACN,CAAC,CAAA,CAEI5K,GAAY,MAAA,GACfrL,CAAAA,CAAO,KAAK,kCAAkC,CAAA,CAC9CA,EAAO,IAAA,CAAK,EAAE,EACd,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhB,IAAMiC,EAASmG,GAAAA,CAAE,KAAA,CAAMA,IAAE,MAAA,EAAQ,CAAA,CAAE,SAAA,CAAUiD,CAAU,CAAA,CACvD,OAAKpJ,EAAO,OAAA,CAKLA,CAAAA,CAAO,MAJZjC,CAAAA,CAAO,KAAA,CAAM,EAAE,CAAA,CACf8L,CAAAA,CAAY,IAAI,KAAA,CAAM,yCAAyC,CAAC,CAAA,CACzD,GAGX,CC5SA,eAAsBqK,GACpBzW,CAAAA,CACA,CACA,IAAMC,CAAAA,CAAkC,GAElCyW,CAAAA,CAAe,CACnB,IAAK1W,CAAAA,CAAQ,GAAA,CACb,aAAcG,UAAAA,CAAK,OAAA,CAAQH,EAAQ,GAAA,CAAKA,CAAAA,CAAQ,YAAY,CAAA,CAC5D,SAAA,CAAWG,WAAK,OAAA,CAAQH,CAAAA,CAAQ,IAAKA,CAAAA,CAAQ,SAAS,CACxD,CAAA,CAGA,OAAKE,EAAG,UAAA,CAAWwW,CAAAA,CAAa,YAAY,CAAA,GAC1CzW,CAAAA,CAAc,IAA2B,CAAA,CAAI,IAAA,CAAA,CAI/C,MAAMC,CAAAA,CAAG,KAAA,CAAMwW,EAAa,SAAA,CAAW,CAAE,UAAW,IAAK,CAAC,EAEtD,MAAA,CAAO,IAAA,CAAKzW,CAAM,CAAA,CAAE,MAAA,CAAS,IAC3BA,CAAAA,CAAc,IAA2B,IAC3CK,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,MACL,CAAA,SAAA,EAAYC,CAAAA,CAAY,KACtBmW,CAAAA,CAAa,YACf,CAAC,CAAA,gBAAA,CACH,CAAA,CAAA,CAGFpW,EAAO,KAAA,EAAM,CACb,QAAQ,IAAA,CAAK,CAAC,GAGT,CACL,MAAA,CAAAL,EACA,YAAA,CAAAyW,CACF,CACF,CClCO,IAAMC,EAAAA,CAAqBjO,IAAE,MAAA,CAAO,CACzC,IAAKA,GAAAA,CAAE,MAAA,GACP,YAAA,CAAcA,GAAAA,CAAE,QAAO,CACvB,SAAA,CAAWA,IAAE,MAAA,EACf,CAAC,CAAA,CAEYkO,EAAAA,CAAQ,IAAI5D,OAAAA,EAAQ,CAC9B,KAAK,OAAO,CAAA,CACZ,YAAY,wCAAwC,CAAA,CACpD,SAAS,YAAA,CAAc,4BAAA,CAA8B,iBAAiB,CAAA,CACtE,MAAA,CACC,sBACA,sCAAA,CACA,YACF,EACC,MAAA,CACC,iBAAA,CACA,4DACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,MAAA,CAAO,MAAO3C,GAAAA,CAAkB4C,CAAAA,GAAS,CACxC,GAAI,CACF,IAAMjT,CAAAA,CAAU2W,EAAAA,CAAmB,MAAM,CACvC,GAAA,CAAUE,UAAQ5D,CAAAA,CAAK,GAAG,CAAA,CAC1B,YAAA,CAAc5C,IACd,SAAA,CAAW4C,CAAAA,CAAK,MAClB,CAAC,CAAA,CAEK,CAAE,YAAA,CAAAyD,CAAa,EAAI,MAAMD,EAAAA,CAAezW,CAAO,CAAA,CAC/CyR,CAAAA,CAAU,MAASqF,CAAA,CAAA,QAAA,CAASJ,CAAAA,CAAa,aAAc,OAAO,CAAA,CAE9DnU,EAASwU,GAAAA,CAAe,SAAA,CAAU,KAAK,KAAA,CAAMtF,CAAO,CAAC,CAAA,CAEtDlP,CAAAA,CAAO,UACVjC,CAAAA,CAAO,KAAA,CACL,kCAAkCC,CAAAA,CAAY,IAAA,CAC5CmW,EAAa,YACf,CAAC,GACH,CAAA,CACA,OAAA,CAAQ,KAAK,CAAC,CAAA,CAAA,CAGhB,IAAMM,CAAAA,CAAe3W,CAAAA,CAAQ,sBAAsB,CAAA,CACnD,IAAA,IAAWkV,OAAgBhT,CAAAA,CAAO,IAAA,CAAK,MAAO,CAC5CyU,CAAAA,CAAa,MAAM,CAAA,SAAA,EAAYzB,GAAAA,CAAa,IAAI,CAAA,GAAA,CAAK,CAAA,CAGrDA,IAAa,OAAA,CACX,iDAAA,CAGF,QAAWpI,CAAAA,IAAQoI,GAAAA,CAAa,OAAS,EAAC,CACxCpI,EAAK,OAAA,CAAa,MAAS2J,WACpBD,CAAA,CAAA,OAAA,CAAQH,CAAAA,CAAa,IAAKvJ,CAAAA,CAAK,IAAI,EACxC,OACF,CAAA,CAIF,IAAM5K,CAAAA,CAASqL,GAAAA,CAAmB,UAAU2H,GAAY,CAAA,CACxD,GAAI,CAAChT,CAAAA,CAAO,OAAA,CAAS,CACnBjC,EAAO,KAAA,CACL,CAAA,gCAAA,EAAmCC,EAAY,IAAA,CAC7CgV,GAAAA,CAAa,IACf,CAAC,CAAA,CAAA,CACH,EACA,QACF,CAGA,MAASuB,CAAA,CAAA,SAAA,CACFD,CAAA,CAAA,OAAA,CAAQH,EAAa,SAAA,CAAW,CAAA,EAAGnU,EAAO,IAAA,CAAK,IAAI,OAAO,CAAA,CAC/D,IAAA,CAAK,UAAUA,CAAAA,CAAO,IAAA,CAAM,KAAM,CAAC,CACrC,EACF,CAGA,MAASuU,WACPJ,CAAAA,CAAa,YAAA,CACRG,UAAQH,CAAAA,CAAa,SAAA,CAAW,eAAe,CACtD,CAAA,CAEAM,EAAa,OAAA,CAAQ,oBAAoB,EAC3C,CAAA,MAASjS,CAAAA,CAAO,CACdzE,CAAAA,CAAO,KAAA,GACP8L,CAAAA,CAAYrH,CAAK,EACnB,CACF,CAAC,EChFH,IAAMkS,EAAAA,CAAsBvO,IAAE,MAAA,CAAO,CACnC,UAAWA,GAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC/B,IAAKA,GAAAA,CAAE,OAAA,EAAQ,CACf,GAAA,CAAKA,IAAE,MAAA,EAAO,CACd,KAAMA,GAAAA,CAAE,MAAA,GAAS,QAAA,EACnB,CAAC,CAAA,CAEYwO,EAAAA,CAAO,IAAIlE,OAAAA,EAAQ,CAC7B,KAAK,MAAM,CAAA,CACX,YAAY,wCAAwC,CAAA,CACpD,SAAS,aAAA,CAAe,oBAAoB,EAC5C,MAAA,CAAO,WAAA,CAAa,4BAA6B,KAAK,CAAA,CACtD,OACC,iBAAA,CACA,2DAAA,CACA,QAAQ,GAAA,EACV,EACC,MAAA,CAAO,MAAOlQ,EAAMmQ,CAAAA,GAAS,CAC5B,GAAI,CACF,IAAMjT,EAAUiX,EAAAA,CAAoB,KAAA,CAAM,CACxC,SAAA,CAAWnU,CAAAA,CACX,GAAGmQ,CACL,CAAC,EAEKjS,GAAAA,CAAMb,UAAAA,CAAK,QAAQH,CAAAA,CAAQ,GAAG,EAE/BgL,UAAAA,CAAWhK,GAAG,IACjBV,CAAAA,CAAO,KAAA,CAAM,YAAYU,GAAG,CAAA,kCAAA,CAAoC,EAChE,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhB,IAAMa,EAAS,MAAM6R,CAAAA,CAAU1S,GAAG,CAAA,CAC7Ba,CAAAA,GACHvB,EAAO,IAAA,CACL,CAAA,qCAAA,EAAwCC,EAAY,OAAA,CAClD,MACF,CAAC,CAAA,kCAAA,CACH,CAAA,CACA,QAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhB,IAAMuQ,EAAgB,MAAMwF,EAAAA,GAO5B,GALKxF,CAAAA,GACH1E,EAAY,IAAI,KAAA,CAAM,iCAAiC,CAAC,CAAA,CACxD,QAAQ,IAAA,CAAK,CAAC,GAGZ,CAACpM,CAAAA,CAAQ,UAAW,CACtB,IAAMmX,EAAYtV,CAAAA,CAAO,aAAA,CAAc,WAGjCuV,CAAAA,CAAoBtG,CAAAA,CAAc,OAAQN,CAAAA,EAAS,CACvD,QAAWrD,CAAAA,IAAQqD,CAAAA,CAAK,OAAS,EAAC,CAAG,CACnC,IAAMb,CAAAA,CAAWxP,WAAK,OAAA,CACpBgX,CAAAA,CACA,OAAOhK,CAAAA,EAAS,QAAA,CAAWA,EAAOA,CAAAA,CAAK,IACzC,EACA,GAAInC,UAAAA,CAAW2E,CAAQ,CAAA,CACrB,OAAO,EAEX,CAEA,OAAO,EACT,CAAC,CAAA,CAGK0H,EAAwB,EAAC,CAC/B,QAAWxJ,CAAAA,IAAauJ,CAAAA,CAAmB,CACzC,IAAME,CAAAA,CAAU,MAAMC,EAAAA,CAAc1J,CAAAA,CAAWhM,CAAM,CAAA,CACjDyV,CAAAA,CAAQ,QACVD,CAAAA,CAAsB,IAAA,CAAK,CACzB,IAAA,CAAMxJ,CAAAA,CAAU,KAChB,OAAA,CAAAyJ,CACF,CAAC,EAEL,CAEKD,EAAsB,MAAA,GACzB/W,CAAAA,CAAO,KAAK,mBAAmB,CAAA,CAC/B,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhBA,CAAAA,CAAO,KAAK,kDAAkD,CAAA,CAC9D,QAAWuN,CAAAA,IAAawJ,CAAAA,CAAuB,CAC7C/W,CAAAA,CAAO,IAAA,CAAK,KAAKuN,CAAAA,CAAU,IAAI,EAAE,CAAA,CACjC,IAAA,IAAW2J,KAAU3J,CAAAA,CAAU,OAAA,CAC7BvN,EAAO,IAAA,CAAK,CAAA,IAAA,EAAOkX,EAAO,QAAQ,CAAA,CAAE,EAExC,CACAlX,CAAAA,CAAO,OAAM,CACbA,CAAAA,CAAO,KACL,CAAA,IAAA,EAAOC,CAAAA,CAAY,QAAQ,kBAAkB,CAAC,sBAChD,CAAA,CACA,OAAA,CAAQ,KAAK,CAAC,EAChB,CAGA,IAAMsN,CAAAA,CAAYiD,EAAc,IAAA,CAC7BN,CAAAA,EAASA,EAAK,IAAA,GAASxQ,CAAAA,CAAQ,SAClC,CAAA,CAEK6N,CAAAA,GACHvN,EAAO,KAAA,CACL,CAAA,cAAA,EAAiBC,EAAY,OAAA,CAC3BP,CAAAA,CAAQ,SACV,CAAC,CAAA,gBAAA,CACH,EACA,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhB,IAAMsX,IAAU,MAAMC,EAAAA,CAAc1J,EAAWhM,CAAM,CAAA,CAEhDyV,IAAQ,MAAA,GACXhX,CAAAA,CAAO,KAAK,CAAA,qBAAA,EAAwBN,CAAAA,CAAQ,SAAS,CAAA,CAAA,CAAG,CAAA,CACxD,QAAQ,IAAA,CAAK,CAAC,GAGhB,IAAA,IAAWwX,CAAAA,IAAUF,GAAAA,CACnBhX,CAAAA,CAAO,KAAK,CAAA,EAAA,EAAKkX,CAAAA,CAAO,QAAQ,CAAA,CAAE,CAAA,CAClC,MAAMC,EAAAA,CAAUD,CAAAA,CAAO,KAAK,CAAA,CAC5BlX,CAAAA,CAAO,KAAK,EAAE,EAElB,OAASyE,CAAAA,CAAO,CACdqH,EAAYrH,CAAK,EACnB,CACF,CAAC,CAAA,CAEH,eAAewS,EAAAA,CACb1J,CAAAA,CACAhM,EACA,CACA,IAAM6V,EAAU,MAAMC,EAAAA,CAAU9V,EAAO,KAAA,CAAO,CAACgM,CAAS,CAAC,CAAA,CACnDqH,EAAY,MAAM0C,EAAAA,CAAqB/V,EAAO,QAAA,CAAS,SAAS,EAEtE,GAAI,CAAC6V,EACH,OAAO,GAGT,IAAMJ,CAAAA,CAAU,EAAC,CAEjB,IAAA,IAAW9G,KAAQkH,CAAAA,CAAS,CAC1B,IAAMP,CAAAA,CAAY,MAAMU,GAAkBhW,CAAAA,CAAQ2O,CAAI,EAEtD,GAAK2G,CAAAA,CAIL,QAAWhK,CAAAA,IAAQqD,CAAAA,CAAK,OAAS,EAAC,CAAG,CACnC,IAAMb,CAAAA,CAAWxP,WAAK,OAAA,CACpBgX,CAAAA,CACA,OAAOhK,CAAAA,EAAS,QAAA,CAAWA,EAAOA,CAAAA,CAAK,IACzC,EAEA,GAAI,CAACnC,WAAW2E,CAAQ,CAAA,CACtB,SAGF,IAAMmI,EAAc,MAAM5X,QAAAA,CAAG,SAASyP,CAAAA,CAAU,MAAM,EAEtD,GAAI,OAAOxC,GAAS,QAAA,EAAY,CAACA,EAAK,OAAA,CACpC,SAGF,IAAM4K,CAAAA,CAAkB,MAAMC,EAAU,CACtC,QAAA,CAAU7K,EAAK,IAAA,CACf,GAAA,CAAKA,EAAK,OAAA,CACV,MAAA,CAAAtL,EACA,SAAA,CAAAqT,CACF,CAAC,CAAA,CAEK+C,CAAAA,CAAQC,UAAUH,CAAAA,CAA2BD,CAAW,EAC1DG,CAAAA,CAAM,MAAA,CAAS,GACjBX,CAAAA,CAAQ,IAAA,CAAK,CACX,QAAA,CAAA3H,CAAAA,CACA,MAAAsI,CACF,CAAC,EAEL,CACF,CAEA,OAAOX,CACT,CAEA,eAAeG,EAAAA,CAAUP,CAAAA,CAAgB,CACvCA,CAAAA,CAAK,OAAA,CAASiB,GAAS,CACrB,GAAIA,EACF,OAAIA,CAAAA,CAAK,MACA,OAAA,CAAQ,MAAA,CAAO,MAAM5X,CAAAA,CAAY,OAAA,CAAQ4X,EAAK,KAAK,CAAC,EAEzDA,CAAAA,CAAK,OAAA,CACA,QAAQ,MAAA,CAAO,KAAA,CAAM5X,EAAY,KAAA,CAAM4X,CAAAA,CAAK,KAAK,CAAC,CAAA,CAGpD,QAAQ,MAAA,CAAO,KAAA,CAAMA,EAAK,KAAK,CAE1C,CAAC,EACH,CCjNO,IAAMC,GAAO,IAAIpF,OAAAA,GACrB,IAAA,CAAK,MAAM,EACX,WAAA,CAAY,oCAAoC,EAChD,MAAA,CACC,iBAAA,CACA,4DACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,MAAA,CAAO,MAAOC,CAAAA,EAAS,CACtB,GAAI,CACF3S,CAAAA,CAAO,KAAK,gBAAgB,CAAA,CAC5B,QAAQ,GAAA,CAAI,MAAMI,IAAeuS,CAAAA,CAAK,GAAG,CAAC,CAAA,CAC1C3S,CAAAA,CAAO,OAAM,CACbA,CAAAA,CAAO,KAAK,mBAAmB,CAAA,CAC/B,QAAQ,GAAA,CAAI,MAAMoT,EAAUT,CAAAA,CAAK,GAAG,CAAC,EACvC,CAAA,MAASlO,EAAO,CACdqH,CAAAA,CAAYrH,CAAK,EACnB,CACF,CAAC,CAAA,CCLH,IAAMsT,EAAAA,CAAqB,SAErBC,EAAAA,CAAU,CACd,CACE,IAAA,CAAM,QAAA,CACN,MAAO,aAAA,CACP,UAAA,CAAY,YACZ,MAAA,CAAQ,CACN,WAAY,CACV,MAAA,CAAQ,CACN,OAAA,CAAS,KAAA,CACT,KAAM,CAAC,CAAA,OAAA,EAAUD,EAAkB,CAAA,CAAA,CAAI,KAAK,CAC9C,CACF,CACF,CACF,CAAA,CACA,CACE,KAAM,QAAA,CACN,KAAA,CAAO,SACP,UAAA,CAAY,kBAAA,CACZ,OAAQ,CACN,UAAA,CAAY,CACV,MAAA,CAAQ,CACN,QAAS,KAAA,CACT,IAAA,CAAM,CAAC,CAAA,OAAA,EAAUA,EAAkB,GAAI,KAAK,CAC9C,CACF,CACF,CACF,EACA,CACE,IAAA,CAAM,SACN,KAAA,CAAO,SAAA,CACP,WAAY,kBAAA,CACZ,MAAA,CAAQ,CACN,OAAA,CAAS,CACP,OAAQ,CACN,OAAA,CAAS,MACT,IAAA,CAAM,CAAC,UAAUA,EAAkB,CAAA,CAAA,CAAI,KAAK,CAC9C,CACF,CACF,CACF,CAAA,CACA,CACE,IAAA,CAAM,OAAA,CACN,MAAO,OAAA,CACP,UAAA,CAAY,qBACZ,MAAA,CAAQ,CAAA;AAAA;AAAA,gBAAA,EAEMA,EAAkB,CAAA;AAAA,CAElC,CACF,CAAA,CAEME,EAAAA,CAAe,CAAC,CAAA,OAAA,EAAUF,EAAkB,CAAA,CAAE,CAAA,CAEvCG,EAAAA,CAAM,IAAIxF,OAAAA,EAAQ,CAC5B,KAAK,KAAK,CAAA,CACV,WAAA,CAAY,uCAAuC,CAAA,CACnD,MAAA,CACC,iBAAA,CACA,2DAAA,CACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,MAAA,CAAO,MAAOhT,CAAAA,EAAY,CACzB,GAAI,CACF,MAAMoP,CAAAA,CAAapP,CAAAA,CAAQ,GAAG,CAAA,CAC9B,IAAMyY,CAAAA,CAAY,IAAIC,oBAAAA,CACtB,MAAMC,GAAAA,CAAO,OAAA,CAAQF,CAAS,EAChC,CAAA,MAAS1T,EAAO,CACdzE,CAAAA,CAAO,KAAA,EAAM,CACb8L,CAAAA,CAAYrH,CAAK,EACnB,CACF,CAAC,CAAA,CAEG6T,EAAAA,CAAuBlQ,EAAAA,CAAE,MAAA,CAAO,CACpC,MAAA,CAAQA,EAAAA,CAAE,KAAK,CAAC,QAAA,CAAU,QAAA,CAAU,QAAA,CAAU,OAAO,CAAC,CAAA,CACtD,GAAA,CAAKA,EAAAA,CAAE,MAAA,EACT,CAAC,CAAA,CAED8P,EAAAA,CACG,OAAA,CAAQ,MAAM,EACd,WAAA,CAAY,8CAA8C,CAAA,CAC1D,MAAA,CACC,mBAAA,CACA,CAAA,YAAA,EAAeF,EAAAA,CAAQ,GAAA,CAAK9B,CAAAA,EAAMA,CAAAA,CAAE,IAAI,CAAA,CAAE,IAAA,CAAK,IAAI,CAAC,CAAA,CAAA,CACtD,EACC,MAAA,CAAO,MAAOvD,GAAAA,CAAM4F,CAAAA,GAAY,CAC/B,GAAI,CAGF,IAAM7X,GAAAA,CAAAA,CADa6X,CAAAA,CAAQ,MAAA,EAAQ,IAAA,EAAK,EAAK,EAAC,EACvB,GAAA,EAAO,QAAQ,GAAA,EAAI,CAEtCC,CAAAA,CAAS7F,GAAAA,CAAK,MAAA,CAElB,GAAI,CAAC6F,CAAAA,CAAQ,CACX,IAAMhK,CAAAA,CAAW,MAAMhF,EAAAA,CAAQ,CAC7B,IAAA,CAAM,QAAA,CACN,KAAM,QAAA,CACN,OAAA,CAAS,iCAAA,CACT,OAAA,CAASwO,EAAAA,CAAQ,GAAA,CAAK9B,CAAAA,GAAO,CAC3B,KAAA,CAAOA,CAAAA,CAAE,KAAA,CACT,KAAA,CAAOA,CAAAA,CAAE,IACX,CAAA,CAAE,CACJ,CAAC,CAAA,CAEI1H,CAAAA,CAAS,MAAA,GACZxO,CAAAA,CAAO,KAAA,EAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhBwY,CAAAA,CAAShK,CAAAA,CAAS,OACpB,CAEA,IAAM9O,CAAAA,CAAU4Y,GAAqB,KAAA,CAAM,CACzC,MAAA,CAAAE,CAAAA,CACA,GAAA,CAAA9X,GACF,CAAC,CAAA,CAEKa,CAAAA,CAAS,MAAM6R,CAAAA,CAAU1T,CAAAA,CAAQ,GAAG,CAAA,CAE1C,GAAIA,CAAAA,CAAQ,SAAW,OAAA,CAAS,CAC9B,GAAI6B,CAAAA,CACF,MAAMwH,CAAAA,CAAmB,EAAC,CAAGkP,EAAAA,CAAc1W,CAAAA,CAAQ,CACjD,MAAA,CAAQ,CAAA,CACV,CAAC,CAAA,CAAA,KACI,CACL,IAAM4H,CAAAA,CAAiB,MAAMS,GAAAA,CAAkBlK,CAAAA,CAAQ,GAAG,CAAA,CACpD+Y,CAAAA,CAAiBtP,CAAAA,GAAmB,KAAA,CAAQ,SAAA,CAAY,KAAA,CACxDuP,CAAAA,CAAUvP,CAAAA,GAAmB,KAAA,CAAQ,YAAA,CAAe,IAAA,CAEpDwP,EAAiB5Y,CAAAA,CAAQ,4BAA4B,CAAA,CAAE,KAAA,EAAM,CACnE,MAAMiK,KAAAA,CACJb,CAAAA,CACA,CAACsP,CAAAA,CAAgBC,CAAAA,CAAS,GAAGT,EAAY,CAAA,CACzC,CACE,GAAA,CAAKvY,EAAQ,GACf,CACF,CAAA,CACAiZ,CAAAA,CAAe,OAAA,CAAQ,0BAA0B,EACnD,CAEA3Y,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,GAAA,CAAI,8CAA8C,CAAA,CACzDA,CAAAA,CAAO,OAAM,CACbA,CAAAA,CAAO,GAAA,CACL,CAAA,2BAAA,EAA8BC,CAAAA,CAAY,IAAA,CACxC,sBACF,CAAC,EACH,CAAA,CACAD,CAAAA,CAAO,GAAA,CAAI,qCAAqC,CAAA,CAChDA,CAAAA,CAAO,GAAA,EAAI,CACXA,EAAO,IAAA,CAAK,CAAA;AAAA;AAAA,gBAAA,EAEF+X,EAAkB,WAAW,CAAA,CACvC/X,CAAAA,CAAO,OAAM,CACbA,CAAAA,CAAO,IAAA,CAAK,yCAAyC,CAAA,CACrDA,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,EAChB,CAEA,IAAM+Q,GAAAA,CAAgBhR,CAAAA,CAAQ,2BAA2B,CAAA,CAAE,KAAA,EAAM,CAC3D6Y,EAAa,MAAMC,EAAAA,CAAWnZ,CAAO,CAAA,CAG3C,GAFAqR,GAAAA,CAAc,QAAQ,yBAAyB,CAAA,CAE3CxP,CAAAA,CACF,MAAMwH,CAAAA,CAAmB,GAAIkP,EAAAA,CAAc1W,CAAAA,CAAQ,CACjD,MAAA,CAAQ,CAAA,CACV,CAAC,OACI,CACL,IAAM4H,CAAAA,CAAiB,MAAMS,GAAAA,CAAkBlK,CAAAA,CAAQ,GAAG,CAAA,CACpD+Y,CAAAA,CAAiBtP,CAAAA,GAAmB,KAAA,CAAQ,SAAA,CAAY,KAAA,CACxDuP,EAAUvP,CAAAA,GAAmB,KAAA,CAAQ,YAAA,CAAe,IAAA,CAEpDwP,CAAAA,CAAiB5Y,CAAAA,CAAQ,4BAA4B,CAAA,CAAE,KAAA,EAAM,CACnE,MAAMiK,KAAAA,CACJb,CAAAA,CACA,CAACsP,CAAAA,CAAgBC,CAAAA,CAAS,GAAGT,EAAY,CAAA,CACzC,CACE,IAAKvY,CAAAA,CAAQ,GACf,CACF,CAAA,CACAiZ,CAAAA,CAAe,OAAA,CAAQ,0BAA0B,EACnD,CAEA3Y,CAAAA,CAAO,KAAA,EAAM,CACbA,CAAAA,CAAO,QAAQ,CAAA,uBAAA,EAA0B4Y,CAAU,CAAA,CAAA,CAAG,CAAA,CACtD5Y,CAAAA,CAAO,KAAA,GACT,CAAA,MAASyE,CAAAA,CAAO,CACdqH,CAAAA,CAAYrH,CAAK,EACnB,CACF,CAAC,CAAA,CAEH,IAAMqU,EAAAA,CAAiB,CAACC,EAAUC,CAAAA,GAAuBA,CAAAA,CAEzD,eAAeH,EAAAA,CAAWnZ,CAAAA,CAA+C,CACvE,GAAM,CAAE,MAAA,CAAA8Y,CAAAA,CAAQ,GAAA,CAAA9X,CAAI,CAAA,CAAIhB,EAElBuZ,CAAAA,CAAajB,EAAAA,CAAQ,IAAA,CAAM,CAAA,EAAM,CAAA,CAAE,IAAA,GAASQ,CAAM,CAAA,CACxD,GAAI,CAACS,CAAAA,CACH,MAAM,IAAI,MACR,CAAA,gBAAA,EAAmBT,CAAM,CAAA,qBAAA,EAAwBR,EAAAA,CAAQ,GAAA,CACtD,CAAA,EAAM,EAAE,IACX,CAAA,CAAE,IAAA,CAAK,IAAI,CAAC,CAAA,CACd,EAGF,IAAMY,CAAAA,CAAa/Y,UAAAA,CAAK,IAAA,CAAKa,CAAAA,CAAKuY,CAAAA,CAAW,UAAU,CAAA,CACjDC,CAAAA,CAAMrZ,UAAAA,CAAK,OAAA,CAAQ+Y,CAAU,CAAA,CACnC,MAAMtJ,CAAAA,CAAQ,SAAA,CAAU4J,CAAG,CAAA,CAG3B,IAAIpG,CAAAA,CAAiB,EAAC,CACtB,GAAI,CACF,IAAM3B,CAAAA,CAAU,MAAMvR,SAAG,QAAA,CAASgZ,CAAAA,CAAY,OAAO,CAAA,CACrD9F,CAAAA,CAAiB,IAAA,CAAK,MAAM3B,CAAO,EACrC,CAAA,KAAQ,CAAC,CAET,IAAMgI,EAAenF,EAAAA,CACnBlB,CAAAA,CACAmG,CAAAA,CAAW,MAAA,CACX,CAAE,UAAA,CAAYH,EAAe,CAC/B,CAAA,CAEA,OAAA,MAAMlZ,QAAAA,CAAG,SAAA,CACPgZ,CAAAA,CACA,KAAK,SAAA,CAAUO,CAAAA,CAAc,IAAA,CAAM,CAAC,CAAA,CAAI;AAAA,CAAA,CACxC,OACF,CAAA,CAEOF,CAAAA,CAAW,UACpB,CCxOA,eAAsBG,EAAAA,CAAa7X,GAAAA,CAAgB,CACjD,GAAI,CAACA,GAAAA,CAAO,aAAA,CAAc,EAAA,CACxB,MAAM,IAAI,KAAA,CACR,8IACF,CAAA,CAGF,IAAM8X,CAAAA,CAAS9X,GAAAA,CAAO,aAAA,CAAc,EAAA,CAC9B,CAAC6L,CAAAA,CAAOkM,CAAa,CAAA,CAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CAC/CC,EAAAA,CAAG,sBAAA,CAAwB,CACzB,GAAA,CAAKF,CACP,CAAC,EACDG,EAAAA,EACF,CAAC,CAAA,CAED,GAAI,MAAA,CAAO,IAAA,CAAKF,CAAa,CAAA,CAAE,MAAA,GAAW,CAAA,CACxC,MAAM,IAAI,KAAA,CAAM,mDAAmD,CAAA,CAGrE,IAAMG,CAAAA,CAAiB,MAAA,CAAO,OAAA,CAAQC,CAAc,CAAA,CAAE,GAAA,CACpD,CAAC,CAAClX,CAAAA,CAAMmX,CAAW,CAAA,IAAO,CACxB,MAAOA,CAAAA,CAAY,IAAA,CACnB,KAAA,CAAOnX,CACT,CAAA,CACF,CAAA,CAEMoX,CAAAA,CAAiB,MAAMpQ,EAAAA,CAAQ,CACnC,CACE,IAAA,CAAM,QAAA,CACN,IAAA,CAAM,gBACN,OAAA,CAAS,CAAA,qCAAA,EAAwCvJ,CAAAA,CAAY,IAAA,CAC3D,cACF,CAAC,IACD,OAAA,CAASwZ,CACX,CAAA,CACA,CACE,IAAA,CAAM,QAAA,CACN,KAAM,eAAA,CACN,OAAA,CAAS,CAAA,qCAAA,EAAwCxZ,CAAAA,CAAY,IAAA,CAC3D,YACF,CAAC,CAAA,CAAA,CAAA,CACD,OAAA,CAASwZ,CACX,CACF,CAAC,CAAA,CAED,GAAIG,EAAe,aAAA,GAAkBA,CAAAA,CAAe,aAAA,CAClD,MAAM,IAAI,KAAA,CACR,sFACF,CAAA,CAGF,GACE,EACEA,CAAAA,CAAe,aAAA,IAAiBF,CAAAA,EAChCE,CAAAA,CAAe,iBAAiBF,CAAAA,CAAAA,CAGlC,MAAM,IAAI,KAAA,CAAM,2DAA2D,CAAA,CAG7E,IAAMG,CAAAA,CACJH,CAAAA,CAAeE,CAAAA,CAAe,aAA4C,CAAA,CACtEE,GAAAA,CACJJ,CAAAA,CAAeE,EAAe,aAA4C,CAAA,CACtE,CAAE,OAAA,CAAAlE,CAAQ,CAAA,CAAI,MAAMlM,EAAAA,CAAQ,CAChC,IAAA,CAAM,SAAA,CACN,IAAA,CAAM,SAAA,CACN,QAAS,IAAA,CACT,OAAA,CAAS,CAAA,gBAAA,EAAmBvJ,CAAAA,CAAY,IAAA,CACtCmN,CAAAA,CAAM,MACR,CAAC,CAAA,UAAA,EAAanN,CAAAA,CAAY,IAAA,CACxB,CAAA,EAAA,EAAKJ,UAAAA,CAAK,QAAA,CAAS0B,IAAO,aAAA,CAAc,GAAA,CAAK8X,CAAM,CAAC,CAAA,CACtD,CAAC,CAAA,MAAA,EAASpZ,CAAAA,CAAY,IAAA,CAAK4Z,CAAAA,CAAc,IAAI,CAAC,CAAA,IAAA,EAAO5Z,CAAAA,CAAY,KAC/D6Z,GAAAA,CAAc,IAChB,CAAC,CAAA,WAAA,CACH,CAAC,CAAA,CAEIpE,CAAAA,GACH1V,CAAAA,CAAO,IAAA,CAAK,sBAAsB,CAAA,CAClC,OAAA,CAAQ,IAAA,CAAK,CAAC,GAGZ8Z,GAAAA,CAAc,OAAA,EAChB,MAAM/Q,CAAAA,CAAmB,CAAC+Q,GAAAA,CAAc,OAAO,CAAA,CAAG,EAAC,CAAGvY,GAAAA,CAAQ,CAC5D,MAAA,CAAQ,KACV,CAAC,CAAA,CAGH,IAAMwY,CAAAA,CAAmBha,CAAAA,CAAQ,oBAAoB,CAAA,EAAG,KAAA,EAAM,CAE9D,MAAM,OAAA,CAAQ,GAAA,CACZqN,CAAAA,CAAM,GAAA,CAAI,MAAOP,CAAAA,EAAS,CACxBkN,CAAAA,CAAiB,IAAA,CAAO,CAAA,UAAA,EAAalN,CAAI,MAEzC,IAAMwC,CAAAA,CAAWxP,UAAAA,CAAK,IAAA,CAAKwZ,CAAAA,CAAQxM,CAAI,EACjC2K,CAAAA,CAAc,MAAM5X,QAAAA,CAAG,QAAA,CAASyP,CAAAA,CAAU,OAAO,CAAA,CAEjD8B,CAAAA,CAAU,MAAM6I,EAAAA,CACpBxC,CAAAA,CACAoC,CAAAA,CAAe,aAAA,CACfA,CAAAA,CAAe,cACfN,CACF,CAAA,CAEA,MAAM1Z,QAAAA,CAAG,SAAA,CAAUyP,CAAAA,CAAU8B,CAAO,EACtC,CAAC,CACH,CAAA,CAEA4I,CAAAA,CAAiB,OAAA,CAAQ,qBAAqB,EAChD,CAEA,eAAsBC,EAAAA,CACpB7I,CAAAA,CACA0I,CAAAA,CACAC,CAAAA,CACAG,CAAAA,CACA,CACA,IAAMC,CAAAA,CAAsBR,CAAAA,CAAeG,CAAa,CAAA,EAAG,OACrDM,CAAAA,CAAsBT,CAAAA,CAAeI,CAAa,CAAA,EAAG,MAAA,CAErDZ,CAAAA,CAAM,MAAMtZ,QAAAA,CAAG,OAAA,CAAQC,UAAAA,CAAK,IAAA,CAAKua,MAAAA,EAAO,CAAG,SAAS,CAAC,CAAA,CACrDC,CAAAA,CAAU,IAAIC,OAAAA,CAAQ,CAC1B,eAAA,CAAiB,EACnB,CAAC,CAAA,CAEKC,CAAAA,CAAW1a,UAAAA,CAAK,IAAA,CACpBqZ,CAAAA,CACA,gBAAgBsB,WAAAA,CAAY,CAAC,CAAA,CAAE,QAAA,CAAS,KAAK,CAAC,MAChD,CAAA,CACMlJ,CAAAA,CAAa+I,CAAAA,CAAQ,gBAAA,CAAiBE,CAAAA,CAAUpJ,CAAAA,CAAS,CAC7D,UAAA,CAAYsJ,UAAAA,CAAW,GACzB,CAAC,CAAA,CAGGC,CAAAA,CAA0B,EAAC,CAC/B,IAAA,IAAWC,CAAAA,IAAqBrJ,CAAAA,CAAW,qBAAA,EAAsB,EAAK,GACpE,GACEqJ,CAAAA,CAAkB,kBAAA,EAAmB,EAAG,OAAA,EAAQ,GAChD,CAAA,CAAA,EAAIT,CAAmB,CAAA,CAAA,CAAA,CAKzB,CAAA,IAAA,IAAWU,CAAAA,IAAaD,CAAAA,CAAkB,eAAA,EAAgB,EAAK,EAAC,CAAG,CACjE,IAAME,CAAAA,CAAWD,CAAAA,CAAU,OAAA,EAAQ,CAG7BE,CAAAA,CAAe,MAAA,CAAO,MAAA,CAAOb,CAAY,CAAA,CAAE,IAAA,CAC9Cc,CAAAA,EAASA,EAAKlB,CAAa,CAAA,GAAMgB,CACpC,CAAA,GAAIf,CAAa,CAAA,CAEb,CAACgB,CAAAA,EAAgBJ,CAAAA,CAAc,QAAA,CAASI,CAAY,CAAA,GAIxDJ,CAAAA,CAAc,KAAKI,CAAY,CAAA,CAG/BF,CAAAA,CAAU,MAAA,EAAO,CAGjBtJ,CAAAA,CACG,oBAAA,CAAqBG,UAAAA,CAAW,qBAAqB,CAAA,CACrD,MAAA,CAAQ/O,CAAAA,EAASA,CAAAA,CAAK,cAAA,IAAkB,OAAA,EAAQ,GAAMmY,CAAQ,CAAA,CAC9D,OAAA,CAASnY,CAAAA,EAASA,CAAAA,CAAK,cAAA,EAAe,EAAG,eAAA,CAAgBoY,CAAY,CAAC,CAAA,EAC3E,CAGIH,EAAkB,eAAA,EAAgB,EAAG,MAAA,GAAW,CAAA,EAClDA,CAAAA,CAAkB,MAAA,GAAO,CAI7B,OAAID,CAAAA,CAAc,MAAA,CAAS,CAAA,EACzBpJ,CAAAA,CAAW,oBAAA,CAAqB,CAC9B,gBAAiB6I,CAAAA,CACjB,YAAA,CAAcO,CAAAA,CAAc,GAAA,CAAKK,CAAAA,GAAU,CACzC,KAAMA,CACR,CAAA,CAAE,CACJ,CAAC,CAAA,CAGI,MAAMzJ,EAAW,OAAA,EAC1B,CC7LA,SAAS0J,EAAAA,CAAaC,CAAAA,CAAqB,CACzC,OAAOA,EACJ,KAAA,CAAM,GAAG,CAAA,CACT,GAAA,CAAKpD,CAAAA,EAASA,CAAAA,CAAK,OAAO,CAAC,CAAA,CAAE,WAAA,EAAY,CAAIA,CAAAA,CAAK,KAAA,CAAM,CAAC,CAAC,CAAA,CAC1D,IAAA,CAAK,EAAE,CACZ,CAEA,SAASqD,GACPC,CAAAA,CACAC,CAAAA,CACAC,CAAAA,CACAC,CAAAA,CACA,CASA,IAAMC,EANiBJ,CAAAA,CACpB,OAAA,CAAQ,WAAA,CAAa,EAAE,CAAA,CACvB,OAAA,CAAQ,oBAAqB,EAAE,CAAA,CAC/B,OAAA,CAAQ,MAAA,CAAQ,GAAG,CAAA,CACnB,IAAA,EAAK,CAGL,KAAA,CAAM,GAAG,CAAA,CACT,GAAA,CAAKK,CAAAA,EAAeA,CAAAA,CAAW,MAAM,CAAA,CACrC,MAAA,CAAO,OAAO,CAAA,CAEjB,IAAA,IAAWA,CAAAA,IAAcD,CAAAA,CAAiB,CACxC,IAAME,CAAAA,CAAkBD,CAAAA,CAAW,KAAA,CAAM,kCAAkC,EACrEE,CAAAA,CAAaF,CAAAA,CAAW,KAAA,CAAM,sBAAsB,CAAA,CAE1D,GAAIC,CAAAA,CAAiB,CAEnB,IAAME,CAAAA,CAAaF,CAAAA,CAAgB,CAAC,CAAA,CAC9BG,CAAAA,CAAcH,EAAgB,CAAC,CAAA,CAEjCH,CAAAA,GAAgB,MAAA,EAAUK,CAAAA,GAAe,MAAA,EAAU,CAACC,CAAAA,CACtDP,CAAAA,CAAQ,IAAA,CAAK,CACX,IAAA,CAAM,MAAA,CACN,MAAO,eAAA,CACP,MAAA,CAAQ,IACV,CAAC,CAAA,CAEDA,CAAAA,CAAQ,IAAA,CAAK,CACX,IAAA,CAAMM,CAAAA,CACN,KAAA,CAAOC,CAAAA,CACP,MAAA,CAAQ,IACV,CAAC,EAEL,CAAA,KAAA,GAAWF,CAAAA,CAAY,CAErB,IAAMC,CAAAA,CAAaD,CAAAA,CAAW,CAAC,CAAA,CACzBE,CAAAA,CAAcF,CAAAA,CAAW,CAAC,CAAA,CAG9BJ,CAAAA,GAAgB,QAChBK,CAAAA,GAAe,MAAA,EACfC,CAAAA,GAAgB,MAAA,CAEhBP,CAAAA,CAAQ,IAAA,CAAK,CACX,IAAA,CAAM,MAAA,CACN,KAAA,CAAO,eAAA,CACP,MAAA,CAAQD,CACV,CAAC,EAEDC,CAAAA,CAAQ,IAAA,CAAK,CACX,IAAA,CAAMM,CAAAA,CACN,KAAA,CAAOC,EACP,MAAA,CAAQR,CACV,CAAC,EAEL,CAAA,KAGME,CAAAA,GAAgB,QAAUE,CAAAA,GAAe,MAAA,CAC3CH,CAAAA,CAAQ,IAAA,CAAK,CACX,IAAA,CAAM,MAAA,CACN,KAAA,CAAO,eAAA,CACP,MAAA,CAAQD,CACV,CAAC,CAAA,CAEDC,CAAAA,CAAQ,KAAK,CACX,IAAA,CAAMG,CAAAA,CACN,MAAA,CAAQJ,CACV,CAAC,EAGP,CACF,CAEA,eAAsBS,EAAAA,CACpBta,GAAAA,CACA7B,CAAAA,CAA6B,GAC7B,CACA,GAAI,CAAC6B,GAAAA,CAAO,aAAA,CAAc,EAAA,CACxB,MAAM,IAAI,KAAA,CACR,8IACF,CAAA,CAGF,IAAM8X,CAAAA,CAAS9X,GAAAA,CAAO,cAAc,EAAA,CAC9B6L,CAAAA,CAAQ,MAAMmM,EAAAA,CAAG,sBAAA,CAAwB,CAC7C,IAAKF,CACP,CAAC,CAAA,CAED,GAAI,CAAC3Z,CAAAA,CAAQ,IAAK,CAChB,GAAM,CAAE,OAAA,CAAAgW,GAAQ,CAAA,CAAI,MAAMlM,EAAAA,CAAQ,CAChC,IAAA,CAAM,SAAA,CACN,IAAA,CAAM,SAAA,CACN,OAAA,CAAS,KACT,OAAA,CAAS,CAAA,gBAAA,EAAmBvJ,CAAAA,CAAY,IAAA,CACtCmN,CAAAA,CAAM,MACR,CAAC,CAAA,UAAA,EAAanN,CAAAA,CAAY,IAAA,CACxB,CAAA,EAAA,EAAKJ,UAAAA,CAAK,QAAA,CAAS0B,IAAO,aAAA,CAAc,GAAA,CAAK8X,CAAM,CAAC,CAAA,CACtD,CAAC,CAAA,IAAA,EAAOpZ,CAAAA,CAAY,IAAA,CAAK,UAAU,CAAC,CAAA,WAAA,CACtC,CAAC,CAAA,CAEIyV,MACH1V,CAAAA,CAAO,IAAA,CAAK,sBAAsB,CAAA,CAClC,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,EAElB,CAEA,IAAM+Z,GAAAA,CAAmBha,CAAAA,CAAQ,sBAAsB,GAAG,KAAA,EAAM,CAC1D+b,CAAAA,CAAgB,IAAI,GAAA,CAE1B,MAAM,OAAA,CAAQ,GAAA,CACZ1O,CAAAA,CAAM,GAAA,CAAI,MAAOP,CAAAA,EAAS,CACxBkN,GAAAA,CAAiB,KAAO,CAAA,UAAA,EAAalN,CAAI,CAAA,GAAA,CAAA,CAEzC,IAAMwC,CAAAA,CAAWxP,UAAAA,CAAK,IAAA,CAAKwZ,CAAAA,CAAQxM,CAAI,CAAA,CACjC2K,CAAAA,CAAc,MAAM5X,QAAAA,CAAG,QAAA,CAASyP,EAAU,OAAO,CAAA,CAEjD,CAAE,OAAA,CAAA8B,CAAAA,CAAS,gBAAA,CAAA4K,CAAiB,CAAA,CAAI,MAAMC,EAAAA,CAAiBxE,CAAW,CAAA,CAGxEuE,CAAAA,CAAiB,OAAA,CAASE,GAAQH,CAAAA,CAAc,GAAA,CAAIG,CAAG,CAAC,CAAA,CAExD,MAAMrc,SAAG,SAAA,CAAUyP,CAAAA,CAAU8B,CAAO,EACtC,CAAC,CACH,EAEA4I,GAAAA,CAAiB,OAAA,CAAQ,oBAAoB,CAAA,CAG7C,IAAMmC,CAAAA,CAAiBnc,CAAAA,CAAQ,0BAA0B,CAAA,EAAG,KAAA,EAAM,CAElE,GAAI,CACF,IAAM8O,IAAcxJ,CAAAA,CAAe9D,GAAAA,CAAO,aAAA,CAAc,GAAA,CAAK,CAAA,CAAK,CAAA,CAElE,GAAI,CAACsN,GAAAA,CAAa,CAChBqN,CAAAA,CAAe,IAAA,CAAK,6BAA6B,CAAA,CACjDlc,EAAO,IAAA,CACL,0GACF,CAAA,CACA,MACF,CAEA,IAAMmc,CAAAA,CAAqB,KAAA,CAAM,IAAA,CAAKL,CAAa,CAAA,CAG7CM,CAAAA,CAAkB,CAAC,cAAA,CAAgB,iBAAiB,CAAA,CAC1D,IAAA,IAAWC,CAAAA,IAAWD,CAAAA,CACpB,GAAIvN,GAAAA,CAAYwN,CAAO,CAAA,CACrB,IAAA,IAAWJ,CAAAA,IAAOE,CAAAA,CACZtN,GAAAA,CAAYwN,CAAO,EAAGJ,CAAG,CAAA,EAC3B,OAAOpN,GAAAA,CAAYwN,CAAO,CAAA,CAAGJ,CAAG,CAAA,CAOxC,GAAIE,CAAAA,CAAmB,MAAA,CAAS,CAAA,CAAG,CAC5BtN,GAAAA,CAAY,eACfA,GAAAA,CAAY,YAAA,CAAe,EAAC,CAAA,CAE9BA,GAAAA,CAAY,YAAA,CAAa,UAAU,CAAA,CAAI,QAAA,CAEvC,IAAMF,CAAAA,CAAkB9O,UAAAA,CAAK,IAAA,CAC3B0B,GAAAA,CAAO,cAAc,GAAA,CACrB,cACF,CAAA,CACA,MAAM3B,QAAAA,CAAG,SAAA,CACP+O,CAAAA,CACA,IAAA,CAAK,SAAA,CAAUE,GAAAA,CAAa,IAAA,CAAM,CAAC,CAAA,CAAI;AAAA,CACzC,CAAA,CAEAqN,CAAAA,CAAe,OAAA,CAAQ,uBAAuB,CAAA,CAG9C,MAAMnT,CAAAA,CAAmB,CAAC,UAAU,CAAA,CAAG,EAAC,CAAGxH,GAAAA,CAAQ,CAAE,MAAA,CAAQ,CAAA,CAAM,CAAC,EACtE,CAAA,KACE2a,CAAAA,CAAe,OAAA,CAAQ,oCAAoC,EAE/D,CAAA,KAAgB,CACdA,CAAAA,CAAe,IAAA,CAAK,+BAA+B,EACnDlc,CAAAA,CAAO,IAAA,CACL,2EACF,EACF,CACF,CAEA,eAAsBgc,EAAAA,CACpB7K,CAAAA,CAC0D,CAG1D,IAAMmL,CAAAA,CACJ,sGAAA,CAEIjB,CAAAA,CAAqE,EAAC,CACtEkB,CAAAA,CAA0B,EAAC,CAC3BR,CAAAA,CAA6B,EAAC,CAChCS,CAAAA,CAAa,GAAA,CACbC,CAAAA,CAAe,KAAA,CAEfxa,CAAAA,CAASkP,CAAAA,CACTuL,CAAAA,CAGJ,MAAQA,CAAAA,CAAQJ,CAAAA,CAAmB,IAAA,CAAKnL,CAAO,CAAA,IAAO,IAAA,EAAM,CAC1D,GAAM,CACJwL,CAAAA,CACAC,CAAAA,CACAC,CAAAA,CACA1B,CAAAA,CACAvT,CAAAA,CACA0T,EACAwB,CACF,CAAA,CAAIJ,CAAAA,CAGJ,GAAIpB,CAAAA,GAAgB,OAAA,EAAWA,CAAAA,CAAY,UAAA,CAAW,QAAQ,CAAA,CAC5D,SAGFiB,CAAAA,CAAc,IAAA,CAAKI,CAAS,EAGxBJ,CAAAA,CAAc,MAAA,GAAW,CAAA,GAC3BC,CAAAA,CAAa5U,CAAAA,CACb6U,CAAAA,CAAeK,CAAAA,GAAc,GAAA,CAAA,CAI/Bf,CAAAA,CAAiB,IAAA,CAAK,CAAA,gBAAA,EAAmBT,CAAW,CAAA,CAAE,CAAA,CAEtD,IAAMF,CAAAA,CAAa,CAAA,CAAQwB,CAAAA,CAE3B,GAAIC,CAAAA,CAAgB,CAElB,IAAME,EAAAA,CAAgB/B,EAAAA,CAAaM,CAAW,CAAA,CAC9CD,CAAAA,CAAQ,IAAA,CAAK,CACX,KAAM0B,EAAAA,CACN,KAAA,CAAOF,CAAAA,CACP,MAAA,CAAQzB,CACV,CAAC,EACH,CAAA,KAAWD,CAAAA,EAKTD,EAAAA,CAAoBC,CAAAA,CAAcC,CAAAA,CAAYC,CAAAA,CAASC,CAAW,EAEtE,CAEA,GAAID,CAAAA,CAAQ,MAAA,GAAW,CAAA,CACrB,OAAO,CACL,OAAA,CAAAlK,CAAAA,CACA,gBAAA,CAAkB,EACpB,CAAA,CAKF,IAAM6L,EAAgB3B,CAAAA,CAAQ,MAAA,CAC5B,CAACM,CAAAA,CAAYsB,CAAAA,CAAOC,CAAAA,GAClBD,CAAAA,GACAC,CAAAA,CAAK,SAAA,CACFC,CAAAA,EACCA,CAAAA,CAAE,IAAA,GAASxB,CAAAA,CAAW,IAAA,EACtBwB,EAAE,KAAA,GAAUxB,CAAAA,CAAW,KAAA,EACvBwB,CAAAA,CAAE,MAAA,GAAWxB,CAAAA,CAAW,MAC5B,CACJ,CAAA,CAYMyB,CAAAA,CAAgB,CAAA,SAAA,EATHJ,CAAAA,CAChB,GAAA,CAAKK,CAAAA,EAAQ,CACZ,IAAMC,CAAAA,CAAaD,CAAAA,CAAI,MAAA,CAAS,OAAA,CAAU,EAAA,CAC1C,OAAOA,CAAAA,CAAI,KAAA,CACP,CAAA,EAAGC,CAAU,CAAA,EAAGD,CAAAA,CAAI,IAAI,OAAOA,CAAAA,CAAI,KAAK,CAAA,CAAA,CACxC,CAAA,EAAGC,CAAU,CAAA,EAAGD,CAAAA,CAAI,IAAI,CAAA,CAC9B,CAAC,CAAA,CACA,IAAA,CAAK,IAAI,CAEgC,WAAWb,CAAU,CAAA,QAAA,EAAWA,CAAU,CAAA,EACpFC,CAAAA,CAAe,GAAA,CAAM,EACvB,CAAA,CAAA,CAGAxa,CAAAA,CAASsa,CAAAA,CAAc,MAAA,CAAO,CAACgB,CAAAA,CAAKC,CAAAA,CAAMP,CAAAA,GACjCM,CAAAA,CAAI,OAAA,CAAQC,CAAAA,CAAMP,CAAAA,GAAU,CAAA,CAAIG,CAAAA,CAAgB,EAAE,CAAA,CACxDnb,CAAM,CAAA,CAGTA,CAAAA,CAASA,CAAAA,CAAO,OAAA,CAAQ,eAAA,CAAiB;;AAAA,CAAM,CAAA,CAMzB+a,CAAAA,CAAc,IAAA,CACjCK,CAAAA,EAAQA,CAAAA,CAAI,IAAA,GAAS,MAAA,EAAUA,CAAAA,CAAI,KAAA,GAAU,eAChD,CAAA,GAgEEpb,CAAAA,CA5DcA,EAAO,KAAA,CAAM;AAAA,CAAI,CAAA,CACA,IAAKub,CAAAA,EAAS,CAE3C,GAAIA,CAAAA,CAAK,IAAA,GAAO,UAAA,CAAW,SAAS,EAClC,OAAOA,CAAAA,CAGT,IAAIC,CAAAA,CAAkBD,CAAAA,CAMtB,OAAAC,CAAAA,CAAkBA,CAAAA,CAAgB,QAChC,gCAAA,CACA,0BACF,EAEAA,CAAAA,CAAkBA,CAAAA,CAAgB,QAChC,yCAAA,CACA,mDACF,EAEAA,CAAAA,CAAkBA,CAAAA,CAAgB,QAChC,kCAAA,CACA,6CACF,EAEAA,CAAAA,CAAkBA,CAAAA,CAAgB,QAChC,sBAAA,CACA,0BACF,EAGAA,CAAAA,CAAkBA,CAAAA,CAAgB,QAChC,WAAA,CACA,CAACf,EAAOgB,CAAAA,CAAQC,CAAAA,GAAW,CAEzB,IAAMC,CAAAA,CAAcD,EAAO,SAAA,CAAU,CAAA,CAAGD,CAAM,CAAA,CACxCG,EAAAA,CAAAA,CAAcD,EAAY,KAAA,CAAM,IAAI,GAAK,EAAC,EAAG,OAC7CE,EAAAA,CAAAA,CAAoBF,CAAAA,CAAY,MAAM,IAAI,CAAA,EAAK,EAAC,EAAG,MAAA,CAGzD,OAAIC,EAAAA,CAAa,CAAA,GAAM,GAAKC,EAAAA,CAAmB,CAAA,GAAM,EAC5CpB,CAAAA,CAGF,sBACT,CACF,CAAA,CAGAe,CAAAA,CAAkBA,EAAgB,OAAA,CAChC,uBAAA,CACA,oBACF,CAAA,CAEOA,CACT,CAAC,CAAA,CAEyB,IAAA,CAAK;AAAA,CAAI,CAAA,CAAA,CAIrC,IAAMM,CAAAA,CAAyB,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIhC,CAAgB,CAAC,CAAA,CAEnE,OAAO,CACL,QAAS9Z,CAAAA,CACT,gBAAA,CAAkB8b,CACpB,CACF,CCrYA,eAAsBC,EAAAA,CACpBte,CAAAA,CACA,CACA,IAAMC,CAAAA,CAAkC,EAAC,CAIzC,GACE,CAACC,CAAAA,CAAG,UAAA,CAAWF,CAAAA,CAAQ,GAAG,CAAA,EAC1B,CAACE,CAAAA,CAAG,UAAA,CAAWC,WAAK,OAAA,CAAQH,CAAAA,CAAQ,GAAA,CAAK,cAAc,CAAC,CAAA,CAExD,OAAAC,CAAAA,CAAc,GAA4B,CAAA,CAAI,IAAA,CACvC,CACL,MAAA,CAAAA,CAAAA,CACA,OAAQ,IACV,CAAA,CAIF,GAAI,CAACC,CAAAA,CAAG,UAAA,CAAWC,UAAAA,CAAK,OAAA,CAAQH,CAAAA,CAAQ,GAAA,CAAK,iBAAiB,CAAC,CAAA,CAC7D,OAAAC,EAAc,GAAc,CAAA,CAAI,IAAA,CACzB,CACL,MAAA,CAAAA,CAAAA,CACA,MAAA,CAAQ,IACV,CAAA,CAGF,GAAI,CACF,IAAM4B,CAAAA,CAAS,MAAM6R,EAAU1T,CAAAA,CAAQ,GAAG,CAAA,CAE1C,OAAO,CACL,MAAA,CAAAC,CAAAA,CACA,MAAA,CAAQ4B,CACV,CACF,CAAA,KAAgB,CACdvB,CAAAA,CAAO,KAAA,GACPA,CAAAA,CAAO,KAAA,CACL,CAAA,WAAA,EAAcC,CAAAA,CAAY,IAAA,CACxB,iBACF,CAAC,CAAA,mBAAA,EAAsBA,CAAAA,CAAY,IAAA,CACjCP,CAAAA,CAAQ,GACV,CAAC,CAAA;AAAA,wDAAA,EAA8DO,EAAY,IAAA,CACzE,iBACF,CAAC,CAAA,qBAAA,EAAwBA,CAAAA,CAAY,KAAK,MAAM,CAAC,WACnD,CAAA,CACAD,CAAAA,CAAO,MACL,CAAA,cAAA,EAAiBC,CAAAA,CAAY,KAC3B,4CACF,CAAC,GACH,CAAA,CACAD,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,KAAK,CAAC,EAChB,CACF,CCrDO,IAAMie,EAAAA,CAAa,CACxB,CACE,IAAA,CAAM,QACN,WAAA,CAAa,yDACf,EACA,CACE,IAAA,CAAM,QACN,WAAA,CAAa,sBACf,CACF,CAAA,CAEaC,EAAAA,CAAuB9V,IAAE,MAAA,CAAO,CAC3C,IAAKA,GAAAA,CAAE,MAAA,GACP,IAAA,CAAMA,GAAAA,CAAE,SAAQ,CAChB,GAAA,CAAKA,IAAE,OAAA,EAAQ,CACf,UAAWA,GAAAA,CACR,MAAA,GACA,MAAA,CACExE,CAAAA,EACCA,GAASqa,EAAAA,CAAW,IAAA,CAAME,GAAcA,CAAAA,CAAU,IAAA,GAASva,CAAK,CAAA,CAClE,CACE,QACE,8FACJ,CACF,EACC,QAAA,EACL,CAAC,CAAA,CAEYwa,EAAAA,CAAU,IAAI1L,OAAAA,EAAQ,CAChC,KAAK,SAAS,CAAA,CACd,YAAY,kBAAkB,CAAA,CAC9B,SAAS,aAAA,CAAe,uBAAuB,EAC/C,MAAA,CACC,iBAAA,CACA,4DACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,MAAA,CAAO,aAAc,sBAAA,CAAwB,KAAK,EAClD,MAAA,CAAO,WAAA,CAAa,4BAA6B,KAAK,CAAA,CACtD,OAAO,MAAOyL,CAAAA,CAAWxL,IAAS,CACjC,GAAI,CACF,IAAMjT,CAAAA,CAAUwe,GAAqB,KAAA,CAAM,CACzC,IAAKre,UAAAA,CAAK,OAAA,CAAQ8S,EAAK,GAAG,CAAA,CAC1B,UAAAwL,CAAAA,CACA,IAAA,CAAMxL,EAAK,IAAA,CACX,GAAA,CAAKA,EAAK,GACZ,CAAC,EAED,GAAIjT,CAAAA,CAAQ,MAAQ,CAACA,CAAAA,CAAQ,UAAW,CACtCM,CAAAA,CAAO,KAAK,uBAAuB,CAAA,CACnC,QAAWme,CAAAA,IAAaF,EAAAA,CACtBje,EAAO,IAAA,CAAK,CAAA,EAAA,EAAKme,EAAU,IAAI,CAAA,EAAA,EAAKA,EAAU,WAAW,CAAA,CAAE,EAE7D,MACF,CAEA,GAAI,CAACze,CAAAA,CAAQ,UACX,MAAM,IAAI,MACR,wFACF,CAAA,CAGF,GAAI,CAAE,MAAA,CAAAC,EAAQ,MAAA,CAAA4B,CAAO,EAAI,MAAMyc,EAAAA,CAAiBte,CAAO,CAAA,CAEvD,GACEC,EAAc,GAA4B,CAAA,EAC1CA,EAAc,GAAc,CAAA,CAE5B,MAAM,IAAI,KAAA,CACR,8EACF,CAAA,CAGF,GAAI,CAAC4B,CAAAA,CACH,MAAM,IAAI,KAAA,CACR,kHACF,EAGE7B,CAAAA,CAAQ,SAAA,GAAc,SACxB,MAAM0Z,EAAAA,CAAa7X,CAAM,CAAA,CAGvB7B,CAAAA,CAAQ,YAAc,OAAA,EACxB,MAAMmc,GAAata,CAAAA,CAAQ,CAAE,IAAK7B,CAAAA,CAAQ,GAAI,CAAC,EAEnD,CAAA,MAAS+E,EAAO,CACdzE,CAAAA,CAAO,OAAM,CACb8L,CAAAA,CAAYrH,CAAK,EACnB,CACF,CAAC,CAAA,CC3FH,eAAsB4Z,GACpB3e,CAAAA,CACA,CACA,IAAMC,CAAAA,CAAkC,GAElCyW,CAAAA,CAAe,CACnB,IAAK1W,CAAAA,CAAQ,GAAA,CACb,aAAcG,UAAAA,CAAK,OAAA,CAAQH,EAAQ,GAAA,CAAKA,CAAAA,CAAQ,YAAY,CAAA,CAC5D,SAAA,CAAWG,WAAK,OAAA,CAAQH,CAAAA,CAAQ,IAAKA,CAAAA,CAAQ,SAAS,CACxD,CAAA,CAGA,GAAI,CAACE,CAAAA,CAAG,UAAA,CAAWwW,EAAa,YAAY,CAAA,CAC1C,OAAAzW,CAAAA,CAAc,IAA2B,EAAI,IAAA,CACtC,CACL,OAAAA,CAAAA,CACA,YAAA,CAAc,KACd,MAAA,CAAQ,IACV,EAIF,GAAI,CAACC,EAAG,UAAA,CAAWC,UAAAA,CAAK,QAAQH,CAAAA,CAAQ,GAAA,CAAK,iBAAiB,CAAC,CAAA,CAC7D,OAAAC,CAAAA,CAAc,GAAc,EAAI,IAAA,CACzB,CACL,OAAAA,CAAAA,CACA,YAAA,CAAc,KACd,MAAA,CAAQ,IACV,EAIF,MAAMC,CAAAA,CAAG,MAAMwW,CAAAA,CAAa,SAAA,CAAW,CAAE,SAAA,CAAW,IAAK,CAAC,CAAA,CAE1D,GAAI,CACF,IAAM7U,GAAAA,CAAS,MAAM6R,CAAAA,CAAU1T,CAAAA,CAAQ,GAAG,CAAA,CAE1C,OAAO,CACL,MAAA,CAAAC,CAAAA,CACA,OAAQ4B,GAAAA,CACR,YAAA,CAAA6U,CACF,CACF,CAAA,KAAgB,CACdpW,CAAAA,CAAO,KAAA,GACPA,CAAAA,CAAO,KAAA,CACL,cAAcC,CAAAA,CAAY,IAAA,CACxB,iBACF,CAAC,CAAA,mBAAA,EAAsBA,EAAY,IAAA,CACjCP,CAAAA,CAAQ,GACV,CAAC,CAAA;AAAA,2DAAA,EAAiEO,EAAY,IAAA,CAC5E,iBACF,CAAC,CAAA,qBAAA,EAAwBA,EAAY,IAAA,CAAK,MAAM,CAAC,CAAA,SAAA,CACnD,EACAD,CAAAA,CAAO,KAAA,EAAM,CACb,OAAA,CAAQ,KAAK,CAAC,EAChB,CACF,CCnDO,IAAMqW,EAAAA,CAAqBjO,IAAE,MAAA,CAAO,CACzC,IAAKA,GAAAA,CAAE,MAAA,GACP,YAAA,CAAcA,GAAAA,CAAE,MAAA,EAAO,CACvB,UAAWA,GAAAA,CAAE,MAAA,EAAO,CACpB,OAAA,CAASA,IAAE,OAAA,EAAQ,CAAE,QAAA,EAAS,CAAE,QAAQ,KAAK,CAC/C,CAAC,CAAA,CAEYkO,EAAAA,CAAQ,IAAI5D,OAAAA,EAAQ,CAC9B,IAAA,CAAK,gBAAgB,EACrB,WAAA,CAAY,oCAAoC,CAAA,CAChD,QAAA,CAAS,aAAc,4BAAA,CAA8B,iBAAiB,CAAA,CACtE,MAAA,CACC,sBACA,sCAAA,CACA,YACF,EACC,MAAA,CACC,iBAAA,CACA,4DACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,OAAO,eAAA,CAAiB,gBAAgB,CAAA,CACxC,MAAA,CAAO,MAAO3C,CAAAA,CAAkB4C,CAAAA,GAAS,CACxC,MAAM2L,GAAc,CAClB,GAAA,CAAUC,UAAQ5L,CAAAA,CAAK,GAAG,EAC1B,YAAA,CAAc5C,CAAAA,CACd,SAAA,CAAW4C,CAAAA,CAAK,OAChB,OAAA,CAASA,CAAAA,CAAK,OAChB,CAAC,EACH,CAAC,CAAA,CAEH,eAAe2L,EAAAA,CAAc3L,IAA0C,CACrE,GAAI,CACF,IAAMjT,CAAAA,CAAU2W,GAAmB,KAAA,CAAM1D,GAAI,CAAA,CAEvC,CAAC,CAAE,MAAA,CAAAhT,CAAAA,CAAQ,YAAA,CAAAyW,CAAAA,CAAc,OAAA7U,CAAO,CAAA,CAAGpB,CAAW,CAAA,CAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CACxEke,EAAAA,CAAuB3e,CAAO,EAC9BU,GAAAA,CAAeV,CAAAA,CAAQ,GAAG,CAC5B,CAAC,CAAA,CAAA,CAEGC,CAAAA,CAAc,GAAc,CAAA,EAAK,CAAC4B,CAAAA,EAAU,CAACpB,CAAAA,IAC/CH,CAAAA,CAAO,MACL,CAAA,EAAA,EAAKC,CAAAA,CAAY,KACf,iBACF,CAAC,gDAAgDA,CAAAA,CAAY,IAAA,CAC3D,aACF,CAAC,iBACH,CAAA,CACAD,CAAAA,CAAO,KAAA,EAAM,CACb,QAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAAA,CAGZL,CAAAA,CAAc,IAA2B,CAAA,EAAK,CAACyW,KACjDpW,CAAAA,CAAO,KAAA,CACL,wCAAwCC,CAAAA,CAAY,IAAA,CAC7Cse,CAAA,CAAA,OAAA,CAAQ7e,CAAAA,CAAQ,IAAKA,CAAAA,CAAQ,YAAY,CAChD,CAAC,GACH,CAAA,CACAM,CAAAA,CAAO,KAAA,EAAM,CACb,QAAQ,IAAA,CAAK,CAAC,GAGhB,IAAMmR,CAAAA,CAAU,MAAS,CAAA,CAAA,QAAA,CAASiF,CAAAA,CAAa,YAAA,CAAc,OAAO,EAC9DnU,GAAAA,CAASwU,GAAAA,CAAe,SAAA,CAAU,IAAA,CAAK,MAAMtF,CAAO,CAAC,CAAA,CAEtDlP,GAAAA,CAAO,UACVjC,CAAAA,CAAO,KAAA,CACL,kCAAkCC,CAAAA,CAAY,IAAA,CAC5CmW,EAAa,YACf,CAAC,CAAA,CAAA,CACH,CAAA,CACApW,EAAO,KAAA,EAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGhB,IAAM0W,CAAAA,CAAe3W,CAAAA,CAAQ,sBAAsB,CAAA,CAG7Cye,CAAAA,CAAmB,MAAMC,EAAAA,CAC7Bxc,GAAAA,CAAO,KACPV,CAAAA,CACApB,CACF,CAAA,CAGA,IAAA,IAAW8U,KAAgBuJ,CAAAA,CAAiB,KAAA,CAE1CvJ,CAAAA,CAAa,KAAA,CAAQA,EAAa,KAAA,EAAO,MAAA,CACvC,CAACpI,CAAAA,CAAMoQ,EAAOC,CAAAA,GACZD,CAAAA,GAAUC,EAAK,SAAA,CAAWwB,CAAAA,EAAMA,EAAE,IAAA,GAAS7R,CAAAA,CAAK,IAAI,CACxD,EAGIoI,CAAAA,CAAa,YAAA,GACfA,CAAAA,CAAa,YAAA,CAAeA,EAAa,YAAA,CAAa,MAAA,CACpD,CAAChL,CAAAA,CAAKgT,EAAOC,CAAAA,GAASD,CAAAA,GAAUC,EAAK,SAAA,CAAWyB,CAAAA,EAAMA,IAAM1U,CAAG,CACjE,CAAA,CAAA,CAIJ,IAAA,IAAWgL,KAAgBuJ,CAAAA,CAAiB,KAAA,CAAO,CACjD,GAAI,CAACvJ,CAAAA,CAAa,KAAA,CAChB,SAGFyB,CAAAA,CAAa,MAAM,CAAA,SAAA,EAAYzB,CAAAA,CAAa,IAAI,CAAA,GAAA,CAAK,CAAA,CAGrDA,EAAa,OAAA,CACX,iDAAA,CAEF,IAAA,IAAWpI,CAAAA,IAAQoI,EAAa,KAAA,CAAO,CACrC,IAAM2J,CAAAA,CAAeL,UAAQnI,CAAAA,CAAa,GAAA,CAAKvJ,CAAAA,CAAK,IAAI,EACxD,GAAI,CAEF,GAAI,CAAA,CADS,MAAS,OAAK+R,CAAO,CAAA,EACxB,MAAA,EAAO,CACf,SAEF/R,CAAAA,CAAK,OAAA,CAAa,MAAS,CAAA,CAAA,QAAA,CAAS+R,EAAS,OAAO,EACtD,CAAA,MAASC,CAAAA,CAAK,CACZ,OAAA,CAAQ,KAAA,CAAM,wCAAyCD,CAAAA,CAASC,CAAG,EACnE,QACF,CACF,CAEA,IAAM5c,EAASqL,GAAAA,CAAmB,SAAA,CAAU2H,CAAY,CAAA,CACxD,GAAI,CAAChT,CAAAA,CAAO,OAAA,CAAS,CACnBjC,EAAO,KAAA,CACL,CAAA,gCAAA,EAAmCC,EAAY,IAAA,CAC7CgV,CAAAA,CAAa,IACf,CAAC,CAAA,CAAA,CACH,CAAA,CACA,QACF,CAGA,MAAS,CAAA,CAAA,SAAA,CACFsJ,CAAA,CAAA,OAAA,CAAQnI,CAAAA,CAAa,UAAW,CAAA,EAAGnU,CAAAA,CAAO,IAAA,CAAK,IAAI,OAAO,CAAA,CAC/D,IAAA,CAAK,UAAUA,CAAAA,CAAO,IAAA,CAAM,KAAM,CAAC,CACrC,EACF,CAUA,GAPA,MAAS,CAAA,CAAA,QAAA,CACPmU,CAAAA,CAAa,YAAA,CACRmI,UAAQnI,CAAAA,CAAa,SAAA,CAAW,eAAe,CACtD,EAEAM,CAAAA,CAAa,OAAA,CAAQ,oBAAoB,CAAA,CAErChX,CAAAA,CAAQ,QAAS,CACnBK,CAAAA,CACE,CAAA,iBAAA,EAAoBE,CAAAA,CAAY,KAC9Bue,CAAAA,CAAiB,KAAA,CAAM,MAAA,CAAO,QAAA,EAChC,CAAC,CAAA,OAAA,CACH,CAAA,CAAE,OAAA,GACF,IAAA,IAAWtO,CAAAA,IAAQsO,EAAiB,KAAA,CAAO,CACzCxe,EAAO,GAAA,CAAI,CAAA,IAAA,EAAOkQ,CAAAA,CAAK,IAAI,KAAKjQ,CAAAA,CAAY,IAAA,CAAKiQ,CAAAA,CAAK,IAAI,CAAC,CAAA,CAAA,CAAG,CAAA,CAC9D,IAAA,IAAWrD,CAAAA,IAAQqD,EAAK,KAAA,EAAS,GAC/BlQ,CAAAA,CAAO,GAAA,CAAI,SAAS6M,CAAAA,CAAK,IAAI,CAAA,CAAE,EAEnC,CACF,CACF,CAAA,MAASpI,CAAAA,CAAO,CACdzE,EAAO,KAAA,EAAM,CACb8L,CAAAA,CAAYrH,CAAK,EACnB,CACF,CAGA,eAAega,EAAAA,CACb1O,CAAAA,CACAxO,EACApB,CAAAA,CACyC,CACzC,IAAA,IAAW+P,CAAAA,IAAQH,EAAS,KAAA,CAC1B,GAAKG,CAAAA,CAAK,KAAA,EAAO,OAKjB,IAAA,IAAWrD,CAAAA,IAAQqD,CAAAA,CAAK,KAAA,CAAO,CAC7B,IAAM4O,CAAAA,CAAU,MAAMC,CAAAA,CACpBlS,CAAAA,CAAK,KACLtL,CAAAA,CACApB,CACF,CAAA,CAGA2e,CAAAA,CAAQ,MAAQA,CAAAA,CAAQ,KAAA,EAAO,MAAA,CAAQE,CAAAA,EAAMA,EAAE,IAAA,GAASnS,CAAAA,CAAK,IAAI,CAAA,CAE7DiS,EAAQ,KAAA,EACV5O,CAAAA,CAAK,MAAM,IAAA,CAAK,GAAG4O,EAAQ,KAAK,CAAA,CAG9BA,CAAAA,CAAQ,YAAA,GACV5O,EAAK,YAAA,CAAeA,CAAAA,CAAK,YAAA,CACrBA,CAAAA,CAAK,aAAa,MAAA,CAAO4O,CAAAA,CAAQ,YAAY,CAAA,CAC7CA,EAAQ,YAAA,EAEhB,CAGF,OAAO/O,CACT,CCtNO,IAAMmI,GAAM,IAAIxF,OAAAA,EAAQ,CAC5B,IAAA,CAAK,cAAc,CAAA,CACnB,WAAA,CAAY,6CAA6C,CAAA,CACzD,OACC,iBAAA,CACA,2DAAA,CACA,QAAQ,GAAA,EACV,EACC,MAAA,CAAO,SAAY,CAClB1S,CAAAA,CAAO,KACL,CAAA,IAAA,EAAOC,CAAAA,CAAY,IAAA,CACjB,qBACF,CAAC,CAAA,gCAAA,EAAmCA,CAAAA,CAAY,IAAA,CAC9C,YACF,CAAC,CAAA,iBAAA,CACH,CAAA,CACAD,EAAO,KAAA,GACT,CAAC,CAAA,CCPH,IAAMif,EAAAA,CAAsB7W,IAAE,MAAA,CAAO,CACnC,IAAKA,GAAAA,CAAE,MAAA,EAAO,CACd,KAAA,CAAOA,IAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC3B,MAAOA,GAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,GAClB,MAAA,CAAQA,GAAAA,CAAE,QAAO,CAAE,QAAA,EACrB,CAAC,CAAA,CAKY8W,EAAAA,CAAS,IAAIxM,SAAQ,CAC/B,IAAA,CAAK,QAAQ,CAAA,CACb,MAAM,MAAM,CAAA,CACZ,WAAA,CAAY,8BAA8B,EAC1C,QAAA,CACC,iBAAA,CACA,iFACF,CAAA,CACC,MAAA,CACC,kBACA,2DAAA,CACA,OAAA,CAAQ,GAAA,EACV,EACC,MAAA,CAAO,qBAAA,CAAuB,cAAc,CAAA,CAC5C,OACC,sBAAA,CACA,iDAAA,CACA,KACF,CAAA,CACC,OAAO,uBAAA,CAAyB,yBAAA,CAA2B,GAAG,CAAA,CAC9D,MAAA,CAAO,MAAOoB,CAAAA,CAAsBnB,CAAAA,GAAS,CAC5C,GAAI,CACF,IAAMjT,CAAAA,CAAUuf,EAAAA,CAAoB,KAAA,CAAM,CACxC,GAAA,CAAKpf,UAAAA,CAAK,OAAA,CAAQ8S,CAAAA,CAAK,GAAG,CAAA,CAC1B,KAAA,CAAOA,EAAK,KAAA,CACZ,KAAA,CAAOA,EAAK,KAAA,CAAQ,QAAA,CAASA,CAAAA,CAAK,KAAA,CAAO,EAAE,CAAA,CAAI,KAAA,CAAA,CAC/C,MAAA,CAAQA,CAAAA,CAAK,OAAS,QAAA,CAASA,CAAAA,CAAK,MAAA,CAAQ,EAAE,EAAI,KAAA,CACpD,CAAC,EAED,MAAM7D,CAAAA,CAAapP,EAAQ,GAAG,CAAA,CAI9B,IAAMwU,GAAAA,CAAgBmB,EAAa,CACjC,KAAA,CAAO,UAAA,CACP,aAAA,CAAe,CACb,GAAA,CAAK3V,CAAAA,CAAQ,GACf,CACF,CAAC,CAAA,CACGkT,CAAAA,CAAe/G,EAAmBqI,GAAa,CAAA,CAG7CrB,IAAqBhT,UAAAA,CAAK,OAAA,CAAQH,CAAAA,CAAQ,GAAA,CAAK,iBAAiB,CAAA,CACtE,GAAI4P,CAAAA,CAAQ,UAAA,CAAWuD,GAAkB,CAAA,CAAG,CAC1C,IAAMC,CAAAA,CAAiB,MAAMxD,CAAAA,CAAQ,QAAA,CAASuD,GAAkB,CAAA,CAC1DsM,GAAAA,CAAgBlO,EAAgB,OAAA,EAAQ,CAAE,KAAA,CAAM6B,CAAc,EACpEF,CAAAA,CAAe/G,CAAAA,CAAmB,CAChC,GAAGqI,IACH,GAAGiL,GACL,CAAC,EACH,CAGA,IAAI5d,CAAAA,CAASqR,EACb,GAAI,CACF,IAAMqB,CAAAA,CAAa,MAAMb,CAAAA,CAAU1T,CAAAA,CAAQ,GAAG,CAAA,CAC1CuU,CAAAA,GACF1S,CAAAA,CAASsK,CAAAA,CAAmBoI,CAAU,CAAA,EAE1C,CAAA,KAAQ,CAER,CAEA,GAAM,CAAE,MAAA,CAAQjD,EAAe,aAAA,CAAAuE,CAAc,EAC3C,MAAMjF,CAAAA,CACJwD,CAAAA,CAAW,GAAA,CAAK/D,GAAa,CAAA,EAAGA,CAAQ,CAAA,SAAA,CAAW,CAAA,CACnDxO,EACA,CACE,MAAA,CAAQ,CAAA,CAAA,CACR,SAAA,CAAW,EACb,CACF,CAAA,CACEgU,EAAc,MAAA,CAAS,CAAA,GACzBhU,EAAO,UAAA,CAAayP,CAAAA,CAAc,UAAA,CAAA,CAIpCoO,CAAAA,CAA+BtL,EAAYvS,CAAM,CAAA,CAGjD,IAAMud,CAAAA,CAAU,MAAMO,EAAAA,CAAiBvL,CAAAA,CAA8B,CACnE,KAAA,CAAOpU,EAAQ,KAAA,CACf,KAAA,CAAOA,EAAQ,KAAA,CACf,MAAA,CAAQA,EAAQ,MAAA,CAChB,MAAA,CAAA6B,CACF,CAAC,EAED,OAAA,CAAQ,GAAA,CAAI,IAAA,CAAK,SAAA,CAAUud,EAAS,IAAA,CAAM,CAAC,CAAC,CAAA,CAC5C,QAAQ,IAAA,CAAK,CAAC,EAChB,CAAA,MAASra,CAAAA,CAAO,CACdqH,CAAAA,CAAYrH,CAAK,EACnB,CAAA,OAAE,CACAwO,CAAAA,GACF,CACF,CAAC,ECxGH,IAAMqM,GAAoBlX,GAAAA,CAAE,MAAA,CAAO,CACjC,GAAA,CAAKA,IAAE,MAAA,EACT,CAAC,CAAA,CAEYmX,GAAO,IAAI7M,OAAAA,GACrB,IAAA,CAAK,MAAM,EACX,WAAA,CAAY,8BAA8B,CAAA,CAC1C,QAAA,CAAS,aAAc,gCAAgC,CAAA,CACvD,MAAA,CACC,iBAAA,CACA,4DACA,OAAA,CAAQ,GAAA,EACV,CAAA,CACC,OAAO,MAAO8M,CAAAA,CAAiB7M,IAAS,CACvC,GAAI,CACF,IAAMjT,CAAAA,CAAU4f,EAAAA,CAAkB,KAAA,CAAM,CACtC,GAAA,CAAKzf,UAAAA,CAAK,OAAA,CAAQ8S,CAAAA,CAAK,GAAG,CAC5B,CAAC,CAAA,CAED,MAAM7D,EAAapP,CAAAA,CAAQ,GAAG,EAG9B,IAAIkT,GAAAA,CAAe/G,EAAmB,EAAE,CAAA,CAGlCgH,CAAAA,CAAqBhT,WAAK,OAAA,CAAQH,CAAAA,CAAQ,GAAA,CAAK,iBAAiB,EACtE,GAAI4P,CAAAA,CAAQ,UAAA,CAAWuD,CAAkB,EAAG,CAC1C,IAAMC,EAAiB,MAAMxD,CAAAA,CAAQ,SAASuD,CAAkB,CAAA,CAC1DsM,CAAAA,CAAgBlO,CAAAA,CAAgB,SAAQ,CAAE,KAAA,CAAM6B,CAAc,CAAA,CACpEF,IAAe/G,CAAAA,CAAmBsT,CAAa,EACjD,CAGA,IAAI5d,CAAAA,CAASqR,GAAAA,CACb,GAAI,CACF,IAAMqB,EAAa,MAAMb,CAAAA,CAAU1T,CAAAA,CAAQ,GAAG,EAC1CuU,CAAAA,GACF1S,CAAAA,CAASsK,CAAAA,CAAmBoI,CAAU,GAE1C,CAAA,KAAQ,CAER,CAEA,GAAM,CAAE,MAAA,CAAQjD,CAAAA,CAAe,cAAAuE,CAAc,CAAA,CAC3C,MAAMjF,CAAAA,CAAyBkP,CAAAA,CAAOje,CAAAA,CAAQ,CAC5C,OAAQ,CAAA,CAAA,CACR,SAAA,CAAW,CAAA,CACb,CAAC,EACCgU,CAAAA,CAAc,MAAA,CAAS,CAAA,GACzBhU,CAAAA,CAAO,WAAayP,CAAAA,CAAc,UAAA,CAAA,CAIpCoO,EAA+BI,CAAAA,CAAOje,CAAM,EAE5C,IAAM6V,CAAAA,CAAU,MAAM/J,GAAAA,CAAiBmS,EAAO,CAAE,MAAA,CAAAje,CAAO,CAAC,EACxD,OAAA,CAAQ,GAAA,CAAI,IAAA,CAAK,SAAA,CAAU6V,EAAS,IAAA,CAAM,CAAC,CAAC,CAAA,CAC5C,OAAA,CAAQ,KAAK,CAAC,EAChB,CAAA,MAAS3S,CAAAA,CAAO,CACdqH,CAAAA,CAAYrH,CAAK,EACnB,CAAA,OAAE,CACAwO,CAAAA,GACF,CACF,CAAC,EC7EH,IAAAwM,GAAA,CAEE,OAAA,CAAW,OAyGb,CAAA,CC3FA,OAAA,CAAQ,EAAA,CAAG,QAAA,CAAU,IAAM,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAC,EAC1C,OAAA,CAAQ,EAAA,CAAG,UAAW,IAAM,OAAA,CAAQ,KAAK,CAAC,CAAC,CAAA,CAE3C,eAAeC,IAAO,CACpB,IAAMC,CAAAA,CAAU,IAAIjN,SAAQ,CACzB,IAAA,CAAK,QAAQ,CAAA,CACb,YAAY,2CAA2C,CAAA,CACvD,QACC+M,EAAAA,CAAY,QACZ,eAAA,CACA,4BACF,CAAA,CAEFE,CAAAA,CACG,WAAWlN,EAAI,CAAA,CACf,UAAA,CAAW0C,EAAG,EACd,UAAA,CAAWyB,EAAI,CAAA,CACf,UAAA,CAAW2I,EAAI,CAAA,CACf,UAAA,CAAWL,EAAM,CAAA,CACjB,UAAA,CAAWd,EAAO,CAAA,CAClB,UAAA,CAAWtG,EAAI,CAAA,CACf,WAAWxB,EAAK,CAAA,CAChB,UAAA,CAAW4B,EAAG,EAEjByH,CAAAA,CAAQ,UAAA,CAAWrJ,EAAa,CAAA,CAAE,WAAW4B,EAAW,CAAA,CAExDyH,EAAQ,KAAA,GACV,CAEAD,EAAAA,EAAK", "file": "index.js", "sourcesContent": ["import path from \"path\"\nimport { initOptionsSchema } from \"@/src/commands/init\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport fs from \"fs-extra\"\nimport { z } from \"zod\"\n\nexport async function preFlightInit(\n  options: z.infer<typeof initOptionsSchema>\n) {\n  const errors: Record<string, boolean> = {}\n\n  // Ensure target directory exists.\n  // Check for empty project. We assume if no package.json exists, the project is empty.\n  if (\n    !fs.existsSync(options.cwd) ||\n    !fs.existsSync(path.resolve(options.cwd, \"package.json\"))\n  ) {\n    errors[ERRORS.MISSING_DIR_OR_EMPTY_PROJECT] = true\n    return {\n      errors,\n      projectInfo: null,\n    }\n  }\n\n  const projectSpinner = spinner(`Preflight checks.`, {\n    silent: options.silent,\n  }).start()\n\n  if (\n    fs.existsSync(path.resolve(options.cwd, \"components.json\")) &&\n    !options.force\n  ) {\n    projectSpinner?.fail()\n    logger.break()\n    logger.error(\n      `A ${highlighter.info(\n        \"components.json\"\n      )} file already exists at ${highlighter.info(\n        options.cwd\n      )}.\\nTo start over, remove the ${highlighter.info(\n        \"components.json\"\n      )} file and run ${highlighter.info(\"init\")} again.`\n    )\n    logger.break()\n    process.exit(1)\n  }\n\n  projectSpinner?.succeed()\n\n  const frameworkSpinner = spinner(`Verifying framework.`, {\n    silent: options.silent,\n  }).start()\n  const projectInfo = await getProjectInfo(options.cwd)\n  if (!projectInfo || projectInfo?.framework.name === \"manual\") {\n    errors[ERRORS.UNSUPPORTED_FRAMEWORK] = true\n    frameworkSpinner?.fail()\n    logger.break()\n    if (projectInfo?.framework.links.installation) {\n      logger.error(\n        `We could not detect a supported framework at ${highlighter.info(\n          options.cwd\n        )}.\\n` +\n          `Visit ${highlighter.info(\n            projectInfo?.framework.links.installation\n          )} to manually configure your project.\\nOnce configured, you can use the cli to add components.`\n      )\n    }\n    logger.break()\n    process.exit(1)\n  }\n  frameworkSpinner?.succeed(\n    `Verifying framework. Found ${highlighter.info(\n      projectInfo.framework.label\n    )}.`\n  )\n\n  let tailwindSpinnerMessage = \"Validating Tailwind CSS.\"\n\n  if (projectInfo.tailwindVersion === \"v4\") {\n    tailwindSpinnerMessage = `Validating Tailwind CSS config. Found ${highlighter.info(\n      \"v4\"\n    )}.`\n  }\n\n  const tailwindSpinner = spinner(tailwindSpinnerMessage, {\n    silent: options.silent,\n  }).start()\n  if (\n    projectInfo.tailwindVersion === \"v3\" &&\n    (!projectInfo?.tailwindConfigFile || !projectInfo?.tailwindCssFile)\n  ) {\n    errors[ERRORS.TAILWIND_NOT_CONFIGURED] = true\n    tailwindSpinner?.fail()\n  } else if (\n    projectInfo.tailwindVersion === \"v4\" &&\n    !projectInfo?.tailwindCssFile\n  ) {\n    errors[ERRORS.TAILWIND_NOT_CONFIGURED] = true\n    tailwindSpinner?.fail()\n  } else if (!projectInfo.tailwindVersion) {\n    errors[ERRORS.TAILWIND_NOT_CONFIGURED] = true\n    tailwindSpinner?.fail()\n  } else {\n    tailwindSpinner?.succeed()\n  }\n\n  const tsConfigSpinner = spinner(`Validating import alias.`, {\n    silent: options.silent,\n  }).start()\n  if (!projectInfo?.aliasPrefix) {\n    errors[ERRORS.IMPORT_ALIAS_MISSING] = true\n    tsConfigSpinner?.fail()\n  } else {\n    tsConfigSpinner?.succeed()\n  }\n\n  if (Object.keys(errors).length > 0) {\n    if (errors[ERRORS.TAILWIND_NOT_CONFIGURED]) {\n      logger.break()\n      logger.error(\n        `No Tailwind CSS configuration found at ${highlighter.info(\n          options.cwd\n        )}.`\n      )\n      logger.error(\n        `It is likely you do not have Tailwind CSS installed or have an invalid configuration.`\n      )\n      logger.error(`Install Tailwind CSS then try again.`)\n      if (projectInfo?.framework.links.tailwind) {\n        logger.error(\n          `Visit ${highlighter.info(\n            projectInfo?.framework.links.tailwind\n          )} to get started.`\n        )\n      }\n    }\n\n    if (errors[ERRORS.IMPORT_ALIAS_MISSING]) {\n      logger.break()\n      logger.error(`No import alias found in your tsconfig.json file.`)\n      if (projectInfo?.framework.links.installation) {\n        logger.error(\n          `Visit ${highlighter.info(\n            projectInfo?.framework.links.installation\n          )} to learn how to set an import alias.`\n        )\n      }\n    }\n\n    logger.break()\n    process.exit(1)\n  }\n\n  return {\n    errors,\n    projectInfo,\n  }\n}\n", "import path from \"path\"\n\nexport function isSafeTarget(targetPath: string, cwd: string): boolean {\n  // Check for null bytes which can be used to bypass validations.\n  if (targetPath.includes(\"\\0\")) {\n    return false\n  }\n\n  // Decode URL-encoded sequences to catch encoded traversal attempts.\n  let decodedPath: string\n  try {\n    // Decode multiple times to catch double-encoded sequences.\n    decodedPath = targetPath\n    let prevPath = \"\"\n    while (decodedPath !== prevPath && decodedPath.includes(\"%\")) {\n      prevPath = decodedPath\n      decodedPath = decodeURIComponent(decodedPath)\n    }\n  } catch {\n    // If decoding fails, treat as unsafe.\n    return false\n  }\n\n  // Normalize both paths to handle different path separators.\n  // Convert Windows backslashes to forward slashes for consistent handling.\n  const normalizedTarget = path.normalize(decodedPath.replace(/\\\\/g, \"/\"))\n  const normalizedRoot = path.normalize(cwd)\n\n  // Check for explicit path traversal sequences in both encoded and decoded forms.\n  // Allow [...] pattern which is common in framework routing (e.g., [...slug])\n  const hasPathTraversal = (path: string) => {\n    // Remove [...] patterns before checking for ..\n    const withoutBrackets = path.replace(/\\[\\.\\.\\..*?\\]/g, \"\")\n    return withoutBrackets.includes(\"..\")\n  }\n\n  if (\n    hasPathTraversal(normalizedTarget) ||\n    hasPathTraversal(decodedPath) ||\n    hasPathTraversal(targetPath)\n  ) {\n    return false\n  }\n\n  // Check for current directory references that might be used in traversal.\n  // First, remove [...] patterns to avoid false positives\n  const cleanPath = (path: string) => path.replace(/\\[\\.\\.\\..*?\\]/g, \"\")\n  const cleanTarget = cleanPath(targetPath)\n  const cleanDecoded = cleanPath(decodedPath)\n\n  const suspiciousPatterns = [\n    /\\.\\.[\\/\\\\]/, // ../ or ..\\\n    /[\\/\\\\]\\.\\./, // /.. or \\..\n    /\\.\\./, // .. anywhere\n    /\\.\\.%/, // URL encoded traversal\n    /\\x00/, // null byte\n    /[\\x01-\\x1f]/, // control characters\n  ]\n\n  if (\n    suspiciousPatterns.some(\n      (pattern) => pattern.test(cleanTarget) || pattern.test(cleanDecoded)\n    )\n  ) {\n    return false\n  }\n\n  // Allow ~/ at the start (home directory expansion within project) but reject ~/../ patterns.\n  if (\n    (targetPath.includes(\"~\") || decodedPath.includes(\"~\")) &&\n    (targetPath.includes(\"../\") || decodedPath.includes(\"../\"))\n  ) {\n    return false\n  }\n\n  // Check for Windows drive letters (even on non-Windows systems for safety).\n  const driveLetterRegex = /^[a-zA-Z]:[\\/\\\\]/\n  if (driveLetterRegex.test(decodedPath)) {\n    // On Windows, check if it starts with the project root.\n    if (process.platform === \"win32\") {\n      return decodedPath.toLowerCase().startsWith(cwd.toLowerCase())\n    }\n    // On non-Windows systems, reject all Windows absolute paths.\n    return false\n  }\n\n  // If it's an absolute path, ensure it's within the project root.\n  if (path.isAbsolute(normalizedTarget)) {\n    return normalizedTarget.startsWith(normalizedRoot + path.sep)\n  }\n\n  // For relative paths, resolve and check if within project bounds.\n  const resolvedPath = path.resolve(normalizedRoot, normalizedTarget)\n  return (\n    resolvedPath.startsWith(normalizedRoot + path.sep) ||\n    resolvedPath === normalizedRoot\n  )\n}\n", "import { promises as fs } from \"fs\"\nimport path from \"path\"\nimport { registryItemCssSchema } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport postcss from \"postcss\"\nimport AtRule from \"postcss/lib/at-rule\"\nimport Declaration from \"postcss/lib/declaration\"\nimport Root from \"postcss/lib/root\"\nimport Rule from \"postcss/lib/rule\"\nimport { z } from \"zod\"\n\nexport async function updateCss(\n  css: z.infer<typeof registryItemCssSchema> | undefined,\n  config: Config,\n  options: {\n    silent?: boolean\n  }\n) {\n  if (\n    !config.resolvedPaths.tailwindCss ||\n    !css ||\n    Object.keys(css).length === 0\n  ) {\n    return\n  }\n\n  options = {\n    silent: false,\n    ...options,\n  }\n\n  const cssFilepath = config.resolvedPaths.tailwindCss\n  const cssFilepathRelative = path.relative(\n    config.resolvedPaths.cwd,\n    cssFilepath\n  )\n  const cssSpinner = spinner(\n    `Updating ${highlighter.info(cssFilepathRelative)}`,\n    {\n      silent: options.silent,\n    }\n  ).start()\n\n  const raw = await fs.readFile(cssFilepath, \"utf8\")\n  let output = await transformCss(raw, css)\n  await fs.writeFile(cssFilepath, output, \"utf8\")\n  cssSpinner.succeed()\n}\n\nexport async function transformCss(\n  input: string,\n  css: z.infer<typeof registryItemCssSchema>\n) {\n  const plugins = [updateCssPlugin(css)]\n\n  const result = await postcss(plugins).process(input, {\n    from: undefined,\n  })\n\n  let output = result.css\n\n  // PostCSS doesn't add semicolons to at-rules without bodies when they're the last node.\n  // We need to manually ensure they have semicolons.\n  const root = result.root\n  if (root.nodes && root.nodes.length > 0) {\n    const lastNode = root.nodes[root.nodes.length - 1]\n    if (\n      lastNode.type === \"atrule\" &&\n      !lastNode.nodes &&\n      !output.trimEnd().endsWith(\";\")\n    ) {\n      output = output.trimEnd() + \";\"\n    }\n  }\n\n  output = output.replace(/\\/\\* ---break--- \\*\\//g, \"\")\n  output = output.replace(/(\\n\\s*\\n)+/g, \"\\n\\n\")\n  output = output.trimEnd()\n\n  return output\n}\n\nfunction updateCssPlugin(css: z.infer<typeof registryItemCssSchema>) {\n  return {\n    postcssPlugin: \"update-css\",\n    Once(root: Root) {\n      for (const [selector, properties] of Object.entries(css)) {\n        if (selector.startsWith(\"@\")) {\n          // Handle at-rules (@layer, @utility, etc.)\n          const atRuleMatch = selector.match(/@([a-zA-Z-]+)\\s*(.*)/)\n          if (!atRuleMatch) continue\n\n          const [, name, params] = atRuleMatch\n\n          // Special handling for imports - place them at the top.\n          if (name === \"import\") {\n            // Check if this import already exists.\n            const existingImport = root.nodes?.find(\n              (node): node is AtRule =>\n                node.type === \"atrule\" &&\n                node.name === \"import\" &&\n                node.params === params\n            )\n\n            if (!existingImport) {\n              const importRule = postcss.atRule({\n                name: \"import\",\n                params,\n                raws: { semicolon: true },\n              })\n\n              // Find the last import to insert after, or insert at beginning.\n              const importNodes = root.nodes?.filter(\n                (node): node is AtRule =>\n                  node.type === \"atrule\" && node.name === \"import\"\n              )\n\n              if (importNodes && importNodes.length > 0) {\n                // Insert after the last existing import.\n                const lastImport = importNodes[importNodes.length - 1]\n                importRule.raws.before = \"\\n\"\n                root.insertAfter(lastImport, importRule)\n              } else {\n                // No imports exist, insert at the very beginning.\n                // Check if the file is empty.\n                if (!root.nodes || root.nodes.length === 0) {\n                  importRule.raws.before = \"\"\n                } else {\n                  importRule.raws.before = \"\"\n                }\n                root.prepend(importRule)\n              }\n            }\n          }\n          // Special handling for plugins - place them after imports.\n          else if (name === \"plugin\") {\n            // Ensure plugin name is quoted if not already.\n            let quotedParams = params\n            if (params && !params.startsWith('\"') && !params.startsWith(\"'\")) {\n              quotedParams = `\"${params}\"`\n            }\n\n            // Normalize params for comparison (remove quotes).\n            const normalizeParams = (p: string) => {\n              if (p.startsWith('\"') && p.endsWith('\"')) {\n                return p.slice(1, -1)\n              }\n              if (p.startsWith(\"'\") && p.endsWith(\"'\")) {\n                return p.slice(1, -1)\n              }\n              return p\n            }\n\n            // Find existing plugin with same normalized params.\n            const existingPlugin = root.nodes?.find((node): node is AtRule => {\n              if (node.type !== \"atrule\" || node.name !== \"plugin\") {\n                return false\n              }\n              return normalizeParams(node.params) === normalizeParams(params)\n            })\n\n            if (!existingPlugin) {\n              const pluginRule = postcss.atRule({\n                name: \"plugin\",\n                params: quotedParams,\n                raws: { semicolon: true, before: \"\\n\" },\n              })\n\n              // Find the last import or plugin node to insert after\n              const importNodes = root.nodes?.filter(\n                (node): node is AtRule =>\n                  node.type === \"atrule\" && node.name === \"import\"\n              )\n\n              const pluginNodes = root.nodes?.filter(\n                (node): node is AtRule =>\n                  node.type === \"atrule\" && node.name === \"plugin\"\n              )\n\n              if (pluginNodes && pluginNodes.length > 0) {\n                // Insert after the last existing plugin\n                const lastPlugin = pluginNodes[pluginNodes.length - 1]\n                root.insertAfter(lastPlugin, pluginRule)\n              } else if (importNodes && importNodes.length > 0) {\n                // Insert after the last import if no plugins exist\n                const lastImport = importNodes[importNodes.length - 1]\n                root.insertAfter(lastImport, pluginRule)\n                // Add a break comment before the first plugin to create spacing\n                root.insertBefore(\n                  pluginRule,\n                  postcss.comment({ text: \"---break---\" })\n                )\n                // Add a break comment after the plugin for spacing from other content\n                root.insertAfter(\n                  pluginRule,\n                  postcss.comment({ text: \"---break---\" })\n                )\n              } else {\n                // If no imports or plugins, insert at the beginning\n                root.prepend(pluginRule)\n                // Add a break comment before the first plugin for spacing\n                root.insertBefore(\n                  pluginRule,\n                  postcss.comment({ text: \"---break---\" })\n                )\n                // Add a break comment after the plugin for spacing from other content\n                root.insertAfter(\n                  pluginRule,\n                  postcss.comment({ text: \"---break---\" })\n                )\n              }\n            }\n          }\n          // Check if this is any at-rule with no body (empty object).\n          else if (\n            typeof properties === \"object\" &&\n            Object.keys(properties).length === 0\n          ) {\n            // Handle any at-rule with no body (e.g., @apply, @tailwind, etc.).\n            const atRule = root.nodes?.find(\n              (node): node is AtRule =>\n                node.type === \"atrule\" &&\n                node.name === name &&\n                node.params === params\n            ) as AtRule | undefined\n\n            if (!atRule) {\n              const newAtRule = postcss.atRule({\n                name,\n                params,\n                raws: { semicolon: true },\n              })\n\n              root.append(newAtRule)\n              root.insertBefore(\n                newAtRule,\n                postcss.comment({ text: \"---break---\" })\n              )\n            }\n          }\n          // Special handling for keyframes - place them under @theme inline.\n          else if (name === \"keyframes\") {\n            let themeInline = root.nodes?.find(\n              (node): node is AtRule =>\n                node.type === \"atrule\" &&\n                node.name === \"theme\" &&\n                node.params === \"inline\"\n            ) as AtRule | undefined\n\n            if (!themeInline) {\n              themeInline = postcss.atRule({\n                name: \"theme\",\n                params: \"inline\",\n                raws: { semicolon: true, between: \" \", before: \"\\n\" },\n              })\n              root.append(themeInline)\n              root.insertBefore(\n                themeInline,\n                postcss.comment({ text: \"---break---\" })\n              )\n            }\n\n            const keyframesRule = postcss.atRule({\n              name: \"keyframes\",\n              params,\n              raws: { semicolon: true, between: \" \", before: \"\\n  \" },\n            })\n\n            themeInline.append(keyframesRule)\n\n            if (typeof properties === \"object\") {\n              for (const [step, stepProps] of Object.entries(properties)) {\n                processRule(keyframesRule, step, stepProps)\n              }\n            }\n          }\n          // Special handling for utility classes to preserve property values\n          else if (name === \"utility\") {\n            const utilityAtRule = root.nodes?.find(\n              (node): node is AtRule =>\n                node.type === \"atrule\" &&\n                node.name === name &&\n                node.params === params\n            ) as AtRule | undefined\n\n            if (!utilityAtRule) {\n              const atRule = postcss.atRule({\n                name,\n                params,\n                raws: { semicolon: true, between: \" \", before: \"\\n\" },\n              })\n\n              root.append(atRule)\n              root.insertBefore(\n                atRule,\n                postcss.comment({ text: \"---break---\" })\n              )\n\n              // Add declarations with their values preserved\n              if (typeof properties === \"object\") {\n                for (const [prop, value] of Object.entries(properties)) {\n                  if (typeof value === \"string\") {\n                    const decl = postcss.decl({\n                      prop,\n                      value: value,\n                      raws: { semicolon: true, before: \"\\n    \" },\n                    })\n                    atRule.append(decl)\n                  } else if (typeof value === \"object\") {\n                    processRule(atRule, prop, value)\n                  }\n                }\n              }\n            } else {\n              // Update existing utility class\n              if (typeof properties === \"object\") {\n                for (const [prop, value] of Object.entries(properties)) {\n                  if (typeof value === \"string\") {\n                    const existingDecl = utilityAtRule.nodes?.find(\n                      (node): node is Declaration =>\n                        node.type === \"decl\" && node.prop === prop\n                    )\n\n                    const decl = postcss.decl({\n                      prop,\n                      value: value,\n                      raws: { semicolon: true, before: \"\\n    \" },\n                    })\n\n                    existingDecl\n                      ? existingDecl.replaceWith(decl)\n                      : utilityAtRule.append(decl)\n                  } else if (typeof value === \"object\") {\n                    processRule(utilityAtRule, prop, value)\n                  }\n                }\n              }\n            }\n          }\n          // Handle at-property as regular CSS rules\n          else if (name === \"property\") {\n            processRule(root, selector, properties)\n          } else {\n            // Handle other at-rules normally\n            processAtRule(root, name, params, properties)\n          }\n        } else {\n          // Handle regular CSS rules\n          processRule(root, selector, properties)\n        }\n      }\n    },\n  }\n}\n\nfunction processAtRule(\n  root: Root | AtRule,\n  name: string,\n  params: string,\n  properties: any\n) {\n  // Find or create the at-rule\n  let atRule = root.nodes?.find(\n    (node): node is AtRule =>\n      node.type === \"atrule\" && node.name === name && node.params === params\n  ) as AtRule | undefined\n\n  if (!atRule) {\n    atRule = postcss.atRule({\n      name,\n      params,\n      raws: { semicolon: true, between: \" \", before: \"\\n\" },\n    })\n    root.append(atRule)\n    root.insertBefore(atRule, postcss.comment({ text: \"---break---\" }))\n  }\n\n  // Process children of this at-rule\n  if (typeof properties === \"object\") {\n    for (const [childSelector, childProps] of Object.entries(properties)) {\n      if (childSelector.startsWith(\"@\")) {\n        // Nested at-rule\n        const nestedMatch = childSelector.match(/@([a-zA-Z-]+)\\s*(.*)/)\n        if (nestedMatch) {\n          const [, nestedName, nestedParams] = nestedMatch\n          processAtRule(atRule, nestedName, nestedParams, childProps)\n        }\n      } else {\n        // CSS rule within at-rule\n        processRule(atRule, childSelector, childProps)\n      }\n    }\n  } else if (typeof properties === \"string\") {\n    // Direct string content for the at-rule\n    try {\n      // Parse the CSS string with PostCSS\n      const parsed = postcss.parse(`.temp{${properties}}`)\n      const tempRule = parsed.first as Rule\n\n      if (tempRule && tempRule.nodes) {\n        // Create a rule for the at-rule if needed\n        const rule = postcss.rule({\n          selector: \"temp\",\n          raws: { semicolon: true, between: \" \", before: \"\\n  \" },\n        })\n\n        // Copy all declarations from the temp rule to our actual rule\n        tempRule.nodes.forEach((node) => {\n          if (node.type === \"decl\") {\n            const clone = node.clone()\n            clone.raws.before = \"\\n    \"\n            rule.append(clone)\n          }\n        })\n\n        // Only add the rule if it has declarations\n        if (rule.nodes?.length) {\n          atRule.append(rule)\n        }\n      }\n    } catch (error) {\n      console.error(\"Error parsing at-rule content:\", properties, error)\n      throw error\n    }\n  }\n}\n\nfunction processRule(parent: Root | AtRule, selector: string, properties: any) {\n  let rule = parent.nodes?.find(\n    (node): node is Rule => node.type === \"rule\" && node.selector === selector\n  ) as Rule | undefined\n\n  if (!rule) {\n    rule = postcss.rule({\n      selector,\n      raws: { semicolon: true, between: \" \", before: \"\\n  \" },\n    })\n    parent.append(rule)\n  }\n\n  if (typeof properties === \"object\") {\n    for (const [prop, value] of Object.entries(properties)) {\n      // Check if this is any at-rule with empty object (no body).\n      if (\n        prop.startsWith(\"@\") &&\n        typeof value === \"object\" &&\n        value !== null &&\n        Object.keys(value).length === 0\n      ) {\n        // Parse the at-rule.\n        const atRuleMatch = prop.match(/@([a-zA-Z-]+)\\s*(.*)/)\n        if (atRuleMatch) {\n          const [, atRuleName, atRuleParams] = atRuleMatch\n          const atRule = postcss.atRule({\n            name: atRuleName,\n            params: atRuleParams,\n            raws: { semicolon: true, before: \"\\n    \" },\n          })\n          rule.append(atRule)\n        }\n      } else if (typeof value === \"string\") {\n        const decl = postcss.decl({\n          prop,\n          value: value,\n          raws: { semicolon: true, before: \"\\n    \" },\n        })\n\n        // Replace existing property or add new one.\n        const existingDecl = rule.nodes?.find(\n          (node): node is Declaration =>\n            node.type === \"decl\" && node.prop === prop\n        )\n\n        existingDecl ? existingDecl.replaceWith(decl) : rule.append(decl)\n      } else if (typeof value === \"object\") {\n        // Nested selector (including & selectors).\n        const nestedSelector = prop.startsWith(\"&\")\n          ? selector.replace(/^([^:]+)/, `$1${prop.substring(1)}`)\n          : prop // Use the original selector for other nested elements.\n        processRule(parent, nestedSelector, value)\n      }\n    }\n  } else if (typeof properties === \"string\") {\n    // Direct string content for the rule\n    try {\n      // Parse the CSS string with PostCSS\n      const parsed = postcss.parse(`.temp{${properties}}`)\n      const tempRule = parsed.first as Rule\n\n      if (tempRule && tempRule.nodes) {\n        // Copy all declarations from the temp rule to our actual rule\n        tempRule.nodes.forEach((node) => {\n          if (node.type === \"decl\") {\n            const clone = node.clone()\n            clone.raws.before = \"\\n    \"\n            rule?.append(clone)\n          }\n        })\n      }\n    } catch (error) {\n      console.error(\"Error parsing rule content:\", selector, properties, error)\n      throw error\n    }\n  }\n}\n", "import { promises as fs } from \"fs\"\nimport path from \"path\"\nimport {\n  registryItemCssVarsSchema,\n  registryItemTailwindSchema,\n} from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { getPackageInfo } from \"@/src/utils/get-package-info\"\nimport { TailwindVersion } from \"@/src/utils/get-project-info\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport postcss from \"postcss\"\nimport AtRule from \"postcss/lib/at-rule\"\nimport Root from \"postcss/lib/root\"\nimport Rule from \"postcss/lib/rule\"\nimport { z } from \"zod\"\n\nexport async function updateCssVars(\n  cssVars: z.infer<typeof registryItemCssVarsSchema> | undefined,\n  config: Config,\n  options: {\n    cleanupDefaultNextStyles?: boolean\n    overwriteCssVars?: boolean\n    initIndex?: boolean\n    silent?: boolean\n    tailwindVersion?: TailwindVersion\n    tailwindConfig?: z.infer<typeof registryItemTailwindSchema>[\"config\"]\n  }\n) {\n  if (!config.resolvedPaths.tailwindCss || !Object.keys(cssVars ?? {}).length) {\n    return\n  }\n\n  options = {\n    cleanupDefaultNextStyles: false,\n    silent: false,\n    tailwindVersion: \"v3\",\n    overwriteCssVars: false,\n    initIndex: true,\n    ...options,\n  }\n  const cssFilepath = config.resolvedPaths.tailwindCss\n  const cssFilepathRelative = path.relative(\n    config.resolvedPaths.cwd,\n    cssFilepath\n  )\n  const cssVarsSpinner = spinner(\n    `Updating CSS variables in ${highlighter.info(cssFilepathRelative)}`,\n    {\n      silent: options.silent,\n    }\n  ).start()\n  const raw = await fs.readFile(cssFilepath, \"utf8\")\n  let output = await transformCssVars(raw, cssVars ?? {}, config, {\n    cleanupDefaultNextStyles: options.cleanupDefaultNextStyles,\n    tailwindVersion: options.tailwindVersion,\n    tailwindConfig: options.tailwindConfig,\n    overwriteCssVars: options.overwriteCssVars,\n    initIndex: options.initIndex,\n  })\n  await fs.writeFile(cssFilepath, output, \"utf8\")\n  cssVarsSpinner.succeed()\n}\n\nexport async function transformCssVars(\n  input: string,\n  cssVars: z.infer<typeof registryItemCssVarsSchema>,\n  config: Config,\n  options: {\n    cleanupDefaultNextStyles?: boolean\n    tailwindVersion?: TailwindVersion\n    tailwindConfig?: z.infer<typeof registryItemTailwindSchema>[\"config\"]\n    overwriteCssVars?: boolean\n    initIndex?: boolean\n  } = {\n    cleanupDefaultNextStyles: false,\n    tailwindVersion: \"v3\",\n    tailwindConfig: undefined,\n    overwriteCssVars: false,\n    initIndex: true,\n  }\n) {\n  options = {\n    cleanupDefaultNextStyles: false,\n    tailwindVersion: \"v3\",\n    tailwindConfig: undefined,\n    overwriteCssVars: false,\n    initIndex: true,\n    ...options,\n  }\n\n  let plugins = [updateCssVarsPlugin(cssVars)]\n\n  if (options.cleanupDefaultNextStyles) {\n    plugins.push(cleanupDefaultNextStylesPlugin())\n  }\n\n  if (options.tailwindVersion === \"v4\") {\n    plugins = []\n\n    // Only add tw-animate-css if project does not have tailwindcss-animate\n    if (config.resolvedPaths?.cwd) {\n      const packageInfo = getPackageInfo(config.resolvedPaths.cwd)\n      if (\n        !packageInfo?.dependencies?.[\"tailwindcss-animate\"] &&\n        !packageInfo?.devDependencies?.[\"tailwindcss-animate\"] &&\n        options.initIndex\n      ) {\n        plugins.push(addCustomImport({ params: \"tw-animate-css\" }))\n      }\n    }\n\n    plugins.push(addCustomVariant({ params: \"dark (&:is(.dark *))\" }))\n\n    if (options.cleanupDefaultNextStyles) {\n      plugins.push(cleanupDefaultNextStylesPlugin())\n    }\n\n    plugins.push(\n      updateCssVarsPluginV4(cssVars, {\n        overwriteCssVars: options.overwriteCssVars,\n      })\n    )\n    plugins.push(updateThemePlugin(cssVars))\n\n    if (options.tailwindConfig) {\n      plugins.push(updateTailwindConfigPlugin(options.tailwindConfig))\n      plugins.push(updateTailwindConfigAnimationPlugin(options.tailwindConfig))\n      plugins.push(updateTailwindConfigKeyframesPlugin(options.tailwindConfig))\n    }\n  }\n\n  if (config.tailwind.cssVariables && options.initIndex) {\n    plugins.push(\n      updateBaseLayerPlugin({ tailwindVersion: options.tailwindVersion })\n    )\n  }\n\n  const result = await postcss(plugins).process(input, {\n    from: undefined,\n  })\n\n  let output = result.css\n\n  output = output.replace(/\\/\\* ---break--- \\*\\//g, \"\")\n\n  if (options.tailwindVersion === \"v4\") {\n    output = output.replace(/(\\n\\s*\\n)+/g, \"\\n\\n\")\n  }\n\n  return output\n}\n\nfunction updateBaseLayerPlugin({\n  tailwindVersion,\n}: {\n  tailwindVersion?: TailwindVersion\n}) {\n  return {\n    postcssPlugin: \"update-base-layer\",\n    Once(root: Root) {\n      const requiredRules = [\n        {\n          selector: \"*\",\n          apply:\n            tailwindVersion === \"v4\"\n              ? \"border-border outline-ring/50\"\n              : \"border-border\",\n        },\n        { selector: \"body\", apply: \"bg-background text-foreground\" },\n      ]\n\n      let baseLayer = root.nodes.find(\n        (node): node is AtRule =>\n          node.type === \"atrule\" &&\n          node.name === \"layer\" &&\n          node.params === \"base\" &&\n          requiredRules.every(({ selector, apply }) =>\n            node.nodes?.some(\n              (rule): rule is Rule =>\n                rule.type === \"rule\" &&\n                rule.selector === selector &&\n                rule.nodes.some(\n                  (applyRule): applyRule is AtRule =>\n                    applyRule.type === \"atrule\" &&\n                    applyRule.name === \"apply\" &&\n                    applyRule.params === apply\n                )\n            )\n          )\n      ) as AtRule | undefined\n\n      if (!baseLayer) {\n        baseLayer = postcss.atRule({\n          name: \"layer\",\n          params: \"base\",\n          raws: { semicolon: true, between: \" \", before: \"\\n\" },\n        })\n        root.append(baseLayer)\n        root.insertBefore(baseLayer, postcss.comment({ text: \"---break---\" }))\n      }\n\n      requiredRules.forEach(({ selector, apply }) => {\n        const existingRule = baseLayer?.nodes?.find(\n          (node): node is Rule =>\n            node.type === \"rule\" && node.selector === selector\n        )\n\n        if (!existingRule) {\n          baseLayer?.append(\n            postcss.rule({\n              selector,\n              nodes: [\n                postcss.atRule({\n                  name: \"apply\",\n                  params: apply,\n                  raws: { semicolon: true, before: \"\\n    \" },\n                }),\n              ],\n              raws: { semicolon: true, between: \" \", before: \"\\n  \" },\n            })\n          )\n        }\n      })\n    },\n  }\n}\n\nfunction updateCssVarsPlugin(\n  cssVars: z.infer<typeof registryItemCssVarsSchema>\n) {\n  return {\n    postcssPlugin: \"update-css-vars\",\n    Once(root: Root) {\n      let baseLayer = root.nodes.find(\n        (node) =>\n          node.type === \"atrule\" &&\n          node.name === \"layer\" &&\n          node.params === \"base\"\n      ) as AtRule | undefined\n\n      if (!(baseLayer instanceof AtRule)) {\n        baseLayer = postcss.atRule({\n          name: \"layer\",\n          params: \"base\",\n          nodes: [],\n          raws: {\n            semicolon: true,\n            before: \"\\n\",\n            between: \" \",\n          },\n        })\n        root.append(baseLayer)\n        root.insertBefore(baseLayer, postcss.comment({ text: \"---break---\" }))\n      }\n\n      if (baseLayer !== undefined) {\n        // Add variables for each key in cssVars\n        Object.entries(cssVars).forEach(([key, vars]) => {\n          const selector = key === \"light\" ? \":root\" : `.${key}`\n          // TODO: Fix typecheck.\n          addOrUpdateVars(baseLayer as AtRule, selector, vars)\n        })\n      }\n    },\n  }\n}\n\nfunction removeConflictVars(root: Rule | Root) {\n  const rootRule = root.nodes.find(\n    (node): node is Rule => node.type === \"rule\" && node.selector === \":root\"\n  )\n\n  if (rootRule) {\n    const propsToRemove = [\"--background\", \"--foreground\"]\n\n    rootRule.nodes\n      .filter(\n        (node): node is postcss.Declaration =>\n          node.type === \"decl\" && propsToRemove.includes(node.prop)\n      )\n      .forEach((node) => node.remove())\n\n    if (rootRule.nodes.length === 0) {\n      rootRule.remove()\n    }\n  }\n}\n\nfunction cleanupDefaultNextStylesPlugin() {\n  return {\n    postcssPlugin: \"cleanup-default-next-styles\",\n    Once(root: Root) {\n      const bodyRule = root.nodes.find(\n        (node): node is Rule => node.type === \"rule\" && node.selector === \"body\"\n      )\n      if (bodyRule) {\n        // Remove color from the body node.\n        bodyRule.nodes\n          .find(\n            (node): node is postcss.Declaration =>\n              node.type === \"decl\" &&\n              node.prop === \"color\" &&\n              [\"rgb(var(--foreground-rgb))\", \"var(--foreground)\"].includes(\n                node.value\n              )\n          )\n          ?.remove()\n\n        // Remove background: linear-gradient.\n        bodyRule.nodes\n          .find((node): node is postcss.Declaration => {\n            return (\n              node.type === \"decl\" &&\n              node.prop === \"background\" &&\n              // This is only going to run on create project, so all good.\n              (node.value.startsWith(\"linear-gradient\") ||\n                node.value === \"var(--background)\")\n            )\n          })\n          ?.remove()\n\n        // Remove font-family: Arial, Helvetica, sans-serif;\n        bodyRule.nodes\n          .find(\n            (node): node is postcss.Declaration =>\n              node.type === \"decl\" &&\n              node.prop === \"font-family\" &&\n              node.value === \"Arial, Helvetica, sans-serif\"\n          )\n          ?.remove()\n\n        // If the body rule is empty, remove it.\n        if (bodyRule.nodes.length === 0) {\n          bodyRule.remove()\n        }\n      }\n\n      removeConflictVars(root)\n\n      const darkRootRule = root.nodes.find(\n        (node): node is Rule =>\n          node.type === \"atrule\" &&\n          node.params === \"(prefers-color-scheme: dark)\"\n      )\n\n      if (darkRootRule) {\n        removeConflictVars(darkRootRule)\n        if (darkRootRule.nodes.length === 0) {\n          darkRootRule.remove()\n        }\n      }\n    },\n  }\n}\n\nfunction addOrUpdateVars(\n  baseLayer: AtRule,\n  selector: string,\n  vars: Record<string, string>\n) {\n  let ruleNode = baseLayer.nodes?.find(\n    (node): node is Rule => node.type === \"rule\" && node.selector === selector\n  )\n\n  if (!ruleNode) {\n    if (Object.keys(vars).length > 0) {\n      ruleNode = postcss.rule({\n        selector,\n        raws: { between: \" \", before: \"\\n  \" },\n      })\n      baseLayer.append(ruleNode)\n    }\n  }\n\n  Object.entries(vars).forEach(([key, value]) => {\n    const prop = `--${key.replace(/^--/, \"\")}`\n    const newDecl = postcss.decl({\n      prop,\n      value,\n      raws: { semicolon: true },\n    })\n\n    const existingDecl = ruleNode?.nodes.find(\n      (node): node is postcss.Declaration =>\n        node.type === \"decl\" && node.prop === prop\n    )\n\n    existingDecl ? existingDecl.replaceWith(newDecl) : ruleNode?.append(newDecl)\n  })\n}\n\nfunction updateCssVarsPluginV4(\n  cssVars: z.infer<typeof registryItemCssVarsSchema>,\n  options: {\n    overwriteCssVars?: boolean\n  }\n) {\n  return {\n    postcssPlugin: \"update-css-vars-v4\",\n    Once(root: Root) {\n      Object.entries(cssVars).forEach(([key, vars]) => {\n        let selector = key === \"light\" ? \":root\" : `.${key}`\n\n        if (key === \"theme\") {\n          selector = \"@theme\"\n          const themeNode = upsertThemeNode(root)\n          Object.entries(vars).forEach(([key, value]) => {\n            const prop = `--${key.replace(/^--/, \"\")}`\n            const newDecl = postcss.decl({\n              prop,\n              value,\n              raws: { semicolon: true },\n            })\n\n            const existingDecl = themeNode?.nodes?.find(\n              (node): node is postcss.Declaration =>\n                node.type === \"decl\" && node.prop === prop\n            )\n\n            // Only overwrite if overwriteCssVars is true\n            // i.e for registry:theme and registry:style\n            // We do not want new components to overwrite existing vars.\n            // Keep user defined vars.\n            if (options.overwriteCssVars) {\n              if (existingDecl) {\n                existingDecl.replaceWith(newDecl)\n              } else {\n                themeNode?.append(newDecl)\n              }\n            } else {\n              if (!existingDecl) {\n                themeNode?.append(newDecl)\n              }\n            }\n          })\n          return\n        }\n\n        let ruleNode = root.nodes?.find(\n          (node): node is Rule =>\n            node.type === \"rule\" && node.selector === selector\n        )\n\n        if (!ruleNode && Object.keys(vars).length > 0) {\n          ruleNode = postcss.rule({\n            selector,\n            nodes: [],\n            raws: { semicolon: true, between: \" \", before: \"\\n\" },\n          })\n          root.append(ruleNode)\n          root.insertBefore(ruleNode, postcss.comment({ text: \"---break---\" }))\n        }\n\n        Object.entries(vars).forEach(([key, value]) => {\n          let prop = `--${key.replace(/^--/, \"\")}`\n\n          // Special case for sidebar-background.\n          if (prop === \"--sidebar-background\") {\n            prop = \"--sidebar\"\n          }\n\n          if (isLocalHSLValue(value)) {\n            value = `hsl(${value})`\n          }\n\n          const newDecl = postcss.decl({\n            prop,\n            value,\n            raws: { semicolon: true },\n          })\n          const existingDecl = ruleNode?.nodes.find(\n            (node): node is postcss.Declaration =>\n              node.type === \"decl\" && node.prop === prop\n          )\n\n          // Only overwrite if overwriteCssVars is true\n          // i.e for registry:theme and registry:style\n          // We do not want new components to overwrite existing vars.\n          // Keep user defined vars.\n          if (options.overwriteCssVars) {\n            if (existingDecl) {\n              existingDecl.replaceWith(newDecl)\n            } else {\n              ruleNode?.append(newDecl)\n            }\n          } else {\n            if (!existingDecl) {\n              ruleNode?.append(newDecl)\n            }\n          }\n        })\n      })\n    },\n  }\n}\n\nfunction updateThemePlugin(cssVars: z.infer<typeof registryItemCssVarsSchema>) {\n  return {\n    postcssPlugin: \"update-theme\",\n    Once(root: Root) {\n      // Find unique color names from light and dark.\n      const variables = Array.from(\n        new Set(\n          Object.keys(cssVars).flatMap((key) =>\n            Object.keys(cssVars[key as keyof typeof cssVars] || {})\n          )\n        )\n      )\n\n      if (!variables.length) {\n        return\n      }\n\n      const themeNode = upsertThemeNode(root)\n\n      const themeVarNodes = themeNode.nodes?.filter(\n        (node): node is postcss.Declaration =>\n          node.type === \"decl\" && node.prop.startsWith(\"--\")\n      )\n\n      for (const variable of variables) {\n        const value = Object.values(cssVars).find((vars) => vars[variable])?.[\n          variable\n        ]\n\n        if (!value) {\n          continue\n        }\n\n        if (variable === \"radius\") {\n          const radiusVariables = {\n            sm: \"calc(var(--radius) - 4px)\",\n            md: \"calc(var(--radius) - 2px)\",\n            lg: \"var(--radius)\",\n            xl: \"calc(var(--radius) + 4px)\",\n          }\n          for (const [key, value] of Object.entries(radiusVariables)) {\n            const cssVarNode = postcss.decl({\n              prop: `--radius-${key}`,\n              value,\n              raws: { semicolon: true },\n            })\n            if (\n              themeNode?.nodes?.find(\n                (node): node is postcss.Declaration =>\n                  node.type === \"decl\" && node.prop === cssVarNode.prop\n              )\n            ) {\n              continue\n            }\n            themeNode?.append(cssVarNode)\n          }\n          continue\n        }\n\n        let prop =\n          isLocalHSLValue(value) || isColorValue(value)\n            ? `--color-${variable.replace(/^--/, \"\")}`\n            : `--${variable.replace(/^--/, \"\")}`\n        if (prop === \"--color-sidebar-background\") {\n          prop = \"--color-sidebar\"\n        }\n\n        let propValue = `var(--${variable})`\n        if (prop === \"--color-sidebar\") {\n          propValue = \"var(--sidebar)\"\n        }\n\n        const cssVarNode = postcss.decl({\n          prop,\n          value: propValue,\n          raws: { semicolon: true },\n        })\n        const existingDecl = themeNode?.nodes?.find(\n          (node): node is postcss.Declaration =>\n            node.type === \"decl\" && node.prop === cssVarNode.prop\n        )\n        if (!existingDecl) {\n          if (themeVarNodes?.length) {\n            themeNode?.insertAfter(\n              themeVarNodes[themeVarNodes.length - 1],\n              cssVarNode\n            )\n          } else {\n            themeNode?.append(cssVarNode)\n          }\n        }\n      }\n    },\n  }\n}\n\nfunction upsertThemeNode(root: Root): AtRule {\n  let themeNode = root.nodes.find(\n    (node): node is AtRule =>\n      node.type === \"atrule\" &&\n      node.name === \"theme\" &&\n      node.params === \"inline\"\n  )\n\n  if (!themeNode) {\n    themeNode = postcss.atRule({\n      name: \"theme\",\n      params: \"inline\",\n      nodes: [],\n      raws: { semicolon: true, between: \" \", before: \"\\n\" },\n    })\n    root.append(themeNode)\n    root.insertBefore(themeNode, postcss.comment({ text: \"---break---\" }))\n  }\n\n  return themeNode\n}\n\nfunction addCustomVariant({ params }: { params: string }) {\n  return {\n    postcssPlugin: \"add-custom-variant\",\n    Once(root: Root) {\n      const customVariant = root.nodes.find(\n        (node): node is AtRule =>\n          node.type === \"atrule\" && node.name === \"custom-variant\"\n      )\n\n      if (!customVariant) {\n        // Find all import nodes\n        const importNodes = root.nodes.filter(\n          (node): node is AtRule =>\n            node.type === \"atrule\" && node.name === \"import\"\n        )\n\n        const variantNode = postcss.atRule({\n          name: \"custom-variant\",\n          params,\n          raws: { semicolon: true, before: \"\\n\" },\n        })\n\n        if (importNodes.length > 0) {\n          // Insert after the last import\n          const lastImport = importNodes[importNodes.length - 1]\n          root.insertAfter(lastImport, variantNode)\n        } else {\n          // If no imports, insert after the first node\n          root.insertAfter(root.nodes[0], variantNode)\n        }\n\n        root.insertBefore(variantNode, postcss.comment({ text: \"---break---\" }))\n      }\n    },\n  }\n}\n\nfunction addCustomImport({ params }: { params: string }) {\n  return {\n    postcssPlugin: \"add-custom-import\",\n    Once(root: Root) {\n      const importNodes = root.nodes.filter(\n        (node): node is AtRule =>\n          node.type === \"atrule\" && node.name === \"import\"\n      )\n\n      // Find custom variant node (to ensure we insert before it)\n      const customVariantNode = root.nodes.find(\n        (node): node is AtRule =>\n          node.type === \"atrule\" && node.name === \"custom-variant\"\n      )\n\n      // Check if our specific import already exists\n      const hasImport = importNodes.some(\n        (node) => node.params.replace(/[\"']/g, \"\") === params\n      )\n\n      if (!hasImport) {\n        const importNode = postcss.atRule({\n          name: \"import\",\n          params: `\"${params}\"`,\n          raws: { semicolon: true, before: \"\\n\" },\n        })\n\n        if (importNodes.length > 0) {\n          // If there are existing imports, add after the last import\n          const lastImport = importNodes[importNodes.length - 1]\n          root.insertAfter(lastImport, importNode)\n        } else if (customVariantNode) {\n          // If no imports but has custom-variant, insert before it\n          root.insertBefore(customVariantNode, importNode)\n          root.insertBefore(\n            customVariantNode,\n            postcss.comment({ text: \"---break---\" })\n          )\n        } else {\n          // If no imports and no custom-variant, insert at the start\n          root.prepend(importNode)\n          root.insertAfter(importNode, postcss.comment({ text: \"---break---\" }))\n        }\n      }\n    },\n  }\n}\n\nfunction updateTailwindConfigPlugin(\n  tailwindConfig: z.infer<typeof registryItemTailwindSchema>[\"config\"]\n) {\n  return {\n    postcssPlugin: \"update-tailwind-config\",\n    Once(root: Root) {\n      if (!tailwindConfig?.plugins) {\n        return\n      }\n\n      const quoteType = getQuoteType(root)\n      const quote = quoteType === \"single\" ? \"'\" : '\"'\n\n      const pluginNodes = root.nodes.filter(\n        (node): node is AtRule =>\n          node.type === \"atrule\" && node.name === \"plugin\"\n      )\n\n      const lastPluginNode =\n        pluginNodes[pluginNodes.length - 1] || root.nodes[0]\n\n      for (const plugin of tailwindConfig.plugins) {\n        const pluginName = plugin.replace(/^require\\([\"']|[\"']\\)$/g, \"\")\n\n        // Check if the plugin is already present.\n        if (\n          pluginNodes.some((node) => {\n            return node.params.replace(/[\"']/g, \"\") === pluginName\n          })\n        ) {\n          continue\n        }\n\n        const pluginNode = postcss.atRule({\n          name: \"plugin\",\n          params: `${quote}${pluginName}${quote}`,\n          raws: { semicolon: true, before: \"\\n\" },\n        })\n        root.insertAfter(lastPluginNode, pluginNode)\n        root.insertBefore(pluginNode, postcss.comment({ text: \"---break---\" }))\n      }\n    },\n  }\n}\n\nfunction updateTailwindConfigKeyframesPlugin(\n  tailwindConfig: z.infer<typeof registryItemTailwindSchema>[\"config\"]\n) {\n  return {\n    postcssPlugin: \"update-tailwind-config-keyframes\",\n    Once(root: Root) {\n      if (!tailwindConfig?.theme?.extend?.keyframes) {\n        return\n      }\n\n      const themeNode = upsertThemeNode(root)\n      const existingKeyFrameNodes = themeNode.nodes?.filter(\n        (node): node is AtRule =>\n          node.type === \"atrule\" && node.name === \"keyframes\"\n      )\n\n      const keyframeValueSchema = z.record(\n        z.string(),\n        z.record(z.string(), z.string())\n      )\n\n      for (const [keyframeName, keyframeValue] of Object.entries(\n        tailwindConfig.theme.extend.keyframes\n      )) {\n        if (typeof keyframeName !== \"string\") {\n          continue\n        }\n\n        const parsedKeyframeValue = keyframeValueSchema.safeParse(keyframeValue)\n\n        if (!parsedKeyframeValue.success) {\n          continue\n        }\n\n        if (\n          existingKeyFrameNodes?.find(\n            (node): node is postcss.AtRule =>\n              node.type === \"atrule\" &&\n              node.name === \"keyframes\" &&\n              node.params === keyframeName\n          )\n        ) {\n          continue\n        }\n\n        const keyframeNode = postcss.atRule({\n          name: \"keyframes\",\n          params: keyframeName,\n          nodes: [],\n          raws: { semicolon: true, between: \" \", before: \"\\n  \" },\n        })\n\n        for (const [key, values] of Object.entries(parsedKeyframeValue.data)) {\n          const rule = postcss.rule({\n            selector: key,\n            nodes: Object.entries(values).map(([key, value]) =>\n              postcss.decl({\n                prop: key,\n                value,\n                raws: { semicolon: true, before: \"\\n      \", between: \": \" },\n              })\n            ),\n            raws: { semicolon: true, between: \" \", before: \"\\n    \" },\n          })\n          keyframeNode.append(rule)\n        }\n\n        themeNode.append(keyframeNode)\n        themeNode.insertBefore(\n          keyframeNode,\n          postcss.comment({ text: \"---break---\" })\n        )\n      }\n    },\n  }\n}\n\nfunction updateTailwindConfigAnimationPlugin(\n  tailwindConfig: z.infer<typeof registryItemTailwindSchema>[\"config\"]\n) {\n  return {\n    postcssPlugin: \"update-tailwind-config-animation\",\n    Once(root: Root) {\n      if (!tailwindConfig?.theme?.extend?.animation) {\n        return\n      }\n\n      const themeNode = upsertThemeNode(root)\n      const existingAnimationNodes = themeNode.nodes?.filter(\n        (node): node is postcss.Declaration =>\n          node.type === \"decl\" && node.prop.startsWith(\"--animate-\")\n      )\n\n      const parsedAnimationValue = z\n        .record(z.string(), z.string())\n        .safeParse(tailwindConfig.theme.extend.animation)\n      if (!parsedAnimationValue.success) {\n        return\n      }\n\n      for (const [key, value] of Object.entries(parsedAnimationValue.data)) {\n        const prop = `--animate-${key}`\n        if (\n          existingAnimationNodes?.find(\n            (node): node is postcss.Declaration => node.prop === prop\n          )\n        ) {\n          continue\n        }\n\n        const animationNode = postcss.decl({\n          prop,\n          value,\n          raws: { semicolon: true, between: \": \", before: \"\\n  \" },\n        })\n        themeNode.append(animationNode)\n      }\n    },\n  }\n}\n\nfunction getQuoteType(root: Root): \"single\" | \"double\" {\n  const firstNode = root.nodes[0]\n  const raw = firstNode.toString()\n\n  if (raw.includes(\"'\")) {\n    return \"single\"\n  }\n  return \"double\"\n}\n\nexport function isLocalHSLValue(value: string) {\n  if (\n    value.startsWith(\"hsl\") ||\n    value.startsWith(\"rgb\") ||\n    value.startsWith(\"#\") ||\n    value.startsWith(\"oklch\")\n  ) {\n    return false\n  }\n\n  const chunks = value.split(\" \")\n\n  return (\n    chunks.length === 3 &&\n    chunks.slice(1, 3).every((chunk) => chunk.includes(\"%\"))\n  )\n}\n\nexport function isColorValue(value: string) {\n  return (\n    value.startsWith(\"hsl\") ||\n    value.startsWith(\"rgb\") ||\n    value.startsWith(\"#\") ||\n    value.startsWith(\"oklch\") ||\n    value.startsWith(\"var(--color-\")\n  )\n}\n", "import { RegistryItem } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { getPackageInfo } from \"@/src/utils/get-package-info\"\nimport { getPackageManager } from \"@/src/utils/get-package-manager\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { execa } from \"execa\"\nimport prompts from \"prompts\"\n\nexport async function updateDependencies(\n  dependencies: RegistryItem[\"dependencies\"],\n  devDependencies: RegistryItem[\"devDependencies\"],\n  config: Config,\n  options: {\n    silent?: boolean\n  }\n) {\n  dependencies = Array.from(new Set(dependencies))\n  devDependencies = Array.from(new Set(devDependencies))\n\n  if (!dependencies?.length && !devDependencies?.length) {\n    return\n  }\n\n  options = {\n    silent: false,\n    ...options,\n  }\n\n  const dependenciesSpinner = spinner(`Installing dependencies.`, {\n    silent: options.silent,\n  })?.start()\n  const packageManager = await getUpdateDependenciesPackageManager(config)\n\n  // Offer to use --force or --legacy-peer-deps if using React 19 with npm.\n  let flag = \"\"\n  if (shouldPromptForNpmFlag(config) && packageManager === \"npm\") {\n    if (options.silent) {\n      flag = \"force\"\n    } else {\n      dependenciesSpinner.stopAndPersist()\n      logger.warn(\n        \"\\nIt looks like you are using React 19. \\nSome packages may fail to install due to peer dependency issues in npm (see https://ui.shadcn.com/react-19).\\n\"\n      )\n      const confirmation = await prompts([\n        {\n          type: \"select\",\n          name: \"flag\",\n          message: \"How would you like to proceed?\",\n          choices: [\n            { title: \"Use --force\", value: \"force\" },\n            { title: \"Use --legacy-peer-deps\", value: \"legacy-peer-deps\" },\n          ],\n        },\n      ])\n\n      if (confirmation) {\n        flag = confirmation.flag\n      }\n    }\n  }\n\n  dependenciesSpinner?.start()\n\n  await installWithPackageManager(\n    packageManager,\n    dependencies,\n    devDependencies,\n    config.resolvedPaths.cwd,\n    flag\n  )\n\n  dependenciesSpinner?.succeed()\n}\n\nfunction shouldPromptForNpmFlag(config: Config) {\n  const packageInfo = getPackageInfo(config.resolvedPaths.cwd, false)\n\n  if (!packageInfo?.dependencies?.react) {\n    return false\n  }\n\n  const hasReact19 = /^(?:\\^|~)?19(?:\\.\\d+)*(?:-.*)?$/.test(\n    packageInfo.dependencies.react\n  )\n  const hasReactDayPicker8 =\n    packageInfo.dependencies[\"react-day-picker\"]?.startsWith(\"8\")\n\n  return hasReact19 && hasReactDayPicker8\n}\n\nasync function getUpdateDependenciesPackageManager(config: Config) {\n  const expoVersion = getPackageInfo(config.resolvedPaths.cwd, false)\n    ?.dependencies?.expo\n\n  if (expoVersion) {\n    // Ensures package versions match the React Native version.\n    // https://docs.expo.dev/more/expo-cli/#install\n    return \"expo\"\n  }\n\n  return getPackageManager(config.resolvedPaths.cwd)\n}\n\nasync function installWithPackageManager(\n  packageManager: Awaited<\n    ReturnType<typeof getUpdateDependenciesPackageManager>\n  >,\n  dependencies: string[],\n  devDependencies: string[],\n  cwd: string,\n  flag?: string\n) {\n  if (packageManager === \"npm\") {\n    return installWithNpm(dependencies, devDependencies, cwd, flag)\n  }\n\n  if (packageManager === \"deno\") {\n    return installWithDeno(dependencies, devDependencies, cwd)\n  }\n\n  if (packageManager === \"expo\") {\n    return installWithExpo(dependencies, devDependencies, cwd)\n  }\n\n  if (dependencies?.length) {\n    await execa(packageManager, [\"add\", ...dependencies], {\n      cwd,\n    })\n  }\n\n  if (devDependencies?.length) {\n    await execa(packageManager, [\"add\", \"-D\", ...devDependencies], { cwd })\n  }\n}\n\nasync function installWithNpm(\n  dependencies: string[],\n  devDependencies: string[],\n  cwd: string,\n  flag?: string\n) {\n  if (dependencies.length) {\n    await execa(\n      \"npm\",\n      [\"install\", ...(flag ? [`--${flag}`] : []), ...dependencies],\n      { cwd }\n    )\n  }\n\n  if (devDependencies.length) {\n    await execa(\n      \"npm\",\n      [\"install\", ...(flag ? [`--${flag}`] : []), \"-D\", ...devDependencies],\n      { cwd }\n    )\n  }\n}\n\nasync function installWithDeno(\n  dependencies: string[],\n  devDependencies: string[],\n  cwd: string\n) {\n  if (dependencies?.length) {\n    await execa(\"deno\", [\"add\", ...dependencies.map((dep) => `npm:${dep}`)], {\n      cwd,\n    })\n  }\n\n  if (devDependencies?.length) {\n    await execa(\n      \"deno\",\n      [\"add\", \"-D\", ...devDependencies.map((dep) => `npm:${dep}`)],\n      { cwd }\n    )\n  }\n}\n\nasync function installWithExpo(\n  dependencies: string[],\n  devDependencies: string[],\n  cwd: string\n) {\n  if (dependencies.length) {\n    await execa(\"npx\", [\"expo\", \"install\", ...dependencies], { cwd })\n  }\n\n  if (devDependencies.length) {\n    await execa(\"npx\", [\"expo\", \"install\", \"-- -D\", ...devDependencies], {\n      cwd,\n    })\n  }\n}\n", "import { existsSync, promises as fs } from \"fs\"\nimport path from \"path\"\nimport { registryItemEnvVarsSchema } from \"@/src/schema\"\nimport {\n  findExistingEnvFile,\n  getNewEnvKeys,\n  mergeEnvContent,\n} from \"@/src/utils/env-helpers\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { z } from \"zod\"\n\nexport async function updateEnvVars(\n  envVars: z.infer<typeof registryItemEnvVarsSchema> | undefined,\n  config: Config,\n  options: {\n    silent?: boolean\n  }\n) {\n  if (!envVars || Object.keys(envVars).length === 0) {\n    return {\n      envVarsAdded: [],\n      envFileUpdated: null,\n      envFileCreated: null,\n    }\n  }\n\n  options = {\n    silent: false,\n    ...options,\n  }\n\n  const envSpinner = spinner(`Adding environment variables.`, {\n    silent: options.silent,\n  })?.start()\n\n  const projectRoot = config.resolvedPaths.cwd\n\n  // Find existing env file or use .env.local as default.\n  let envFilePath = path.join(projectRoot, \".env.local\")\n  const existingEnvFile = findExistingEnvFile(projectRoot)\n\n  if (existingEnvFile) {\n    envFilePath = existingEnvFile\n  }\n\n  const envFileExists = existsSync(envFilePath)\n  const envFileName = path.basename(envFilePath)\n\n  // Convert envVars object to env file format\n  const newEnvContent = Object.entries(envVars)\n    .map(([key, value]) => `${key}=${value}`)\n    .join(\"\\n\")\n\n  let envVarsAdded: string[] = []\n  let envFileUpdated: string | null = null\n  let envFileCreated: string | null = null\n\n  if (envFileExists) {\n    const existingContent = await fs.readFile(envFilePath, \"utf-8\")\n    const mergedContent = mergeEnvContent(existingContent, newEnvContent)\n    envVarsAdded = getNewEnvKeys(existingContent, newEnvContent)\n\n    if (envVarsAdded.length > 0) {\n      await fs.writeFile(envFilePath, mergedContent, \"utf-8\")\n      envFileUpdated = path.relative(projectRoot, envFilePath)\n\n      envSpinner?.succeed(\n        `Added the following variables to ${highlighter.info(envFileName)}:`\n      )\n\n      if (!options.silent) {\n        for (const key of envVarsAdded) {\n          logger.log(`  ${highlighter.success(\"+\")} ${key}`)\n        }\n      }\n    } else {\n      envSpinner?.stop()\n    }\n  } else {\n    // Create new env file\n    await fs.writeFile(envFilePath, newEnvContent + \"\\n\", \"utf-8\")\n    envFileCreated = path.relative(projectRoot, envFilePath)\n    envVarsAdded = Object.keys(envVars)\n\n    envSpinner?.succeed(\n      `Added the following variables to ${highlighter.info(envFileName)}:`\n    )\n\n    if (!options.silent) {\n      for (const key of envVarsAdded) {\n        logger.log(`  ${highlighter.success(\"+\")} ${key}`)\n      }\n    }\n  }\n\n  if (!options.silent && envVarsAdded.length > 0) {\n    logger.break()\n  }\n\n  return {\n    envVarsAdded,\n    envFileUpdated,\n    envFileCreated,\n  }\n}\n", "import path from \"path\"\nimport { getRegistryItems } from \"@/src/registry/api\"\nimport { configWithDefaults } from \"@/src/registry/config\"\nimport { resolveRegistryTree } from \"@/src/registry/resolver\"\nimport {\n  configSchema,\n  registryItemFileSchema,\n  registryItemSchema,\n  workspaceConfigSchema,\n} from \"@/src/schema\"\nimport {\n  findCommonRoot,\n  findPackageRoot,\n  getWorkspaceConfig,\n  type Config,\n} from \"@/src/utils/get-config\"\nimport { getProjectTailwindVersionFromConfig } from \"@/src/utils/get-project-info\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { isSafeTarget } from \"@/src/utils/is-safe-target\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { updateCss } from \"@/src/utils/updaters/update-css\"\nimport { updateCssVars } from \"@/src/utils/updaters/update-css-vars\"\nimport { updateDependencies } from \"@/src/utils/updaters/update-dependencies\"\nimport { updateEnvVars } from \"@/src/utils/updaters/update-env-vars\"\nimport { updateFiles } from \"@/src/utils/updaters/update-files\"\nimport { updateTailwindConfig } from \"@/src/utils/updaters/update-tailwind-config\"\nimport { z } from \"zod\"\n\nexport async function addComponents(\n  components: string[],\n  config: Config,\n  options: {\n    overwrite?: boolean\n    silent?: boolean\n    isNewProject?: boolean\n    baseStyle?: boolean\n    registryHeaders?: Record<string, Record<string, string>>\n    path?: string\n  }\n) {\n  options = {\n    overwrite: false,\n    silent: false,\n    isNewProject: false,\n    baseStyle: true,\n    ...options,\n  }\n\n  const workspaceConfig = await getWorkspaceConfig(config)\n  if (\n    workspaceConfig &&\n    workspaceConfig.ui &&\n    workspaceConfig.ui.resolvedPaths.cwd !== config.resolvedPaths.cwd\n  ) {\n    return await addWorkspaceComponents(components, config, workspaceConfig, {\n      ...options,\n      isRemote:\n        components?.length === 1 && !!components[0].match(/\\/chat\\/b\\//),\n    })\n  }\n\n  return await addProjectComponents(components, config, options)\n}\n\nasync function addProjectComponents(\n  components: string[],\n  config: z.infer<typeof configSchema>,\n  options: {\n    overwrite?: boolean\n    silent?: boolean\n    isNewProject?: boolean\n    baseStyle?: boolean\n    path?: string\n  }\n) {\n  if (!options.baseStyle && !components.length) {\n    return\n  }\n\n  const registrySpinner = spinner(`Checking registry.`, {\n    silent: options.silent,\n  })?.start()\n  const tree = await resolveRegistryTree(components, configWithDefaults(config))\n\n  if (!tree) {\n    registrySpinner?.fail()\n    return handleError(new Error(\"Failed to fetch components from registry.\"))\n  }\n\n  try {\n    validateFilesTarget(tree.files ?? [], config.resolvedPaths.cwd)\n  } catch (error) {\n    registrySpinner?.fail()\n    return handleError(error)\n  }\n\n  registrySpinner?.succeed()\n\n  const tailwindVersion = await getProjectTailwindVersionFromConfig(config)\n\n  await updateTailwindConfig(tree.tailwind?.config, config, {\n    silent: options.silent,\n    tailwindVersion,\n  })\n\n  const overwriteCssVars = await shouldOverwriteCssVars(components, config)\n  await updateCssVars(tree.cssVars, config, {\n    cleanupDefaultNextStyles: options.isNewProject,\n    silent: options.silent,\n    tailwindVersion,\n    tailwindConfig: tree.tailwind?.config,\n    overwriteCssVars,\n    initIndex: options.baseStyle,\n  })\n\n  // Add CSS updater\n  await updateCss(tree.css, config, {\n    silent: options.silent,\n  })\n\n  await updateEnvVars(tree.envVars, config, {\n    silent: options.silent,\n  })\n\n  await updateDependencies(tree.dependencies, tree.devDependencies, config, {\n    silent: options.silent,\n  })\n  await updateFiles(tree.files, config, {\n    overwrite: options.overwrite,\n    silent: options.silent,\n    path: options.path,\n  })\n\n  if (tree.docs) {\n    logger.info(tree.docs)\n  }\n}\n\nasync function addWorkspaceComponents(\n  components: string[],\n  config: z.infer<typeof configSchema>,\n  workspaceConfig: z.infer<typeof workspaceConfigSchema>,\n  options: {\n    overwrite?: boolean\n    silent?: boolean\n    isNewProject?: boolean\n    isRemote?: boolean\n    baseStyle?: boolean\n    path?: string\n  }\n) {\n  if (!options.baseStyle && !components.length) {\n    return\n  }\n\n  const registrySpinner = spinner(`Checking registry.`, {\n    silent: options.silent,\n  })?.start()\n  const tree = await resolveRegistryTree(components, configWithDefaults(config))\n\n  if (!tree) {\n    registrySpinner?.fail()\n    return handleError(new Error(\"Failed to fetch components from registry.\"))\n  }\n\n  try {\n    validateFilesTarget(tree.files ?? [], config.resolvedPaths.cwd)\n  } catch (error) {\n    registrySpinner?.fail()\n    return handleError(error)\n  }\n\n  registrySpinner?.succeed()\n\n  const filesCreated: string[] = []\n  const filesUpdated: string[] = []\n  const filesSkipped: string[] = []\n\n  const rootSpinner = spinner(`Installing components.`)?.start()\n\n  // Process global updates (tailwind, css vars, dependencies) first for the main target.\n  // These should typically go to the UI package in a workspace.\n  const mainTargetConfig = workspaceConfig.ui\n  const tailwindVersion = await getProjectTailwindVersionFromConfig(\n    mainTargetConfig\n  )\n  const workspaceRoot = findCommonRoot(\n    config.resolvedPaths.cwd,\n    mainTargetConfig.resolvedPaths.ui\n  )\n\n  // 1. Update tailwind config.\n  if (tree.tailwind?.config) {\n    await updateTailwindConfig(tree.tailwind?.config, mainTargetConfig, {\n      silent: true,\n      tailwindVersion,\n    })\n    filesUpdated.push(\n      path.relative(\n        workspaceRoot,\n        mainTargetConfig.resolvedPaths.tailwindConfig\n      )\n    )\n  }\n\n  // 2. Update css vars.\n  if (tree.cssVars) {\n    const overwriteCssVars = await shouldOverwriteCssVars(components, config)\n    await updateCssVars(tree.cssVars, mainTargetConfig, {\n      silent: true,\n      tailwindVersion,\n      tailwindConfig: tree.tailwind?.config,\n      overwriteCssVars,\n    })\n    filesUpdated.push(\n      path.relative(workspaceRoot, mainTargetConfig.resolvedPaths.tailwindCss)\n    )\n  }\n\n  // 3. Update CSS\n  if (tree.css) {\n    await updateCss(tree.css, mainTargetConfig, {\n      silent: true,\n    })\n    filesUpdated.push(\n      path.relative(workspaceRoot, mainTargetConfig.resolvedPaths.tailwindCss)\n    )\n  }\n\n  // 4. Update environment variables\n  if (tree.envVars) {\n    await updateEnvVars(tree.envVars, mainTargetConfig, {\n      silent: true,\n    })\n  }\n\n  // 5. Update dependencies.\n  await updateDependencies(\n    tree.dependencies,\n    tree.devDependencies,\n    mainTargetConfig,\n    {\n      silent: true,\n    }\n  )\n\n  // 6. Group files by their type to determine target config and update files.\n  const filesByType = new Map<string, typeof tree.files>()\n\n  for (const file of tree.files ?? []) {\n    const type = file.type || \"registry:ui\"\n    if (!filesByType.has(type)) {\n      filesByType.set(type, [])\n    }\n    filesByType.get(type)!.push(file)\n  }\n\n  // Process each type of component with its appropriate target config.\n  for (const type of Array.from(filesByType.keys())) {\n    const typeFiles = filesByType.get(type)!\n\n    let targetConfig = type === \"registry:ui\" ? workspaceConfig.ui : config\n\n    const typeWorkspaceRoot = findCommonRoot(\n      config.resolvedPaths.cwd,\n      targetConfig.resolvedPaths.ui || targetConfig.resolvedPaths.cwd\n    )\n    const packageRoot =\n      (await findPackageRoot(\n        typeWorkspaceRoot,\n        targetConfig.resolvedPaths.cwd\n      )) ?? targetConfig.resolvedPaths.cwd\n\n    // Update files for this type.\n    const files = await updateFiles(typeFiles, targetConfig, {\n      overwrite: options.overwrite,\n      silent: true,\n      rootSpinner,\n      isRemote: options.isRemote,\n      isWorkspace: true,\n      path: options.path,\n    })\n\n    filesCreated.push(\n      ...files.filesCreated.map((file) =>\n        path.relative(typeWorkspaceRoot, path.join(packageRoot, file))\n      )\n    )\n    filesUpdated.push(\n      ...files.filesUpdated.map((file) =>\n        path.relative(typeWorkspaceRoot, path.join(packageRoot, file))\n      )\n    )\n    filesSkipped.push(\n      ...files.filesSkipped.map((file) =>\n        path.relative(typeWorkspaceRoot, path.join(packageRoot, file))\n      )\n    )\n  }\n\n  rootSpinner?.succeed()\n\n  // Sort files.\n  filesCreated.sort()\n  filesUpdated.sort()\n  filesSkipped.sort()\n\n  const hasUpdatedFiles = filesCreated.length || filesUpdated.length\n  if (!hasUpdatedFiles && !filesSkipped.length) {\n    spinner(`No files updated.`, {\n      silent: options.silent,\n    })?.info()\n  }\n\n  if (filesCreated.length) {\n    spinner(\n      `Created ${filesCreated.length} ${\n        filesCreated.length === 1 ? \"file\" : \"files\"\n      }:`,\n      {\n        silent: options.silent,\n      }\n    )?.succeed()\n    for (const file of filesCreated) {\n      logger.log(`  - ${file}`)\n    }\n  }\n\n  if (filesUpdated.length) {\n    spinner(\n      `Updated ${filesUpdated.length} ${\n        filesUpdated.length === 1 ? \"file\" : \"files\"\n      }:`,\n      {\n        silent: options.silent,\n      }\n    )?.info()\n    for (const file of filesUpdated) {\n      logger.log(`  - ${file}`)\n    }\n  }\n\n  if (filesSkipped.length) {\n    spinner(\n      `Skipped ${filesSkipped.length} ${\n        filesUpdated.length === 1 ? \"file\" : \"files\"\n      }: (use --overwrite to overwrite)`,\n      {\n        silent: options.silent,\n      }\n    )?.info()\n    for (const file of filesSkipped) {\n      logger.log(`  - ${file}`)\n    }\n  }\n\n  if (tree.docs) {\n    logger.info(tree.docs)\n  }\n}\n\nasync function shouldOverwriteCssVars(\n  components: z.infer<typeof registryItemSchema>[\"name\"][],\n  config: z.infer<typeof configSchema>\n) {\n  const result = await getRegistryItems(components, { config })\n  const payload = z.array(registryItemSchema).parse(result)\n\n  return payload.some(\n    (component) =>\n      component.type === \"registry:theme\" || component.type === \"registry:style\"\n  )\n}\n\nfunction validateFilesTarget(\n  files: z.infer<typeof registryItemFileSchema>[],\n  cwd: string\n) {\n  for (const file of files) {\n    if (!file?.target) {\n      continue\n    }\n\n    if (!isSafeTarget(file.target, cwd)) {\n      throw new Error(\n        `We found an unsafe file path \"${file.target} in the registry item. Installation aborted.`\n      )\n    }\n  }\n}\n", "import os from \"os\"\nimport path from \"path\"\nimport { initOptionsSchema } from \"@/src/commands/init\"\nimport { fetchRegistry } from \"@/src/registry/fetcher\"\nimport { getPackageManager } from \"@/src/utils/get-package-manager\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { execa } from \"execa\"\nimport fs from \"fs-extra\"\nimport prompts from \"prompts\"\nimport { z } from \"zod\"\n\nconst MONOREPO_TEMPLATE_URL =\n  \"https://codeload.github.com/shadcn-ui/ui/tar.gz/main\"\n\nexport const TEMPLATES = {\n  next: \"next\",\n  \"next-16\": \"next-16\",\n  \"next-monorepo\": \"next-monorepo\",\n} as const\n\nexport async function createProject(\n  options: Pick<\n    z.infer<typeof initOptionsSchema>,\n    \"cwd\" | \"force\" | \"srcDir\" | \"components\" | \"template\"\n  >\n) {\n  options = {\n    srcDir: false,\n    ...options,\n  }\n\n  let template: keyof typeof TEMPLATES =\n    options.template && TEMPLATES[options.template as keyof typeof TEMPLATES]\n      ? (options.template as keyof typeof TEMPLATES)\n      : \"next\"\n  let projectName: string =\n    template === TEMPLATES.next ? \"my-app\" : \"my-monorepo\"\n  let nextVersion = \"15\"\n\n  const isRemoteComponent =\n    options.components?.length === 1 &&\n    !!options.components[0].match(/\\/chat\\/b\\//)\n\n  if (options.components && isRemoteComponent) {\n    try {\n      const [result] = await fetchRegistry(options.components)\n      const { meta } = z\n        .object({\n          meta: z.object({\n            nextVersion: z.string(),\n          }),\n        })\n        .parse(result)\n      nextVersion = meta.nextVersion\n\n      // Force template to next for remote components.\n      template = TEMPLATES.next\n    } catch (error) {\n      logger.break()\n      handleError(error)\n    }\n  }\n\n  if (!options.force) {\n    const { type, name } = await prompts([\n      {\n        type: options.template || isRemoteComponent ? null : \"select\",\n        name: \"type\",\n        message: `The path ${highlighter.info(\n          options.cwd\n        )} does not contain a package.json file.\\n  Would you like to start a new project?`,\n        choices: [\n          { title: \"Next.js 15\", value: \"next\" },\n          { title: \"Next.js 16\", value: \"next-16\" },\n          { title: \"Next.js (Monorepo)\", value: \"next-monorepo\" },\n        ],\n        initial: 0,\n      },\n      {\n        type: \"text\",\n        name: \"name\",\n        message: \"What is your project named?\",\n        initial: projectName,\n        format: (value: string) => value.trim(),\n        validate: (value: string) =>\n          value.length > 128\n            ? `Name should be less than 128 characters.`\n            : true,\n      },\n    ])\n\n    template = type ?? template\n    projectName = name\n\n    if (type === \"next-16\") {\n      nextVersion = \"latest\"\n    }\n  }\n\n  const packageManager = await getPackageManager(options.cwd, {\n    withFallback: true,\n  })\n\n  const projectPath = `${options.cwd}/${projectName}`\n\n  // Check if path is writable.\n  try {\n    await fs.access(options.cwd, fs.constants.W_OK)\n  } catch (error) {\n    logger.break()\n    logger.error(`The path ${highlighter.info(options.cwd)} is not writable.`)\n    logger.error(\n      `It is likely you do not have write permissions for this folder or the path ${highlighter.info(\n        options.cwd\n      )} does not exist.`\n    )\n    logger.break()\n    process.exit(1)\n  }\n\n  if (fs.existsSync(path.resolve(options.cwd, projectName, \"package.json\"))) {\n    logger.break()\n    logger.error(\n      `A project with the name ${highlighter.info(projectName)} already exists.`\n    )\n    logger.error(`Please choose a different name and try again.`)\n    logger.break()\n    process.exit(1)\n  }\n\n  if (template === TEMPLATES.next || template === TEMPLATES[\"next-16\"]) {\n    await createNextProject(projectPath, {\n      version: nextVersion,\n      cwd: options.cwd,\n      packageManager,\n      srcDir: !!options.srcDir,\n    })\n  }\n\n  if (template === TEMPLATES[\"next-monorepo\"]) {\n    await createMonorepoProject(projectPath, {\n      packageManager,\n    })\n  }\n\n  return {\n    projectPath,\n    projectName,\n    template,\n  }\n}\n\nasync function createNextProject(\n  projectPath: string,\n  options: {\n    version: string\n    cwd: string\n    packageManager: string\n    srcDir: boolean\n  }\n) {\n  const createSpinner = spinner(\n    `Creating a new Next.js ${\n      options.version.startsWith(\"latest\") ? \"16\" : \"15\"\n    } project. This may take a few minutes.`\n  ).start()\n\n  // Note: pnpm fails here. Fallback to npx with --use-PACKAGE-MANAGER.\n  const args = [\n    \"--tailwind\",\n    \"--eslint\",\n    \"--typescript\",\n    \"--app\",\n    options.srcDir ? \"--src-dir\" : \"--no-src-dir\",\n    \"--no-import-alias\",\n    `--use-${options.packageManager}`,\n  ]\n\n  if (\n    options.version.startsWith(\"15\") ||\n    options.version.startsWith(\"latest\") ||\n    options.version.startsWith(\"canary\")\n  ) {\n    args.push(\"--turbopack\")\n  }\n\n  if (\n    options.version.startsWith(\"latest\") ||\n    options.version.startsWith(\"canary\")\n  ) {\n    args.push(\"--no-react-compiler\")\n  }\n\n  try {\n    await execa(\n      \"npx\",\n      [`create-next-app@${options.version}`, projectPath, \"--silent\", ...args],\n      {\n        cwd: options.cwd,\n      }\n    )\n  } catch (error) {\n    logger.break()\n    logger.error(\n      `Something went wrong creating a new Next.js project. Please try again.`\n    )\n    process.exit(1)\n  }\n\n  createSpinner?.succeed(\n    `Creating a new Next.js ${\n      options.version.startsWith(\"latest\") ? \"16\" : \"15\"\n    } project.`\n  )\n}\n\nasync function createMonorepoProject(\n  projectPath: string,\n  options: {\n    packageManager: string\n  }\n) {\n  const createSpinner = spinner(\n    `Creating a new Next.js monorepo. This may take a few minutes.`\n  ).start()\n\n  try {\n    // Get the template.\n    const templatePath = path.join(os.tmpdir(), `shadcn-template-${Date.now()}`)\n    await fs.ensureDir(templatePath)\n    const response = await fetch(MONOREPO_TEMPLATE_URL)\n    if (!response.ok) {\n      throw new Error(`Failed to download template: ${response.statusText}`)\n    }\n\n    // Write the tar file\n    const tarPath = path.resolve(templatePath, \"template.tar.gz\")\n    await fs.writeFile(tarPath, Buffer.from(await response.arrayBuffer()))\n    await execa(\"tar\", [\n      \"-xzf\",\n      tarPath,\n      \"-C\",\n      templatePath,\n      \"--strip-components=2\",\n      \"ui-main/templates/monorepo-next\",\n    ])\n    const extractedPath = path.resolve(templatePath, \"monorepo-next\")\n    await fs.move(extractedPath, projectPath)\n    await fs.remove(templatePath)\n\n    // Run install.\n    await execa(options.packageManager, [\"install\"], {\n      cwd: projectPath,\n    })\n    // await execa(\"cd\", [cwd])\n\n    // Write project name to the package.json\n    const packageJsonPath = path.join(projectPath, \"package.json\")\n    if (fs.existsSync(packageJsonPath)) {\n      const packageJsonContent = await fs.readFile(packageJsonPath, \"utf8\")\n      const packageJson = JSON.parse(packageJsonContent)\n      packageJson.name = projectPath.split(\"/\").pop()\n      await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2))\n    }\n\n    // Try git init.\n    const cwd = process.cwd()\n    await execa(\"git\", [\"--version\"], { cwd: projectPath })\n    await execa(\"git\", [\"init\"], { cwd: projectPath })\n    await execa(\"git\", [\"add\", \"-A\"], { cwd: projectPath })\n    await execa(\"git\", [\"commit\", \"-m\", \"Initial commit\"], {\n      cwd: projectPath,\n    })\n\n    createSpinner?.succeed(\"Creating a new Next.js monorepo.\")\n  } catch (error) {\n    createSpinner?.fail(\"Something went wrong creating a new Next.js monorepo.\")\n    handleError(error)\n  }\n}\n", "import { existsSync } from \"fs\"\nimport { join } from \"path\"\nimport { logger } from \"@/src/utils/logger\"\n\nexport async function loadEnvFiles(cwd: string = process.cwd()): Promise<void> {\n  try {\n    const { config } = await import(\"@dotenvx/dotenvx\")\n    const envFiles = [\n      \".env.local\",\n      \".env.development.local\",\n      \".env.development\",\n      \".env\",\n    ]\n\n    for (const envFile of envFiles) {\n      const envPath = join(cwd, envFile)\n      if (existsSync(envPath)) {\n        config({\n          path: envPath,\n          overload: false,\n          quiet: true,\n        })\n      }\n    }\n  } catch (error) {\n    logger.warn(\"Failed to load env files:\", error)\n  }\n}\n", "import fsExtra from \"fs-extra\"\n\nexport const FILE_BACKUP_SUFFIX = \".bak\"\n\nexport function createFileBackup(filePath: string): string | null {\n  if (!fsExtra.existsSync(filePath)) {\n    return null\n  }\n\n  const backupPath = `${filePath}${FILE_BACKUP_SUFFIX}`\n  try {\n    fsExtra.renameSync(filePath, backupPath)\n    return backupPath\n  } catch (error) {\n    console.error(`Failed to create backup of ${filePath}: ${error}`)\n    return null\n  }\n}\n\nexport function restoreFileBackup(filePath: string): boolean {\n  const backupPath = `${filePath}${FILE_BACKUP_SUFFIX}`\n\n  if (!fsExtra.existsSync(backupPath)) {\n    return false\n  }\n\n  try {\n    fsExtra.renameSync(backupPath, filePath)\n    return true\n  } catch (error) {\n    console.error(\n      `Warning: Could not restore backup file ${backupPath}: ${error}`\n    )\n    return false\n  }\n}\n\nexport function deleteFileBackup(filePath: string): boolean {\n  const backupPath = `${filePath}${FILE_BACKUP_SUFFIX}`\n\n  if (!fsExtra.existsSync(backupPath)) {\n    return false\n  }\n\n  try {\n    fsExtra.unlinkSync(backupPath)\n    return true\n  } catch (error) {\n    // Best effort - don't log as this is just cleanup\n    return false\n  }\n}\n", "import { BUILTIN_REGISTRIES } from \"@/src/registry/constants\"\nimport { RegistryNotConfiguredError } from \"@/src/registry/errors\"\nimport { parseRegistryAndItemFromString } from \"@/src/registry/parser\"\nimport { fetchRegistryItems } from \"@/src/registry/resolver\"\nimport { Config } from \"@/src/utils/get-config\"\n\n// Recursively discovers all registry namespaces including nested ones.\nexport async function resolveRegistryNamespaces(\n  components: string[],\n  config: Config\n) {\n  const discoveredNamespaces = new Set<string>()\n  const visitedItems = new Set<string>()\n  const itemsToProcess = [...components]\n\n  while (itemsToProcess.length > 0) {\n    const currentItem = itemsToProcess.shift()!\n\n    if (visitedItems.has(currentItem)) {\n      continue\n    }\n    visitedItems.add(currentItem)\n\n    const { registry } = parseRegistryAndItemFromString(currentItem)\n    if (registry && !BUILTIN_REGISTRIES[registry]) {\n      discoveredNamespaces.add(registry)\n    }\n\n    try {\n      const [item] = await fetchRegistryItems([currentItem], config, {\n        useCache: true,\n      })\n\n      if (item?.registryDependencies) {\n        for (const dep of item.registryDependencies) {\n          const { registry: depRegistry } = parseRegistryAndItemFromString(dep)\n          if (depRegistry && !BUILTIN_REGISTRIES[depRegistry]) {\n            discoveredNamespaces.add(depRegistry)\n          }\n\n          if (!visitedItems.has(dep)) {\n            itemsToProcess.push(dep)\n          }\n        }\n      }\n    } catch (error) {\n      // If a registry is not configured, we still track it.\n      if (error instanceof RegistryNotConfiguredError) {\n        const { registry } = parseRegistryAndItemFromString(currentItem)\n        if (registry && !BUILTIN_REGISTRIES[registry]) {\n          discoveredNamespaces.add(registry)\n        }\n        continue\n      }\n\n      // For other errors (network, parsing, etc.), we skip this item\n      // but continue processing others to discover as many namespaces as possible.\n      continue\n    }\n  }\n\n  return Array.from(discoveredNamespaces)\n}\n", "import path from \"path\"\nimport { getRegistriesIndex } from \"@/src/registry/api\"\nimport { BUILTIN_REGISTRIES } from \"@/src/registry/constants\"\nimport { resolveRegistryNamespaces } from \"@/src/registry/namespaces\"\nimport { rawConfigSchema } from \"@/src/registry/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport fs from \"fs-extra\"\n\nexport async function ensureRegistriesInConfig(\n  components: string[],\n  config: Config,\n  options: {\n    silent?: boolean\n    writeFile?: boolean\n  } = {}\n) {\n  options = {\n    silent: false,\n    writeFile: true,\n    ...options,\n  }\n\n  // Use resolveRegistryNamespaces to discover all namespaces including dependencies.\n  const registryNames = await resolveRegistryNamespaces(components, config)\n\n  const missingRegistries = registryNames.filter(\n    (registry) =>\n      !config.registries?.[registry] &&\n      !Object.keys(BUILTIN_REGISTRIES).includes(registry)\n  )\n\n  if (missingRegistries.length === 0) {\n    return {\n      config,\n      newRegistries: [],\n    }\n  }\n\n  // We'll fail silently if we can't fetch the registry index.\n  // The error handling by caller will guide user to add the missing registries.\n  const registryIndex = await getRegistriesIndex({\n    useCache: process.env.NODE_ENV !== \"development\",\n  })\n\n  if (!registryIndex) {\n    return {\n      config,\n      newRegistries: [],\n    }\n  }\n\n  const foundRegistries: Record<string, string> = {}\n  for (const registry of missingRegistries) {\n    if (registryIndex[registry]) {\n      foundRegistries[registry] = registryIndex[registry]\n    }\n  }\n\n  if (Object.keys(foundRegistries).length === 0) {\n    return {\n      config,\n      newRegistries: [],\n    }\n  }\n\n  // Filter out built-in registries from existing config before merging\n  const existingRegistries = Object.fromEntries(\n    Object.entries(config.registries || {}).filter(\n      ([key]) => !Object.keys(BUILTIN_REGISTRIES).includes(key)\n    )\n  )\n\n  const newConfigWithRegistries = {\n    ...config,\n    registries: {\n      ...existingRegistries,\n      ...foundRegistries,\n    },\n  }\n\n  if (options.writeFile) {\n    const { resolvedPaths, ...configWithoutResolvedPaths } =\n      newConfigWithRegistries\n    const configSpinner = spinner(\"Updating components.json.\", {\n      silent: options.silent,\n    }).start()\n    const updatedConfig = rawConfigSchema.parse(configWithoutResolvedPaths)\n    await fs.writeFile(\n      path.resolve(config.resolvedPaths.cwd, \"components.json\"),\n      JSON.stringify(updatedConfig, null, 2) + \"\\n\",\n      \"utf-8\"\n    )\n    configSpinner.succeed()\n  }\n\n  return {\n    config: newConfigWithRegistries,\n    newRegistries: Object.keys(foundRegistries),\n  }\n}\n", "import { promises as fs } from \"fs\"\nimport path from \"path\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport {\n  _createSourceFile,\n  _getQuote<PERSON>har,\n} from \"@/src/utils/updaters/update-tailwind-config\"\nimport { ObjectLiteralExpression, SyntaxKind } from \"ts-morph\"\n\nexport async function updateTailwindContent(\n  content: string[],\n  config: Config,\n  options: {\n    silent?: boolean\n  }\n) {\n  if (!content) {\n    return\n  }\n\n  options = {\n    silent: false,\n    ...options,\n  }\n\n  const tailwindFileRelativePath = path.relative(\n    config.resolvedPaths.cwd,\n    config.resolvedPaths.tailwindConfig\n  )\n  const tailwindSpinner = spinner(\n    `Updating ${highlighter.info(tailwindFileRelativePath)}`,\n    {\n      silent: options.silent,\n    }\n  ).start()\n  const raw = await fs.readFile(config.resolvedPaths.tailwindConfig, \"utf8\")\n  const output = await transformTailwindContent(raw, content, config)\n  await fs.writeFile(config.resolvedPaths.tailwindConfig, output, \"utf8\")\n  tailwindSpinner?.succeed()\n}\n\nexport async function transformTailwindContent(\n  input: string,\n  content: string[],\n  config: Config\n) {\n  const sourceFile = await _createSourceFile(input, config)\n  // Find the object with content property.\n  // This is faster than traversing the default export.\n  // TODO: maybe we do need to traverse the default export?\n  const configObject = sourceFile\n    .getDescendantsOfKind(SyntaxKind.ObjectLiteralExpression)\n    .find((node) =>\n      node\n        .getProperties()\n        .some(\n          (property) =>\n            property.isKind(SyntaxKind.PropertyAssignment) &&\n            property.getName() === \"content\"\n        )\n    )\n\n  // We couldn't find the config object, so we return the input as is.\n  if (!configObject) {\n    return input\n  }\n\n  addTailwindConfigContent(configObject, content)\n\n  return sourceFile.getFullText()\n}\n\nasync function addTailwindConfigContent(\n  configObject: ObjectLiteralExpression,\n  content: string[]\n) {\n  const quoteChar = _getQuoteChar(configObject)\n\n  const existingProperty = configObject.getProperty(\"content\")\n\n  if (!existingProperty) {\n    const newProperty = {\n      name: \"content\",\n      initializer: `[${quoteChar}${content.join(\n        `${quoteChar}, ${quoteChar}`\n      )}${quoteChar}]`,\n    }\n    configObject.addPropertyAssignment(newProperty)\n\n    return configObject\n  }\n\n  if (existingProperty.isKind(SyntaxKind.PropertyAssignment)) {\n    const initializer = existingProperty.getInitializer()\n\n    // If property is an array, append.\n    if (initializer?.isKind(SyntaxKind.ArrayLiteralExpression)) {\n      for (const contentItem of content) {\n        const newValue = `${quoteChar}${contentItem}${quoteChar}`\n\n        // Check if the array already contains the value.\n        if (\n          initializer\n            .getElements()\n            .map((element) => element.getText())\n            .includes(newValue)\n        ) {\n          continue\n        }\n\n        initializer.addElement(newValue)\n      }\n    }\n\n    return configObject\n  }\n\n  return configObject\n}\n", "import { promises as fs } from \"fs\"\nimport path from \"path\"\nimport { preFlightInit } from \"@/src/preflights/preflight-init\"\nimport {\n  getRegistryBaseColors,\n  getRegistryItems,\n  getRegistryStyles,\n} from \"@/src/registry/api\"\nimport { buildUrlAndHeadersForRegistryItem } from \"@/src/registry/builder\"\nimport { configWithDefaults } from \"@/src/registry/config\"\nimport { BASE_COLORS, BUILTIN_REGISTRIES } from \"@/src/registry/constants\"\nimport { clearRegistryContext } from \"@/src/registry/context\"\nimport { rawConfigSchema } from \"@/src/schema\"\nimport { addComponents } from \"@/src/utils/add-components\"\nimport { TEMPLATES, createProject } from \"@/src/utils/create-project\"\nimport { loadEnvFiles } from \"@/src/utils/env-loader\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport {\n  FILE_BACKUP_SUFFIX,\n  createFileBackup,\n  deleteFileBackup,\n  restoreFileBackup,\n} from \"@/src/utils/file-helper\"\nimport {\n  DEFAULT_COMPONENTS,\n  DEFAULT_TAILWIND_CONFIG,\n  DEFAULT_TAILWIND_CSS,\n  DEFAULT_UTILS,\n  getConfig,\n  resolveConfigPaths,\n  type Config,\n} from \"@/src/utils/get-config\"\nimport {\n  getProjectConfig,\n  getProjectInfo,\n  getProjectTailwindVersionFromConfig,\n} from \"@/src/utils/get-project-info\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { ensureRegistriesInConfig } from \"@/src/utils/registries\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { updateTailwindContent } from \"@/src/utils/updaters/update-tailwind-content\"\nimport { Command } from \"commander\"\nimport deepmerge from \"deepmerge\"\nimport fsExtra from \"fs-extra\"\nimport prompts from \"prompts\"\nimport { z } from \"zod\"\n\nprocess.on(\"exit\", (code) => {\n  const filePath = path.resolve(process.cwd(), \"components.json\")\n\n  // Delete backup if successful.\n  if (code === 0) {\n    return deleteFileBackup(filePath)\n  }\n\n  // Restore backup if error.\n  return restoreFileBackup(filePath)\n})\n\nexport const initOptionsSchema = z.object({\n  cwd: z.string(),\n  components: z.array(z.string()).optional(),\n  yes: z.boolean(),\n  defaults: z.boolean(),\n  force: z.boolean(),\n  silent: z.boolean(),\n  isNewProject: z.boolean(),\n  srcDir: z.boolean().optional(),\n  cssVariables: z.boolean(),\n  template: z\n    .string()\n    .optional()\n    .refine(\n      (val) => {\n        if (val) {\n          return TEMPLATES[val as keyof typeof TEMPLATES]\n        }\n        return true\n      },\n      {\n        message:\n          \"Invalid template. Please use 'next', 'next-16' or 'next-monorepo'.\",\n      }\n    ),\n  baseColor: z\n    .string()\n    .optional()\n    .refine(\n      (val) => {\n        if (val) {\n          return BASE_COLORS.find((color) => color.name === val)\n        }\n\n        return true\n      },\n      {\n        message: `Invalid base color. Please use '${BASE_COLORS.map(\n          (color) => color.name\n        ).join(\"', '\")}'`,\n      }\n    ),\n  baseStyle: z.boolean(),\n})\n\nexport const init = new Command()\n  .name(\"init\")\n  .description(\"initialize your project and install dependencies\")\n  .argument(\"[components...]\", \"names, url or local path to component\")\n  .option(\n    \"-t, --template <template>\",\n    \"the template to use. (next, next-16, next-monorepo)\"\n  )\n  .option(\n    \"-b, --base-color <base-color>\",\n    \"the base color to use. (neutral, gray, zinc, stone, slate)\",\n    undefined\n  )\n  .option(\"-y, --yes\", \"skip confirmation prompt.\", true)\n  .option(\"-d, --defaults,\", \"use default configuration.\", false)\n  .option(\"-f, --force\", \"force overwrite of existing configuration.\", false)\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .option(\"-s, --silent\", \"mute output.\", false)\n  .option(\n    \"--src-dir\",\n    \"use the src directory when creating a new project.\",\n    false\n  )\n  .option(\n    \"--no-src-dir\",\n    \"do not use the src directory when creating a new project.\"\n  )\n  .option(\"--css-variables\", \"use css variables for theming.\", true)\n  .option(\"--no-css-variables\", \"do not use css variables for theming.\")\n  .option(\"--no-base-style\", \"do not install the base shadcn style.\")\n  .action(async (components, opts) => {\n    try {\n      // Apply defaults when --defaults flag is set.\n      if (opts.defaults) {\n        opts.template = opts.template || \"next\"\n        opts.baseColor = opts.baseColor || \"neutral\"\n      }\n\n      const options = initOptionsSchema.parse({\n        cwd: path.resolve(opts.cwd),\n        isNewProject: false,\n        components,\n        ...opts,\n      })\n\n      await loadEnvFiles(options.cwd)\n\n      // We need to check if we're initializing with a new style.\n      // This will allow us to determine if we need to install the base style.\n      // And if we should prompt the user for a base color.\n      if (components.length > 0) {\n        // We don't know the full config at this point.\n        // So we'll use a shadow config to fetch the first item.\n        let shadowConfig = configWithDefaults({})\n\n        // Check if there's a components.json file.\n        // If so, we'll merge with our shadow config.\n        const componentsJsonPath = path.resolve(options.cwd, \"components.json\")\n        if (fsExtra.existsSync(componentsJsonPath)) {\n          const existingConfig = await fsExtra.readJson(componentsJsonPath)\n          const config = rawConfigSchema.partial().parse(existingConfig)\n          shadowConfig = configWithDefaults(config)\n\n          // Since components.json might not be valid at this point.\n          // Temporarily rename components.json to allow preflight to run.\n          // We'll rename it back after preflight.\n          createFileBackup(componentsJsonPath)\n        }\n\n        // Ensure all registries used in components are configured.\n        const { config: updatedConfig } = await ensureRegistriesInConfig(\n          components,\n          shadowConfig,\n          {\n            silent: true,\n          }\n        )\n        shadowConfig = updatedConfig\n\n        // This forces a shadowConfig validation early in the process.\n        buildUrlAndHeadersForRegistryItem(components[0], shadowConfig)\n\n        const [item] = await getRegistryItems([components[0]], {\n          config: shadowConfig,\n        })\n        if (item?.type === \"registry:style\") {\n          // Set a default base color so we're not prompted.\n          // The style will extend or override it.\n          options.baseColor = \"neutral\"\n\n          // If the style extends none, we don't want to install the base style.\n          options.baseStyle =\n            item.extends === \"none\" ? false : options.baseStyle\n        }\n      }\n\n      // If --no-base-style, we don't want to prompt for a base color either.\n      // The style will extend or override it.\n      if (!options.baseStyle) {\n        options.baseColor = \"neutral\"\n      }\n\n      await runInit(options)\n\n      logger.log(\n        `${highlighter.success(\n          \"Success!\"\n        )} Project initialization completed.\\nYou may now add components.`\n      )\n\n      // We need when runninng with custom cwd.\n      deleteFileBackup(path.resolve(options.cwd, \"components.json\"))\n      logger.break()\n    } catch (error) {\n      logger.break()\n      handleError(error)\n    } finally {\n      clearRegistryContext()\n    }\n  })\n\nexport async function runInit(\n  options: z.infer<typeof initOptionsSchema> & {\n    skipPreflight?: boolean\n  }\n) {\n  let projectInfo\n  let newProjectTemplate\n  if (!options.skipPreflight) {\n    const preflight = await preFlightInit(options)\n    if (preflight.errors[ERRORS.MISSING_DIR_OR_EMPTY_PROJECT]) {\n      const { projectPath, template } = await createProject(options)\n      if (!projectPath) {\n        process.exit(1)\n      }\n      options.cwd = projectPath\n      options.isNewProject = true\n      newProjectTemplate = template\n    }\n    projectInfo = preflight.projectInfo\n  } else {\n    projectInfo = await getProjectInfo(options.cwd)\n  }\n\n  if (newProjectTemplate === \"next-monorepo\") {\n    options.cwd = path.resolve(options.cwd, \"apps/web\")\n    return await getConfig(options.cwd)\n  }\n\n  const projectConfig = await getProjectConfig(options.cwd, projectInfo)\n\n  let config = projectConfig\n    ? await promptForMinimalConfig(projectConfig, options)\n    : await promptForConfig(await getConfig(options.cwd))\n\n  if (!options.yes) {\n    const { proceed } = await prompts({\n      type: \"confirm\",\n      name: \"proceed\",\n      message: `Write configuration to ${highlighter.info(\n        \"components.json\"\n      )}. Proceed?`,\n      initial: true,\n    })\n\n    if (!proceed) {\n      process.exit(0)\n    }\n  }\n\n  // Prepare the list of components to be added.\n  const components = [\n    // \"index\" is the default shadcn style.\n    // Why index? Because when style is true, we read style from components.json and fetch that.\n    // i.e new-york from components.json then fetch /styles/new-york/index.\n    // TODO: Fix this so that we can extend any style i.e --style=new-york.\n    ...(options.baseStyle ? [\"index\"] : []),\n    ...(options.components ?? []),\n  ]\n\n  // Ensure registries are configured for the components we're about to add.\n  const fullConfigForRegistry = await resolveConfigPaths(options.cwd, config)\n  const { config: configWithRegistries } = await ensureRegistriesInConfig(\n    components,\n    fullConfigForRegistry,\n    {\n      silent: true,\n    }\n  )\n\n  // Update config with any new registries found.\n  if (configWithRegistries.registries) {\n    config.registries = configWithRegistries.registries\n  }\n\n  const componentSpinner = spinner(`Writing components.json.`).start()\n  const targetPath = path.resolve(options.cwd, \"components.json\")\n  const backupPath = `${targetPath}${FILE_BACKUP_SUFFIX}`\n\n  // Merge with backup config if it exists and not using --force\n  if (!options.force && fsExtra.existsSync(backupPath)) {\n    const existingConfig = await fsExtra.readJson(backupPath)\n\n    // Move registries at the end of the config.\n    const { registries, ...merged } = deepmerge(existingConfig, config)\n    config = { ...merged, registries }\n  }\n\n  // Make sure to filter out built-in registries.\n  // TODO: fix this in ensureRegistriesInConfig.\n  config.registries = Object.fromEntries(\n    Object.entries(config.registries || {}).filter(\n      ([key]) => !Object.keys(BUILTIN_REGISTRIES).includes(key)\n    )\n  )\n\n  // Write components.json.\n  await fs.writeFile(targetPath, `${JSON.stringify(config, null, 2)}\\n`, \"utf8\")\n  componentSpinner.succeed()\n\n  // Add components.\n  const fullConfig = await resolveConfigPaths(options.cwd, config)\n  await addComponents(components, fullConfig, {\n    // Init will always overwrite files.\n    overwrite: true,\n    silent: options.silent,\n    baseStyle: options.baseStyle,\n    isNewProject:\n      options.isNewProject || projectInfo?.framework.name === \"next-app\",\n  })\n\n  // If a new project is using src dir, let's update the tailwind content config.\n  // TODO: Handle this per framework.\n  if (options.isNewProject && options.srcDir) {\n    await updateTailwindContent(\n      [\"./src/**/*.{js,ts,jsx,tsx,mdx}\"],\n      fullConfig,\n      {\n        silent: options.silent,\n      }\n    )\n  }\n\n  return fullConfig\n}\n\nasync function promptForConfig(defaultConfig: Config | null = null) {\n  const [styles, baseColors] = await Promise.all([\n    getRegistryStyles(),\n    getRegistryBaseColors(),\n  ])\n\n  logger.info(\"\")\n  const options = await prompts([\n    {\n      type: \"toggle\",\n      name: \"typescript\",\n      message: `Would you like to use ${highlighter.info(\n        \"TypeScript\"\n      )} (recommended)?`,\n      initial: defaultConfig?.tsx ?? true,\n      active: \"yes\",\n      inactive: \"no\",\n    },\n    {\n      type: \"select\",\n      name: \"style\",\n      message: `Which ${highlighter.info(\"style\")} would you like to use?`,\n      choices: styles.map((style) => ({\n        title: style.label,\n        value: style.name,\n      })),\n    },\n    {\n      type: \"select\",\n      name: \"tailwindBaseColor\",\n      message: `Which color would you like to use as the ${highlighter.info(\n        \"base color\"\n      )}?`,\n      choices: baseColors.map((color) => ({\n        title: color.label,\n        value: color.name,\n      })),\n    },\n    {\n      type: \"text\",\n      name: \"tailwindCss\",\n      message: `Where is your ${highlighter.info(\"global CSS\")} file?`,\n      initial: defaultConfig?.tailwind.css ?? DEFAULT_TAILWIND_CSS,\n    },\n    {\n      type: \"toggle\",\n      name: \"tailwindCssVariables\",\n      message: `Would you like to use ${highlighter.info(\n        \"CSS variables\"\n      )} for theming?`,\n      initial: defaultConfig?.tailwind.cssVariables ?? true,\n      active: \"yes\",\n      inactive: \"no\",\n    },\n    {\n      type: \"text\",\n      name: \"tailwindPrefix\",\n      message: `Are you using a custom ${highlighter.info(\n        \"tailwind prefix eg. tw-\"\n      )}? (Leave blank if not)`,\n      initial: \"\",\n    },\n    {\n      type: \"text\",\n      name: \"tailwindConfig\",\n      message: `Where is your ${highlighter.info(\n        \"tailwind.config.js\"\n      )} located?`,\n      initial: defaultConfig?.tailwind.config ?? DEFAULT_TAILWIND_CONFIG,\n    },\n    {\n      type: \"text\",\n      name: \"components\",\n      message: `Configure the import alias for ${highlighter.info(\n        \"components\"\n      )}:`,\n      initial: defaultConfig?.aliases[\"components\"] ?? DEFAULT_COMPONENTS,\n    },\n    {\n      type: \"text\",\n      name: \"utils\",\n      message: `Configure the import alias for ${highlighter.info(\"utils\")}:`,\n      initial: defaultConfig?.aliases[\"utils\"] ?? DEFAULT_UTILS,\n    },\n    {\n      type: \"toggle\",\n      name: \"rsc\",\n      message: `Are you using ${highlighter.info(\"React Server Components\")}?`,\n      initial: defaultConfig?.rsc ?? true,\n      active: \"yes\",\n      inactive: \"no\",\n    },\n  ])\n\n  return rawConfigSchema.parse({\n    $schema: \"https://ui.shadcn.com/schema.json\",\n    style: options.style,\n    tailwind: {\n      config: options.tailwindConfig,\n      css: options.tailwindCss,\n      baseColor: options.tailwindBaseColor,\n      cssVariables: options.tailwindCssVariables,\n      prefix: options.tailwindPrefix,\n    },\n    rsc: options.rsc,\n    tsx: options.typescript,\n    aliases: {\n      utils: options.utils,\n      components: options.components,\n      // TODO: fix this.\n      lib: options.components.replace(/\\/components$/, \"lib\"),\n      hooks: options.components.replace(/\\/components$/, \"hooks\"),\n    },\n  })\n}\n\nasync function promptForMinimalConfig(\n  defaultConfig: Config,\n  opts: z.infer<typeof initOptionsSchema>\n) {\n  let style = defaultConfig.style\n  let baseColor = opts.baseColor\n  let cssVariables = defaultConfig.tailwind.cssVariables\n\n  if (!opts.defaults) {\n    const [styles, baseColors, tailwindVersion] = await Promise.all([\n      getRegistryStyles(),\n      getRegistryBaseColors(),\n      getProjectTailwindVersionFromConfig(defaultConfig),\n    ])\n\n    const options = await prompts([\n      {\n        type: tailwindVersion === \"v4\" ? null : \"select\",\n        name: \"style\",\n        message: `Which ${highlighter.info(\"style\")} would you like to use?`,\n        choices: styles.map((style) => ({\n          title:\n            style.name === \"new-york\" ? \"New York (Recommended)\" : style.label,\n          value: style.name,\n        })),\n        initial: 0,\n      },\n      {\n        type: opts.baseColor ? null : \"select\",\n        name: \"tailwindBaseColor\",\n        message: `Which color would you like to use as the ${highlighter.info(\n          \"base color\"\n        )}?`,\n        choices: baseColors.map((color) => ({\n          title: color.label,\n          value: color.name,\n        })),\n      },\n    ])\n\n    style = options.style ?? \"new-york\"\n    baseColor = options.tailwindBaseColor ?? baseColor\n    cssVariables = opts.cssVariables\n  }\n\n  return rawConfigSchema.parse({\n    $schema: defaultConfig?.$schema,\n    style,\n    tailwind: {\n      ...defaultConfig?.tailwind,\n      baseColor,\n      cssVariables,\n    },\n    rsc: defaultConfig?.rsc,\n    tsx: defaultConfig?.tsx,\n    iconLibrary: defaultConfig?.iconLibrary,\n    aliases: defaultConfig?.aliases,\n  })\n}\n", "import path from \"path\"\nimport { addOptionsSchema } from \"@/src/commands/add\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { getConfig } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport fs from \"fs-extra\"\nimport { z } from \"zod\"\n\nexport async function preFlightAdd(options: z.infer<typeof addOptionsSchema>) {\n  const errors: Record<string, boolean> = {}\n\n  // Ensure target directory exists.\n  // Check for empty project. We assume if no package.json exists, the project is empty.\n  if (\n    !fs.existsSync(options.cwd) ||\n    !fs.existsSync(path.resolve(options.cwd, \"package.json\"))\n  ) {\n    errors[ERRORS.MISSING_DIR_OR_EMPTY_PROJECT] = true\n    return {\n      errors,\n      config: null,\n    }\n  }\n\n  // Check for existing components.json file.\n  if (!fs.existsSync(path.resolve(options.cwd, \"components.json\"))) {\n    errors[ERRORS.MISSING_CONFIG] = true\n    return {\n      errors,\n      config: null,\n    }\n  }\n\n  try {\n    const config = await getConfig(options.cwd)\n\n    return {\n      errors,\n      config: config!,\n    }\n  } catch (error) {\n    logger.break()\n    logger.error(\n      `An invalid ${highlighter.info(\n        \"components.json\"\n      )} file was found at ${highlighter.info(\n        options.cwd\n      )}.\\nBefore you can add components, you must create a valid ${highlighter.info(\n        \"components.json\"\n      )} file by running the ${highlighter.info(\"init\")} command.`\n    )\n    logger.error(\n      `Learn more at ${highlighter.info(\n        \"https://ui.shadcn.com/docs/components-json\"\n      )}.`\n    )\n    logger.break()\n    process.exit(1)\n  }\n}\n", "import fs from \"fs/promises\"\nimport path from \"path\"\nimport { getRegistryItems } from \"@/src/registry/api\"\nimport { Config } from \"@/src/utils/get-config\"\n\nexport async function updateAppIndex(component: string, config: Config) {\n  const indexPath = path.join(config.resolvedPaths.cwd, \"app/page.tsx\")\n\n  if (!(await fs.stat(indexPath)).isFile()) {\n    return\n  }\n\n  const [registryItem] = await getRegistryItems([component], { config })\n  if (\n    !registryItem?.meta?.importSpecifier ||\n    !registryItem?.meta?.moduleSpecifier\n  ) {\n    return\n  }\n\n  // Overwrite the index file with the new import.\n  const content = `import { ${registryItem?.meta?.importSpecifier} } from \"${registryItem.meta.moduleSpecifier}\"\\n\\nexport default function Page() {\\n  return <${registryItem?.meta?.importSpecifier} />\\n}`\n  await fs.writeFile(indexPath, content, \"utf8\")\n}\n", "import path from \"path\"\nimport { runInit } from \"@/src/commands/init\"\nimport { preFlightAdd } from \"@/src/preflights/preflight-add\"\nimport { getRegistryItems, getShadcnRegistryIndex } from \"@/src/registry/api\"\nimport { DEPRECATED_COMPONENTS } from \"@/src/registry/constants\"\nimport { clearRegistryContext } from \"@/src/registry/context\"\nimport { isUniversalRegistryItem } from \"@/src/registry/utils\"\nimport { addComponents } from \"@/src/utils/add-components\"\nimport { createProject } from \"@/src/utils/create-project\"\nimport { loadEnvFiles } from \"@/src/utils/env-loader\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { createConfig, getConfig } from \"@/src/utils/get-config\"\nimport { getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { ensureRegistriesInConfig } from \"@/src/utils/registries\"\nimport { updateAppIndex } from \"@/src/utils/update-app-index\"\nimport { Command } from \"commander\"\nimport prompts from \"prompts\"\nimport { z } from \"zod\"\n\nexport const addOptionsSchema = z.object({\n  components: z.array(z.string()).optional(),\n  yes: z.boolean(),\n  overwrite: z.boolean(),\n  cwd: z.string(),\n  all: z.boolean(),\n  path: z.string().optional(),\n  silent: z.boolean(),\n  srcDir: z.boolean().optional(),\n  cssVariables: z.boolean(),\n})\n\nexport const add = new Command()\n  .name(\"add\")\n  .description(\"add a component to your project\")\n  .argument(\"[components...]\", \"names, url or local path to component\")\n  .option(\"-y, --yes\", \"skip confirmation prompt.\", false)\n  .option(\"-o, --overwrite\", \"overwrite existing files.\", false)\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .option(\"-a, --all\", \"add all available components\", false)\n  .option(\"-p, --path <path>\", \"the path to add the component to.\")\n  .option(\"-s, --silent\", \"mute output.\", false)\n  .option(\n    \"--src-dir\",\n    \"use the src directory when creating a new project.\",\n    false\n  )\n  .option(\n    \"--no-src-dir\",\n    \"do not use the src directory when creating a new project.\"\n  )\n  .option(\"--css-variables\", \"use css variables for theming.\", true)\n  .option(\"--no-css-variables\", \"do not use css variables for theming.\")\n  .action(async (components, opts) => {\n    try {\n      const options = addOptionsSchema.parse({\n        components,\n        cwd: path.resolve(opts.cwd),\n        ...opts,\n      })\n\n      await loadEnvFiles(options.cwd)\n\n      let initialConfig = await getConfig(options.cwd)\n      if (!initialConfig) {\n        initialConfig = createConfig({\n          style: \"new-york\",\n          resolvedPaths: {\n            cwd: options.cwd,\n          },\n        })\n      }\n\n      let hasNewRegistries = false\n      if (components.length > 0) {\n        const { config: updatedConfig, newRegistries } =\n          await ensureRegistriesInConfig(components, initialConfig, {\n            silent: options.silent,\n            writeFile: false,\n          })\n        initialConfig = updatedConfig\n        hasNewRegistries = newRegistries.length > 0\n      }\n\n      if (components.length > 0) {\n        const [registryItem] = await getRegistryItems([components[0]], {\n          config: initialConfig,\n        })\n        const itemType = registryItem?.type\n\n        if (isUniversalRegistryItem(registryItem)) {\n          await addComponents(components, initialConfig, options)\n          return\n        }\n\n        if (\n          !options.yes &&\n          (itemType === \"registry:style\" || itemType === \"registry:theme\")\n        ) {\n          logger.break()\n          const { confirm } = await prompts({\n            type: \"confirm\",\n            name: \"confirm\",\n            message: highlighter.warn(\n              `You are about to install a new ${itemType.replace(\n                \"registry:\",\n                \"\"\n              )}. \\nExisting CSS variables and components will be overwritten. Continue?`\n            ),\n          })\n          if (!confirm) {\n            logger.break()\n            logger.log(`Installation cancelled.`)\n            logger.break()\n            process.exit(1)\n          }\n        }\n      }\n\n      if (!options.components?.length) {\n        options.components = await promptForRegistryComponents(options)\n      }\n\n      const projectInfo = await getProjectInfo(options.cwd)\n      if (projectInfo?.tailwindVersion === \"v4\") {\n        const deprecatedComponents = DEPRECATED_COMPONENTS.filter((component) =>\n          options.components?.includes(component.name)\n        )\n\n        if (deprecatedComponents?.length) {\n          logger.break()\n          deprecatedComponents.forEach((component) => {\n            logger.warn(highlighter.warn(component.message))\n          })\n          logger.break()\n          process.exit(1)\n        }\n      }\n\n      let { errors, config } = await preFlightAdd(options)\n\n      // No components.json file. Prompt the user to run init.\n      let initHasRun = false\n      if (errors[ERRORS.MISSING_CONFIG]) {\n        const { proceed } = await prompts({\n          type: \"confirm\",\n          name: \"proceed\",\n          message: `You need to create a ${highlighter.info(\n            \"components.json\"\n          )} file to add components. Proceed?`,\n          initial: true,\n        })\n\n        if (!proceed) {\n          logger.break()\n          process.exit(1)\n        }\n\n        config = await runInit({\n          cwd: options.cwd,\n          yes: true,\n          force: true,\n          defaults: false,\n          skipPreflight: false,\n          silent: options.silent || !hasNewRegistries,\n          isNewProject: false,\n          srcDir: options.srcDir,\n          cssVariables: options.cssVariables,\n          baseStyle: true,\n          components: options.components,\n        })\n        initHasRun = true\n      }\n\n      let shouldUpdateAppIndex = false\n\n      if (errors[ERRORS.MISSING_DIR_OR_EMPTY_PROJECT]) {\n        const { projectPath, template } = await createProject({\n          cwd: options.cwd,\n          force: options.overwrite,\n          srcDir: options.srcDir,\n          components: options.components,\n        })\n        if (!projectPath) {\n          logger.break()\n          process.exit(1)\n        }\n        options.cwd = projectPath\n\n        if (template === \"next-monorepo\") {\n          options.cwd = path.resolve(options.cwd, \"apps/web\")\n          config = await getConfig(options.cwd)\n        } else {\n          config = await runInit({\n            cwd: options.cwd,\n            yes: true,\n            force: true,\n            defaults: false,\n            skipPreflight: true,\n            silent: !hasNewRegistries && options.silent,\n            isNewProject: true,\n            srcDir: options.srcDir,\n            cssVariables: options.cssVariables,\n            baseStyle: true,\n            components: options.components,\n          })\n          initHasRun = true\n\n          shouldUpdateAppIndex =\n            options.components?.length === 1 &&\n            !!options.components[0].match(/\\/chat\\/b\\//)\n        }\n      }\n\n      if (!config) {\n        throw new Error(\n          `Failed to read config at ${highlighter.info(options.cwd)}.`\n        )\n      }\n\n      const { config: updatedConfig } = await ensureRegistriesInConfig(\n        options.components,\n        config,\n        {\n          silent: options.silent || hasNewRegistries,\n        }\n      )\n      config = updatedConfig\n\n      if (!initHasRun) {\n        await addComponents(options.components, config, options)\n      }\n\n      // If we're adding a single component and it's from the v0 registry,\n      // let's update the app/page.tsx file to import the component.\n      if (shouldUpdateAppIndex) {\n        await updateAppIndex(options.components[0], config)\n      }\n    } catch (error) {\n      logger.break()\n      handleError(error)\n    } finally {\n      clearRegistryContext()\n    }\n  })\n\nasync function promptForRegistryComponents(\n  options: z.infer<typeof addOptionsSchema>\n) {\n  const registryIndex = await getShadcnRegistryIndex()\n  if (!registryIndex) {\n    logger.break()\n    handleError(new Error(\"Failed to fetch registry index.\"))\n    return []\n  }\n\n  if (options.all) {\n    return registryIndex\n      .map((entry) => entry.name)\n      .filter(\n        (component) => !DEPRECATED_COMPONENTS.some((c) => c.name === component)\n      )\n  }\n\n  if (options.components?.length) {\n    return options.components\n  }\n\n  const { components } = await prompts({\n    type: \"multiselect\",\n    name: \"components\",\n    message: \"Which components would you like to add?\",\n    hint: \"Space to select. A to toggle all. Enter to submit.\",\n    instructions: false,\n    choices: registryIndex\n      .filter(\n        (entry) =>\n          entry.type === \"registry:ui\" &&\n          !DEPRECATED_COMPONENTS.some(\n            (component) => component.name === entry.name\n          )\n      )\n      .map((entry) => ({\n        title: entry.name,\n        value: entry.name,\n        selected: options.all ? true : options.components?.includes(entry.name),\n      })),\n  })\n\n  if (!components?.length) {\n    logger.warn(\"No components selected. Exiting.\")\n    logger.info(\"\")\n    process.exit(1)\n  }\n\n  const result = z.array(z.string()).safeParse(components)\n  if (!result.success) {\n    logger.error(\"\")\n    handleError(new Error(\"Something went wrong. Please try again.\"))\n    return []\n  }\n  return result.data\n}\n", "import path from \"path\"\nimport { buildOptionsSchema } from \"@/src/commands/build\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport fs from \"fs-extra\"\nimport { z } from \"zod\"\n\nexport async function preFlightBuild(\n  options: z.infer<typeof buildOptionsSchema>\n) {\n  const errors: Record<string, boolean> = {}\n\n  const resolvePaths = {\n    cwd: options.cwd,\n    registryFile: path.resolve(options.cwd, options.registryFile),\n    outputDir: path.resolve(options.cwd, options.outputDir),\n  }\n\n  // Ensure registry file exists.\n  if (!fs.existsSync(resolvePaths.registryFile)) {\n    errors[ERRORS.BUILD_MISSING_REGISTRY_FILE] = true\n  }\n\n  // Create output directory if it doesn't exist.\n  await fs.mkdir(resolvePaths.outputDir, { recursive: true })\n\n  if (Object.keys(errors).length > 0) {\n    if (errors[ERRORS.BUILD_MISSING_REGISTRY_FILE]) {\n      logger.break()\n      logger.error(\n        `The path ${highlighter.info(\n          resolvePaths.registryFile\n        )} does not exist.`\n      )\n    }\n\n    logger.break()\n    process.exit(1)\n  }\n\n  return {\n    errors,\n    resolvePaths,\n  }\n}\n", "import * as fs from \"fs/promises\"\nimport * as path from \"path\"\nimport { preFlightBuild } from \"@/src/preflights/preflight-build\"\nimport { registryItemSchema, registrySchema } from \"@/src/schema\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { Command } from \"commander\"\nimport { z } from \"zod\"\n\nexport const buildOptionsSchema = z.object({\n  cwd: z.string(),\n  registryFile: z.string(),\n  outputDir: z.string(),\n})\n\nexport const build = new Command()\n  .name(\"build\")\n  .description(\"build components for a shadcn registry\")\n  .argument(\"[registry]\", \"path to registry.json file\", \"./registry.json\")\n  .option(\n    \"-o, --output <path>\",\n    \"destination directory for json files\",\n    \"./public/r\"\n  )\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .action(async (registry: string, opts) => {\n    try {\n      const options = buildOptionsSchema.parse({\n        cwd: path.resolve(opts.cwd),\n        registryFile: registry,\n        outputDir: opts.output,\n      })\n\n      const { resolvePaths } = await preFlightBuild(options)\n      const content = await fs.readFile(resolvePaths.registryFile, \"utf-8\")\n\n      const result = registrySchema.safeParse(JSON.parse(content))\n\n      if (!result.success) {\n        logger.error(\n          `Invalid registry file found at ${highlighter.info(\n            resolvePaths.registryFile\n          )}.`\n        )\n        process.exit(1)\n      }\n\n      const buildSpinner = spinner(\"Building registry...\")\n      for (const registryItem of result.data.items) {\n        buildSpinner.start(`Building ${registryItem.name}...`)\n\n        // Add the schema to the registry item.\n        registryItem[\"$schema\"] =\n          \"https://ui.shadcn.com/schema/registry-item.json\"\n\n        // Loop through each file in the files array.\n        for (const file of registryItem.files ?? []) {\n          file[\"content\"] = await fs.readFile(\n            path.resolve(resolvePaths.cwd, file.path),\n            \"utf-8\"\n          )\n        }\n\n        // Validate the registry item.\n        const result = registryItemSchema.safeParse(registryItem)\n        if (!result.success) {\n          logger.error(\n            `Invalid registry item found for ${highlighter.info(\n              registryItem.name\n            )}.`\n          )\n          continue\n        }\n\n        // Write the registry item to the output directory.\n        await fs.writeFile(\n          path.resolve(resolvePaths.outputDir, `${result.data.name}.json`),\n          JSON.stringify(result.data, null, 2)\n        )\n      }\n\n      // Copy registry.json to the output directory.\n      await fs.copyFile(\n        resolvePaths.registryFile,\n        path.resolve(resolvePaths.outputDir, \"registry.json\")\n      )\n\n      buildSpinner.succeed(\"Building registry.\")\n    } catch (error) {\n      logger.break()\n      handleError(error)\n    }\n  })\n", "import { existsSync, promises as fs } from \"fs\"\nimport path from \"path\"\nimport {\n  fetchTree,\n  getItemTargetPath,\n  getRegistryBaseColor,\n  getShadcnRegistryIndex,\n} from \"@/src/registry/api\"\nimport { registryIndexSchema } from \"@/src/schema\"\nimport { Config, getConfig } from \"@/src/utils/get-config\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { transform } from \"@/src/utils/transformers\"\nimport { Command } from \"commander\"\nimport { diffLines, type Change } from \"diff\"\nimport { z } from \"zod\"\n\nconst updateOptionsSchema = z.object({\n  component: z.string().optional(),\n  yes: z.boolean(),\n  cwd: z.string(),\n  path: z.string().optional(),\n})\n\nexport const diff = new Command()\n  .name(\"diff\")\n  .description(\"check for updates against the registry\")\n  .argument(\"[component]\", \"the component name\")\n  .option(\"-y, --yes\", \"skip confirmation prompt.\", false)\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .action(async (name, opts) => {\n    try {\n      const options = updateOptionsSchema.parse({\n        component: name,\n        ...opts,\n      })\n\n      const cwd = path.resolve(options.cwd)\n\n      if (!existsSync(cwd)) {\n        logger.error(`The path ${cwd} does not exist. Please try again.`)\n        process.exit(1)\n      }\n\n      const config = await getConfig(cwd)\n      if (!config) {\n        logger.warn(\n          `Configuration is missing. Please run ${highlighter.success(\n            `init`\n          )} to create a components.json file.`\n        )\n        process.exit(1)\n      }\n\n      const registryIndex = await getShadcnRegistryIndex()\n\n      if (!registryIndex) {\n        handleError(new Error(\"Failed to fetch registry index.\"))\n        process.exit(1)\n      }\n\n      if (!options.component) {\n        const targetDir = config.resolvedPaths.components\n\n        // Find all components that exist in the project.\n        const projectComponents = registryIndex.filter((item) => {\n          for (const file of item.files ?? []) {\n            const filePath = path.resolve(\n              targetDir,\n              typeof file === \"string\" ? file : file.path\n            )\n            if (existsSync(filePath)) {\n              return true\n            }\n          }\n\n          return false\n        })\n\n        // Check for updates.\n        const componentsWithUpdates = []\n        for (const component of projectComponents) {\n          const changes = await diffComponent(component, config)\n          if (changes.length) {\n            componentsWithUpdates.push({\n              name: component.name,\n              changes,\n            })\n          }\n        }\n\n        if (!componentsWithUpdates.length) {\n          logger.info(\"No updates found.\")\n          process.exit(0)\n        }\n\n        logger.info(\"The following components have updates available:\")\n        for (const component of componentsWithUpdates) {\n          logger.info(`- ${component.name}`)\n          for (const change of component.changes) {\n            logger.info(`  - ${change.filePath}`)\n          }\n        }\n        logger.break()\n        logger.info(\n          `Run ${highlighter.success(`diff <component>`)} to see the changes.`\n        )\n        process.exit(0)\n      }\n\n      // Show diff for a single component.\n      const component = registryIndex.find(\n        (item) => item.name === options.component\n      )\n\n      if (!component) {\n        logger.error(\n          `The component ${highlighter.success(\n            options.component\n          )} does not exist.`\n        )\n        process.exit(1)\n      }\n\n      const changes = await diffComponent(component, config)\n\n      if (!changes.length) {\n        logger.info(`No updates found for ${options.component}.`)\n        process.exit(0)\n      }\n\n      for (const change of changes) {\n        logger.info(`- ${change.filePath}`)\n        await printDiff(change.patch)\n        logger.info(\"\")\n      }\n    } catch (error) {\n      handleError(error)\n    }\n  })\n\nasync function diffComponent(\n  component: z.infer<typeof registryIndexSchema>[number],\n  config: Config\n) {\n  const payload = await fetchTree(config.style, [component])\n  const baseColor = await getRegistryBaseColor(config.tailwind.baseColor)\n\n  if (!payload) {\n    return []\n  }\n\n  const changes = []\n\n  for (const item of payload) {\n    const targetDir = await getItemTargetPath(config, item)\n\n    if (!targetDir) {\n      continue\n    }\n\n    for (const file of item.files ?? []) {\n      const filePath = path.resolve(\n        targetDir,\n        typeof file === \"string\" ? file : file.path\n      )\n\n      if (!existsSync(filePath)) {\n        continue\n      }\n\n      const fileContent = await fs.readFile(filePath, \"utf8\")\n\n      if (typeof file === \"string\" || !file.content) {\n        continue\n      }\n\n      const registryContent = await transform({\n        filename: file.path,\n        raw: file.content,\n        config,\n        baseColor,\n      })\n\n      const patch = diffLines(registryContent as string, fileContent)\n      if (patch.length > 1) {\n        changes.push({\n          filePath,\n          patch,\n        })\n      }\n    }\n  }\n\n  return changes\n}\n\nasync function printDiff(diff: Change[]) {\n  diff.forEach((part) => {\n    if (part) {\n      if (part.added) {\n        return process.stdout.write(highlighter.success(part.value))\n      }\n      if (part.removed) {\n        return process.stdout.write(highlighter.error(part.value))\n      }\n\n      return process.stdout.write(part.value)\n    }\n  })\n}\n", "import { getConfig } from \"@/src/utils/get-config\"\nimport { getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { logger } from \"@/src/utils/logger\"\nimport { Command } from \"commander\"\n\nexport const info = new Command()\n  .name(\"info\")\n  .description(\"get information about your project\")\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .action(async (opts) => {\n    try {\n      logger.info(\"> project info\")\n      console.log(await getProjectInfo(opts.cwd))\n      logger.break()\n      logger.info(\"> components.json\")\n      console.log(await getConfig(opts.cwd))\n    } catch (error) {\n      handleError(error)\n    }\n  })\n", "import { promises as fs } from \"fs\"\nimport path from \"path\"\nimport { server } from \"@/src/mcp\"\nimport { loadEnvFiles } from \"@/src/utils/env-loader\"\nimport { getConfig } from \"@/src/utils/get-config\"\nimport { getPackageManager } from \"@/src/utils/get-package-manager\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { updateDependencies } from \"@/src/utils/updaters/update-dependencies\"\nimport { StdioServerTransport } from \"@modelcontextprotocol/sdk/server/stdio.js\"\nimport { Command } from \"commander\"\nimport deepmerge from \"deepmerge\"\nimport { execa } from \"execa\"\nimport fsExtra from \"fs-extra\"\nimport prompts from \"prompts\"\nimport z from \"zod\"\n\nconst SHADCN_MCP_VERSION = \"latest\"\n\nconst CLIENTS = [\n  {\n    name: \"claude\",\n    label: \"Claude Code\",\n    configPath: \".mcp.json\",\n    config: {\n      mcpServers: {\n        shadcn: {\n          command: \"npx\",\n          args: [`shadcn@${SHADCN_MCP_VERSION}`, \"mcp\"],\n        },\n      },\n    },\n  },\n  {\n    name: \"cursor\",\n    label: \"Cursor\",\n    configPath: \".cursor/mcp.json\",\n    config: {\n      mcpServers: {\n        shadcn: {\n          command: \"npx\",\n          args: [`shadcn@${SHADCN_MCP_VERSION}`, \"mcp\"],\n        },\n      },\n    },\n  },\n  {\n    name: \"vscode\",\n    label: \"VS Code\",\n    configPath: \".vscode/mcp.json\",\n    config: {\n      servers: {\n        shadcn: {\n          command: \"npx\",\n          args: [`shadcn@${SHADCN_MCP_VERSION}`, \"mcp\"],\n        },\n      },\n    },\n  },\n  {\n    name: \"codex\",\n    label: \"Codex\",\n    configPath: \".codex/config.toml\",\n    config: `[mcp_servers.shadcn]\ncommand = \"npx\"\nargs = [\"shadcn@${SHADCN_MCP_VERSION}\", \"mcp\"]\n`,\n  },\n] as const\n\nconst DEPENDENCIES = [`shadcn@${SHADCN_MCP_VERSION}`]\n\nexport const mcp = new Command()\n  .name(\"mcp\")\n  .description(\"MCP server and configuration commands\")\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .action(async (options) => {\n    try {\n      await loadEnvFiles(options.cwd)\n      const transport = new StdioServerTransport()\n      await server.connect(transport)\n    } catch (error) {\n      logger.break()\n      handleError(error)\n    }\n  })\n\nconst mcpInitOptionsSchema = z.object({\n  client: z.enum([\"claude\", \"cursor\", \"vscode\", \"codex\"]),\n  cwd: z.string(),\n})\n\nmcp\n  .command(\"init\")\n  .description(\"Initialize MCP configuration for your client\")\n  .option(\n    \"--client <client>\",\n    `MCP client (${CLIENTS.map((c) => c.name).join(\", \")})`\n  )\n  .action(async (opts, command) => {\n    try {\n      // Get the cwd from parent command.\n      const parentOpts = command.parent?.opts() || {}\n      const cwd = parentOpts.cwd || process.cwd()\n\n      let client = opts.client\n\n      if (!client) {\n        const response = await prompts({\n          type: \"select\",\n          name: \"client\",\n          message: \"Which MCP client are you using?\",\n          choices: CLIENTS.map((c) => ({\n            title: c.label,\n            value: c.name,\n          })),\n        })\n\n        if (!response.client) {\n          logger.break()\n          process.exit(1)\n        }\n\n        client = response.client\n      }\n\n      const options = mcpInitOptionsSchema.parse({\n        client,\n        cwd,\n      })\n\n      const config = await getConfig(options.cwd)\n\n      if (options.client === \"codex\") {\n        if (config) {\n          await updateDependencies([], DEPENDENCIES, config, {\n            silent: false,\n          })\n        } else {\n          const packageManager = await getPackageManager(options.cwd)\n          const installCommand = packageManager === \"npm\" ? \"install\" : \"add\"\n          const devFlag = packageManager === \"npm\" ? \"--save-dev\" : \"-D\"\n\n          const installSpinner = spinner(\"Installing dependencies...\").start()\n          await execa(\n            packageManager,\n            [installCommand, devFlag, ...DEPENDENCIES],\n            {\n              cwd: options.cwd,\n            }\n          )\n          installSpinner.succeed(\"Installing dependencies.\")\n        }\n\n        logger.break()\n        logger.log(\"To configure the shadcn MCP server in Codex:\")\n        logger.break()\n        logger.log(\n          `1. Open or create the file ${highlighter.info(\n            \"~/.codex/config.toml\"\n          )}`\n        )\n        logger.log(\"2. Add the following configuration:\")\n        logger.log()\n        logger.info(`[mcp_servers.shadcn]\ncommand = \"npx\"\nargs = [\"shadcn@${SHADCN_MCP_VERSION}\", \"mcp\"]`)\n        logger.break()\n        logger.info(\"3. Restart Codex to load the MCP server\")\n        logger.break()\n        process.exit(0)\n      }\n\n      const configSpinner = spinner(\"Configuring MCP server...\").start()\n      const configPath = await runMcpInit(options)\n      configSpinner.succeed(\"Configuring MCP server.\")\n\n      if (config) {\n        await updateDependencies([], DEPENDENCIES, config, {\n          silent: false,\n        })\n      } else {\n        const packageManager = await getPackageManager(options.cwd)\n        const installCommand = packageManager === \"npm\" ? \"install\" : \"add\"\n        const devFlag = packageManager === \"npm\" ? \"--save-dev\" : \"-D\"\n\n        const installSpinner = spinner(\"Installing dependencies...\").start()\n        await execa(\n          packageManager,\n          [installCommand, devFlag, ...DEPENDENCIES],\n          {\n            cwd: options.cwd,\n          }\n        )\n        installSpinner.succeed(\"Installing dependencies.\")\n      }\n\n      logger.break()\n      logger.success(`Configuration saved to ${configPath}.`)\n      logger.break()\n    } catch (error) {\n      handleError(error)\n    }\n  })\n\nconst overwriteMerge = (_: any[], sourceArray: any[]) => sourceArray\n\nasync function runMcpInit(options: z.infer<typeof mcpInitOptionsSchema>) {\n  const { client, cwd } = options\n\n  const clientInfo = CLIENTS.find((c) => c.name === client)\n  if (!clientInfo) {\n    throw new Error(\n      `Unknown client: ${client}. Available clients: ${CLIENTS.map(\n        (c) => c.name\n      ).join(\", \")}`\n    )\n  }\n\n  const configPath = path.join(cwd, clientInfo.configPath)\n  const dir = path.dirname(configPath)\n  await fsExtra.ensureDir(dir)\n\n  // Handle JSON format.\n  let existingConfig = {}\n  try {\n    const content = await fs.readFile(configPath, \"utf-8\")\n    existingConfig = JSON.parse(content)\n  } catch {}\n\n  const mergedConfig = deepmerge(\n    existingConfig,\n    clientInfo.config as Record<string, unknown>,\n    { arrayMerge: overwriteMerge }\n  )\n\n  await fs.writeFile(\n    configPath,\n    JSON.stringify(mergedConfig, null, 2) + \"\\n\",\n    \"utf-8\"\n  )\n\n  return clientInfo.configPath\n}\n", "import { randomBytes } from \"crypto\"\nimport { promises as fs } from \"fs\"\nimport { tmpdir } from \"os\"\nimport path from \"path\"\nimport { getRegistryIcons } from \"@/src/registry/api\"\nimport { iconsSchema } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { ICON_LIBRARIES } from \"@/src/utils/icon-libraries\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { updateDependencies } from \"@/src/utils/updaters/update-dependencies\"\nimport fg from \"fast-glob\"\nimport prompts from \"prompts\"\nimport { Project, ScriptKind, SyntaxKind } from \"ts-morph\"\nimport { z } from \"zod\"\n\nexport async function migrateIcons(config: Config) {\n  if (!config.resolvedPaths.ui) {\n    throw new Error(\n      \"We could not find a valid `ui` path in your `components.json` file. Please ensure you have a valid `ui` path in your `components.json` file.\"\n    )\n  }\n\n  const uiPath = config.resolvedPaths.ui\n  const [files, registryIcons] = await Promise.all([\n    fg(\"**/*.{js,ts,jsx,tsx}\", {\n      cwd: uiPath,\n    }),\n    getRegistryIcons(),\n  ])\n\n  if (Object.keys(registryIcons).length === 0) {\n    throw new Error(\"Something went wrong fetching the registry icons.\")\n  }\n\n  const libraryChoices = Object.entries(ICON_LIBRARIES).map(\n    ([name, iconLibrary]) => ({\n      title: iconLibrary.name,\n      value: name,\n    })\n  )\n\n  const migrateOptions = await prompts([\n    {\n      type: \"select\",\n      name: \"sourceLibrary\",\n      message: `Which icon library would you like to ${highlighter.info(\n        \"migrate from\"\n      )}?`,\n      choices: libraryChoices,\n    },\n    {\n      type: \"select\",\n      name: \"targetLibrary\",\n      message: `Which icon library would you like to ${highlighter.info(\n        \"migrate to\"\n      )}?`,\n      choices: libraryChoices,\n    },\n  ])\n\n  if (migrateOptions.sourceLibrary === migrateOptions.targetLibrary) {\n    throw new Error(\n      \"You cannot migrate to the same icon library. Please choose a different icon library.\"\n    )\n  }\n\n  if (\n    !(\n      migrateOptions.sourceLibrary in ICON_LIBRARIES &&\n      migrateOptions.targetLibrary in ICON_LIBRARIES\n    )\n  ) {\n    throw new Error(\"Invalid icon library. Please choose a valid icon library.\")\n  }\n\n  const sourceLibrary =\n    ICON_LIBRARIES[migrateOptions.sourceLibrary as keyof typeof ICON_LIBRARIES]\n  const targetLibrary =\n    ICON_LIBRARIES[migrateOptions.targetLibrary as keyof typeof ICON_LIBRARIES]\n  const { confirm } = await prompts({\n    type: \"confirm\",\n    name: \"confirm\",\n    initial: true,\n    message: `We will migrate ${highlighter.info(\n      files.length\n    )} files in ${highlighter.info(\n      `./${path.relative(config.resolvedPaths.cwd, uiPath)}`\n    )} from ${highlighter.info(sourceLibrary.name)} to ${highlighter.info(\n      targetLibrary.name\n    )}. Continue?`,\n  })\n\n  if (!confirm) {\n    logger.info(\"Migration cancelled.\")\n    process.exit(0)\n  }\n\n  if (targetLibrary.package) {\n    await updateDependencies([targetLibrary.package], [], config, {\n      silent: false,\n    })\n  }\n\n  const migrationSpinner = spinner(`Migrating icons...`)?.start()\n\n  await Promise.all(\n    files.map(async (file) => {\n      migrationSpinner.text = `Migrating ${file}...`\n\n      const filePath = path.join(uiPath, file)\n      const fileContent = await fs.readFile(filePath, \"utf-8\")\n\n      const content = await migrateIconsFile(\n        fileContent,\n        migrateOptions.sourceLibrary,\n        migrateOptions.targetLibrary,\n        registryIcons\n      )\n\n      await fs.writeFile(filePath, content)\n    })\n  )\n\n  migrationSpinner.succeed(\"Migration complete.\")\n}\n\nexport async function migrateIconsFile(\n  content: string,\n  sourceLibrary: keyof typeof ICON_LIBRARIES,\n  targetLibrary: keyof typeof ICON_LIBRARIES,\n  iconsMapping: z.infer<typeof iconsSchema>\n) {\n  const sourceLibraryImport = ICON_LIBRARIES[sourceLibrary]?.import\n  const targetLibraryImport = ICON_LIBRARIES[targetLibrary]?.import\n\n  const dir = await fs.mkdtemp(path.join(tmpdir(), \"shadcn-\"))\n  const project = new Project({\n    compilerOptions: {},\n  })\n\n  const tempFile = path.join(\n    dir,\n    `shadcn-icons-${randomBytes(4).toString(\"hex\")}.tsx`\n  )\n  const sourceFile = project.createSourceFile(tempFile, content, {\n    scriptKind: ScriptKind.TSX,\n  })\n\n  // Find all sourceLibrary imports.\n  let targetedIcons: string[] = []\n  for (const importDeclaration of sourceFile.getImportDeclarations() ?? []) {\n    if (\n      importDeclaration.getModuleSpecifier()?.getText() !==\n      `\"${sourceLibraryImport}\"`\n    ) {\n      continue\n    }\n\n    for (const specifier of importDeclaration.getNamedImports() ?? []) {\n      const iconName = specifier.getName()\n\n      // TODO: this is O(n^2) but okay for now.\n      const targetedIcon = Object.values(iconsMapping).find(\n        (icon) => icon[sourceLibrary] === iconName\n      )?.[targetLibrary]\n\n      if (!targetedIcon || targetedIcons.includes(targetedIcon)) {\n        continue\n      }\n\n      targetedIcons.push(targetedIcon)\n\n      // Remove the named import.\n      specifier.remove()\n\n      // Replace with the targeted icon.\n      sourceFile\n        .getDescendantsOfKind(SyntaxKind.JsxSelfClosingElement)\n        .filter((node) => node.getTagNameNode()?.getText() === iconName)\n        .forEach((node) => node.getTagNameNode()?.replaceWithText(targetedIcon))\n    }\n\n    // If the named import is empty, remove the import declaration.\n    if (importDeclaration.getNamedImports()?.length === 0) {\n      importDeclaration.remove()\n    }\n  }\n\n  if (targetedIcons.length > 0) {\n    sourceFile.addImportDeclaration({\n      moduleSpecifier: targetLibraryImport,\n      namedImports: targetedIcons.map((icon) => ({\n        name: icon,\n      })),\n    })\n  }\n\n  return await sourceFile.getText()\n}\n", "import { promises as fs } from \"fs\"\nimport path from \"path\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { getPackageInfo } from \"@/src/utils/get-package-info\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { updateDependencies } from \"@/src/utils/updaters/update-dependencies\"\nimport fg from \"fast-glob\"\nimport prompts from \"prompts\"\n\nfunction toPascalCase(str: string): string {\n  return str\n    .split(\"-\")\n    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))\n    .join(\"\")\n}\n\nfunction processNamedImports(\n  namedImports: string,\n  isTypeOnly: boolean,\n  imports: Array<{ name: string; alias?: string; isType?: boolean }>,\n  packageName: string\n) {\n  // Clean up multi-line imports.\n  // Remove comments and whitespace.\n  const cleanedImports = namedImports\n    .replace(/\\/\\/.*$/gm, \"\")\n    .replace(/\\/\\*[\\s\\S]*?\\*\\//g, \"\")\n    .replace(/\\s+/g, \" \")\n    .trim()\n\n  const namedImportList = cleanedImports\n    .split(\",\")\n    .map((importItem) => importItem.trim())\n    .filter(Boolean)\n\n  for (const importItem of namedImportList) {\n    const inlineTypeMatch = importItem.match(/^type\\s+(\\w+)(?:\\s+as\\s+(\\w+))?$/)\n    const aliasMatch = importItem.match(/^(\\w+)\\s+as\\s+(\\w+)$/)\n\n    if (inlineTypeMatch) {\n      // Inline type: \"type DialogProps\" or \"type DialogProps as Props\"\n      const importName = inlineTypeMatch[1]\n      const importAlias = inlineTypeMatch[2]\n\n      if (packageName === \"slot\" && importName === \"Slot\" && !importAlias) {\n        imports.push({\n          name: \"Slot\",\n          alias: \"SlotPrimitive\",\n          isType: true,\n        })\n      } else {\n        imports.push({\n          name: importName,\n          alias: importAlias,\n          isType: true,\n        })\n      }\n    } else if (aliasMatch) {\n      // Regular import with alias: \"Root as DialogRoot\"\n      const importName = aliasMatch[1]\n      const importAlias = aliasMatch[2]\n\n      if (\n        packageName === \"slot\" &&\n        importName === \"Slot\" &&\n        importAlias === \"Slot\"\n      ) {\n        imports.push({\n          name: \"Slot\",\n          alias: \"SlotPrimitive\",\n          isType: isTypeOnly,\n        })\n      } else {\n        imports.push({\n          name: importName,\n          alias: importAlias,\n          isType: isTypeOnly,\n        })\n      }\n    } else {\n      // Simple import: \"Root\"\n      // Special handling for Slot: always alias it as SlotPrimitive\n      if (packageName === \"slot\" && importItem === \"Slot\") {\n        imports.push({\n          name: \"Slot\",\n          alias: \"SlotPrimitive\",\n          isType: isTypeOnly,\n        })\n      } else {\n        imports.push({\n          name: importItem,\n          isType: isTypeOnly,\n        })\n      }\n    }\n  }\n}\n\nexport async function migrateRadix(\n  config: Config,\n  options: { yes?: boolean } = {}\n) {\n  if (!config.resolvedPaths.ui) {\n    throw new Error(\n      \"We could not find a valid `ui` path in your `components.json` file. Please ensure you have a valid `ui` path in your `components.json` file.\"\n    )\n  }\n\n  const uiPath = config.resolvedPaths.ui\n  const files = await fg(\"**/*.{js,ts,jsx,tsx}\", {\n    cwd: uiPath,\n  })\n\n  if (!options.yes) {\n    const { confirm } = await prompts({\n      type: \"confirm\",\n      name: \"confirm\",\n      initial: true,\n      message: `We will migrate ${highlighter.info(\n        files.length\n      )} files in ${highlighter.info(\n        `./${path.relative(config.resolvedPaths.cwd, uiPath)}`\n      )} to ${highlighter.info(\"radix-ui\")}. Continue?`,\n    })\n\n    if (!confirm) {\n      logger.info(\"Migration cancelled.\")\n      process.exit(0)\n    }\n  }\n\n  const migrationSpinner = spinner(`Migrating imports...`)?.start()\n  const foundPackages = new Set<string>()\n\n  await Promise.all(\n    files.map(async (file) => {\n      migrationSpinner.text = `Migrating ${file}...`\n\n      const filePath = path.join(uiPath, file)\n      const fileContent = await fs.readFile(filePath, \"utf-8\")\n\n      const { content, replacedPackages } = await migrateRadixFile(fileContent)\n\n      // Track which packages we found\n      replacedPackages.forEach((pkg) => foundPackages.add(pkg))\n\n      await fs.writeFile(filePath, content)\n    })\n  )\n\n  migrationSpinner.succeed(\"Migrating imports.\")\n\n  // Update package.json dependencies\n  const packageSpinner = spinner(`Updating package.json...`)?.start()\n\n  try {\n    const packageJson = getPackageInfo(config.resolvedPaths.cwd, false)\n\n    if (!packageJson) {\n      packageSpinner.fail(\"Could not read package.json\")\n      logger.warn(\n        \"Could not update package.json. You may need to manually replace @radix-ui/react-* packages with radix-ui\"\n      )\n      return\n    }\n\n    const foundPackagesArray = Array.from(foundPackages)\n\n    // Remove packages from both dependencies and devDependencies if found in source files\n    const dependencyTypes = [\"dependencies\", \"devDependencies\"] as const\n    for (const depType of dependencyTypes) {\n      if (packageJson[depType]) {\n        for (const pkg of foundPackagesArray) {\n          if (packageJson[depType]![pkg]) {\n            delete packageJson[depType]![pkg]\n          }\n        }\n      }\n    }\n\n    // Add radix-ui if we found any Radix packages.\n    if (foundPackagesArray.length > 0) {\n      if (!packageJson.dependencies) {\n        packageJson.dependencies = {}\n      }\n      packageJson.dependencies[\"radix-ui\"] = \"latest\"\n\n      const packageJsonPath = path.join(\n        config.resolvedPaths.cwd,\n        \"package.json\"\n      )\n      await fs.writeFile(\n        packageJsonPath,\n        JSON.stringify(packageJson, null, 2) + \"\\n\"\n      )\n\n      packageSpinner.succeed(`Updated package.json.`)\n\n      // Install radix-ui dependency.\n      await updateDependencies([\"radix-ui\"], [], config, { silent: false })\n    } else {\n      packageSpinner.succeed(\"No packages found in source files.\")\n    }\n  } catch (error) {\n    packageSpinner.fail(\"Failed to update package.json\")\n    logger.warn(\n      \"You may need to manually replace @radix-ui/react-* packages with radix-ui\"\n    )\n  }\n}\n\nexport async function migrateRadixFile(\n  content: string\n): Promise<{ content: string; replacedPackages: string[] }> {\n  // Enhanced regex to handle type-only imports, but exclude react-icons\n  // Also capture optional semicolon at the end\n  const radixImportPattern =\n    /import\\s+(?:(type)\\s+)?(?:\\*\\s+as\\s+(\\w+)|{([^}]+)})\\s+from\\s+([\"'])@radix-ui\\/react-([^\"']+)\\4(;?)/g\n\n  const imports: Array<{ name: string; alias?: string; isType?: boolean }> = []\n  const linesToRemove: string[] = []\n  const replacedPackages: string[] = []\n  let quoteStyle = '\"' // Default to double quotes\n  let hasSemicolon = false // Track if any import had a semicolon\n\n  let result = content\n  let match\n\n  // Find all Radix imports\n  while ((match = radixImportPattern.exec(content)) !== null) {\n    const [\n      fullMatch,\n      typeKeyword,\n      namespaceAlias,\n      namedImports,\n      quote,\n      packageName,\n      semicolon,\n    ] = match\n\n    // Skip react-icons package and any sub-paths (like react-icons/dist/types)\n    if (packageName === \"icons\" || packageName.startsWith(\"icons/\")) {\n      continue\n    }\n\n    linesToRemove.push(fullMatch)\n\n    // Use the quote style and semicolon style from the first import\n    if (linesToRemove.length === 1) {\n      quoteStyle = quote\n      hasSemicolon = semicolon === \";\"\n    }\n\n    // Track which package we're replacing\n    replacedPackages.push(`@radix-ui/react-${packageName}`)\n\n    const isTypeOnly = Boolean(typeKeyword)\n\n    if (namespaceAlias) {\n      // Handle namespace imports: import * as DialogPrimitive from \"@radix-ui/react-dialog\"\n      const componentName = toPascalCase(packageName)\n      imports.push({\n        name: componentName,\n        alias: namespaceAlias,\n        isType: isTypeOnly,\n      })\n    } else if (namedImports) {\n      // Handle named imports: import { Root, Trigger } from \"@radix-ui/react-dialog\"\n      // or import type { DialogProps } from \"@radix-ui/react-dialog\"\n      // or import { type DialogProps, Root } from \"@radix-ui/react-dialog\"\n\n      processNamedImports(namedImports, isTypeOnly, imports, packageName)\n    }\n  }\n\n  if (imports.length === 0) {\n    return {\n      content,\n      replacedPackages: [],\n    }\n  }\n\n  // Remove duplicates.\n  // Considering name, alias, and type status.\n  const uniqueImports = imports.filter(\n    (importName, index, self) =>\n      index ===\n      self.findIndex(\n        (i) =>\n          i.name === importName.name &&\n          i.alias === importName.alias &&\n          i.isType === importName.isType\n      )\n  )\n\n  // Create the unified import with preserved quote style and type annotations\n  const importList = uniqueImports\n    .map((imp) => {\n      const typePrefix = imp.isType ? \"type \" : \"\"\n      return imp.alias\n        ? `${typePrefix}${imp.name} as ${imp.alias}`\n        : `${typePrefix}${imp.name}`\n    })\n    .join(\", \")\n\n  const unifiedImport = `import { ${importList} } from ${quoteStyle}radix-ui${quoteStyle}${\n    hasSemicolon ? \";\" : \"\"\n  }`\n\n  // Replace first import with unified import, remove the rest\n  result = linesToRemove.reduce((acc, line, index) => {\n    return acc.replace(line, index === 0 ? unifiedImport : \"\")\n  }, result)\n\n  // Clean up extra blank lines\n  result = result.replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\n\n  // Handle special case for Slot usage transformation\n  // Now that we import { Slot as SlotPrimitive }, we need to:\n  // 1. Transform: const Comp = asChild ? Slot : [ANYTHING] -> const Comp = asChild ? SlotPrimitive.Slot : [ANYTHING]\n  // 2. Transform: React.ComponentProps<typeof Slot> -> React.ComponentProps<typeof SlotPrimitive.Slot>\n  const hasSlotImport = uniqueImports.some(\n    (imp) => imp.name === \"Slot\" && imp.alias === \"SlotPrimitive\"\n  )\n\n  if (hasSlotImport) {\n    // Find all lines that are NOT import lines to avoid transforming the import statement itself\n    const lines = result.split(\"\\n\")\n    const transformedLines = lines.map((line) => {\n      // Skip import lines\n      if (line.trim().startsWith(\"import \")) {\n        return line\n      }\n\n      let transformedLine = line\n\n      // Handle all Slot references in one comprehensive pass\n      // Use placeholders to avoid double replacements\n\n      // First, mark specific patterns with placeholders\n      transformedLine = transformedLine.replace(\n        /\\b(asChild\\s*\\?\\s*)Slot(\\s*:)/g,\n        \"$1__SLOT_PLACEHOLDER__$2\"\n      )\n\n      transformedLine = transformedLine.replace(\n        /\\bReact\\.ComponentProps<typeof\\s+Slot>/g,\n        \"React.ComponentProps<typeof __SLOT_PLACEHOLDER__>\"\n      )\n\n      transformedLine = transformedLine.replace(\n        /\\bComponentProps<typeof\\s+Slot>/g,\n        \"ComponentProps<typeof __SLOT_PLACEHOLDER__>\"\n      )\n\n      transformedLine = transformedLine.replace(\n        /(<\\/?)Slot(\\s*\\/?>)/g,\n        \"$1__SLOT_PLACEHOLDER__$2\"\n      )\n\n      // Handle any other standalone Slot usage\n      transformedLine = transformedLine.replace(\n        /\\bSlot\\b/g,\n        (match, offset, string) => {\n          // Don't transform if it's inside quotes\n          const beforeMatch = string.substring(0, offset)\n          const openQuotes = (beforeMatch.match(/\"/g) || []).length\n          const openSingleQuotes = (beforeMatch.match(/'/g) || []).length\n\n          // If we're inside quotes, don't transform\n          if (openQuotes % 2 !== 0 || openSingleQuotes % 2 !== 0) {\n            return match\n          }\n\n          return \"__SLOT_PLACEHOLDER__\"\n        }\n      )\n\n      // Finally, replace all placeholders with SlotPrimitive.Slot\n      transformedLine = transformedLine.replace(\n        /__SLOT_PLACEHOLDER__/g,\n        \"SlotPrimitive.Slot\"\n      )\n\n      return transformedLine\n    })\n\n    result = transformedLines.join(\"\\n\")\n  }\n\n  // Remove duplicate packages\n  const uniqueReplacedPackages = Array.from(new Set(replacedPackages))\n\n  return {\n    content: result,\n    replacedPackages: uniqueReplacedPackages,\n  }\n}\n", "import path from \"path\"\nimport { addOptionsSchema } from \"@/src/commands/add\"\nimport { migrateOptionsSchema } from \"@/src/commands/migrate\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { getConfig } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport fs from \"fs-extra\"\nimport { z } from \"zod\"\n\nexport async function preFlightMigrate(\n  options: z.infer<typeof migrateOptionsSchema>\n) {\n  const errors: Record<string, boolean> = {}\n\n  // Ensure target directory exists.\n  // Check for empty project. We assume if no package.json exists, the project is empty.\n  if (\n    !fs.existsSync(options.cwd) ||\n    !fs.existsSync(path.resolve(options.cwd, \"package.json\"))\n  ) {\n    errors[ERRORS.MISSING_DIR_OR_EMPTY_PROJECT] = true\n    return {\n      errors,\n      config: null,\n    }\n  }\n\n  // Check for existing components.json file.\n  if (!fs.existsSync(path.resolve(options.cwd, \"components.json\"))) {\n    errors[ERRORS.MISSING_CONFIG] = true\n    return {\n      errors,\n      config: null,\n    }\n  }\n\n  try {\n    const config = await getConfig(options.cwd)\n\n    return {\n      errors,\n      config: config!,\n    }\n  } catch (error) {\n    logger.break()\n    logger.error(\n      `An invalid ${highlighter.info(\n        \"components.json\"\n      )} file was found at ${highlighter.info(\n        options.cwd\n      )}.\\nBefore you can run a migration, you must create a valid ${highlighter.info(\n        \"components.json\"\n      )} file by running the ${highlighter.info(\"init\")} command.`\n    )\n    logger.error(\n      `Learn more at ${highlighter.info(\n        \"https://ui.shadcn.com/docs/components-json\"\n      )}.`\n    )\n    logger.break()\n    process.exit(1)\n  }\n}\n", "import path from \"path\"\nimport { migrateIcons } from \"@/src/migrations/migrate-icons\"\nimport { migrateRadix } from \"@/src/migrations/migrate-radix\"\nimport { preFlightMigrate } from \"@/src/preflights/preflight-migrate\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { logger } from \"@/src/utils/logger\"\nimport { Command } from \"commander\"\nimport { z } from \"zod\"\n\nexport const migrations = [\n  {\n    name: \"icons\",\n    description: \"migrate your ui components to a different icon library.\",\n  },\n  {\n    name: \"radix\",\n    description: \"migrate to radix-ui.\",\n  },\n] as const\n\nexport const migrateOptionsSchema = z.object({\n  cwd: z.string(),\n  list: z.boolean(),\n  yes: z.boolean(),\n  migration: z\n    .string()\n    .refine(\n      (value) =>\n        value && migrations.some((migration) => migration.name === value),\n      {\n        message:\n          \"You must specify a valid migration. Run `shadcn migrate --list` to see available migrations.\",\n      }\n    )\n    .optional(),\n})\n\nexport const migrate = new Command()\n  .name(\"migrate\")\n  .description(\"run a migration.\")\n  .argument(\"[migration]\", \"the migration to run.\")\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .option(\"-l, --list\", \"list all migrations.\", false)\n  .option(\"-y, --yes\", \"skip confirmation prompt.\", false)\n  .action(async (migration, opts) => {\n    try {\n      const options = migrateOptionsSchema.parse({\n        cwd: path.resolve(opts.cwd),\n        migration,\n        list: opts.list,\n        yes: opts.yes,\n      })\n\n      if (options.list || !options.migration) {\n        logger.info(\"Available migrations:\")\n        for (const migration of migrations) {\n          logger.info(`- ${migration.name}: ${migration.description}`)\n        }\n        return\n      }\n\n      if (!options.migration) {\n        throw new Error(\n          \"You must specify a migration. Run `shadcn migrate --list` to see available migrations.\"\n        )\n      }\n\n      let { errors, config } = await preFlightMigrate(options)\n\n      if (\n        errors[ERRORS.MISSING_DIR_OR_EMPTY_PROJECT] ||\n        errors[ERRORS.MISSING_CONFIG]\n      ) {\n        throw new Error(\n          \"No `components.json` file found. Ensure you are at the root of your project.\"\n        )\n      }\n\n      if (!config) {\n        throw new Error(\n          \"Something went wrong reading your `components.json` file. Please ensure you have a valid `components.json` file.\"\n        )\n      }\n\n      if (options.migration === \"icons\") {\n        await migrateIcons(config)\n      }\n\n      if (options.migration === \"radix\") {\n        await migrateRadix(config, { yes: options.yes })\n      }\n    } catch (error) {\n      logger.break()\n      handleError(error)\n    }\n  })\n", "import path from \"path\"\nimport { buildOptionsSchema } from \"@/src/commands/build\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { getConfig } from \"@/src/utils/get-config\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport fs from \"fs-extra\"\nimport { z } from \"zod\"\n\nexport async function preFlightRegistryBuild(\n  options: z.infer<typeof buildOptionsSchema>\n) {\n  const errors: Record<string, boolean> = {}\n\n  const resolvePaths = {\n    cwd: options.cwd,\n    registryFile: path.resolve(options.cwd, options.registryFile),\n    outputDir: path.resolve(options.cwd, options.outputDir),\n  }\n\n  // Ensure registry file exists.\n  if (!fs.existsSync(resolvePaths.registryFile)) {\n    errors[ERRORS.BUILD_MISSING_REGISTRY_FILE] = true\n    return {\n      errors,\n      resolvePaths: null,\n      config: null,\n    }\n  }\n\n  // Check for existing components.json file.\n  if (!fs.existsSync(path.resolve(options.cwd, \"components.json\"))) {\n    errors[ERRORS.MISSING_CONFIG] = true\n    return {\n      errors,\n      resolvePaths: null,\n      config: null,\n    }\n  }\n\n  // Create output directory if it doesn't exist.\n  await fs.mkdir(resolvePaths.outputDir, { recursive: true })\n\n  try {\n    const config = await getConfig(options.cwd)\n\n    return {\n      errors,\n      config: config!,\n      resolvePaths,\n    }\n  } catch (error) {\n    logger.break()\n    logger.error(\n      `An invalid ${highlighter.info(\n        \"components.json\"\n      )} file was found at ${highlighter.info(\n        options.cwd\n      )}.\\nBefore you can build the registry, you must create a valid ${highlighter.info(\n        \"components.json\"\n      )} file by running the ${highlighter.info(\"init\")} command.`\n    )\n    logger.break()\n    process.exit(1)\n  }\n}\n", "import * as fs from \"fs/promises\"\nimport * as path from \"path\"\nimport { preFlightRegistryBuild } from \"@/src/preflights/preflight-registry\"\nimport { recursivelyResolveFileImports } from \"@/src/registry/utils\"\nimport { configSchema, registryItemSchema, registrySchema } from \"@/src/schema\"\nimport * as ERRORS from \"@/src/utils/errors\"\nimport { ProjectInfo, getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { Command } from \"commander\"\nimport { z } from \"zod\"\n\nexport const buildOptionsSchema = z.object({\n  cwd: z.string(),\n  registryFile: z.string(),\n  outputDir: z.string(),\n  verbose: z.boolean().optional().default(false),\n})\n\nexport const build = new Command()\n  .name(\"registry:build\")\n  .description(\"builds the registry [EXPERIMENTAL]\")\n  .argument(\"[registry]\", \"path to registry.json file\", \"./registry.json\")\n  .option(\n    \"-o, --output <path>\",\n    \"destination directory for json files\",\n    \"./public/r\"\n  )\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .option(\"-v, --verbose\", \"verbose output\")\n  .action(async (registry: string, opts) => {\n    await buildRegistry({\n      cwd: path.resolve(opts.cwd),\n      registryFile: registry,\n      outputDir: opts.output,\n      verbose: opts.verbose,\n    })\n  })\n\nasync function buildRegistry(opts: z.infer<typeof buildOptionsSchema>) {\n  try {\n    const options = buildOptionsSchema.parse(opts)\n\n    const [{ errors, resolvePaths, config }, projectInfo] = await Promise.all([\n      preFlightRegistryBuild(options),\n      getProjectInfo(options.cwd),\n    ])\n\n    if (errors[ERRORS.MISSING_CONFIG] || !config || !projectInfo) {\n      logger.error(\n        `A ${highlighter.info(\n          \"components.json\"\n        )} file is required to build the registry. Run ${highlighter.info(\n          \"shadcn init\"\n        )} to create one.`\n      )\n      logger.break()\n      process.exit(1)\n    }\n\n    if (errors[ERRORS.BUILD_MISSING_REGISTRY_FILE] || !resolvePaths) {\n      logger.error(\n        `We could not find a registry file at ${highlighter.info(\n          path.resolve(options.cwd, options.registryFile)\n        )}.`\n      )\n      logger.break()\n      process.exit(1)\n    }\n\n    const content = await fs.readFile(resolvePaths.registryFile, \"utf-8\")\n    const result = registrySchema.safeParse(JSON.parse(content))\n\n    if (!result.success) {\n      logger.error(\n        `Invalid registry file found at ${highlighter.info(\n          resolvePaths.registryFile\n        )}.`\n      )\n      logger.break()\n      process.exit(1)\n    }\n\n    const buildSpinner = spinner(\"Building registry...\")\n\n    // Recursively resolve the registry items.\n    const resolvedRegistry = await resolveRegistryItems(\n      result.data,\n      config,\n      projectInfo\n    )\n\n    // Loop through the registry items and remove duplicates files i.e same path.\n    for (const registryItem of resolvedRegistry.items) {\n      // Deduplicate files\n      registryItem.files = registryItem.files?.filter(\n        (file, index, self) =>\n          index === self.findIndex((t) => t.path === file.path)\n      )\n\n      // Deduplicate dependencies\n      if (registryItem.dependencies) {\n        registryItem.dependencies = registryItem.dependencies.filter(\n          (dep, index, self) => index === self.findIndex((d) => d === dep)\n        )\n      }\n    }\n\n    for (const registryItem of resolvedRegistry.items) {\n      if (!registryItem.files) {\n        continue\n      }\n\n      buildSpinner.start(`Building ${registryItem.name}...`)\n\n      // Add the schema to the registry item.\n      registryItem[\"$schema\"] =\n        \"https://ui.shadcn.com/schema/registry-item.json\"\n\n      for (const file of registryItem.files) {\n        const absPath = path.resolve(resolvePaths.cwd, file.path)\n        try {\n          const stat = await fs.stat(absPath)\n          if (!stat.isFile()) {\n            continue\n          }\n          file[\"content\"] = await fs.readFile(absPath, \"utf-8\")\n        } catch (err) {\n          console.error(\"Error reading file in registry build:\", absPath, err)\n          continue\n        }\n      }\n\n      const result = registryItemSchema.safeParse(registryItem)\n      if (!result.success) {\n        logger.error(\n          `Invalid registry item found for ${highlighter.info(\n            registryItem.name\n          )}.`\n        )\n        continue\n      }\n\n      // Write the registry item to the output directory.\n      await fs.writeFile(\n        path.resolve(resolvePaths.outputDir, `${result.data.name}.json`),\n        JSON.stringify(result.data, null, 2)\n      )\n    }\n\n    // Copy registry.json to the output directory.\n    await fs.copyFile(\n      resolvePaths.registryFile,\n      path.resolve(resolvePaths.outputDir, \"registry.json\")\n    )\n\n    buildSpinner.succeed(\"Building registry.\")\n\n    if (options.verbose) {\n      spinner(\n        `The registry has ${highlighter.info(\n          resolvedRegistry.items.length.toString()\n        )} items:`\n      ).succeed()\n      for (const item of resolvedRegistry.items) {\n        logger.log(`  - ${item.name} (${highlighter.info(item.type)})`)\n        for (const file of item.files ?? []) {\n          logger.log(`    - ${file.path}`)\n        }\n      }\n    }\n  } catch (error) {\n    logger.break()\n    handleError(error)\n  }\n}\n\n// This reads the registry and recursively resolves the file imports.\nasync function resolveRegistryItems(\n  registry: z.infer<typeof registrySchema>,\n  config: z.infer<typeof configSchema>,\n  projectInfo: ProjectInfo\n): Promise<z.infer<typeof registrySchema>> {\n  for (const item of registry.items) {\n    if (!item.files?.length) {\n      continue\n    }\n\n    // Process all files in the array instead of just the first one\n    for (const file of item.files) {\n      const results = await recursivelyResolveFileImports(\n        file.path,\n        config,\n        projectInfo\n      )\n\n      // Remove file from results.files\n      results.files = results.files?.filter((f) => f.path !== file.path)\n\n      if (results.files) {\n        item.files.push(...results.files)\n      }\n\n      if (results.dependencies) {\n        item.dependencies = item.dependencies\n          ? item.dependencies.concat(results.dependencies)\n          : results.dependencies\n      }\n    }\n  }\n\n  return registry\n}\n", "import { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { Command } from \"commander\"\n\nexport const mcp = new Command()\n  .name(\"registry:mcp\")\n  .description(\"starts the registry MCP server [DEPRECATED]\")\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .action(async () => {\n    logger.warn(\n      `The ${highlighter.info(\n        \"shadcn registry:mcp\"\n      )} command is deprecated. Use the ${highlighter.info(\n        \"shadcn mcp\"\n      )} command instead.`\n    )\n    logger.break()\n  })\n", "import path from \"path\"\nimport { configWithDefaults } from \"@/src/registry/config\"\nimport { clearRegistryContext } from \"@/src/registry/context\"\nimport { searchRegistries } from \"@/src/registry/search\"\nimport { validateRegistryConfigForItems } from \"@/src/registry/validator\"\nimport { rawConfigSchema } from \"@/src/schema\"\nimport { loadEnvFiles } from \"@/src/utils/env-loader\"\nimport { createConfig, getConfig } from \"@/src/utils/get-config\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { ensureRegistriesInConfig } from \"@/src/utils/registries\"\nimport { Command } from \"commander\"\nimport fsExtra from \"fs-extra\"\nimport { z } from \"zod\"\n\nconst searchOptionsSchema = z.object({\n  cwd: z.string(),\n  query: z.string().optional(),\n  limit: z.number().optional(),\n  offset: z.number().optional(),\n})\n\n// TODO: We're duplicating logic for shadowConfig here.\n// Revisit and properly abstract this.\n\nexport const search = new Command()\n  .name(\"search\")\n  .alias(\"list\")\n  .description(\"search items from registries\")\n  .argument(\n    \"<registries...>\",\n    \"the registry names or urls to search items from. Names must be prefixed with @.\"\n  )\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .option(\"-q, --query <query>\", \"query string\")\n  .option(\n    \"-l, --limit <number>\",\n    \"maximum number of items to display per registry\",\n    \"100\"\n  )\n  .option(\"-o, --offset <number>\", \"number of items to skip\", \"0\")\n  .action(async (registries: string[], opts) => {\n    try {\n      const options = searchOptionsSchema.parse({\n        cwd: path.resolve(opts.cwd),\n        query: opts.query,\n        limit: opts.limit ? parseInt(opts.limit, 10) : undefined,\n        offset: opts.offset ? parseInt(opts.offset, 10) : undefined,\n      })\n\n      await loadEnvFiles(options.cwd)\n\n      // Start with a shadow config to support partial components.json.\n      // Use createConfig to get proper default paths\n      const defaultConfig = createConfig({\n        style: \"new-york\",\n        resolvedPaths: {\n          cwd: options.cwd,\n        },\n      })\n      let shadowConfig = configWithDefaults(defaultConfig)\n\n      // Check if there's a components.json file (partial or complete).\n      const componentsJsonPath = path.resolve(options.cwd, \"components.json\")\n      if (fsExtra.existsSync(componentsJsonPath)) {\n        const existingConfig = await fsExtra.readJson(componentsJsonPath)\n        const partialConfig = rawConfigSchema.partial().parse(existingConfig)\n        shadowConfig = configWithDefaults({\n          ...defaultConfig,\n          ...partialConfig,\n        })\n      }\n\n      // Try to get the full config, but fall back to shadow config if it fails.\n      let config = shadowConfig\n      try {\n        const fullConfig = await getConfig(options.cwd)\n        if (fullConfig) {\n          config = configWithDefaults(fullConfig)\n        }\n      } catch {\n        // Use shadow config if getConfig fails (partial components.json).\n      }\n\n      const { config: updatedConfig, newRegistries } =\n        await ensureRegistriesInConfig(\n          registries.map((registry) => `${registry}/registry`),\n          config,\n          {\n            silent: true,\n            writeFile: false,\n          }\n        )\n      if (newRegistries.length > 0) {\n        config.registries = updatedConfig.registries\n      }\n\n      // Validate registries early for better error messages.\n      validateRegistryConfigForItems(registries, config)\n\n      // Use searchRegistries for both search and non-search cases\n      const results = await searchRegistries(registries as `@${string}`[], {\n        query: options.query,\n        limit: options.limit,\n        offset: options.offset,\n        config,\n      })\n\n      console.log(JSON.stringify(results, null, 2))\n      process.exit(0)\n    } catch (error) {\n      handleError(error)\n    } finally {\n      clearRegistryContext()\n    }\n  })\n", "import path from \"path\"\nimport { getRegistryItems } from \"@/src/registry/api\"\nimport { configWithDefaults } from \"@/src/registry/config\"\nimport { clearRegistryContext } from \"@/src/registry/context\"\nimport { validateRegistryConfigForItems } from \"@/src/registry/validator\"\nimport { rawConfigSchema } from \"@/src/schema\"\nimport { loadEnvFiles } from \"@/src/utils/env-loader\"\nimport { getConfig } from \"@/src/utils/get-config\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { ensureRegistriesInConfig } from \"@/src/utils/registries\"\nimport { Command } from \"commander\"\nimport fsExtra from \"fs-extra\"\nimport { z } from \"zod\"\n\nconst viewOptionsSchema = z.object({\n  cwd: z.string(),\n})\n\nexport const view = new Command()\n  .name(\"view\")\n  .description(\"view items from the registry\")\n  .argument(\"<items...>\", \"the item names or URLs to view\")\n  .option(\n    \"-c, --cwd <cwd>\",\n    \"the working directory. defaults to the current directory.\",\n    process.cwd()\n  )\n  .action(async (items: string[], opts) => {\n    try {\n      const options = viewOptionsSchema.parse({\n        cwd: path.resolve(opts.cwd),\n      })\n\n      await loadEnvFiles(options.cwd)\n\n      // Start with a shadow config to support partial components.json.\n      let shadowConfig = configWithDefaults({})\n\n      // Check if there's a components.json file (partial or complete).\n      const componentsJsonPath = path.resolve(options.cwd, \"components.json\")\n      if (fsExtra.existsSync(componentsJsonPath)) {\n        const existingConfig = await fsExtra.readJson(componentsJsonPath)\n        const partialConfig = rawConfigSchema.partial().parse(existingConfig)\n        shadowConfig = configWithDefaults(partialConfig)\n      }\n\n      // Try to get the full config, but fall back to shadow config if it fails.\n      let config = shadowConfig\n      try {\n        const fullConfig = await getConfig(options.cwd)\n        if (fullConfig) {\n          config = configWithDefaults(fullConfig)\n        }\n      } catch {\n        // Use shadow config if getConfig fails (partial components.json).\n      }\n\n      const { config: updatedConfig, newRegistries } =\n        await ensureRegistriesInConfig(items, config, {\n          silent: true,\n          writeFile: false,\n        })\n      if (newRegistries.length > 0) {\n        config.registries = updatedConfig.registries\n      }\n\n      // Validate registries early for better error messages.\n      validateRegistryConfigForItems(items, config)\n\n      const payload = await getRegistryItems(items, { config })\n      console.log(JSON.stringify(payload, null, 2))\n      process.exit(0)\n    } catch (error) {\n      handleError(error)\n    } finally {\n      clearRegistryContext()\n    }\n  })\n", "{\n  \"name\": \"shadcn\",\n  \"version\": \"3.5.0\",\n  \"description\": \"Add components to your apps.\",\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"license\": \"MIT\",\n  \"author\": {\n    \"name\": \"shadcn\",\n    \"url\": \"https://twitter.com/shadcn\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/shadcn/ui.git\",\n    \"directory\": \"packages/shadcn\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"keywords\": [\n    \"components\",\n    \"ui\",\n    \"tailwind\",\n    \"radix-ui\",\n    \"shadcn\"\n  ],\n  \"type\": \"module\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/index.d.ts\",\n      \"default\": \"./dist/index.js\"\n    },\n    \"./registry\": {\n      \"types\": \"./dist/registry/index.d.ts\",\n      \"default\": \"./dist/registry/index.js\"\n    },\n    \"./schema\": {\n      \"types\": \"./dist/schema/index.d.ts\",\n      \"default\": \"./dist/schema/index.js\"\n    },\n    \"./mcp\": {\n      \"types\": \"./dist/mcp/index.d.ts\",\n      \"default\": \"./dist/mcp/index.js\"\n    }\n  },\n  \"bin\": \"./dist/index.js\",\n  \"scripts\": {\n    \"dev\": \"tsup --watch\",\n    \"build\": \"tsup\",\n    \"typecheck\": \"tsc --noEmit\",\n    \"clean\": \"rimraf dist && rimraf components\",\n    \"start:dev\": \"cross-env REGISTRY_URL=http://localhost:4000/r node dist/index.js\",\n    \"start:prod\": \"cross-env REGISTRY_URL=https://ui.shadcn.com/r node dist/index.js\",\n    \"start\": \"node dist/index.js\",\n    \"format:write\": \"prettier --write \\\"**/*.{ts,tsx,mdx}\\\" --cache\",\n    \"format:check\": \"prettier --check \\\"**/*.{ts,tsx,mdx}\\\" --cache\",\n    \"release\": \"changeset version\",\n    \"pub:beta\": \"pnpm build && pnpm publish --no-git-checks --access public --tag beta\",\n    \"pub:next\": \"pnpm build && pnpm publish --no-git-checks --access public --tag next\",\n    \"pub:release\": \"pnpm build && pnpm publish --access public\",\n    \"test\": \"vitest run\",\n    \"test:dev\": \"REGISTRY_URL=http://localhost:4000/r vitest run\",\n    \"mcp:inspect\": \"pnpm dlx @modelcontextprotocol/inspector node dist/index.js mcp\"\n  },\n  \"dependencies\": {\n    \"@antfu/ni\": \"^25.0.0\",\n    \"@babel/core\": \"^7.28.0\",\n    \"@babel/parser\": \"^7.28.0\",\n    \"@babel/plugin-transform-typescript\": \"^7.28.0\",\n    \"@babel/preset-typescript\": \"^7.27.1\",\n    \"@dotenvx/dotenvx\": \"^1.48.4\",\n    \"@modelcontextprotocol/sdk\": \"^1.17.2\",\n    \"browserslist\": \"^4.26.2\",\n    \"commander\": \"^14.0.0\",\n    \"cosmiconfig\": \"^9.0.0\",\n    \"dedent\": \"^1.6.0\",\n    \"deepmerge\": \"^4.3.1\",\n    \"diff\": \"^8.0.2\",\n    \"execa\": \"^9.6.0\",\n    \"fast-glob\": \"^3.3.3\",\n    \"fs-extra\": \"^11.3.1\",\n    \"fuzzysort\": \"^3.1.0\",\n    \"https-proxy-agent\": \"^7.0.6\",\n    \"kleur\": \"^4.1.5\",\n    \"msw\": \"^2.10.4\",\n    \"node-fetch\": \"^3.3.2\",\n    \"ora\": \"^8.2.0\",\n    \"postcss\": \"^8.5.6\",\n    \"prompts\": \"^2.4.2\",\n    \"recast\": \"^0.23.11\",\n    \"stringify-object\": \"^5.0.0\",\n    \"ts-morph\": \"^26.0.0\",\n    \"tsconfig-paths\": \"^4.2.0\",\n    \"zod\": \"^3.24.1\",\n    \"zod-to-json-schema\": \"^3.24.6\"\n  },\n  \"devDependencies\": {\n    \"@types/babel__core\": \"^7.20.5\",\n    \"@types/fs-extra\": \"^11.0.4\",\n    \"@types/prompts\": \"^2.4.9\",\n    \"@types/stringify-object\": \"^4.0.5\",\n    \"rimraf\": \"^6.0.1\",\n    \"tsup\": \"^8.5.0\",\n    \"type-fest\": \"^4.41.0\",\n    \"typescript\": \"^5.9.2\"\n  }\n}\n", "#!/usr/bin/env node\nimport { add } from \"@/src/commands/add\"\nimport { build } from \"@/src/commands/build\"\nimport { diff } from \"@/src/commands/diff\"\nimport { info } from \"@/src/commands/info\"\nimport { init } from \"@/src/commands/init\"\nimport { mcp } from \"@/src/commands/mcp\"\nimport { migrate } from \"@/src/commands/migrate\"\nimport { build as registryBuild } from \"@/src/commands/registry/build\"\nimport { mcp as registryMcp } from \"@/src/commands/registry/mcp\"\nimport { search } from \"@/src/commands/search\"\nimport { view } from \"@/src/commands/view\"\nimport { Command } from \"commander\"\n\nimport packageJson from \"../package.json\"\n\nprocess.on(\"SIGINT\", () => process.exit(0))\nprocess.on(\"SIGTERM\", () => process.exit(0))\n\nasync function main() {\n  const program = new Command()\n    .name(\"shadcn\")\n    .description(\"add items from registries to your project\")\n    .version(\n      packageJson.version || \"1.0.0\",\n      \"-v, --version\",\n      \"display the version number\"\n    )\n\n  program\n    .addCommand(init)\n    .addCommand(add)\n    .addCommand(diff)\n    .addCommand(view)\n    .addCommand(search)\n    .addCommand(migrate)\n    .addCommand(info)\n    .addCommand(build)\n    .addCommand(mcp)\n  // Registry commands\n  program.addCommand(registryBuild).addCommand(registryMcp)\n\n  program.parse()\n}\n\nmain()\n\nexport * from \"./registry/api\"\n"]}