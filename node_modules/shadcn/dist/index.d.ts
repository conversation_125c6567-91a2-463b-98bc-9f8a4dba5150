#!/usr/bin/env node
export { k as fetchTree, l as getItemTargetPath, c as getRegistriesConfig, b as getRegistriesIndex, a as getRegistry, i as getRegistryBaseColor, h as getRegistryBaseColors, f as getRegistryIcons, g as getRegistryItems, e as getRegistryStyles, d as getShadcnRegistryIndex, r as resolveRegistryItems, j as resolveTree } from './index-jrlOVvd4.js';
import './schema/index.js';
import 'zod';
