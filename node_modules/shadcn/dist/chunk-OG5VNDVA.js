import {z}from'zod';var i=z.enum(["registry:lib","registry:block","registry:component","registry:ui","registry:hook","registry:page","registry:file","registry:theme","registry:style","registry:item","registry:example","registry:internal"]),o=z.discriminatedUnion("type",[z.object({path:z.string(),content:z.string().optional(),type:z.enum(["registry:file","registry:page"]),target:z.string()}),z.object({path:z.string(),content:z.string().optional(),type:i.exclude(["registry:file","registry:page"]),target:z.string().optional()})]),a=z.object({config:z.object({content:z.array(z.string()).optional(),theme:z.record(z.string(),z.any()).optional(),plugins:z.array(z.string()).optional()}).optional()}),r=z.object({theme:z.record(z.string(),z.string()).optional(),light:z.record(z.string(),z.string()).optional(),dark:z.record(z.string(),z.string()).optional()}),n=z.lazy(()=>z.union([z.string(),z.record(z.string(),n)])),g=z.record(z.string(),n),c=z.record(z.string(),z.string()),s=z.object({$schema:z.string().optional(),extends:z.string().optional(),name:z.string(),type:i,title:z.string().optional(),author:z.string().min(2).optional(),description:z.string().optional(),dependencies:z.array(z.string()).optional(),devDependencies:z.array(z.string()).optional(),registryDependencies:z.array(z.string()).optional(),files:z.array(o).optional(),tailwind:a.optional(),cssVars:r.optional(),css:g.optional(),envVars:c.optional(),meta:z.record(z.string(),z.any()).optional(),docs:z.string().optional(),categories:z.array(z.string()).optional()}),u=z.object({name:z.string(),homepage:z.string(),items:z.array(s)}),b=z.array(s),x=z.array(z.object({name:z.string(),label:z.string()})),f=z.record(z.string(),z.record(z.string(),z.string())),S=z.object({inlineColors:z.object({light:z.record(z.string(),z.string()),dark:z.record(z.string(),z.string())}),cssVars:r,cssVarsV4:r.optional(),inlineColorsTemplate:z.string(),cssVarsTemplate:z.string()}),j=s.pick({dependencies:true,devDependencies:true,files:true,tailwind:true,cssVars:true,css:true,envVars:true,docs:true}),l=z.union([z.string().refine(e=>e.includes("{name}"),{message:"Registry URL must include {name} placeholder"}),z.object({url:z.string().refine(e=>e.includes("{name}"),{message:"Registry URL must include {name} placeholder"}),params:z.record(z.string(),z.string()).optional(),headers:z.record(z.string(),z.string()).optional()})]),p=z.record(z.string().refine(e=>e.startsWith("@"),{message:"Registry names must start with @ (e.g., @v0, @acme)"}),l),m=z.object({$schema:z.string().optional(),style:z.string(),rsc:z.coerce.boolean().default(false),tsx:z.coerce.boolean().default(true),tailwind:z.object({config:z.string().optional(),css:z.string(),baseColor:z.string(),cssVariables:z.boolean().default(true),prefix:z.string().default("").optional()}),iconLibrary:z.string().optional(),aliases:z.object({components:z.string(),utils:z.string(),ui:z.string().optional(),lib:z.string().optional(),hooks:z.string().optional()}),registries:p.optional()}).strict(),y=m.extend({resolvedPaths:z.object({cwd:z.string(),tailwindConfig:z.string(),tailwindCss:z.string(),utils:z.string(),components:z.string(),lib:z.string(),hooks:z.string(),ui:z.string()})}),I=z.record(y),d=z.object({name:z.string(),type:z.string().optional(),description:z.string().optional(),registry:z.string(),addCommandArgument:z.string()}),C=z.object({pagination:z.object({total:z.number(),offset:z.number(),limit:z.number(),hasMore:z.boolean()}),items:z.array(d)}),V=z.record(z.string().regex(/^@[a-zA-Z0-9][a-zA-Z0-9-_]*$/),z.string());export{i as a,o as b,a as c,r as d,g as e,c as f,s as g,u as h,b as i,x as j,f as k,S as l,j as m,l as n,p as o,m as p,y as q,I as r,d as s,C as t,V as u};//# sourceMappingURL=chunk-OG5VNDVA.js.map
//# sourceMappingURL=chunk-OG5VNDVA.js.map