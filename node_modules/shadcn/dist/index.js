#!/usr/bin/env node
import {b as b$1,a as a$1}from'./chunk-EN57B455.js';import {a,O,R,_ as _$1,s,d,Y,P,p as p$1,i,q as q$1,j,t,b,ca,ea,g,h,e,f,r,n,N,c,ba,ha,fa,ia,K,M,Q,ka,ja,S,k,da,J,o,F,W as W$1,z as z$2,X as X$1,m,T,l,L as L$1,U,G,I,H,V}from'./chunk-OYVM23Y7.js';export{ha as fetchTree,ia as getItemTargetPath,aa as getRegistriesConfig,ja as getRegistriesIndex,Z as getRegistry,fa as getRegistryBaseColor,ea as getRegistryBaseColors,da as getRegistryIcons,_ as getRegistryItems,ca as getRegistryStyles,ba as getShadcnRegistryIndex,$ as resolveRegistryItems,ga as resolveTree}from'./chunk-OYVM23Y7.js';import {p,h as h$1,g as g$1}from'./chunk-OG5VNDVA.js';import*as W from'path';import W__default,{join}from'path';import {promises,existsSync}from'fs';import A from'fs-extra';import R$1 from'postcss';import Vr from'postcss/lib/at-rule';import tt,{z as z$1}from'zod';import {execa}from'execa';import He from'prompts';import fi,{tmpdir}from'os';import {SyntaxKind,Project,ScriptKind}from'ts-morph';import {Command}from'commander';import Ci from'deepmerge';import*as z from'fs/promises';import z__default from'fs/promises';import {diffLines}from'diff';import {StdioServerTransport}from'@modelcontextprotocol/sdk/server/stdio.js';import {randomBytes}from'crypto';import Hi from'fast-glob';async function Dt(t$1){let e={};if(!A.existsSync(t$1.cwd)||!A.existsSync(W__default.resolve(t$1.cwd,"package.json")))return e["1"]=true,{errors:e,projectInfo:null};let r=t("Preflight checks.",{silent:t$1.silent}).start();A.existsSync(W__default.resolve(t$1.cwd,"components.json"))&&!t$1.force&&(r?.fail(),s.break(),s.error(`A ${d.info("components.json")} file already exists at ${d.info(t$1.cwd)}.
To start over, remove the ${d.info("components.json")} file and run ${d.info("init")} again.`),s.break(),process.exit(1)),r?.succeed();let i=t("Verifying framework.",{silent:t$1.silent}).start(),o=await p$1(t$1.cwd);(!o||o?.framework.name==="manual")&&(e["7"]=true,i?.fail(),s.break(),o?.framework.links.installation&&s.error(`We could not detect a supported framework at ${d.info(t$1.cwd)}.
Visit ${d.info(o?.framework.links.installation)} to manually configure your project.
Once configured, you can use the cli to add components.`),s.break(),process.exit(1)),i?.succeed(`Verifying framework. Found ${d.info(o.framework.label)}.`);let n="Validating Tailwind CSS.";o.tailwindVersion==="v4"&&(n=`Validating Tailwind CSS config. Found ${d.info("v4")}.`);let a=t(n,{silent:t$1.silent}).start();o.tailwindVersion==="v3"&&(!o?.tailwindConfigFile||!o?.tailwindCssFile)?(e["5"]=true,a?.fail()):o.tailwindVersion==="v4"&&!o?.tailwindCssFile?(e["5"]=true,a?.fail()):o.tailwindVersion?a?.succeed():(e["5"]=true,a?.fail());let s$1=t("Validating import alias.",{silent:t$1.silent}).start();return o?.aliasPrefix?s$1?.succeed():(e["6"]=true,s$1?.fail()),Object.keys(e).length>0&&(e["5"]&&(s.break(),s.error(`No Tailwind CSS configuration found at ${d.info(t$1.cwd)}.`),s.error("It is likely you do not have Tailwind CSS installed or have an invalid configuration."),s.error("Install Tailwind CSS then try again."),o?.framework.links.tailwind&&s.error(`Visit ${d.info(o?.framework.links.tailwind)} to get started.`)),e["6"]&&(s.break(),s.error("No import alias found in your tsconfig.json file."),o?.framework.links.installation&&s.error(`Visit ${d.info(o?.framework.links.installation)} to learn how to set an import alias.`)),s.break(),process.exit(1)),{errors:e,projectInfo:o}}function Lt(t,e){if(t.includes("\0"))return  false;let r;try{r=t;let u="";for(;r!==u&&r.includes("%");)u=r,r=decodeURIComponent(r);}catch{return  false}let i=W__default.normalize(r.replace(/\\/g,"/")),o=W__default.normalize(e),n=u=>u.replace(/\[\.\.\..*?\]/g,"").includes("..");if(n(i)||n(r)||n(t))return  false;let a=u=>u.replace(/\[\.\.\..*?\]/g,""),s=a(t),c=a(r);if([/\.\.[\/\\]/,/[\/\\]\.\./,/\.\./,/\.\.%/,/\x00/,/[\x01-\x1f]/].some(u=>u.test(s)||u.test(c))||(t.includes("~")||r.includes("~"))&&(t.includes("../")||r.includes("../")))return  false;if(/^[a-zA-Z]:[\/\\]/.test(r))return process.platform==="win32"?r.toLowerCase().startsWith(e.toLowerCase()):false;if(W__default.isAbsolute(i))return i.startsWith(o+W__default.sep);let p=W__default.resolve(o,i);return p.startsWith(o+W__default.sep)||p===o}async function Ue(t$1,e,r){if(!e.resolvedPaths.tailwindCss||!t$1||Object.keys(t$1).length===0)return;r={silent:false,...r};let i=e.resolvedPaths.tailwindCss,o=W__default.relative(e.resolvedPaths.cwd,i),n=t(`Updating ${d.info(o)}`,{silent:r.silent}).start(),a=await promises.readFile(i,"utf8"),s=await Ar(a,t$1);await promises.writeFile(i,s,"utf8"),n.succeed();}async function Ar(t,e){let r=[Dr(e)],i=await R$1(r).process(t,{from:void 0}),o=i.css,n=i.root;if(n.nodes&&n.nodes.length>0){let a=n.nodes[n.nodes.length-1];a.type==="atrule"&&!a.nodes&&!o.trimEnd().endsWith(";")&&(o=o.trimEnd()+";");}return o=o.replace(/\/\* ---break--- \*\//g,""),o=o.replace(/(\n\s*\n)+/g,`

`),o=o.trimEnd(),o}function Dr(t){return {postcssPlugin:"update-css",Once(e){for(let[r,i]of Object.entries(t))if(r.startsWith("@")){let o=r.match(/@([a-zA-Z-]+)\s*(.*)/);if(!o)continue;let[,n,a]=o;if(n==="import"){if(!e.nodes?.find(c=>c.type==="atrule"&&c.name==="import"&&c.params===a)){let c=R$1.atRule({name:"import",params:a,raws:{semicolon:true}}),l=e.nodes?.filter(m=>m.type==="atrule"&&m.name==="import");if(l&&l.length>0){let m=l[l.length-1];c.raws.before=`
`,e.insertAfter(m,c);}else !e.nodes||e.nodes.length,c.raws.before="",e.prepend(c);}}else if(n==="plugin"){let s=a;a&&!a.startsWith('"')&&!a.startsWith("'")&&(s=`"${a}"`);let c=m=>m.startsWith('"')&&m.endsWith('"')||m.startsWith("'")&&m.endsWith("'")?m.slice(1,-1):m;if(!e.nodes?.find(m=>m.type!=="atrule"||m.name!=="plugin"?false:c(m.params)===c(a))){let m=R$1.atRule({name:"plugin",params:s,raws:{semicolon:true,before:`
`}}),p=e.nodes?.filter(g=>g.type==="atrule"&&g.name==="import"),u=e.nodes?.filter(g=>g.type==="atrule"&&g.name==="plugin");if(u&&u.length>0){let g=u[u.length-1];e.insertAfter(g,m);}else if(p&&p.length>0){let g=p[p.length-1];e.insertAfter(g,m),e.insertBefore(m,R$1.comment({text:"---break---"})),e.insertAfter(m,R$1.comment({text:"---break---"}));}else e.prepend(m),e.insertBefore(m,R$1.comment({text:"---break---"})),e.insertAfter(m,R$1.comment({text:"---break---"}));}}else if(typeof i=="object"&&Object.keys(i).length===0){if(!e.nodes?.find(c=>c.type==="atrule"&&c.name===n&&c.params===a)){let c=R$1.atRule({name:n,params:a,raws:{semicolon:true}});e.append(c),e.insertBefore(c,R$1.comment({text:"---break---"}));}}else if(n==="keyframes"){let s=e.nodes?.find(l=>l.type==="atrule"&&l.name==="theme"&&l.params==="inline");s||(s=R$1.atRule({name:"theme",params:"inline",raws:{semicolon:true,between:" ",before:`
`}}),e.append(s),e.insertBefore(s,R$1.comment({text:"---break---"})));let c=R$1.atRule({name:"keyframes",params:a,raws:{semicolon:true,between:" ",before:`
  `}});if(s.append(c),typeof i=="object")for(let[l,m]of Object.entries(i))X(c,l,m);}else if(n==="utility"){let s=e.nodes?.find(c=>c.type==="atrule"&&c.name===n&&c.params===a);if(s){if(typeof i=="object")for(let[c,l]of Object.entries(i))if(typeof l=="string"){let m=s.nodes?.find(u=>u.type==="decl"&&u.prop===c),p=R$1.decl({prop:c,value:l,raws:{semicolon:true,before:`
    `}});m?m.replaceWith(p):s.append(p);}else typeof l=="object"&&X(s,c,l);}else {let c=R$1.atRule({name:n,params:a,raws:{semicolon:true,between:" ",before:`
`}});if(e.append(c),e.insertBefore(c,R$1.comment({text:"---break---"})),typeof i=="object")for(let[l,m]of Object.entries(i))if(typeof m=="string"){let p=R$1.decl({prop:l,value:m,raws:{semicolon:true,before:`
    `}});c.append(p);}else typeof m=="object"&&X(c,l,m);}}else n==="property"?X(e,r,i):Mt(e,n,a,i);}else X(e,r,i);}}}function Mt(t,e,r,i){let o=t.nodes?.find(n=>n.type==="atrule"&&n.name===e&&n.params===r);if(o||(o=R$1.atRule({name:e,params:r,raws:{semicolon:true,between:" ",before:`
`}}),t.append(o),t.insertBefore(o,R$1.comment({text:"---break---"}))),typeof i=="object")for(let[n,a]of Object.entries(i))if(n.startsWith("@")){let s=n.match(/@([a-zA-Z-]+)\s*(.*)/);if(s){let[,c,l]=s;Mt(o,c,l,a);}}else X(o,n,a);else if(typeof i=="string")try{let a=R$1.parse(`.temp{${i}}`).first;if(a&&a.nodes){let s=R$1.rule({selector:"temp",raws:{semicolon:!0,between:" ",before:`
  `}});a.nodes.forEach(c=>{if(c.type==="decl"){let l=c.clone();l.raws.before=`
    `,s.append(l);}}),s.nodes?.length&&o.append(s);}}catch(n){throw console.error("Error parsing at-rule content:",i,n),n}}function X(t,e,r){let i=t.nodes?.find(o=>o.type==="rule"&&o.selector===e);if(i||(i=R$1.rule({selector:e,raws:{semicolon:true,between:" ",before:`
  `}}),t.append(i)),typeof r=="object"){for(let[o,n]of Object.entries(r))if(o.startsWith("@")&&typeof n=="object"&&n!==null&&Object.keys(n).length===0){let a=o.match(/@([a-zA-Z-]+)\s*(.*)/);if(a){let[,s,c]=a,l=R$1.atRule({name:s,params:c,raws:{semicolon:true,before:`
    `}});i.append(l);}}else if(typeof n=="string"){let a=R$1.decl({prop:o,value:n,raws:{semicolon:true,before:`
    `}}),s=i.nodes?.find(c=>c.type==="decl"&&c.prop===o);s?s.replaceWith(a):i.append(a);}else if(typeof n=="object"){let a=o.startsWith("&")?e.replace(/^([^:]+)/,`$1${o.substring(1)}`):o;X(t,a,n);}}else if(typeof r=="string")try{let n=R$1.parse(`.temp{${r}}`).first;n&&n.nodes&&n.nodes.forEach(a=>{if(a.type==="decl"){let s=a.clone();s.raws.before=`
    `,i?.append(s);}});}catch(o){throw console.error("Error parsing rule content:",e,r,o),o}}async function Ge(t$1,e,r){if(!e.resolvedPaths.tailwindCss||!Object.keys(t$1??{}).length)return;r={cleanupDefaultNextStyles:false,silent:false,tailwindVersion:"v3",overwriteCssVars:false,initIndex:true,...r};let i=e.resolvedPaths.tailwindCss,o=W__default.relative(e.resolvedPaths.cwd,i),n=t(`Updating CSS variables in ${d.info(o)}`,{silent:r.silent}).start(),a=await promises.readFile(i,"utf8"),s=await Mr(a,t$1??{},e,{cleanupDefaultNextStyles:r.cleanupDefaultNextStyles,tailwindVersion:r.tailwindVersion,tailwindConfig:r.tailwindConfig,overwriteCssVars:r.overwriteCssVars,initIndex:r.initIndex});await promises.writeFile(i,s,"utf8"),n.succeed();}async function Mr(t,e,r,i={cleanupDefaultNextStyles:false,tailwindVersion:"v3",tailwindConfig:void 0,overwriteCssVars:false,initIndex:true}){i={cleanupDefaultNextStyles:false,tailwindVersion:"v3",tailwindConfig:void 0,overwriteCssVars:false,initIndex:true,...i};let o$1=[zr(e)];if(i.cleanupDefaultNextStyles&&o$1.push(Bt()),i.tailwindVersion==="v4"){if(o$1=[],r.resolvedPaths?.cwd){let s=o(r.resolvedPaths.cwd);!s?.dependencies?.["tailwindcss-animate"]&&!s?.devDependencies?.["tailwindcss-animate"]&&i.initIndex&&o$1.push(Yr({params:"tw-animate-css"}));}o$1.push(Jr({params:"dark (&:is(.dark *))"})),i.cleanupDefaultNextStyles&&o$1.push(Bt()),o$1.push(Ur(e,{overwriteCssVars:i.overwriteCssVars})),o$1.push(Gr(e)),i.tailwindConfig&&(o$1.push(qr(i.tailwindConfig)),o$1.push(Hr(i.tailwindConfig)),o$1.push(Kr(i.tailwindConfig)));}r.tailwind.cssVariables&&i.initIndex&&o$1.push(Wr({tailwindVersion:i.tailwindVersion}));let a=(await R$1(o$1).process(t,{from:void 0})).css;return a=a.replace(/\/\* ---break--- \*\//g,""),i.tailwindVersion==="v4"&&(a=a.replace(/(\n\s*\n)+/g,`

`)),a}function Wr({tailwindVersion:t}){return {postcssPlugin:"update-base-layer",Once(e){let r=[{selector:"*",apply:t==="v4"?"border-border outline-ring/50":"border-border"},{selector:"body",apply:"bg-background text-foreground"}],i=e.nodes.find(o=>o.type==="atrule"&&o.name==="layer"&&o.params==="base"&&r.every(({selector:n,apply:a})=>o.nodes?.some(s=>s.type==="rule"&&s.selector===n&&s.nodes.some(c=>c.type==="atrule"&&c.name==="apply"&&c.params===a))));i||(i=R$1.atRule({name:"layer",params:"base",raws:{semicolon:true,between:" ",before:`
`}}),e.append(i),e.insertBefore(i,R$1.comment({text:"---break---"}))),r.forEach(({selector:o,apply:n})=>{i?.nodes?.find(s=>s.type==="rule"&&s.selector===o)||i?.append(R$1.rule({selector:o,nodes:[R$1.atRule({name:"apply",params:n,raws:{semicolon:true,before:`
    `}})],raws:{semicolon:true,between:" ",before:`
  `}}));});}}}function zr(t){return {postcssPlugin:"update-css-vars",Once(e){let r=e.nodes.find(i=>i.type==="atrule"&&i.name==="layer"&&i.params==="base");r instanceof Vr||(r=R$1.atRule({name:"layer",params:"base",nodes:[],raws:{semicolon:true,before:`
`,between:" "}}),e.append(r),e.insertBefore(r,R$1.comment({text:"---break---"}))),r!==void 0&&Object.entries(t).forEach(([i,o])=>{let n=i==="light"?":root":`.${i}`;Br(r,n,o);});}}}function zt(t){let e=t.nodes.find(r=>r.type==="rule"&&r.selector===":root");if(e){let r=["--background","--foreground"];e.nodes.filter(i=>i.type==="decl"&&r.includes(i.prop)).forEach(i=>i.remove()),e.nodes.length===0&&e.remove();}}function Bt(){return {postcssPlugin:"cleanup-default-next-styles",Once(t){let e=t.nodes.find(i=>i.type==="rule"&&i.selector==="body");e&&(e.nodes.find(i=>i.type==="decl"&&i.prop==="color"&&["rgb(var(--foreground-rgb))","var(--foreground)"].includes(i.value))?.remove(),e.nodes.find(i=>i.type==="decl"&&i.prop==="background"&&(i.value.startsWith("linear-gradient")||i.value==="var(--background)"))?.remove(),e.nodes.find(i=>i.type==="decl"&&i.prop==="font-family"&&i.value==="Arial, Helvetica, sans-serif")?.remove(),e.nodes.length===0&&e.remove()),zt(t);let r=t.nodes.find(i=>i.type==="atrule"&&i.params==="(prefers-color-scheme: dark)");r&&(zt(r),r.nodes.length===0&&r.remove());}}}function Br(t,e,r){let i=t.nodes?.find(o=>o.type==="rule"&&o.selector===e);i||Object.keys(r).length>0&&(i=R$1.rule({selector:e,raws:{between:" ",before:`
  `}}),t.append(i)),Object.entries(r).forEach(([o,n])=>{let a=`--${o.replace(/^--/,"")}`,s=R$1.decl({prop:a,value:n,raws:{semicolon:true}}),c=i?.nodes.find(l=>l.type==="decl"&&l.prop===a);c?c.replaceWith(s):i?.append(s);});}function Ur(t,e){return {postcssPlugin:"update-css-vars-v4",Once(r){Object.entries(t).forEach(([i,o])=>{let n=i==="light"?":root":`.${i}`;if(i==="theme"){n="@theme";let s=Ce(r);Object.entries(o).forEach(([c,l])=>{let m=`--${c.replace(/^--/,"")}`,p=R$1.decl({prop:m,value:l,raws:{semicolon:true}}),u=s?.nodes?.find(g=>g.type==="decl"&&g.prop===m);e.overwriteCssVars?u?u.replaceWith(p):s?.append(p):u||s?.append(p);});return}let a=r.nodes?.find(s=>s.type==="rule"&&s.selector===n);!a&&Object.keys(o).length>0&&(a=R$1.rule({selector:n,nodes:[],raws:{semicolon:true,between:" ",before:`
`}}),r.append(a),r.insertBefore(a,R$1.comment({text:"---break---"}))),Object.entries(o).forEach(([s,c])=>{let l=`--${s.replace(/^--/,"")}`;l==="--sidebar-background"&&(l="--sidebar"),Ut(c)&&(c=`hsl(${c})`);let m=R$1.decl({prop:l,value:c,raws:{semicolon:true}}),p=a?.nodes.find(u=>u.type==="decl"&&u.prop===l);e.overwriteCssVars?p?p.replaceWith(m):a?.append(m):p||a?.append(m);});});}}}function Gr(t){return {postcssPlugin:"update-theme",Once(e){let r=Array.from(new Set(Object.keys(t).flatMap(n=>Object.keys(t[n]||{}))));if(!r.length)return;let i=Ce(e),o=i.nodes?.filter(n=>n.type==="decl"&&n.prop.startsWith("--"));for(let n of r){let a=Object.values(t).find(p=>p[n])?.[n];if(!a)continue;if(n==="radius"){let p={sm:"calc(var(--radius) - 4px)",md:"calc(var(--radius) - 2px)",lg:"var(--radius)",xl:"calc(var(--radius) + 4px)"};for(let[u,g]of Object.entries(p)){let w=R$1.decl({prop:`--radius-${u}`,value:g,raws:{semicolon:true}});i?.nodes?.find(y=>y.type==="decl"&&y.prop===w.prop)||i?.append(w);}continue}let s=Ut(a)||Xr(a)?`--color-${n.replace(/^--/,"")}`:`--${n.replace(/^--/,"")}`;s==="--color-sidebar-background"&&(s="--color-sidebar");let c=`var(--${n})`;s==="--color-sidebar"&&(c="var(--sidebar)");let l=R$1.decl({prop:s,value:c,raws:{semicolon:true}});i?.nodes?.find(p=>p.type==="decl"&&p.prop===l.prop)||(o?.length?i?.insertAfter(o[o.length-1],l):i?.append(l));}}}}function Ce(t){let e=t.nodes.find(r=>r.type==="atrule"&&r.name==="theme"&&r.params==="inline");return e||(e=R$1.atRule({name:"theme",params:"inline",nodes:[],raws:{semicolon:true,between:" ",before:`
`}}),t.append(e),t.insertBefore(e,R$1.comment({text:"---break---"}))),e}function Jr({params:t}){return {postcssPlugin:"add-custom-variant",Once(e){if(!e.nodes.find(i=>i.type==="atrule"&&i.name==="custom-variant")){let i=e.nodes.filter(n=>n.type==="atrule"&&n.name==="import"),o=R$1.atRule({name:"custom-variant",params:t,raws:{semicolon:true,before:`
`}});if(i.length>0){let n=i[i.length-1];e.insertAfter(n,o);}else e.insertAfter(e.nodes[0],o);e.insertBefore(o,R$1.comment({text:"---break---"}));}}}}function Yr({params:t}){return {postcssPlugin:"add-custom-import",Once(e){let r=e.nodes.filter(n=>n.type==="atrule"&&n.name==="import"),i=e.nodes.find(n=>n.type==="atrule"&&n.name==="custom-variant");if(!r.some(n=>n.params.replace(/["']/g,"")===t)){let n=R$1.atRule({name:"import",params:`"${t}"`,raws:{semicolon:true,before:`
`}});if(r.length>0){let a=r[r.length-1];e.insertAfter(a,n);}else i?(e.insertBefore(i,n),e.insertBefore(i,R$1.comment({text:"---break---"}))):(e.prepend(n),e.insertAfter(n,R$1.comment({text:"---break---"})));}}}}function qr(t){return {postcssPlugin:"update-tailwind-config",Once(e){if(!t?.plugins)return;let i=Qr(e)==="single"?"'":'"',o=e.nodes.filter(a=>a.type==="atrule"&&a.name==="plugin"),n=o[o.length-1]||e.nodes[0];for(let a of t.plugins){let s=a.replace(/^require\(["']|["']\)$/g,"");if(o.some(l=>l.params.replace(/["']/g,"")===s))continue;let c=R$1.atRule({name:"plugin",params:`${i}${s}${i}`,raws:{semicolon:true,before:`
`}});e.insertAfter(n,c),e.insertBefore(c,R$1.comment({text:"---break---"}));}}}}function Kr(t){return {postcssPlugin:"update-tailwind-config-keyframes",Once(e){if(!t?.theme?.extend?.keyframes)return;let r=Ce(e),i=r.nodes?.filter(n=>n.type==="atrule"&&n.name==="keyframes"),o=z$1.record(z$1.string(),z$1.record(z$1.string(),z$1.string()));for(let[n,a]of Object.entries(t.theme.extend.keyframes)){if(typeof n!="string")continue;let s=o.safeParse(a);if(!s.success||i?.find(l=>l.type==="atrule"&&l.name==="keyframes"&&l.params===n))continue;let c=R$1.atRule({name:"keyframes",params:n,nodes:[],raws:{semicolon:true,between:" ",before:`
  `}});for(let[l,m]of Object.entries(s.data)){let p=R$1.rule({selector:l,nodes:Object.entries(m).map(([u,g])=>R$1.decl({prop:u,value:g,raws:{semicolon:true,before:`
      `,between:": "}})),raws:{semicolon:true,between:" ",before:`
    `}});c.append(p);}r.append(c),r.insertBefore(c,R$1.comment({text:"---break---"}));}}}}function Hr(t){return {postcssPlugin:"update-tailwind-config-animation",Once(e){if(!t?.theme?.extend?.animation)return;let r=Ce(e),i=r.nodes?.filter(n=>n.type==="decl"&&n.prop.startsWith("--animate-")),o=z$1.record(z$1.string(),z$1.string()).safeParse(t.theme.extend.animation);if(o.success)for(let[n,a]of Object.entries(o.data)){let s=`--animate-${n}`;if(i?.find(l=>l.prop===s))continue;let c=R$1.decl({prop:s,value:a,raws:{semicolon:true,between:": ",before:`
  `}});r.append(c);}}}}function Qr(t){return t.nodes[0].toString().includes("'")?"single":"double"}function Ut(t){if(t.startsWith("hsl")||t.startsWith("rgb")||t.startsWith("#")||t.startsWith("oklch"))return  false;let e=t.split(" ");return e.length===3&&e.slice(1,3).every(r=>r.includes("%"))}function Xr(t){return t.startsWith("hsl")||t.startsWith("rgb")||t.startsWith("#")||t.startsWith("oklch")||t.startsWith("var(--color-")}async function _(t$1,e,r,i){if(t$1=Array.from(new Set(t$1)),e=Array.from(new Set(e)),!t$1?.length&&!e?.length)return;i={silent:false,...i};let o=t("Installing dependencies.",{silent:i.silent})?.start(),n=await ti(r),a="";if(ei(r)&&n==="npm")if(i.silent)a="force";else {o.stopAndPersist(),s.warn(`
It looks like you are using React 19. 
Some packages may fail to install due to peer dependency issues in npm (see https://ui.shadcn.com/react-19).
`);let s$1=await He([{type:"select",name:"flag",message:"How would you like to proceed?",choices:[{title:"Use --force",value:"force"},{title:"Use --legacy-peer-deps",value:"legacy-peer-deps"}]}]);s$1&&(a=s$1.flag);}o?.start(),await ri(n,t$1,e,r.resolvedPaths.cwd,a),o?.succeed();}function ei(t){let e=o(t.resolvedPaths.cwd,false);if(!e?.dependencies?.react)return  false;let r=/^(?:\^|~)?19(?:\.\d+)*(?:-.*)?$/.test(e.dependencies.react),i=e.dependencies["react-day-picker"]?.startsWith("8");return r&&i}async function ti(t){return o(t.resolvedPaths.cwd,false)?.dependencies?.expo?"expo":a$1(t.resolvedPaths.cwd)}async function ri(t,e,r,i,o){if(t==="npm")return ii(e,r,i,o);if(t==="deno")return oi(e,r,i);if(t==="expo")return ni(e,r,i);e?.length&&await execa(t,["add",...e],{cwd:i}),r?.length&&await execa(t,["add","-D",...r],{cwd:i});}async function ii(t,e,r,i){t.length&&await execa("npm",["install",...i?[`--${i}`]:[],...t],{cwd:r}),e.length&&await execa("npm",["install",...i?[`--${i}`]:[],"-D",...e],{cwd:r});}async function oi(t,e,r){t?.length&&await execa("deno",["add",...t.map(i=>`npm:${i}`)],{cwd:r}),e?.length&&await execa("deno",["add","-D",...e.map(i=>`npm:${i}`)],{cwd:r});}async function ni(t,e,r){t.length&&await execa("npx",["expo","install",...t],{cwd:r}),e.length&&await execa("npx",["expo","install","-- -D",...e],{cwd:r});}async function Ye(t$1,e,r){if(!t$1||Object.keys(t$1).length===0)return {envVarsAdded:[],envFileUpdated:null,envFileCreated:null};r={silent:false,...r};let i=t("Adding environment variables.",{silent:r.silent})?.start(),o=e.resolvedPaths.cwd,n=W__default.join(o,".env.local"),a=G(o);a&&(n=a);let s$1=existsSync(n),c=W__default.basename(n),l=Object.entries(t$1).map(([g,w])=>`${g}=${w}`).join(`
`),m=[],p=null,u=null;if(s$1){let g=await promises.readFile(n,"utf-8"),w=I(g,l);if(m=H(g,l),m.length>0){if(await promises.writeFile(n,w,"utf-8"),p=W__default.relative(o,n),i?.succeed(`Added the following variables to ${d.info(c)}:`),!r.silent)for(let y of m)s.log(`  ${d.success("+")} ${y}`);}else i?.stop();}else if(await promises.writeFile(n,l+`
`,"utf-8"),u=W__default.relative(o,n),m=Object.keys(t$1),i?.succeed(`Added the following variables to ${d.info(c)}:`),!r.silent)for(let g of m)s.log(`  ${d.success("+")} ${g}`);return !r.silent&&m.length>0&&s.break(),{envVarsAdded:m,envFileUpdated:p,envFileCreated:u}}async function le(t,e,r){r={overwrite:false,silent:false,isNewProject:false,baseStyle:true,...r};let i=await k(e);return i&&i.ui&&i.ui.resolvedPaths.cwd!==e.resolvedPaths.cwd?await li(t,e,i,{...r,isRemote:t?.length===1&&!!t[0].match(/\/chat\/b\//)}):await ci(t,e,r)}async function ci(t$1,e,r$1){if(!r$1.baseStyle&&!t$1.length)return;let i=t("Checking registry.",{silent:r$1.silent})?.start(),o=await X$1(t$1,O(e));if(!o)return i?.fail(),Y(new Error("Failed to fetch components from registry."));try{Jt(o.files??[],e.resolvedPaths.cwd);}catch(s){return i?.fail(),Y(s)}i?.succeed();let n=await r(e);await T(o.tailwind?.config,e,{silent:r$1.silent,tailwindVersion:n});let a=await Gt(t$1,e);await Ge(o.cssVars,e,{cleanupDefaultNextStyles:r$1.isNewProject,silent:r$1.silent,tailwindVersion:n,tailwindConfig:o.tailwind?.config,overwriteCssVars:a,initIndex:r$1.baseStyle}),await Ue(o.css,e,{silent:r$1.silent}),await Ye(o.envVars,e,{silent:r$1.silent}),await _(o.dependencies,o.devDependencies,e,{silent:r$1.silent}),await L$1(o.files,e,{overwrite:r$1.overwrite,silent:r$1.silent,path:r$1.path}),o.docs&&s.info(o.docs);}async function li(t$1,e,r$1,i){if(!i.baseStyle&&!t$1.length)return;let o=t("Checking registry.",{silent:i.silent})?.start(),n=await X$1(t$1,O(e));if(!n)return o?.fail(),Y(new Error("Failed to fetch components from registry."));try{Jt(n.files??[],e.resolvedPaths.cwd);}catch(y){return o?.fail(),Y(y)}o?.succeed();let a=[],s$1=[],c=[],l$1=t("Installing components.")?.start(),m$1=r$1.ui,p=await r(m$1),u=m(e.resolvedPaths.cwd,m$1.resolvedPaths.ui);if(n.tailwind?.config&&(await T(n.tailwind?.config,m$1,{silent:true,tailwindVersion:p}),s$1.push(W__default.relative(u,m$1.resolvedPaths.tailwindConfig))),n.cssVars){let y=await Gt(t$1,e);await Ge(n.cssVars,m$1,{silent:true,tailwindVersion:p,tailwindConfig:n.tailwind?.config,overwriteCssVars:y}),s$1.push(W__default.relative(u,m$1.resolvedPaths.tailwindCss));}n.css&&(await Ue(n.css,m$1,{silent:true}),s$1.push(W__default.relative(u,m$1.resolvedPaths.tailwindCss))),n.envVars&&await Ye(n.envVars,m$1,{silent:true}),await _(n.dependencies,n.devDependencies,m$1,{silent:true});let g=new Map;for(let y of n.files??[]){let C=y.type||"registry:ui";g.has(C)||g.set(C,[]),g.get(C).push(y);}for(let y of Array.from(g.keys())){let C=g.get(y),x=y==="registry:ui"?r$1.ui:e,B=m(e.resolvedPaths.cwd,x.resolvedPaths.ui||x.resolvedPaths.cwd),E=await l(B,x.resolvedPaths.cwd)??x.resolvedPaths.cwd,K=await L$1(C,x,{overwrite:i.overwrite,silent:true,rootSpinner:l$1,isRemote:i.isRemote,isWorkspace:true,path:i.path});a.push(...K.filesCreated.map(j=>W__default.relative(B,W__default.join(E,j)))),s$1.push(...K.filesUpdated.map(j=>W__default.relative(B,W__default.join(E,j)))),c.push(...K.filesSkipped.map(j=>W__default.relative(B,W__default.join(E,j))));}if(l$1?.succeed(),a.sort(),s$1.sort(),c.sort(),!(a.length||s$1.length)&&!c.length&&t("No files updated.",{silent:i.silent})?.info(),a.length){t(`Created ${a.length} ${a.length===1?"file":"files"}:`,{silent:i.silent})?.succeed();for(let y of a)s.log(`  - ${y}`);}if(s$1.length){t(`Updated ${s$1.length} ${s$1.length===1?"file":"files"}:`,{silent:i.silent})?.info();for(let y of s$1)s.log(`  - ${y}`);}if(c.length){t(`Skipped ${c.length} ${s$1.length===1?"file":"files"}: (use --overwrite to overwrite)`,{silent:i.silent})?.info();for(let y of c)s.log(`  - ${y}`);}n.docs&&s.info(n.docs);}async function Gt(t,e){let r=await _$1(t,{config:e});return z$1.array(g$1).parse(r).some(o=>o.type==="registry:theme"||o.type==="registry:style")}function Jt(t,e){for(let r of t)if(r?.target&&!Lt(r.target,e))throw new Error(`We found an unsafe file path "${r.target} in the registry item. Installation aborted.`)}var pi="https://codeload.github.com/shadcn-ui/ui/tar.gz/main",q={next:"next","next-16":"next-16","next-monorepo":"next-monorepo"};async function ke(t){t={srcDir:false,...t};let e=t.template&&q[t.template]?t.template:"next",r=e===q.next?"my-app":"my-monorepo",i="15",o=t.components?.length===1&&!!t.components[0].match(/\/chat\/b\//);if(t.components&&o)try{let[s]=await S(t.components),{meta:c}=z$1.object({meta:z$1.object({nextVersion:z$1.string()})}).parse(s);i=c.nextVersion,e=q.next;}catch(s$1){s.break(),Y(s$1);}if(!t.force){let{type:s,name:c}=await He([{type:t.template||o?null:"select",name:"type",message:`The path ${d.info(t.cwd)} does not contain a package.json file.
  Would you like to start a new project?`,choices:[{title:"Next.js 15",value:"next"},{title:"Next.js 16",value:"next-16"},{title:"Next.js (Monorepo)",value:"next-monorepo"}],initial:0},{type:"text",name:"name",message:"What is your project named?",initial:r,format:l=>l.trim(),validate:l=>l.length>128?"Name should be less than 128 characters.":true}]);e=s??e,r=c,s==="next-16"&&(i="latest");}let n=await a$1(t.cwd,{withFallback:true}),a=`${t.cwd}/${r}`;try{await A.access(t.cwd,A.constants.W_OK);}catch{s.break(),s.error(`The path ${d.info(t.cwd)} is not writable.`),s.error(`It is likely you do not have write permissions for this folder or the path ${d.info(t.cwd)} does not exist.`),s.break(),process.exit(1);}return A.existsSync(W__default.resolve(t.cwd,r,"package.json"))&&(s.break(),s.error(`A project with the name ${d.info(r)} already exists.`),s.error("Please choose a different name and try again."),s.break(),process.exit(1)),(e===q.next||e===q["next-16"])&&await di(a,{version:i,cwd:t.cwd,packageManager:n,srcDir:!!t.srcDir}),e===q["next-monorepo"]&&await ui(a,{packageManager:n}),{projectPath:a,projectName:r,template:e}}async function di(t$1,e){let r=t(`Creating a new Next.js ${e.version.startsWith("latest")?"16":"15"} project. This may take a few minutes.`).start(),i=["--tailwind","--eslint","--typescript","--app",e.srcDir?"--src-dir":"--no-src-dir","--no-import-alias",`--use-${e.packageManager}`];(e.version.startsWith("15")||e.version.startsWith("latest")||e.version.startsWith("canary"))&&i.push("--turbopack"),(e.version.startsWith("latest")||e.version.startsWith("canary"))&&i.push("--no-react-compiler");try{await execa("npx",[`create-next-app@${e.version}`,t$1,"--silent",...i],{cwd:e.cwd});}catch{s.break(),s.error("Something went wrong creating a new Next.js project. Please try again."),process.exit(1);}r?.succeed(`Creating a new Next.js ${e.version.startsWith("latest")?"16":"15"} project.`);}async function ui(t$1,e){let r=t("Creating a new Next.js monorepo. This may take a few minutes.").start();try{let i=W__default.join(fi.tmpdir(),`shadcn-template-${Date.now()}`);await A.ensureDir(i);let o=await fetch(pi);if(!o.ok)throw new Error(`Failed to download template: ${o.statusText}`);let n=W__default.resolve(i,"template.tar.gz");await A.writeFile(n,Buffer.from(await o.arrayBuffer())),await execa("tar",["-xzf",n,"-C",i,"--strip-components=2","ui-main/templates/monorepo-next"]);let a=W__default.resolve(i,"monorepo-next");await A.move(a,t$1),await A.remove(i),await execa(e.packageManager,["install"],{cwd:t$1});let s=W__default.join(t$1,"package.json");if(A.existsSync(s)){let l=await A.readFile(s,"utf8"),m=JSON.parse(l);m.name=t$1.split("/").pop(),await A.writeFile(s,JSON.stringify(m,null,2));}let c=process.cwd();await execa("git",["--version"],{cwd:t$1}),await execa("git",["init"],{cwd:t$1}),await execa("git",["add","-A"],{cwd:t$1}),await execa("git",["commit","-m","Initial commit"],{cwd:t$1}),r?.succeed("Creating a new Next.js monorepo.");}catch(i){r?.fail("Something went wrong creating a new Next.js monorepo."),Y(i);}}async function D(t=process.cwd()){try{let{config:e}=await import('@dotenvx/dotenvx'),r=[".env.local",".env.development.local",".env.development",".env"];for(let i of r){let o=join(t,i);existsSync(o)&&e({path:o,overload:!1,quiet:!0});}}catch(e){s.warn("Failed to load env files:",e);}}var me=".bak";function Yt(t){if(!A.existsSync(t))return null;let e=`${t}${me}`;try{return A.renameSync(t,e),e}catch(r){return console.error(`Failed to create backup of ${t}: ${r}`),null}}function qt(t){let e=`${t}${me}`;if(!A.existsSync(e))return  false;try{return A.renameSync(e,t),!0}catch(r){return console.error(`Warning: Could not restore backup file ${e}: ${r}`),false}}function Ke(t){let e=`${t}${me}`;if(!A.existsSync(e))return  false;try{return A.unlinkSync(e),!0}catch{return  false}}async function Kt(t,e){let r=new Set,i=new Set,o=[...t];for(;o.length>0;){let n=o.shift();if(i.has(n))continue;i.add(n);let{registry:a}=F(n);a&&!b[a]&&r.add(a);try{let[s]=await W$1([n],e,{useCache:!0});if(s?.registryDependencies)for(let c of s.registryDependencies){let{registry:l}=F(c);l&&!b[l]&&r.add(l),i.has(c)||o.push(c);}}catch(s){if(s instanceof z$2){let{registry:c}=F(n);c&&!b[c]&&r.add(c);continue}continue}}return Array.from(r)}async function L(t$1,e,r={}){r={silent:false,writeFile:true,...r};let o=(await Kt(t$1,e)).filter(l=>!e.registries?.[l]&&!Object.keys(b).includes(l));if(o.length===0)return {config:e,newRegistries:[]};let n=await ja({useCache:process.env.NODE_ENV!=="development"});if(!n)return {config:e,newRegistries:[]};let a={};for(let l of o)n[l]&&(a[l]=n[l]);if(Object.keys(a).length===0)return {config:e,newRegistries:[]};let s=Object.fromEntries(Object.entries(e.registries||{}).filter(([l])=>!Object.keys(b).includes(l))),c={...e,registries:{...s,...a}};if(r.writeFile){let{resolvedPaths:l,...m}=c,p$1=t("Updating components.json.",{silent:r.silent}).start(),u=p.parse(m);await A.writeFile(W__default.resolve(e.resolvedPaths.cwd,"components.json"),JSON.stringify(u,null,2)+`
`,"utf-8"),p$1.succeed();}return {config:c,newRegistries:Object.keys(a)}}async function Qt(t$1,e,r){if(!t$1)return;r={silent:false,...r};let i=W__default.relative(e.resolvedPaths.cwd,e.resolvedPaths.tailwindConfig),o=t(`Updating ${d.info(i)}`,{silent:r.silent}).start(),n=await promises.readFile(e.resolvedPaths.tailwindConfig,"utf8"),a=await Ri(n,t$1,e);await promises.writeFile(e.resolvedPaths.tailwindConfig,a,"utf8"),o?.succeed();}async function Ri(t,e,r){let i=await U(t,r),o=i.getDescendantsOfKind(SyntaxKind.ObjectLiteralExpression).find(n=>n.getProperties().some(a=>a.isKind(SyntaxKind.PropertyAssignment)&&a.getName()==="content"));return o?(xi(o,e),i.getFullText()):t}async function xi(t,e){let r=V(t),i=t.getProperty("content");if(!i){let o={name:"content",initializer:`[${r}${e.join(`${r}, ${r}`)}${r}]`};return t.addPropertyAssignment(o),t}if(i.isKind(SyntaxKind.PropertyAssignment)){let o=i.getInitializer();if(o?.isKind(SyntaxKind.ArrayLiteralExpression))for(let n of e){let a=`${r}${n}${r}`;o.getElements().map(s=>s.getText()).includes(a)||o.addElement(a);}return t}return t}process.on("exit",t=>{let e=W__default.resolve(process.cwd(),"components.json");return t===0?Ke(e):qt(e)});var Ii=z$1.object({cwd:z$1.string(),components:z$1.array(z$1.string()).optional(),yes:z$1.boolean(),defaults:z$1.boolean(),force:z$1.boolean(),silent:z$1.boolean(),isNewProject:z$1.boolean(),srcDir:z$1.boolean().optional(),cssVariables:z$1.boolean(),template:z$1.string().optional().refine(t=>t?q[t]:true,{message:"Invalid template. Please use 'next', 'next-16' or 'next-monorepo'."}),baseColor:z$1.string().optional().refine(t=>t?a.find(e=>e.name===t):true,{message:`Invalid base color. Please use '${a.map(t=>t.name).join("', '")}'`}),baseStyle:z$1.boolean()}),Xt=new Command().name("init").description("initialize your project and install dependencies").argument("[components...]","names, url or local path to component").option("-t, --template <template>","the template to use. (next, next-16, next-monorepo)").option("-b, --base-color <base-color>","the base color to use. (neutral, gray, zinc, stone, slate)",void 0).option("-y, --yes","skip confirmation prompt.",true).option("-d, --defaults,","use default configuration.",false).option("-f, --force","force overwrite of existing configuration.",false).option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).option("-s, --silent","mute output.",false).option("--src-dir","use the src directory when creating a new project.",false).option("--no-src-dir","do not use the src directory when creating a new project.").option("--css-variables","use css variables for theming.",true).option("--no-css-variables","do not use css variables for theming.").option("--no-base-style","do not install the base shadcn style.").action(async(t,e)=>{try{e.defaults&&(e.template=e.template||"next",e.baseColor=e.baseColor||"neutral");let r=Ii.parse({cwd:W__default.resolve(e.cwd),isNewProject:!1,components:t,...e});if(await D(r.cwd),t.length>0){let i=O({}),o=W__default.resolve(r.cwd,"components.json");if(A.existsSync(o)){let s=await A.readJson(o),c=p.partial().parse(s);i=O(c),Yt(o);}let{config:n}=await L(t,i,{silent:!0});i=n,R(t[0],i);let[a]=await _$1([t[0]],{config:i});a?.type==="registry:style"&&(r.baseColor="neutral",r.baseStyle=a.extends==="none"?!1:r.baseStyle);}r.baseStyle||(r.baseColor="neutral"),await je(r),s.log(`${d.success("Success!")} Project initialization completed.
You may now add components.`),Ke(W__default.resolve(r.cwd,"components.json")),s.break();}catch(r){s.break(),Y(r);}finally{P();}});async function je(t$1){let e,r;if(t$1.skipPreflight)e=await p$1(t$1.cwd);else {let u=await Dt(t$1);if(u.errors["1"]){let{projectPath:g,template:w}=await ke(t$1);g||process.exit(1),t$1.cwd=g,t$1.isNewProject=true,r=w;}e=u.projectInfo;}if(r==="next-monorepo")return t$1.cwd=W__default.resolve(t$1.cwd,"apps/web"),await i(t$1.cwd);let i$1=await q$1(t$1.cwd,e),o=i$1?await Pi(i$1,t$1):await ki(await i(t$1.cwd));if(!t$1.yes){let{proceed:u}=await He({type:"confirm",name:"proceed",message:`Write configuration to ${d.info("components.json")}. Proceed?`,initial:true});u||process.exit(0);}let n=[...t$1.baseStyle?["index"]:[],...t$1.components??[]],a=await j(t$1.cwd,o),{config:s}=await L(n,a,{silent:true});s.registries&&(o.registries=s.registries);let c=t("Writing components.json.").start(),l=W__default.resolve(t$1.cwd,"components.json"),m=`${l}${me}`;if(!t$1.force&&A.existsSync(m)){let u=await A.readJson(m),{registries:g,...w}=Ci(u,o);o={...w,registries:g};}o.registries=Object.fromEntries(Object.entries(o.registries||{}).filter(([u])=>!Object.keys(b).includes(u))),await promises.writeFile(l,`${JSON.stringify(o,null,2)}
`,"utf8"),c.succeed();let p=await j(t$1.cwd,o);return await le(n,p,{overwrite:true,silent:t$1.silent,baseStyle:t$1.baseStyle,isNewProject:t$1.isNewProject||e?.framework.name==="next-app"}),t$1.isNewProject&&t$1.srcDir&&await Qt(["./src/**/*.{js,ts,jsx,tsx,mdx}"],p,{silent:t$1.silent}),p}async function ki(t=null){let[e$1,r]=await Promise.all([ca(),ea()]);s.info("");let i=await He([{type:"toggle",name:"typescript",message:`Would you like to use ${d.info("TypeScript")} (recommended)?`,initial:t?.tsx??true,active:"yes",inactive:"no"},{type:"select",name:"style",message:`Which ${d.info("style")} would you like to use?`,choices:e$1.map(o=>({title:o.label,value:o.name}))},{type:"select",name:"tailwindBaseColor",message:`Which color would you like to use as the ${d.info("base color")}?`,choices:r.map(o=>({title:o.label,value:o.name}))},{type:"text",name:"tailwindCss",message:`Where is your ${d.info("global CSS")} file?`,initial:t?.tailwind.css??g},{type:"toggle",name:"tailwindCssVariables",message:`Would you like to use ${d.info("CSS variables")} for theming?`,initial:t?.tailwind.cssVariables??true,active:"yes",inactive:"no"},{type:"text",name:"tailwindPrefix",message:`Are you using a custom ${d.info("tailwind prefix eg. tw-")}? (Leave blank if not)`,initial:""},{type:"text",name:"tailwindConfig",message:`Where is your ${d.info("tailwind.config.js")} located?`,initial:t?.tailwind.config??h},{type:"text",name:"components",message:`Configure the import alias for ${d.info("components")}:`,initial:t?.aliases.components??e},{type:"text",name:"utils",message:`Configure the import alias for ${d.info("utils")}:`,initial:t?.aliases.utils??f},{type:"toggle",name:"rsc",message:`Are you using ${d.info("React Server Components")}?`,initial:t?.rsc??true,active:"yes",inactive:"no"}]);return p.parse({$schema:"https://ui.shadcn.com/schema.json",style:i.style,tailwind:{config:i.tailwindConfig,css:i.tailwindCss,baseColor:i.tailwindBaseColor,cssVariables:i.tailwindCssVariables,prefix:i.tailwindPrefix},rsc:i.rsc,tsx:i.typescript,aliases:{utils:i.utils,components:i.components,lib:i.components.replace(/\/components$/,"lib"),hooks:i.components.replace(/\/components$/,"hooks")}})}async function Pi(t,e){let r$1=t.style,i=e.baseColor,o=t.tailwind.cssVariables;if(!e.defaults){let[n,a,s]=await Promise.all([ca(),ea(),r(t)]),c=await He([{type:s==="v4"?null:"select",name:"style",message:`Which ${d.info("style")} would you like to use?`,choices:n.map(l=>({title:l.name==="new-york"?"New York (Recommended)":l.label,value:l.name})),initial:0},{type:e.baseColor?null:"select",name:"tailwindBaseColor",message:`Which color would you like to use as the ${d.info("base color")}?`,choices:a.map(l=>({title:l.label,value:l.name}))}]);r$1=c.style??"new-york",i=c.tailwindBaseColor??i,o=e.cssVariables;}return p.parse({$schema:t?.$schema,style:r$1,tailwind:{...t?.tailwind,baseColor:i,cssVariables:o},rsc:t?.rsc,tsx:t?.tsx,iconLibrary:t?.iconLibrary,aliases:t?.aliases})}async function er(t){let e={};if(!A.existsSync(t.cwd)||!A.existsSync(W__default.resolve(t.cwd,"package.json")))return e["1"]=true,{errors:e,config:null};if(!A.existsSync(W__default.resolve(t.cwd,"components.json")))return e["3"]=true,{errors:e,config:null};try{let r=await i(t.cwd);return {errors:e,config:r}}catch{s.break(),s.error(`An invalid ${d.info("components.json")} file was found at ${d.info(t.cwd)}.
Before you can add components, you must create a valid ${d.info("components.json")} file by running the ${d.info("init")} command.`),s.error(`Learn more at ${d.info("https://ui.shadcn.com/docs/components-json")}.`),s.break(),process.exit(1);}}async function rr(t,e){let r=W__default.join(e.resolvedPaths.cwd,"app/page.tsx");if(!(await z__default.stat(r)).isFile())return;let[i]=await _$1([t],{config:e});if(!i?.meta?.importSpecifier||!i?.meta?.moduleSpecifier)return;let o=`import { ${i?.meta?.importSpecifier} } from "${i.meta.moduleSpecifier}"

export default function Page() {
  return <${i?.meta?.importSpecifier} />
}`;await z__default.writeFile(r,o,"utf8");}var Oi=z$1.object({components:z$1.array(z$1.string()).optional(),yes:z$1.boolean(),overwrite:z$1.boolean(),cwd:z$1.string(),all:z$1.boolean(),path:z$1.string().optional(),silent:z$1.boolean(),srcDir:z$1.boolean().optional(),cssVariables:z$1.boolean()}),or=new Command().name("add").description("add a component to your project").argument("[components...]","names, url or local path to component").option("-y, --yes","skip confirmation prompt.",false).option("-o, --overwrite","overwrite existing files.",false).option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).option("-a, --all","add all available components",false).option("-p, --path <path>","the path to add the component to.").option("-s, --silent","mute output.",false).option("--src-dir","use the src directory when creating a new project.",false).option("--no-src-dir","do not use the src directory when creating a new project.").option("--css-variables","use css variables for theming.",true).option("--no-css-variables","do not use css variables for theming.").action(async(t,e)=>{try{let r=Oi.parse({components:t,cwd:W__default.resolve(e.cwd),...e});await D(r.cwd);let i$1=await i(r.cwd);i$1||(i$1=n({style:"new-york",resolvedPaths:{cwd:r.cwd}}));let o=!1;if(t.length>0){let{config:p,newRegistries:u}=await L(t,i$1,{silent:r.silent,writeFile:!1});i$1=p,o=u.length>0;}if(t.length>0){let[p]=await _$1([t[0]],{config:i$1}),u=p?.type;if(N(p)){await le(t,i$1,r);return}if(!r.yes&&(u==="registry:style"||u==="registry:theme")){s.break();let{confirm:g}=await He({type:"confirm",name:"confirm",message:d.warn(`You are about to install a new ${u.replace("registry:","")}. 
Existing CSS variables and components will be overwritten. Continue?`)});g||(s.break(),s.log("Installation cancelled."),s.break(),process.exit(1));}}if(r.components?.length||(r.components=await $i(r)),(await p$1(r.cwd))?.tailwindVersion==="v4"){let p=c.filter(u=>r.components?.includes(u.name));p?.length&&(s.break(),p.forEach(u=>{s.warn(d.warn(u.message));}),s.break(),process.exit(1));}let{errors:a,config:s$1}=await er(r),c$1=!1;if(a["3"]){let{proceed:p}=await He({type:"confirm",name:"proceed",message:`You need to create a ${d.info("components.json")} file to add components. Proceed?`,initial:!0});p||(s.break(),process.exit(1)),s$1=await je({cwd:r.cwd,yes:!0,force:!0,defaults:!1,skipPreflight:!1,silent:r.silent||!o,isNewProject:!1,srcDir:r.srcDir,cssVariables:r.cssVariables,baseStyle:!0,components:r.components}),c$1=!0;}let l=!1;if(a["1"]){let{projectPath:p,template:u}=await ke({cwd:r.cwd,force:r.overwrite,srcDir:r.srcDir,components:r.components});p||(s.break(),process.exit(1)),r.cwd=p,u==="next-monorepo"?(r.cwd=W__default.resolve(r.cwd,"apps/web"),s$1=await i(r.cwd)):(s$1=await je({cwd:r.cwd,yes:!0,force:!0,defaults:!1,skipPreflight:!0,silent:!o&&r.silent,isNewProject:!0,srcDir:r.srcDir,cssVariables:r.cssVariables,baseStyle:!0,components:r.components}),c$1=!0,l=r.components?.length===1&&!!r.components[0].match(/\/chat\/b\//));}if(!s$1)throw new Error(`Failed to read config at ${d.info(r.cwd)}.`);let{config:m}=await L(r.components,s$1,{silent:r.silent||o});s$1=m,c$1||await le(r.components,s$1,r),l&&await rr(r.components[0],s$1);}catch(r){s.break(),Y(r);}finally{P();}});async function $i(t){let e=await ba();if(!e)return s.break(),Y(new Error("Failed to fetch registry index.")),[];if(t.all)return e.map(o=>o.name).filter(o=>!c.some(n=>n.name===o));if(t.components?.length)return t.components;let{components:r}=await He({type:"multiselect",name:"components",message:"Which components would you like to add?",hint:"Space to select. A to toggle all. Enter to submit.",instructions:false,choices:e.filter(o=>o.type==="registry:ui"&&!c.some(n=>n.name===o.name)).map(o=>({title:o.name,value:o.name,selected:t.all?true:t.components?.includes(o.name)}))});r?.length||(s.warn("No components selected. Exiting."),s.info(""),process.exit(1));let i=z$1.array(z$1.string()).safeParse(r);return i.success?i.data:(s.error(""),Y(new Error("Something went wrong. Please try again.")),[])}async function ar(t){let e={},r={cwd:t.cwd,registryFile:W__default.resolve(t.cwd,t.registryFile),outputDir:W__default.resolve(t.cwd,t.outputDir)};return A.existsSync(r.registryFile)||(e["13"]=true),await A.mkdir(r.outputDir,{recursive:true}),Object.keys(e).length>0&&(e["13"]&&(s.break(),s.error(`The path ${d.info(r.registryFile)} does not exist.`)),s.break(),process.exit(1)),{errors:e,resolvePaths:r}}var Fi=z$1.object({cwd:z$1.string(),registryFile:z$1.string(),outputDir:z$1.string()}),cr=new Command().name("build").description("build components for a shadcn registry").argument("[registry]","path to registry.json file","./registry.json").option("-o, --output <path>","destination directory for json files","./public/r").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).action(async(t$1,e)=>{try{let r=Fi.parse({cwd:W.resolve(e.cwd),registryFile:t$1,outputDir:e.output}),{resolvePaths:i}=await ar(r),o=await z.readFile(i.registryFile,"utf-8"),n=h$1.safeParse(JSON.parse(o));n.success||(s.error(`Invalid registry file found at ${d.info(i.registryFile)}.`),process.exit(1));let a=t("Building registry...");for(let s$1 of n.data.items){a.start(`Building ${s$1.name}...`),s$1.$schema="https://ui.shadcn.com/schema/registry-item.json";for(let l of s$1.files??[])l.content=await z.readFile(W.resolve(i.cwd,l.path),"utf-8");let c=g$1.safeParse(s$1);if(!c.success){s.error(`Invalid registry item found for ${d.info(s$1.name)}.`);continue}await z.writeFile(W.resolve(i.outputDir,`${c.data.name}.json`),JSON.stringify(c.data,null,2));}await z.copyFile(i.registryFile,W.resolve(i.outputDir,"registry.json")),a.succeed("Building registry.");}catch(r){s.break(),Y(r);}});var Di=z$1.object({component:z$1.string().optional(),yes:z$1.boolean(),cwd:z$1.string(),path:z$1.string().optional()}),fr=new Command().name("diff").description("check for updates against the registry").argument("[component]","the component name").option("-y, --yes","skip confirmation prompt.",false).option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).action(async(t,e)=>{try{let r=Di.parse({component:t,...e}),i$1=W__default.resolve(r.cwd);existsSync(i$1)||(s.error(`The path ${i$1} does not exist. Please try again.`),process.exit(1));let o=await i(i$1);o||(s.warn(`Configuration is missing. Please run ${d.success("init")} to create a components.json file.`),process.exit(1));let n=await ba();if(n||(Y(new Error("Failed to fetch registry index.")),process.exit(1)),!r.component){let c=o.resolvedPaths.components,l=n.filter(p=>{for(let u of p.files??[]){let g=W__default.resolve(c,typeof u=="string"?u:u.path);if(existsSync(g))return !0}return !1}),m=[];for(let p of l){let u=await lr(p,o);u.length&&m.push({name:p.name,changes:u});}m.length||(s.info("No updates found."),process.exit(0)),s.info("The following components have updates available:");for(let p of m){s.info(`- ${p.name}`);for(let u of p.changes)s.info(`  - ${u.filePath}`);}s.break(),s.info(`Run ${d.success("diff <component>")} to see the changes.`),process.exit(0);}let a=n.find(c=>c.name===r.component);a||(s.error(`The component ${d.success(r.component)} does not exist.`),process.exit(1));let s$1=await lr(a,o);s$1.length||(s.info(`No updates found for ${r.component}.`),process.exit(0));for(let c of s$1)s.info(`- ${c.filePath}`),await Li(c.patch),s.info("");}catch(r){Y(r);}});async function lr(t,e){let r=await ha(e.style,[t]),i=await fa(e.tailwind.baseColor);if(!r)return [];let o=[];for(let n of r){let a=await ia(e,n);if(a)for(let s of n.files??[]){let c=W__default.resolve(a,typeof s=="string"?s:s.path);if(!existsSync(c))continue;let l=await promises.readFile(c,"utf8");if(typeof s=="string"||!s.content)continue;let m=await K({filename:s.path,raw:s.content,config:e,baseColor:i}),p=diffLines(m,l);p.length>1&&o.push({filePath:c,patch:p});}}return o}async function Li(t){t.forEach(e=>{if(e)return e.added?process.stdout.write(d.success(e.value)):e.removed?process.stdout.write(d.error(e.value)):process.stdout.write(e.value)});}var mr=new Command().name("info").description("get information about your project").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).action(async t=>{try{s.info("> project info"),console.log(await p$1(t.cwd)),s.break(),s.info("> components.json"),console.log(await i(t.cwd));}catch(e){Y(e);}});var se="latest",Ne=[{name:"claude",label:"Claude Code",configPath:".mcp.json",config:{mcpServers:{shadcn:{command:"npx",args:[`shadcn@${se}`,"mcp"]}}}},{name:"cursor",label:"Cursor",configPath:".cursor/mcp.json",config:{mcpServers:{shadcn:{command:"npx",args:[`shadcn@${se}`,"mcp"]}}}},{name:"vscode",label:"VS Code",configPath:".vscode/mcp.json",config:{servers:{shadcn:{command:"npx",args:[`shadcn@${se}`,"mcp"]}}}},{name:"codex",label:"Codex",configPath:".codex/config.toml",config:`[mcp_servers.shadcn]
command = "npx"
args = ["shadcn@${se}", "mcp"]
`}],$e=[`shadcn@${se}`],rt=new Command().name("mcp").description("MCP server and configuration commands").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).action(async t=>{try{await D(t.cwd);let e=new StdioServerTransport;await b$1.connect(e);}catch(e){s.break(),Y(e);}}),Gi=tt.object({client:tt.enum(["claude","cursor","vscode","codex"]),cwd:tt.string()});rt.command("init").description("Initialize MCP configuration for your client").option("--client <client>",`MCP client (${Ne.map(t=>t.name).join(", ")})`).action(async(t$1,e)=>{try{let i$1=(e.parent?.opts()||{}).cwd||process.cwd(),o=t$1.client;if(!o){let l=await He({type:"select",name:"client",message:"Which MCP client are you using?",choices:Ne.map(m=>({title:m.label,value:m.name}))});l.client||(s.break(),process.exit(1)),o=l.client;}let n=Gi.parse({client:o,cwd:i$1}),a=await i(n.cwd);if(n.client==="codex"){if(a)await _([],$e,a,{silent:!1});else {let l=await a$1(n.cwd),m=l==="npm"?"install":"add",p=l==="npm"?"--save-dev":"-D",u=t("Installing dependencies...").start();await execa(l,[m,p,...$e],{cwd:n.cwd}),u.succeed("Installing dependencies.");}s.break(),s.log("To configure the shadcn MCP server in Codex:"),s.break(),s.log(`1. Open or create the file ${d.info("~/.codex/config.toml")}`),s.log("2. Add the following configuration:"),s.log(),s.info(`[mcp_servers.shadcn]
command = "npx"
args = ["shadcn@${se}", "mcp"]`),s.break(),s.info("3. Restart Codex to load the MCP server"),s.break(),process.exit(0);}let s$1=t("Configuring MCP server...").start(),c=await Yi(n);if(s$1.succeed("Configuring MCP server."),a)await _([],$e,a,{silent:!1});else {let l=await a$1(n.cwd),m=l==="npm"?"install":"add",p=l==="npm"?"--save-dev":"-D",u=t("Installing dependencies...").start();await execa(l,[m,p,...$e],{cwd:n.cwd}),u.succeed("Installing dependencies.");}s.break(),s.success(`Configuration saved to ${c}.`),s.break();}catch(r){Y(r);}});var Ji=(t,e)=>e;async function Yi(t){let{client:e,cwd:r}=t,i=Ne.find(c=>c.name===e);if(!i)throw new Error(`Unknown client: ${e}. Available clients: ${Ne.map(c=>c.name).join(", ")}`);let o=W__default.join(r,i.configPath),n=W__default.dirname(o);await A.ensureDir(n);let a={};try{let c=await promises.readFile(o,"utf-8");a=JSON.parse(c);}catch{}let s=Ci(a,i.config,{arrayMerge:Ji});return await promises.writeFile(o,JSON.stringify(s,null,2)+`
`,"utf-8"),i.configPath}async function hr(t$1){if(!t$1.resolvedPaths.ui)throw new Error("We could not find a valid `ui` path in your `components.json` file. Please ensure you have a valid `ui` path in your `components.json` file.");let e=t$1.resolvedPaths.ui,[r,i]=await Promise.all([Hi("**/*.{js,ts,jsx,tsx}",{cwd:e}),da()]);if(Object.keys(i).length===0)throw new Error("Something went wrong fetching the registry icons.");let o=Object.entries(J).map(([m,p])=>({title:p.name,value:m})),n=await He([{type:"select",name:"sourceLibrary",message:`Which icon library would you like to ${d.info("migrate from")}?`,choices:o},{type:"select",name:"targetLibrary",message:`Which icon library would you like to ${d.info("migrate to")}?`,choices:o}]);if(n.sourceLibrary===n.targetLibrary)throw new Error("You cannot migrate to the same icon library. Please choose a different icon library.");if(!(n.sourceLibrary in J&&n.targetLibrary in J))throw new Error("Invalid icon library. Please choose a valid icon library.");let a=J[n.sourceLibrary],s$1=J[n.targetLibrary],{confirm:c}=await He({type:"confirm",name:"confirm",initial:true,message:`We will migrate ${d.info(r.length)} files in ${d.info(`./${W__default.relative(t$1.resolvedPaths.cwd,e)}`)} from ${d.info(a.name)} to ${d.info(s$1.name)}. Continue?`});c||(s.info("Migration cancelled."),process.exit(0)),s$1.package&&await _([s$1.package],[],t$1,{silent:false});let l=t("Migrating icons...")?.start();await Promise.all(r.map(async m=>{l.text=`Migrating ${m}...`;let p=W__default.join(e,m),u=await promises.readFile(p,"utf-8"),g=await eo(u,n.sourceLibrary,n.targetLibrary,i);await promises.writeFile(p,g);})),l.succeed("Migration complete.");}async function eo(t,e,r,i){let o=J[e]?.import,n=J[r]?.import,a=await promises.mkdtemp(W__default.join(tmpdir(),"shadcn-")),s=new Project({compilerOptions:{}}),c=W__default.join(a,`shadcn-icons-${randomBytes(4).toString("hex")}.tsx`),l=s.createSourceFile(c,t,{scriptKind:ScriptKind.TSX}),m=[];for(let p of l.getImportDeclarations()??[])if(p.getModuleSpecifier()?.getText()===`"${o}"`){for(let u of p.getNamedImports()??[]){let g=u.getName(),w=Object.values(i).find(y=>y[e]===g)?.[r];!w||m.includes(w)||(m.push(w),u.remove(),l.getDescendantsOfKind(SyntaxKind.JsxSelfClosingElement).filter(y=>y.getTagNameNode()?.getText()===g).forEach(y=>y.getTagNameNode()?.replaceWithText(w)));}p.getNamedImports()?.length===0&&p.remove();}return m.length>0&&l.addImportDeclaration({moduleSpecifier:n,namedImports:m.map(p=>({name:p}))}),await l.getText()}function io(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join("")}function oo(t,e,r,i){let n=t.replace(/\/\/.*$/gm,"").replace(/\/\*[\s\S]*?\*\//g,"").replace(/\s+/g," ").trim().split(",").map(a=>a.trim()).filter(Boolean);for(let a of n){let s=a.match(/^type\s+(\w+)(?:\s+as\s+(\w+))?$/),c=a.match(/^(\w+)\s+as\s+(\w+)$/);if(s){let l=s[1],m=s[2];i==="slot"&&l==="Slot"&&!m?r.push({name:"Slot",alias:"SlotPrimitive",isType:true}):r.push({name:l,alias:m,isType:true});}else if(c){let l=c[1],m=c[2];i==="slot"&&l==="Slot"&&m==="Slot"?r.push({name:"Slot",alias:"SlotPrimitive",isType:e}):r.push({name:l,alias:m,isType:e});}else i==="slot"&&a==="Slot"?r.push({name:"Slot",alias:"SlotPrimitive",isType:e}):r.push({name:a,isType:e});}}async function wr(t$1,e={}){if(!t$1.resolvedPaths.ui)throw new Error("We could not find a valid `ui` path in your `components.json` file. Please ensure you have a valid `ui` path in your `components.json` file.");let r=t$1.resolvedPaths.ui,i=await Hi("**/*.{js,ts,jsx,tsx}",{cwd:r});if(!e.yes){let{confirm:s$1}=await He({type:"confirm",name:"confirm",initial:true,message:`We will migrate ${d.info(i.length)} files in ${d.info(`./${W__default.relative(t$1.resolvedPaths.cwd,r)}`)} to ${d.info("radix-ui")}. Continue?`});s$1||(s.info("Migration cancelled."),process.exit(0));}let o$1=t("Migrating imports...")?.start(),n=new Set;await Promise.all(i.map(async s=>{o$1.text=`Migrating ${s}...`;let c=W__default.join(r,s),l=await promises.readFile(c,"utf-8"),{content:m,replacedPackages:p}=await no(l);p.forEach(u=>n.add(u)),await promises.writeFile(c,m);})),o$1.succeed("Migrating imports.");let a=t("Updating package.json...")?.start();try{let s$1=o(t$1.resolvedPaths.cwd,!1);if(!s$1){a.fail("Could not read package.json"),s.warn("Could not update package.json. You may need to manually replace @radix-ui/react-* packages with radix-ui");return}let c=Array.from(n),l=["dependencies","devDependencies"];for(let m of l)if(s$1[m])for(let p of c)s$1[m][p]&&delete s$1[m][p];if(c.length>0){s$1.dependencies||(s$1.dependencies={}),s$1.dependencies["radix-ui"]="latest";let m=W__default.join(t$1.resolvedPaths.cwd,"package.json");await promises.writeFile(m,JSON.stringify(s$1,null,2)+`
`),a.succeed("Updated package.json."),await _(["radix-ui"],[],t$1,{silent:!1});}else a.succeed("No packages found in source files.");}catch{a.fail("Failed to update package.json"),s.warn("You may need to manually replace @radix-ui/react-* packages with radix-ui");}}async function no(t){let e=/import\s+(?:(type)\s+)?(?:\*\s+as\s+(\w+)|{([^}]+)})\s+from\s+(["'])@radix-ui\/react-([^"']+)\4(;?)/g,r=[],i=[],o=[],n='"',a=false,s=t,c;for(;(c=e.exec(t))!==null;){let[w,y,C,x,B,E,K]=c;if(E==="icons"||E.startsWith("icons/"))continue;i.push(w),i.length===1&&(n=B,a=K===";"),o.push(`@radix-ui/react-${E}`);let j=!!y;if(C){let Te=io(E);r.push({name:Te,alias:C,isType:j});}else x&&oo(x,j,r,E);}if(r.length===0)return {content:t,replacedPackages:[]};let l=r.filter((w,y,C)=>y===C.findIndex(x=>x.name===w.name&&x.alias===w.alias&&x.isType===w.isType)),p=`import { ${l.map(w=>{let y=w.isType?"type ":"";return w.alias?`${y}${w.name} as ${w.alias}`:`${y}${w.name}`}).join(", ")} } from ${n}radix-ui${n}${a?";":""}`;s=i.reduce((w,y,C)=>w.replace(y,C===0?p:""),s),s=s.replace(/\n\s*\n\s*\n/g,`

`),l.some(w=>w.name==="Slot"&&w.alias==="SlotPrimitive")&&(s=s.split(`
`).map(C=>{if(C.trim().startsWith("import "))return C;let x=C;return x=x.replace(/\b(asChild\s*\?\s*)Slot(\s*:)/g,"$1__SLOT_PLACEHOLDER__$2"),x=x.replace(/\bReact\.ComponentProps<typeof\s+Slot>/g,"React.ComponentProps<typeof __SLOT_PLACEHOLDER__>"),x=x.replace(/\bComponentProps<typeof\s+Slot>/g,"ComponentProps<typeof __SLOT_PLACEHOLDER__>"),x=x.replace(/(<\/?)Slot(\s*\/?>)/g,"$1__SLOT_PLACEHOLDER__$2"),x=x.replace(/\bSlot\b/g,(B,E,K)=>{let j=K.substring(0,E),Te=(j.match(/"/g)||[]).length,Fr=(j.match(/'/g)||[]).length;return Te%2!==0||Fr%2!==0?B:"__SLOT_PLACEHOLDER__"}),x=x.replace(/__SLOT_PLACEHOLDER__/g,"SlotPrimitive.Slot"),x}).join(`
`));let g=Array.from(new Set(o));return {content:s,replacedPackages:g}}async function br(t){let e={};if(!A.existsSync(t.cwd)||!A.existsSync(W__default.resolve(t.cwd,"package.json")))return e["1"]=true,{errors:e,config:null};if(!A.existsSync(W__default.resolve(t.cwd,"components.json")))return e["3"]=true,{errors:e,config:null};try{let r=await i(t.cwd);return {errors:e,config:r}}catch{s.break(),s.error(`An invalid ${d.info("components.json")} file was found at ${d.info(t.cwd)}.
Before you can run a migration, you must create a valid ${d.info("components.json")} file by running the ${d.info("init")} command.`),s.error(`Learn more at ${d.info("https://ui.shadcn.com/docs/components-json")}.`),s.break(),process.exit(1);}}var Rr=[{name:"icons",description:"migrate your ui components to a different icon library."},{name:"radix",description:"migrate to radix-ui."}],co=z$1.object({cwd:z$1.string(),list:z$1.boolean(),yes:z$1.boolean(),migration:z$1.string().refine(t=>t&&Rr.some(e=>e.name===t),{message:"You must specify a valid migration. Run `shadcn migrate --list` to see available migrations."}).optional()}),xr=new Command().name("migrate").description("run a migration.").argument("[migration]","the migration to run.").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).option("-l, --list","list all migrations.",false).option("-y, --yes","skip confirmation prompt.",false).action(async(t,e)=>{try{let r=co.parse({cwd:W__default.resolve(e.cwd),migration:t,list:e.list,yes:e.yes});if(r.list||!r.migration){s.info("Available migrations:");for(let n of Rr)s.info(`- ${n.name}: ${n.description}`);return}if(!r.migration)throw new Error("You must specify a migration. Run `shadcn migrate --list` to see available migrations.");let{errors:i,config:o}=await br(r);if(i["1"]||i["3"])throw new Error("No `components.json` file found. Ensure you are at the root of your project.");if(!o)throw new Error("Something went wrong reading your `components.json` file. Please ensure you have a valid `components.json` file.");r.migration==="icons"&&await hr(o),r.migration==="radix"&&await wr(o,{yes:r.yes});}catch(r){s.break(),Y(r);}});async function vr(t){let e={},r={cwd:t.cwd,registryFile:W__default.resolve(t.cwd,t.registryFile),outputDir:W__default.resolve(t.cwd,t.outputDir)};if(!A.existsSync(r.registryFile))return e["13"]=true,{errors:e,resolvePaths:null,config:null};if(!A.existsSync(W__default.resolve(t.cwd,"components.json")))return e["3"]=true,{errors:e,resolvePaths:null,config:null};await A.mkdir(r.outputDir,{recursive:true});try{let i$1=await i(t.cwd);return {errors:e,config:i$1,resolvePaths:r}}catch{s.break(),s.error(`An invalid ${d.info("components.json")} file was found at ${d.info(t.cwd)}.
Before you can build the registry, you must create a valid ${d.info("components.json")} file by running the ${d.info("init")} command.`),s.break(),process.exit(1);}}var fo=z$1.object({cwd:z$1.string(),registryFile:z$1.string(),outputDir:z$1.string(),verbose:z$1.boolean().optional().default(false)}),Sr=new Command().name("registry:build").description("builds the registry [EXPERIMENTAL]").argument("[registry]","path to registry.json file","./registry.json").option("-o, --output <path>","destination directory for json files","./public/r").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).option("-v, --verbose","verbose output").action(async(t,e)=>{await mo({cwd:W.resolve(e.cwd),registryFile:t,outputDir:e.output,verbose:e.verbose});});async function mo(t$1){try{let e=fo.parse(t$1),[{errors:r,resolvePaths:i,config:o},n]=await Promise.all([vr(e),p$1(e.cwd)]);(r["3"]||!o||!n)&&(s.error(`A ${d.info("components.json")} file is required to build the registry. Run ${d.info("shadcn init")} to create one.`),s.break(),process.exit(1)),(r["13"]||!i)&&(s.error(`We could not find a registry file at ${d.info(W.resolve(e.cwd,e.registryFile))}.`),s.break(),process.exit(1));let a=await z.readFile(i.registryFile,"utf-8"),s$1=h$1.safeParse(JSON.parse(a));s$1.success||(s.error(`Invalid registry file found at ${d.info(i.registryFile)}.`),s.break(),process.exit(1));let c=t("Building registry..."),l=await po(s$1.data,o,n);for(let m of l.items)m.files=m.files?.filter((p,u,g)=>u===g.findIndex(w=>w.path===p.path)),m.dependencies&&(m.dependencies=m.dependencies.filter((p,u,g)=>u===g.findIndex(w=>w===p)));for(let m of l.items){if(!m.files)continue;c.start(`Building ${m.name}...`),m.$schema="https://ui.shadcn.com/schema/registry-item.json";for(let u of m.files){let g=W.resolve(i.cwd,u.path);try{if(!(await z.stat(g)).isFile())continue;u.content=await z.readFile(g,"utf-8");}catch(w){console.error("Error reading file in registry build:",g,w);continue}}let p=g$1.safeParse(m);if(!p.success){s.error(`Invalid registry item found for ${d.info(m.name)}.`);continue}await z.writeFile(W.resolve(i.outputDir,`${p.data.name}.json`),JSON.stringify(p.data,null,2));}if(await z.copyFile(i.registryFile,W.resolve(i.outputDir,"registry.json")),c.succeed("Building registry."),e.verbose){t(`The registry has ${d.info(l.items.length.toString())} items:`).succeed();for(let m of l.items){s.log(`  - ${m.name} (${d.info(m.type)})`);for(let p of m.files??[])s.log(`    - ${p.path}`);}}}catch(e){s.break(),Y(e);}}async function po(t,e,r){for(let i of t.items)if(i.files?.length)for(let o of i.files){let n=await M(o.path,e,r);n.files=n.files?.filter(a=>a.path!==o.path),n.files&&i.files.push(...n.files),n.dependencies&&(i.dependencies=i.dependencies?i.dependencies.concat(n.dependencies):n.dependencies);}return t}var Cr=new Command().name("registry:mcp").description("starts the registry MCP server [DEPRECATED]").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).action(async()=>{s.warn(`The ${d.info("shadcn registry:mcp")} command is deprecated. Use the ${d.info("shadcn mcp")} command instead.`),s.break();});var ho=z$1.object({cwd:z$1.string(),query:z$1.string().optional(),limit:z$1.number().optional(),offset:z$1.number().optional()}),Pr=new Command().name("search").alias("list").description("search items from registries").argument("<registries...>","the registry names or urls to search items from. Names must be prefixed with @.").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).option("-q, --query <query>","query string").option("-l, --limit <number>","maximum number of items to display per registry","100").option("-o, --offset <number>","number of items to skip","0").action(async(t,e)=>{try{let r=ho.parse({cwd:W__default.resolve(e.cwd),query:e.query,limit:e.limit?parseInt(e.limit,10):void 0,offset:e.offset?parseInt(e.offset,10):void 0});await D(r.cwd);let i$1=n({style:"new-york",resolvedPaths:{cwd:r.cwd}}),o=O(i$1),n$1=W__default.resolve(r.cwd,"components.json");if(A.existsSync(n$1)){let m=await A.readJson(n$1),p$1=p.partial().parse(m);o=O({...i$1,...p$1});}let a=o;try{let m=await i(r.cwd);m&&(a=O(m));}catch{}let{config:s,newRegistries:c}=await L(t.map(m=>`${m}/registry`),a,{silent:!0,writeFile:!1});c.length>0&&(a.registries=s.registries),Q(t,a);let l=await ka(t,{query:r.query,limit:r.limit,offset:r.offset,config:a});console.log(JSON.stringify(l,null,2)),process.exit(0);}catch(r){Y(r);}finally{P();}});var yo=z$1.object({cwd:z$1.string()}),$r=new Command().name("view").description("view items from the registry").argument("<items...>","the item names or URLs to view").option("-c, --cwd <cwd>","the working directory. defaults to the current directory.",process.cwd()).action(async(t,e)=>{try{let r=yo.parse({cwd:W__default.resolve(e.cwd)});await D(r.cwd);let i$1=O({}),o=W__default.resolve(r.cwd,"components.json");if(A.existsSync(o)){let l=await A.readJson(o),m=p.partial().parse(l);i$1=O(m);}let n=i$1;try{let l=await i(r.cwd);l&&(n=O(l));}catch{}let{config:a,newRegistries:s}=await L(t,n,{silent:!0,writeFile:!1});s.length>0&&(n.registries=a.registries),Q(t,n);let c=await _$1(t,{config:n});console.log(JSON.stringify(c,null,2)),process.exit(0);}catch(r){Y(r);}finally{P();}});var Nr={version:"3.5.0"};process.on("SIGINT",()=>process.exit(0));process.on("SIGTERM",()=>process.exit(0));async function xo(){let t=new Command().name("shadcn").description("add items from registries to your project").version(Nr.version,"-v, --version","display the version number");t.addCommand(Xt).addCommand(or).addCommand(fr).addCommand($r).addCommand(Pr).addCommand(xr).addCommand(mr).addCommand(cr).addCommand(rt),t.addCommand(Sr).addCommand(Cr),t.parse();}xo();//# sourceMappingURL=index.js.map
//# sourceMappingURL=index.js.map