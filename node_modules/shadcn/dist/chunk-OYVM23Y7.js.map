{"version": 3, "sources": ["../src/registry/constants.ts", "../src/registry/env.ts", "../src/registry/errors.ts", "../src/registry/parser.ts", "../src/utils/frameworks.ts", "../src/utils/highlighter.ts", "../src/utils/resolve-import.ts", "../src/utils/get-config.ts", "../src/utils/get-package-info.ts", "../src/utils/get-project-info.ts", "../src/utils/compare.ts", "../src/utils/env-helpers.ts", "../src/utils/logger.ts", "../src/utils/spinner.ts", "../src/utils/transformers/transform-css-vars.ts", "../src/utils/icon-libraries.ts", "../src/utils/transformers/transform-icons.ts", "../src/utils/transformers/transform-import.ts", "../src/utils/transformers/transform-jsx.ts", "../src/utils/transformers/transform-rsc.ts", "../src/utils/transformers/transform-tw-prefix.ts", "../src/utils/transformers/index.ts", "../src/utils/transformers/transform-next.ts", "../src/utils/updaters/update-files.ts", "../src/registry/utils.ts", "../src/registry/config.ts", "../src/registry/context.ts", "../src/registry/validator.ts", "../src/registry/builder.ts", "../src/registry/fetcher.ts", "../src/utils/updaters/update-tailwind-config.ts", "../src/registry/resolver.ts", "../src/utils/handle-error.ts", "../src/registry/api.ts", "../src/registry/search.ts"], "names": ["REGISTRY_URL", "FALLBACK_STYLE", "BASE_COLORS", "BUILTIN_REGISTRIES", "DEPRECATED_COMPONENTS", "expandEnvVars", "value", "_match", "key", "extractEnvVars", "vars", "regex", "match", "RegistryErrorCode", "RegistryError", "message", "options", "RegistryNotFoundError", "url", "cause", "RegistryUnauthorizedError", "RegistryForbiddenError", "RegistryFetchError", "statusCode", "responseBody", "baseMessage", "suggestion", "RegistryNotConfiguredError", "registryName", "RegistryLocalFileError", "filePath", "RegistryParseError", "item", "parseError", "z", "e", "RegistryMissingEnvironmentVariablesError", "missingVars", "v", "RegistryInvalidNamespaceError", "name", "ConfigParseError", "cwd", "RegistriesIndexParseError", "invalidNamespaces", "arr", "REGISTRY_PATTERN", "parseRegistryAndItemFromString", "FRAMEWORKS", "highlighter", "red", "yellow", "cyan", "green", "resolveImport", "importPath", "config", "createMatchPath", "DEFAULT_COMPONENTS", "DEFAULT_UTILS", "DEFAULT_TAILWIND_CSS", "DEFAULT_TAILWIND_CONFIG", "explorer", "cosmiconfig", "getConfig", "getRawConfig", "resolveConfigPaths", "tsConfig", "loadConfig", "configSchema", "path", "config<PERSON><PERSON><PERSON>", "rawConfigSchema", "error", "componentPath", "getWorkspaceConfig", "resolvedAliases", "is<PERSON>lias<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "packageRoot", "findPackageRoot", "result", "workspaceConfigSchema", "commonRoot", "findCommonRoot", "relativePath", "matchingPackageRoot", "fg", "pkgPath", "pkgDir", "parts1", "parts2", "commonParts", "getTargetStyleFromConfig", "fallback", "getProjectInfo", "createConfig", "partial", "defaultConfig", "getPackageInfo", "shouldThrow", "packageJsonPath", "fs", "PROJECT_SHARED_IGNORE", "configFiles", "isSrcDir", "isTsx", "tailwindConfigFile", "tailwindCssFile", "tailwindVersion", "aliasPrefix", "packageJson", "isTypeScriptProject", "getTailwindConfigFile", "getTailwindCssFile", "getTailwindVersion", "getTsConfigAliasPrefix", "isUsingAppDir", "type", "file", "getFrameworkVersion", "dep", "appConfig", "framework", "version", "versionMatch", "rangeMatch", "packageInfo", "files", "contents", "alias", "paths", "getProjectConfig", "defaultProjectInfo", "existingConfig", "projectInfo", "getProjectTailwindVersionFromConfig", "isContentSame", "existingContent", "newContent", "ignoreImports", "normalizedExisting", "normalizedNew", "importRegex", "normalizeImports", "content", "prefix", "suffix", "parts", "lastPart", "existingNormalized", "newNormalized", "isEnvFile", "fileName", "findExistingEnvFile", "targetDir", "variants", "variant", "existsSync", "parseEnvContent", "lines", "env", "line", "trimmed", "equalIndex", "getNewEnvKeys", "existingEnv", "newEnv", "newKeys", "mergeEnvContent", "logger", "args", "spinner", "text", "ora", "transformCssVars", "sourceFile", "baseColor", "SyntaxKind", "node", "raw", "mapped", "applyColorMapping", "splitClassName", "className", "rest", "alpha", "split", "PREFIXES", "input", "mapping", "classNames", "lightMode", "darkMode", "modifier", "needle", "ICON_LIBRARIES", "SOURCE_LIBRARY", "transformIcons", "registryIcons", "getRegistryIcons", "sourceLibrary", "targetLibrary", "targetedIcons", "importDeclaration", "specifier", "iconName", "targetedIcon", "iconImportDeclaration", "icon", "_useSemicolon", "transformImport", "isRemote", "utilsImport", "updated", "updateImportAliases", "namedImport", "moduleSpecifier", "PARSE_OPTIONS", "transformJsx", "output", "ast", "code", "parse", "transformFromAstSync", "transformTypescript", "directiveRegex", "transformRsc", "first", "transformTwPrefixes", "defaultClassNames", "applyPrefix", "callExpression", "arg", "project", "Project", "createTempSourceFile", "filename", "dir", "tmpdir", "transform", "opts", "transformers", "tempFile", "ScriptKind", "transformer", "transformNext", "func", "variable", "exportDecl", "namedExport", "updateFiles", "filesCreatedSpinner", "getRegistryBaseColor", "filesCreated", "filesUpdated", "filesSkipped", "envVarsAdded", "envFile", "index", "resolve<PERSON><PERSON><PERSON><PERSON>", "f", "basename", "alternativeEnvFile", "existingFile", "statSync", "_isNext16Middleware", "existingFileContent", "overwrite", "prompts", "mergedContent", "allFiles", "updatedFiles", "resolveImports", "target", "resolvePageTarget", "resolveFileTargetDirectory", "resolveNestedFilePath", "normalizedPaths", "p", "normalizedNeedle", "needleDir", "needleSegments", "i", "testPath", "normalizedFilePath", "normalizedTargetDir", "fileSegments", "targetSegments", "lastTargetSegment", "commonDirIndex", "segment", "filePaths", "filepath", "importDeclarations", "probableImportFilePath", "resolvedImportFilePath", "resolveModuleByProbablePath", "newImport", "toAliasedImport", "extensions", "relativeFiles", "fileSet", "extInPath", "hasExt", "absBase", "relBase", "tryExts", "candidates", "absCand", "relCand", "absIdx", "relIdx", "a", "b", "aExt", "bExt", "ord", "aStrong", "bStrong", "abs", "matches", "root", "<PERSON><PERSON><PERSON>", "rootDir", "rel", "ext", "keepExt", "noExt", "alias<PERSON>ase", "isRootMiddleware", "isNextJs", "majorVersion", "FILE_EXTENSIONS_FOR_LOOKUP", "FILE_PATH_SKIP_LIST", "DEPENDENCY_SKIP_LIST", "getDependencyFromModuleSpecifier", "pattern", "recursivelyResolveFileImports", "processedFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "P", "relativeRegistryFile<PERSON>ath", "fileExtension", "dependencies", "fileType", "determineFileType", "originalFile", "importStatements", "importStatement", "isRelativeImport", "dependency", "pathWithExt", "nestedRelativeRegistryFilePath", "nestedResults", "uniqueFiles", "isUrl", "isLocalFile", "isUniversalRegistryItem", "registryItem", "deduplicateFilesByTarget", "filesArrays", "canDeduplicateFiles", "registryItemFileSchema", "targetMap", "resolveStyleFromConfig", "configWithDefaults", "baseConfig", "deepmerge", "context", "setRegistryHeaders", "headers", "getRegistryHeadersFromContext", "clearRegistryContext", "extractEnvVarsFromRegistryConfig", "validateRegistryConfig", "missing", "validateRegistryConfigForItems", "items", "buildUrlAndHeadersForRegistryItem", "NAME_PLACEHOLDER", "STYLE_PLACEHOLDER", "ENV_VAR_PATTERN", "QUERY_PARAM_SEPARATOR", "QUERY_PARAM_DELIMITER", "registry", "registryConfig", "buildUrlFromRegistryConfig", "buildHeadersFromRegistryConfig", "baseUrl", "appendQueryParams", "expandedValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "urlParams", "queryString", "separator", "originalValue", "trimmedExpanded", "templateWithoutVars", "resolveRegistryUrl", "pathOrUrl", "agent", "HttpsProxyAgent", "registryCache", "fetchRegistry", "fetchPromise", "response", "fetch", "messageFromServer", "json", "parsed", "fetchRegistryLocal", "expandedPath", "homedir", "registryItemSchema", "updateTailwindConfig", "tailwindConfig", "tailwindFileRelativePath", "tailwindSpinner", "transformTailwindConfig", "_createSourceFile", "configObject", "property", "quoteChar", "_getQuoteChar", "addTailwindConfigProperty", "plugin", "addTailwindConfigPlugin", "addTailwindConfigTheme", "existingProperty", "newProperty", "initializer", "newValue", "initializerText", "element", "theme", "nestSpreadProperties", "themeInitializer", "themeObjectString", "themeObject", "parseObjectLiteral", "dst", "src", "resultString", "objectToString", "unnestSpreadProperties", "existingPlugins", "Quo<PERSON><PERSON><PERSON>", "obj", "properties", "prop", "spreadAssignment", "spreadText", "nestSpreadElements", "elements", "j", "propAssignment", "unsetSpreadElements", "spreadTest", "objectLiteralString", "statement", "parseObjectLiteralExpression", "parseArrayLiteralExpression", "parseValue", "buildTailwindThemeColorsFromCssVars", "cssVars", "colorName", "subType", "resolveRegistryItemsFromRegistries", "registryHeaders", "resolvedItems", "resolved", "fetchRegistryItems", "resolveRegistryTree", "names", "payload", "allDependencyItems", "allDependencyRegistryNames", "uniqueNames", "results", "resultMap", "sourceName", "itemWithSource", "resolvedDependencies", "namespacedDeps", "registryNames", "resolveDependenciesRecursively", "uniqueRegistryNames", "nonNamespacedItems", "namespacedDepItems", "depResults", "getShadcnRegistryIndex", "registryUrls", "itemDependencies", "resolveRegistryDependencies", "uniqueUrls", "registryPayload", "registryGetTheme", "sourceMap", "source", "topologicalSortRegistryItems", "tailwind", "css", "docs", "envVars", "deduplicatedFiles", "registryResolvedItemsTreeSchema", "visited", "resolvedDeps", "nested", "style", "urls", "computeItemHash", "identifier", "hash", "createHash", "extractItemIdentifierFromDependency", "pathname", "itemMap", "hashToItem", "inDegree", "adjacencyList", "depToHashes", "itemSource", "itemHash", "depHash", "exactMatches", "nameMatches", "queue", "sorted", "degree", "currentHash", "dependentHash", "newDegree", "sortedHashes", "handleError", "getRegistry", "useCache", "registrySchema", "urlAndHeaders", "getRegistryItems", "resolveRegistryItems", "getRegistriesConfig", "registriesConfig", "registryConfigSchema", "registryIndexSchema", "getRegistryStyles", "stylesSchema", "iconsSchema", "getRegistryBaseColors", "registryBaseColorSchema", "resolveTree", "tree", "entry", "component", "self", "c", "fetchTree", "getItemTargetPath", "override", "parent", "getRegistriesIndex", "data", "registriesIndexSchema", "searchRegistries", "registries", "query", "limit", "offset", "allItems", "itemsWithRegistry", "buildRegistryItemNameFromRegistry", "searchItems", "paginationOffset", "paginationLimit", "totalItems", "searchResultsSchema", "searchableItemSchema", "fuzzysort", "string", "protocolEnd", "hostEnd", "queryStart", "before<PERSON><PERSON><PERSON>", "updatedQuery", "hostPart", "pathAndQuery", "pathEnd", "pathOnly", "queryAndAfter", "lastIndex", "updatedPath"], "mappings": "06BAGO,IAAMA,EAAAA,CACX,QAAQ,GAAA,CAAI,YAAA,EAAgB,yBAAA,CAEjBC,EAAAA,CAAiB,aAAA,CAEjBC,EAAAA,CAAc,CACzB,CACE,IAAA,CAAM,SAAA,CACN,KAAA,CAAO,SACT,CAAA,CACA,CACE,IAAA,CAAM,MAAA,CACN,KAAA,CAAO,MACT,CAAA,CACA,CACE,KAAM,MAAA,CACN,KAAA,CAAO,MACT,CAAA,CACA,CACE,IAAA,CAAM,QACN,KAAA,CAAO,OACT,CAAA,CACA,CACE,IAAA,CAAM,OAAA,CACN,MAAO,OACT,CACF,CAAA,CAGaC,CAAAA,CAA2D,CACtE,SAAA,CAAW,GAAGH,EAAY,CAAA,2BAAA,CAC5B,EA8HO,IAAMI,EAAAA,CAAwB,CACnC,CACE,IAAA,CAAM,OAAA,CACN,YAAA,CAAc,QAAA,CACd,OAAA,CACE,sEACJ,EACA,CACE,IAAA,CAAM,SAAA,CACN,YAAA,CAAc,QAAA,CACd,OAAA,CACE,wEACJ,CACF,EC7KO,SAASC,EAAAA,CAAcC,CAAAA,CAAe,CAC3C,OAAOA,CAAAA,CAAM,OAAA,CAAQ,YAAA,CAAc,CAACC,CAAAA,CAAQC,CAAAA,GAAQ,QAAQ,GAAA,CAAIA,CAAG,CAAA,EAAK,EAAE,CAC5E,CAEO,SAASC,EAAAA,CAAeH,CAAAA,CAAe,CAC5C,IAAMI,CAAAA,CAAiB,GACjBC,CAAAA,CAAQ,YAAA,CACVC,CAAAA,CAEJ,KAAA,CAAQA,CAAAA,CAAQD,CAAAA,CAAM,KAAKL,CAAK,CAAA,IAAO,IAAA,EACrCI,CAAAA,CAAK,IAAA,CAAKE,CAAAA,CAAM,CAAC,CAAC,CAAA,CAGpB,OAAOF,CACT,CCXO,IAAMG,CAAAA,CAAoB,CAG/B,SAAA,CAAW,WAAA,CACX,YAAA,CAAc,cAAA,CACd,SAAA,CAAW,WAAA,CACX,YAAa,aAAA,CAGb,cAAA,CAAgB,gBAAA,CAChB,cAAA,CAAgB,gBAAA,CAChB,gBAAA,CAAkB,mBAGlB,gBAAA,CAAkB,kBAAA,CAGlB,WAAA,CAAa,aAAA,CACb,gBAAA,CAAkB,kBAAA,CAGlB,cAAe,eACjB,CAAA,CAKaC,CAAAA,CAAN,cAA4B,KAAM,CACvB,KACA,UAAA,CACA,OAAA,CACA,UAAA,CACA,SAAA,CACA,KAAA,CAEhB,WAAA,CACEC,EACAC,CAAAA,CAMI,EAAC,CACL,CACA,KAAA,CAAMD,CAAO,EACb,IAAA,CAAK,IAAA,CAAO,eAAA,CACZ,IAAA,CAAK,IAAA,CAAOC,CAAAA,CAAQ,MAAQH,CAAAA,CAAkB,aAAA,CAC9C,IAAA,CAAK,UAAA,CAAaG,CAAAA,CAAQ,UAAA,CAC1B,KAAK,KAAA,CAAQA,CAAAA,CAAQ,KAAA,CACrB,IAAA,CAAK,OAAA,CAAUA,CAAAA,CAAQ,QACvB,IAAA,CAAK,UAAA,CAAaA,CAAAA,CAAQ,UAAA,CAC1B,IAAA,CAAK,SAAA,CAAY,IAAI,IAAA,CAEjB,KAAA,CAAM,iBAAA,EACR,KAAA,CAAM,iBAAA,CAAkB,IAAA,CAAM,KAAK,WAAW,EAElD,CAEA,MAAA,EAAS,CACP,OAAO,CACL,IAAA,CAAM,IAAA,CAAK,IAAA,CACX,OAAA,CAAS,IAAA,CAAK,OAAA,CACd,KAAM,IAAA,CAAK,IAAA,CACX,UAAA,CAAY,IAAA,CAAK,UAAA,CACjB,OAAA,CAAS,KAAK,OAAA,CACd,UAAA,CAAY,KAAK,UAAA,CACjB,SAAA,CAAW,KAAK,SAAA,CAChB,KAAA,CAAO,IAAA,CAAK,KACd,CACF,CACF,EAEaC,CAAAA,CAAN,cAAoCH,CAAc,CACvD,WAAA,CAA4BI,CAAAA,CAAaC,EAAiB,CACxD,IAAMJ,CAAAA,CAAU,CAAA,YAAA,EAAeG,CAAG,CAAA,iDAAA,CAAA,CAElC,MAAMH,CAAAA,CAAS,CACb,IAAA,CAAMF,CAAAA,CAAkB,SAAA,CACxB,UAAA,CAAY,IACZ,KAAA,CAAAM,CAAAA,CACA,OAAA,CAAS,CAAE,GAAA,CAAAD,CAAI,EACf,UAAA,CACE,uEACJ,CAAC,CAAA,CAVyB,IAAA,CAAA,GAAA,CAAAA,CAAAA,CAW1B,KAAK,IAAA,CAAO,wBACd,CACF,CAAA,CAEaE,EAAAA,CAAN,cAAwCN,CAAc,CAC3D,WAAA,CAA4BI,CAAAA,CAAaC,CAAAA,CAAiB,CACxD,IAAMJ,EAAU,CAAA,6CAAA,EAAgDG,CAAG,CAAA,6DAAA,CAAA,CAEnE,KAAA,CAAMH,CAAAA,CAAS,CACb,KAAMF,CAAAA,CAAkB,YAAA,CACxB,UAAA,CAAY,GAAA,CACZ,KAAA,CAAAM,CAAAA,CACA,QAAS,CAAE,GAAA,CAAAD,CAAI,CAAA,CACf,UAAA,CACE,kEACJ,CAAC,CAAA,CAVyB,IAAA,CAAA,GAAA,CAAAA,CAAAA,CAW1B,IAAA,CAAK,IAAA,CAAO,4BACd,CACF,CAAA,CAEaG,EAAAA,CAAN,cAAqCP,CAAc,CACxD,WAAA,CAA4BI,EAAaC,CAAAA,CAAiB,CACxD,IAAMJ,CAAAA,CAAU,CAAA,6CAAA,EAAgDG,CAAG,gEAEnE,KAAA,CAAMH,CAAAA,CAAS,CACb,IAAA,CAAMF,CAAAA,CAAkB,SAAA,CACxB,WAAY,GAAA,CACZ,KAAA,CAAAM,CAAAA,CACA,OAAA,CAAS,CAAE,GAAA,CAAAD,CAAI,CAAA,CACf,UAAA,CACE,kEACJ,CAAC,CAAA,CAVyB,IAAA,CAAA,GAAA,CAAAA,EAW1B,IAAA,CAAK,IAAA,CAAO,yBACd,CACF,CAAA,CAEaI,EAAAA,CAAN,cAAiCR,CAAc,CACpD,WAAA,CACkBI,CAAAA,CAChBK,CAAAA,CACgBC,CAAAA,CAChBL,EACA,CAEA,IAAMM,CAAAA,CAAcF,CAAAA,CAChB,CAAA,+BAAA,EAAkCA,CAAU,MAAML,CAAG,CAAA,CAAA,CACrD,CAAA,+BAAA,EAAkCA,CAAG,CAAA,CAAA,CAEnCH,CAAAA,CACJ,OAAOI,CAAAA,EAAU,QAAA,EAAYA,CAAAA,CACzB,CAAA,EAAGM,CAAW,CAAA,GAAA,EAAMN,CAAK,CAAA,CAAA,CACzBM,CAAAA,CAEFC,CAAAA,CAAa,8CAAA,CACbH,CAAAA,GAAe,GAAA,CACjBG,EACE,mEAAA,CACOH,CAAAA,GAAe,GAAA,CACxBG,CAAAA,CAAa,4DAAA,CACJH,CAAAA,EAAcA,GAAc,GAAA,EAAOA,CAAAA,CAAa,GAAA,GACzDG,CAAAA,CAAa,0DAAA,CAAA,CAGf,KAAA,CAAMX,EAAS,CACb,IAAA,CAAMF,CAAAA,CAAkB,WAAA,CACxB,UAAA,CAAAU,CAAAA,CACA,MAAAJ,CAAAA,CACA,OAAA,CAAS,CAAE,GAAA,CAAAD,CAAAA,CAAK,YAAA,CAAAM,CAAa,CAAA,CAC7B,UAAA,CAAAE,CACF,CAAC,CAAA,CA/Be,IAAA,CAAA,GAAA,CAAAR,EAEA,IAAA,CAAA,YAAA,CAAAM,CAAAA,CA8BhB,IAAA,CAAK,IAAA,CAAO,qBACd,CACF,EAEaG,CAAAA,CAAN,cAAyCb,CAAc,CAC5D,WAAA,CAA4Bc,CAAAA,CAA6B,CACvD,IAAMb,CAAAA,CAAUa,CAAAA,CACZ,CAAA,kBAAA,EAAqBA,CAAY,CAAA;AAAA;AAAA;AAAA,KAAA,EAGlCA,CAAY,CAAA;AAAA;AAAA,CAAA,CAAA,CAGX,kFAAA,CAEJ,KAAA,CAAMb,CAAAA,CAAS,CACb,KAAMF,CAAAA,CAAkB,cAAA,CACxB,OAAA,CAAS,CAAE,aAAAe,CAAa,CAAA,CACxB,UAAA,CACE,yHACJ,CAAC,CAAA,CAfyB,IAAA,CAAA,YAAA,CAAAA,CAAAA,CAgB1B,IAAA,CAAK,KAAO,6BACd,CACF,CAAA,CAEaC,CAAAA,CAAN,cAAqCf,CAAc,CACxD,WAAA,CAA4BgB,CAAAA,CAAkBX,EAAiB,CAC7D,KAAA,CAAM,CAAA,oCAAA,EAAuCW,CAAQ,GAAI,CACvD,IAAA,CAAMjB,CAAAA,CAAkB,gBAAA,CACxB,MAAAM,CAAAA,CACA,OAAA,CAAS,CAAE,QAAA,CAAAW,CAAS,CAAA,CACpB,UAAA,CAAY,yDACd,CAAC,EANyB,IAAA,CAAA,QAAA,CAAAA,CAAAA,CAO1B,IAAA,CAAK,IAAA,CAAO,yBACd,CACF,CAAA,CAEaC,CAAAA,CAAN,cAAiCjB,CAAc,CAGpD,WAAA,CAA4BkB,CAAAA,CAAcC,CAAAA,CAAqB,CAC7D,IAAIlB,CAAAA,CAAU,CAAA,+BAAA,EAAkCiB,CAAI,GAEhDC,CAAAA,YAAsBC,CAAAA,CAAE,QAAA,GAC1BnB,CAAAA,CAAU,kCAAkCiB,CAAI;AAAA,EAAKC,CAAAA,CAAW,MAAA,CAC7D,GAAA,CAAKE,CAAAA,EAAM,OAAOA,CAAAA,CAAE,IAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAA,EAAA,EAAKA,CAAAA,CAAE,OAAO,CAAA,CAAE,EAClD,IAAA,CAAK;AAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAGf,KAAA,CAAMpB,EAAS,CACb,IAAA,CAAMF,EAAkB,WAAA,CACxB,KAAA,CAAOoB,CAAAA,CACP,OAAA,CAAS,CAAE,IAAA,CAAAD,CAAK,EAChB,UAAA,CACE,qKACJ,CAAC,CAAA,CAfyB,IAAA,CAAA,IAAA,CAAAA,CAAAA,CAiB1B,IAAA,CAAK,WAAaC,CAAAA,CAClB,IAAA,CAAK,KAAO,qBACd,CArBgB,UAsBlB,CAAA,CAEaG,EAAAA,CAAN,cAAuDtB,CAAc,CAC1E,YACkBc,CAAAA,CACAS,CAAAA,CAChB,CACA,IAAMtB,CAAAA,CACJ,aAAaa,CAAY,CAAA;;AAAA,CAAA,CACzBS,EAAY,GAAA,CAAKC,CAAAA,EAAM,YAAOA,CAAC,CAAA,CAAE,EAAE,IAAA,CAAK;AAAA,CAAI,EAE9C,KAAA,CAAMvB,CAAAA,CAAS,CACb,IAAA,CAAMF,EAAkB,gBAAA,CACxB,OAAA,CAAS,CAAE,YAAA,CAAAe,EAAc,WAAA,CAAAS,CAAY,EACrC,UAAA,CACE,yEACJ,CAAC,CAAA,CAZe,IAAA,CAAA,YAAA,CAAAT,CAAAA,CACA,IAAA,CAAA,WAAA,CAAAS,EAYhB,IAAA,CAAK,IAAA,CAAO,2CACd,CACF,EAEaE,EAAAA,CAAN,cAA4CzB,CAAc,CAC/D,YAA4B0B,CAAAA,CAAc,CACxC,IAAMzB,CAAAA,CAAU,CAAA,6BAAA,EAAgCyB,CAAI,CAAA,yDAAA,CAAA,CAEpD,KAAA,CAAMzB,CAAAA,CAAS,CACb,KAAMF,CAAAA,CAAkB,gBAAA,CACxB,OAAA,CAAS,CAAE,KAAA2B,CAAK,CAAA,CAChB,UAAA,CACE,oFACJ,CAAC,CAAA,CARyB,IAAA,CAAA,IAAA,CAAAA,EAS1B,IAAA,CAAK,IAAA,CAAO,gCACd,CACF,EAgBO,IAAMC,EAAAA,CAAN,cAA+B3B,CAAc,CAClD,WAAA,CAA4B4B,CAAAA,CAAaT,EAAqB,CAC5D,IAAIlB,CAAAA,CAAU,CAAA,yCAAA,EAA4C2B,CAAG,CAAA,CAAA,CAAA,CAEzDT,CAAAA,YAAsBC,EAAE,QAAA,GAC1BnB,CAAAA,CAAU,4CAA4C2B,CAAG,CAAA;AAAA,EAAMT,CAAAA,CAAW,MAAA,CACvE,GAAA,CAAKE,CAAAA,EAAM,OAAOA,CAAAA,CAAE,IAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAA,EAAA,EAAKA,CAAAA,CAAE,OAAO,CAAA,CAAE,EAClD,IAAA,CAAK;AAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAGf,KAAA,CAAMpB,CAAAA,CAAS,CACb,KAAMF,CAAAA,CAAkB,cAAA,CACxB,KAAA,CAAOoB,CAAAA,CACP,QAAS,CAAE,GAAA,CAAAS,CAAI,CAAA,CACf,UAAA,CACE,+IACJ,CAAC,CAAA,CAfyB,IAAA,CAAA,GAAA,CAAAA,CAAAA,CAgB1B,KAAK,IAAA,CAAO,mBACd,CACF,CAAA,CAEaC,GAAN,cAAwC7B,CAAc,CAC3C,UAAA,CAEhB,YAAYmB,CAAAA,CAAqB,CAC/B,IAAIlB,CAAAA,CAAU,kCAAA,CAEd,GAAIkB,CAAAA,YAAsBC,CAAAA,CAAE,QAAA,CAAU,CACpC,IAAMU,CAAAA,CAAoBX,CAAAA,CAAW,MAAA,CAClC,MAAA,CAAQE,GAAMA,CAAAA,CAAE,IAAA,CAAK,MAAA,CAAS,CAAC,EAC/B,GAAA,CAAKA,CAAAA,EAAM,IAAIA,CAAAA,CAAE,IAAA,CAAK,CAAC,CAAC,CAAA,CAAA,CAAG,CAAA,CAC3B,MAAA,CAAO,CAACG,CAAAA,CAAG,CAAA,CAAGO,IAAQA,CAAAA,CAAI,OAAA,CAAQP,CAAC,CAAA,GAAM,CAAC,CAAA,CAEzCM,CAAAA,CAAkB,OAAS,CAAA,CAC7B7B,CAAAA,CAAU,oEAAoE6B,CAAAA,CAAkB,IAAA,CAC9F,IACF,CAAC;AAAA,EAAKX,CAAAA,CAAW,MAAA,CACd,GAAA,CAAKE,CAAAA,EAAM,OAAOA,CAAAA,CAAE,IAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAA,EAAA,EAAKA,CAAAA,CAAE,OAAO,CAAA,CAAE,EAClD,IAAA,CAAK;AAAA,CAAI,CAAC,GAEbpB,CAAAA,CAAU,CAAA;AAAA,EAAsCkB,CAAAA,CAAW,MAAA,CACxD,GAAA,CAAKE,CAAAA,EAAM,OAAOA,CAAAA,CAAE,IAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAA,EAAA,EAAKA,CAAAA,CAAE,OAAO,CAAA,CAAE,EAClD,IAAA,CAAK;AAAA,CAAI,CAAC,GAEjB,CAEA,KAAA,CAAMpB,EAAS,CACb,IAAA,CAAMF,CAAAA,CAAkB,WAAA,CACxB,KAAA,CAAOoB,CAAAA,CACP,QAAS,CAAE,UAAA,CAAAA,CAAW,CAAA,CACtB,UAAA,CACE,8IACJ,CAAC,CAAA,CAED,IAAA,CAAK,UAAA,CAAaA,CAAAA,CAClB,IAAA,CAAK,IAAA,CAAO,4BACd,CACF,ECnUA,IAAMa,EAAAA,CAAmB,sDAAA,CAElB,SAASC,GAA+BP,CAAAA,CAAc,CAC3D,GAAI,CAACA,CAAAA,CAAK,UAAA,CAAW,GAAG,CAAA,CACtB,OAAO,CACL,QAAA,CAAU,IAAA,CACV,IAAA,CAAMA,CACR,CAAA,CAGF,IAAM5B,CAAAA,CAAQ4B,CAAAA,CAAK,KAAA,CAAMM,EAAgB,EACzC,OAAIlC,CAAAA,CACK,CACL,QAAA,CAAUA,CAAAA,CAAM,CAAC,EACjB,IAAA,CAAMA,CAAAA,CAAM,CAAC,CACf,CAAA,CAGK,CACL,SAAU,IAAA,CACV,IAAA,CAAM4B,CACR,CACF,CCvBO,IAAMQ,CAAAA,CAAa,CACxB,UAAA,CAAY,CACV,IAAA,CAAM,UAAA,CACN,KAAA,CAAO,SAAA,CACP,MAAO,CACL,YAAA,CAAc,8CAAA,CACd,QAAA,CAAU,4CACZ,CACF,EACA,YAAA,CAAc,CACZ,IAAA,CAAM,YAAA,CACN,KAAA,CAAO,SAAA,CACP,MAAO,CACL,YAAA,CAAc,8CAAA,CACd,QAAA,CAAU,4CACZ,CACF,EACA,KAAA,CAAO,CACL,IAAA,CAAM,OAAA,CACN,KAAA,CAAO,OAAA,CACP,MAAO,CACL,YAAA,CAAc,+CAAA,CACd,QAAA,CAAU,2CACZ,CACF,EACA,cAAA,CAAgB,CACd,IAAA,CAAM,cAAA,CACN,KAAA,CAAO,cAAA,CACP,MAAO,CACL,YAAA,CAAc,sDAAA,CACd,QAAA,CACE,yEACJ,CACF,EACA,IAAA,CAAM,CACJ,IAAA,CAAM,MAAA,CACN,KAAA,CAAO,MAAA,CACP,MAAO,CACL,YAAA,CAAc,8CAAA,CACd,QAAA,CAAU,0CACZ,CACF,EACA,KAAA,CAAO,CACL,IAAA,CAAM,OAAA,CACN,KAAA,CAAO,OAAA,CACP,MAAO,CACL,YAAA,CAAc,+CAAA,CACd,QAAA,CAAU,2CACZ,CACF,EACA,OAAA,CAAS,CACP,IAAA,CAAM,SAAA,CACN,KAAA,CAAO,SAAA,CACP,MAAO,CACL,YAAA,CAAc,iDAAA,CACd,QAAA,CAAU,6CACZ,CACF,EACA,gBAAA,CAAkB,CAChB,IAAA,CAAM,gBAAA,CACN,KAAA,CAAO,gBAAA,CACP,MAAO,CACL,YAAA,CAAc,kDAAA,CACd,QAAA,CAAU,yDACZ,CACF,EACA,MAAA,CAAQ,CACN,IAAA,CAAM,QAAA,CACN,KAAA,CAAO,QAAA,CACP,MAAO,CACL,YAAA,CAAc,gDAAA,CACd,QAAA,CAAU,4CACZ,CACF,EACA,IAAA,CAAM,CACJ,IAAA,CAAM,MAAA,CACN,KAAA,CAAO,MAAA,CACP,MAAO,CACL,YAAA,CAAc,+CACd,QAAA,CAAU,8DACZ,CACF,CAAA,CACA,MAAA,CAAQ,CACN,IAAA,CAAM,QAAA,CACN,KAAA,CAAO,SACP,KAAA,CAAO,CACL,YAAA,CAAc,gDAAA,CACd,QAAA,CAAU,2CACZ,CACF,CACF,CAAA,CCxFO,IAAMC,CAAAA,CAAc,CACzB,KAAA,CAAOC,GAAAA,CACP,KAAMC,MAAAA,CACN,IAAA,CAAMC,IAAAA,CACN,OAAA,CAASC,KACX,ECLA,eAAsBC,CAAAA,CACpBC,CAAAA,CACAC,EACA,CACA,OAAOC,eAAAA,CAAgBD,CAAAA,CAAO,eAAA,CAAiBA,CAAAA,CAAO,KAAK,CAAA,CACzDD,CAAAA,CACA,MAAA,CACA,IAAM,IAAA,CACN,CAAC,MAAO,MAAA,CAAQ,MAAA,CAAQ,KAAA,CAAO,MAAM,CACvC,CACF,CCIO,IAAMG,EAAAA,CAAqB,eACrBC,EAAAA,CAAgB,aAAA,CAChBC,EAAAA,CAAuB,iBAAA,CACvBC,EAAAA,CAA0B,qBAKhC,IAAMC,EAAAA,CAAWC,WAAAA,CAAY,YAAA,CAAc,CAChD,YAAA,CAAc,CAAC,iBAAiB,CAClC,CAAC,CAAA,CAID,eAAsBC,EAAAA,CAAUtB,CAAAA,CAAa,CAC3C,IAAMc,CAAAA,CAAS,MAAMS,EAAAA,CAAavB,CAAG,CAAA,CAErC,OAAKc,CAAAA,EAKAA,CAAAA,CAAO,WAAA,GACVA,CAAAA,CAAO,WAAA,CAAcA,CAAAA,CAAO,QAAU,UAAA,CAAa,OAAA,CAAU,QAAA,CAAA,CAGxD,MAAMU,EAAAA,CAAmBxB,CAAAA,CAAKc,CAAM,CAAA,EARlC,IASX,CAEA,eAAsBU,EAAAA,CACpBxB,CAAAA,CACAc,EACA,CAEAA,CAAAA,CAAO,UAAA,CAAa,CAClB,GAAGrD,CAAAA,CACH,GAAIqD,CAAAA,CAAO,UAAA,EAAc,EAC3B,CAAA,CAGA,IAAMW,EAAW,MAAMC,UAAAA,CAAW1B,CAAG,CAAA,CAErC,GAAIyB,CAAAA,CAAS,aAAe,QAAA,CAC1B,MAAM,IAAI,KAAA,CACR,CAAA,eAAA,EAAkBX,CAAAA,CAAO,IAAM,UAAA,CAAa,UAAU,CAAA,OAAA,EACpDW,CAAAA,CAAS,OAAA,EAAW,EACtB,GAAG,IAAA,EACL,CAAA,CAGF,OAAOE,GAAAA,CAAa,KAAA,CAAM,CACxB,GAAGb,CAAAA,CACH,aAAA,CAAe,CACb,GAAA,CAAAd,CAAAA,CACA,eAAgBc,CAAAA,CAAO,QAAA,CAAS,MAAA,CAC5Bc,UAAAA,CAAK,OAAA,CAAQ5B,CAAAA,CAAKc,EAAO,QAAA,CAAS,MAAM,CAAA,CACxC,EAAA,CACJ,WAAA,CAAac,UAAAA,CAAK,QAAQ5B,CAAAA,CAAKc,CAAAA,CAAO,QAAA,CAAS,GAAG,CAAA,CAClD,KAAA,CAAO,MAAMF,CAAAA,CAAcE,CAAAA,CAAO,OAAA,CAAQ,KAAA,CAAUW,CAAQ,CAAA,CAC5D,WAAY,MAAMb,CAAAA,CAAcE,CAAAA,CAAO,OAAA,CAAQ,UAAA,CAAeW,CAAQ,EACtE,EAAA,CAAIX,CAAAA,CAAO,QAAQ,EAAA,CACf,MAAMF,EAAcE,CAAAA,CAAO,OAAA,CAAQ,EAAA,CAAOW,CAAQ,CAAA,CAClDG,UAAAA,CAAK,QACF,MAAMhB,CAAAA,CAAcE,CAAAA,CAAO,OAAA,CAAQ,UAAA,CAAeW,CAAQ,GACzDzB,CAAAA,CACF,IACF,CAAA,CAGJ,GAAA,CAAKc,CAAAA,CAAO,OAAA,CAAQ,IAChB,MAAMF,CAAAA,CAAcE,CAAAA,CAAO,OAAA,CAAQ,GAAA,CAAQW,CAAQ,EACnDG,UAAAA,CAAK,OAAA,CACF,MAAMhB,CAAAA,CAAcE,CAAAA,CAAO,OAAA,CAAQ,MAAUW,CAAQ,CAAA,EAAMzB,CAAAA,CAC5D,IACF,CAAA,CACJ,KAAA,CAAOc,EAAO,OAAA,CAAQ,KAAA,CAClB,MAAMF,CAAAA,CAAcE,CAAAA,CAAO,OAAA,CAAQ,MAAUW,CAAQ,CAAA,CACrDG,UAAAA,CAAK,OAAA,CACF,MAAMhB,CAAAA,CAAcE,EAAO,OAAA,CAAQ,UAAA,CAAeW,CAAQ,CAAA,EACzDzB,CAAAA,CACF,IAAA,CACA,OACF,CACN,CACF,CAAC,CACH,CAEA,eAAsBuB,GACpBvB,CAAAA,CACiD,CACjD,GAAI,CACF,IAAM6B,CAAAA,CAAe,MAAMT,EAAAA,CAAS,MAAA,CAAOpB,CAAG,CAAA,CAE9C,GAAI,CAAC6B,EACH,OAAO,IAAA,CAGT,IAAMf,CAAAA,CAASgB,CAAAA,CAAgB,KAAA,CAAMD,EAAa,MAAM,CAAA,CAGxD,GAAIf,CAAAA,CAAO,UAAA,CAAA,CACT,IAAA,IAAW5B,KAAgB,MAAA,CAAO,IAAA,CAAK4B,CAAAA,CAAO,UAAU,CAAA,CACtD,GAAI5B,KAAgBzB,CAAAA,CAClB,MAAM,IAAI,KAAA,CACR,CAAA,CAAA,EAAIyB,CAAY,oDAClB,CAAA,CAKN,OAAO4B,CACT,CAAA,MAASiB,CAAAA,CAAO,CACd,IAAMC,CAAAA,CAAgB,CAAA,EAAGhC,CAAG,CAAA,gBAAA,CAAA,CAC5B,MAAI+B,CAAAA,YAAiB,OAASA,CAAAA,CAAM,OAAA,CAAQ,QAAA,CAAS,mBAAmB,CAAA,CAChEA,CAAAA,CAEF,IAAI,KAAA,CACR,CAAA,+BAAA,EAAkCxB,CAAAA,CAAY,IAAA,CAAKyB,CAAa,CAAC,GACnE,CACF,CACF,CAKA,eAAsBC,EAAAA,CAAmBnB,CAAAA,CAAgB,CACvD,IAAIoB,CAAAA,CAAuB,EAAC,CAE5B,IAAA,IAAWpE,CAAAA,IAAO,OAAO,IAAA,CAAKgD,CAAAA,CAAO,OAAO,CAAA,CAAG,CAC7C,GAAI,CAACqB,EAAAA,CAAWrE,CAAAA,CAAKgD,CAAM,CAAA,CACzB,SAGF,IAAMsB,EAAetB,CAAAA,CAAO,aAAA,CAAchD,CAAG,CAAA,CACvCuE,CAAAA,CAAc,MAAMC,GACxBxB,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrBsB,CACF,CAAA,CAEA,GAAI,CAACC,CAAAA,CAAa,CAChBH,CAAAA,CAAgBpE,CAAG,CAAA,CAAIgD,CAAAA,CACvB,QACF,CAEAoB,CAAAA,CAAgBpE,CAAG,CAAA,CAAI,MAAMwD,EAAAA,CAAUe,CAAW,EACpD,CAEA,IAAME,GAAAA,CAASC,CAAAA,CAAsB,SAAA,CAAUN,CAAe,CAAA,CAC9D,OAAKK,GAAAA,CAAO,OAAA,CAILA,GAAAA,CAAO,IAAA,CAHL,IAIX,CAEA,eAAsBD,EAAAA,CAAgBtC,CAAAA,CAAaoC,CAAAA,CAAsB,CACvE,IAAMK,CAAAA,CAAaC,EAAAA,CAAe1C,CAAAA,CAAKoC,CAAY,CAAA,CAC7CO,CAAAA,CAAef,WAAK,QAAA,CAASa,CAAAA,CAAYL,CAAY,CAAA,CAQrDQ,CAAAA,CAAAA,CANe,MAAMC,GAAG,IAAA,CAAK,iBAAA,CAAmB,CACpD,GAAA,CAAKJ,CAAAA,CACL,IAAA,CAAM,EACN,MAAA,CAAQ,CAAC,oBAAA,CAAsB,YAAA,CAAc,aAAA,CAAe,cAAc,CAC5E,CAAC,CAAA,EAGE,GAAA,CAAKK,CAAAA,EAAYlB,UAAAA,CAAK,OAAA,CAAQkB,CAAO,CAAC,CAAA,CACtC,IAAA,CAAMC,CAAAA,EAAWJ,CAAAA,CAAa,UAAA,CAAWI,CAAM,CAAC,CAAA,CAEnD,OAAOH,CAAAA,CAAsBhB,UAAAA,CAAK,IAAA,CAAKa,EAAYG,CAAmB,CAAA,CAAI,IAC5E,CAEA,SAAST,EAAAA,CACPrE,EACAgD,CAAAA,CACgC,CAChC,OAAO,MAAA,CAAO,IAAA,CAAKA,CAAAA,CAAO,aAAa,CAAA,CACpC,MAAA,CAAQhD,CAAAA,EAAQA,CAAAA,GAAQ,OAAO,CAAA,CAC/B,SAASA,CAAG,CACjB,CAEO,SAAS4E,EAAAA,CAAe1C,CAAAA,CAAaoC,EAAsB,CAChE,IAAMY,CAAAA,CAAShD,CAAAA,CAAI,KAAA,CAAM4B,UAAAA,CAAK,GAAG,CAAA,CAC3BqB,CAAAA,CAASb,CAAAA,CAAa,KAAA,CAAMR,UAAAA,CAAK,GAAG,EACpCsB,CAAAA,CAAc,EAAC,CAErB,IAAA,IAAS,CAAA,CAAI,CAAA,CAAG,EAAI,IAAA,CAAK,GAAA,CAAIF,CAAAA,CAAO,MAAA,CAAQC,CAAAA,CAAO,MAAM,GACnDD,CAAAA,CAAO,CAAC,CAAA,GAAMC,CAAAA,CAAO,CAAC,CAAA,CADgC,IAI1DC,CAAAA,CAAY,IAAA,CAAKF,CAAAA,CAAO,CAAC,CAAC,CAAA,CAG5B,OAAOE,CAAAA,CAAY,IAAA,CAAKtB,UAAAA,CAAK,GAAG,CAClC,CAGA,eAAsBuB,EAAAA,CAAyBnD,CAAAA,CAAaoD,CAAAA,CAAkB,CAE5E,OAAA,CADoB,MAAMC,EAAerD,CAAG,CAAA,GACxB,eAAA,GAAoB,IAAA,CAAO,aAAA,CAAgBoD,CACjE,CAaO,SAASE,EAAAA,CAAaC,CAAAA,CAAuC,CAClE,IAAMC,CAAAA,CAAwB,CAC5B,aAAA,CAAe,CACb,GAAA,CAAK,OAAA,CAAQ,GAAA,EAAI,CACjB,eAAgB,EAAA,CAChB,WAAA,CAAa,EAAA,CACb,KAAA,CAAO,EAAA,CACP,UAAA,CAAY,GACZ,EAAA,CAAI,EAAA,CACJ,GAAA,CAAK,EAAA,CACL,KAAA,CAAO,EACT,EACA,KAAA,CAAO,EAAA,CACP,QAAA,CAAU,CACR,MAAA,CAAQ,EAAA,CACR,IAAK,EAAA,CACL,SAAA,CAAW,EAAA,CACX,YAAA,CAAc,KAChB,CAAA,CACA,IAAK,KAAA,CACL,GAAA,CAAK,IAAA,CACL,OAAA,CAAS,CACP,UAAA,CAAY,GACZ,KAAA,CAAO,EACT,CAAA,CACA,UAAA,CAAY,CACV,GAAG/F,CACL,CACF,CAAA,CAGA,OAAI8F,CAAAA,CACK,CACL,GAAGC,EACH,GAAGD,CAAAA,CACH,aAAA,CAAe,CACb,GAAGC,CAAAA,CAAc,cACjB,GAAID,CAAAA,CAAQ,aAAA,EAAiB,EAC/B,CAAA,CACA,SAAU,CACR,GAAGC,CAAAA,CAAc,QAAA,CACjB,GAAID,CAAAA,CAAQ,UAAY,EAC1B,EACA,OAAA,CAAS,CACP,GAAGC,CAAAA,CAAc,OAAA,CACjB,GAAID,CAAAA,CAAQ,OAAA,EAAW,EACzB,CAAA,CACA,UAAA,CAAY,CACV,GAAGC,CAAAA,CAAc,UAAA,CACjB,GAAID,CAAAA,CAAQ,UAAA,EAAc,EAC5B,CACF,CAAA,CAGKC,CACT,CCxRO,SAASC,EAAAA,CACdzD,CAAAA,CAAc,EAAA,CACd0D,CAAAA,CAAuB,IAAA,CACH,CACpB,IAAMC,CAAAA,CAAkB/B,UAAAA,CAAK,IAAA,CAAK5B,CAAAA,CAAK,cAAc,EAErD,OAAO4D,EAAAA,CAAG,YAAA,CAAaD,CAAAA,CAAiB,CACtC,MAAA,CAAQD,CACV,CAAC,CACH,CCWA,IAAMG,EAAAA,CAAwB,CAC5B,qBACA,OAAA,CACA,QAAA,CACA,MAAA,CACA,OACF,CAAA,CAEyBrE,EAAE,MAAA,CAAO,CAChC,eAAA,CAAiBA,CAAAA,CAAE,MAAA,CAAO,CACxB,MAAOA,CAAAA,CAAE,MAAA,CAAOA,CAAAA,CAAE,MAAA,EAAO,CAAE,EAAA,CAAGA,EAAE,KAAA,CAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EAED,eAAsB6D,CAAAA,CAAerD,EAA0C,CAC7E,GAAM,CACJ8D,CAAAA,CACAC,CAAAA,CACAC,CAAAA,CACAC,EACAC,CAAAA,CACAC,CAAAA,CACAC,CAAAA,CACAC,CACF,CAAA,CAAI,MAAM,QAAQ,GAAA,CAAI,CACpBxB,EAAAA,CAAG,IAAA,CACD,uFAAA,CACA,CACE,IAAA7C,CAAAA,CACA,IAAA,CAAM,CAAA,CACN,MAAA,CAAQ6D,EACV,CACF,EACAD,EAAAA,CAAG,UAAA,CAAWhC,UAAAA,CAAK,OAAA,CAAQ5B,CAAAA,CAAK,KAAK,CAAC,CAAA,CACtCsE,EAAAA,CAAoBtE,CAAG,CAAA,CACvBuE,EAAAA,CAAsBvE,CAAG,EACzBwE,EAAAA,CAAmBxE,CAAG,CAAA,CACtByE,EAAAA,CAAmBzE,CAAG,CAAA,CACtB0E,GAAuB1E,CAAG,CAAA,CAC1ByD,EAAAA,CAAezD,CAAAA,CAAK,KAAK,CAC3B,CAAC,CAAA,CAEK2E,CAAAA,CAAgB,MAAMf,EAAAA,CAAG,UAAA,CAC7BhC,UAAAA,CAAK,QAAQ5B,CAAAA,CAAK,CAAA,EAAG+D,CAAAA,CAAW,MAAA,CAAS,EAAE,CAAA,GAAA,CAAK,CAClD,CAAA,CAEMa,CAAAA,CAAoB,CACxB,SAAA,CAAWtE,CAAAA,CAAW,MAAA,CACtB,SAAAyD,CAAAA,CACA,KAAA,CAAO,KAAA,CACP,KAAA,CAAAC,CAAAA,CACA,kBAAA,CAAAC,EACA,eAAA,CAAAC,CAAAA,CACA,eAAA,CAAAC,CAAAA,CACA,gBAAA,CAAkB,IAAA,CAClB,YAAAC,CACF,CAAA,CAGA,GAAIN,CAAAA,CAAY,IAAA,CAAMe,CAAAA,EAASA,EAAK,UAAA,CAAW,cAAc,CAAC,CAAA,EAAG,MAAA,CAC/D,OAAAD,EAAK,SAAA,CAAYD,CAAAA,CACbrE,CAAAA,CAAW,UAAU,CAAA,CACrBA,CAAAA,CAAW,YAAY,CAAA,CAC3BsE,CAAAA,CAAK,KAAA,CAAQD,CAAAA,CACbC,CAAAA,CAAK,gBAAA,CAAmB,MAAME,EAAAA,CAC5BF,CAAAA,CAAK,SAAA,CACLP,CACF,CAAA,CACOO,CAAAA,CAIT,GAAId,CAAAA,CAAY,IAAA,CAAMe,CAAAA,EAASA,CAAAA,CAAK,UAAA,CAAW,eAAe,CAAC,CAAA,EAAG,MAAA,CAChE,OAAAD,CAAAA,CAAK,SAAA,CAAYtE,CAAAA,CAAW,MACrBsE,CAAAA,CAIT,GAAId,CAAAA,CAAY,IAAA,CAAMe,CAAAA,EAASA,CAAAA,CAAK,WAAW,gBAAgB,CAAC,CAAA,EAAG,MAAA,CACjE,OAAAD,CAAAA,CAAK,UAAYtE,CAAAA,CAAW,MAAA,CACrBsE,CAAAA,CAIT,GAAId,CAAAA,CAAY,IAAA,CAAMe,GAASA,CAAAA,CAAK,UAAA,CAAW,eAAe,CAAC,CAAA,EAAG,MAAA,CAChE,OAAAD,CAAAA,CAAK,SAAA,CAAYtE,CAAAA,CAAW,OAAA,CACrBsE,CAAAA,CAIT,GACE,OAAO,IAAA,CAAKP,CAAAA,EAAa,YAAA,EAAgB,EAAE,CAAA,CAAE,KAAMU,CAAAA,EACjDA,CAAAA,CAAI,UAAA,CAAW,aAAa,CAC9B,CAAA,CAEA,OAAAH,CAAAA,CAAK,SAAA,CAAYtE,CAAAA,CAAW,KAAA,CACrBsE,CAAAA,CAIT,GACE,CACE,GAAG,MAAA,CAAO,IAAA,CAAKP,CAAAA,EAAa,YAAA,EAAgB,EAAE,CAAA,CAC9C,GAAG,MAAA,CAAO,IAAA,CAAKA,CAAAA,EAAa,eAAA,EAAmB,EAAE,CACnD,CAAA,CAAE,IAAA,CAAMU,CAAAA,EAAQA,CAAAA,CAAI,WAAW,uBAAuB,CAAC,CAAA,CAEvD,OAAAH,CAAAA,CAAK,SAAA,CAAYtE,EAAW,gBAAgB,CAAA,CACrCsE,CAAAA,CAIT,GACEd,CAAAA,CAAY,IAAA,CAAMe,GAASA,CAAAA,CAAK,UAAA,CAAW,sBAAsB,CAAC,CAAA,EAAG,MAAA,CAErE,OAAAD,CAAAA,CAAK,SAAA,CAAYtE,CAAAA,CAAW,cAAc,CAAA,CACnCsE,CAAAA,CAMT,GAAId,CAAAA,CAAY,IAAA,CAAMe,CAAAA,EAASA,CAAAA,CAAK,UAAA,CAAW,cAAc,CAAC,CAAA,EAAG,MAAA,CAC/D,OAAAD,CAAAA,CAAK,SAAA,CAAYtE,CAAAA,CAAW,KACrBsE,CAAAA,CAKT,IAAMI,CAAAA,CAAYlB,CAAAA,CAAY,IAAA,CAAMe,CAAAA,EAASA,EAAK,UAAA,CAAW,YAAY,CAAC,CAAA,CAC1E,OAAIG,CAAAA,EAAW,SACa,MAAMpB,EAAAA,CAAG,QAAA,CACjChC,UAAAA,CAAK,OAAA,CAAQ5B,CAAAA,CAAKgF,CAAS,CAAA,CAC3B,MACF,CAAA,EACsB,QAAA,CAAS,cAAc,CAAA,EAC3CJ,EAAK,SAAA,CAAYtE,CAAAA,CAAW,IAAA,CACrBsE,CAAAA,GAKPP,CAAAA,EAAa,YAAA,EAAc,OAC7BO,CAAAA,CAAK,SAAA,CAAYtE,CAAAA,CAAW,IAAA,CAAA,CACrBsE,CAAAA,CAIX,CAEA,eAAsBE,EAAAA,CACpBG,CAAAA,CACAZ,CAAAA,CACA,CAMA,GALI,CAACA,GAKD,CAAC,CAAC,UAAA,CAAY,YAAY,CAAA,CAAE,QAAA,CAASY,EAAU,IAAI,CAAA,CACrD,OAAO,IAAA,CAGT,IAAMC,CAAAA,CACJb,EAAY,YAAA,EAAc,IAAA,EAAQA,CAAAA,CAAY,eAAA,EAAiB,IAAA,CAEjE,GAAI,CAACa,CAAAA,CACH,OAAO,IAAA,CAIT,IAAMC,CAAAA,CAAeD,CAAAA,CAAQ,MAAM,wBAAwB,CAAA,CAC3D,GAAIC,CAAAA,CACF,OAAOA,EAAa,CAAC,CAAA,CAIvB,IAAMC,CAAAA,CAAaF,CAAAA,CAAQ,KAAA,CAAM,iBAAiB,CAAA,CAClD,OAAIE,CAAAA,CACKA,CAAAA,CAAW,CAAC,CAAA,CAIdF,CACT,CAEA,eAAsBT,EAAAA,CACpBzE,CAAAA,CACyC,CACzC,GAAM,CAACqF,CAAAA,CAAavE,CAAM,CAAA,CAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CAC9C2C,EAAAA,CAAezD,CAAAA,CAAK,KAAK,CAAA,CACzBsB,EAAAA,CAAUtB,CAAG,CACf,CAAC,CAAA,CAGD,OAAIc,CAAAA,EAAQ,QAAA,EAAU,MAAA,GAAW,GACxB,IAAA,CAIP,CAACuE,CAAAA,EAAa,YAAA,EAAc,WAAA,EAC5B,CAACA,GAAa,eAAA,EAAiB,WAAA,CAExB,IAAA,CAIP,gCAAA,CAAiC,IAAA,CAC/BA,CAAAA,EAAa,cAAc,WAAA,EACzBA,CAAAA,EAAa,eAAA,EAAiB,WAAA,EAC9B,EACJ,CAAA,CAEO,KAGF,IACT,CAEA,eAAsBb,EAAAA,CAAmBxE,CAAAA,CAAa,CACpD,GAAM,CAACsF,CAAAA,CAAOnB,CAAe,CAAA,CAAI,MAAM,OAAA,CAAQ,IAAI,CACjDtB,EAAAA,CAAG,IAAA,CAAK,CAAC,UAAA,CAAY,WAAW,EAAG,CACjC,GAAA,CAAA7C,CAAAA,CACA,IAAA,CAAM,CAAA,CACN,MAAA,CAAQ6D,EACV,CAAC,CAAA,CACDY,EAAAA,CAAmBzE,CAAG,CACxB,CAAC,EAED,GAAI,CAACsF,CAAAA,CAAM,MAAA,CACT,OAAO,IAAA,CAKT,IAAA,IAAWT,KAAQS,CAAAA,CAAO,CACxB,IAAMC,CAAAA,CAAW,MAAM3B,EAAAA,CAAG,SAAShC,UAAAA,CAAK,OAAA,CAAQ5B,CAAAA,CAAK6E,CAAI,CAAA,CAAG,MAAM,EAClE,GACEU,CAAAA,CAAS,QAAA,CAAS,uBAAuB,CAAA,EACzCA,CAAAA,CAAS,SAAS,uBAAuB,CAAA,EACzCA,CAAAA,CAAS,QAAA,CAAS,gBAAgB,CAAA,CAElC,OAAOV,CAEX,CAEA,OAAO,IACT,CAEA,eAAsBN,GAAsBvE,CAAAA,CAAa,CACvD,IAAMsF,CAAAA,CAAQ,MAAMzC,EAAAA,CAAG,KAAK,mBAAA,CAAqB,CAC/C,GAAA,CAAA7C,CAAAA,CACA,IAAA,CAAM,CAAA,CACN,OAAQ6D,EACV,CAAC,CAAA,CAED,OAAKyB,CAAAA,CAAM,MAAA,CAIJA,EAAM,CAAC,CAAA,CAHL,IAIX,CAEA,eAAsBZ,EAAAA,CAAuB1E,EAAa,CACxD,IAAMyB,CAAAA,CAAW,MAAMC,UAAAA,CAAW1B,CAAG,EAErC,GACEyB,CAAAA,EAAU,UAAA,GAAe,QAAA,EACzB,CAAC,MAAA,CAAO,QAAQA,CAAAA,EAAU,KAAK,CAAA,CAAE,MAAA,CAEjC,OAAO,IAAA,CAIT,OAAW,CAAC+D,CAAAA,CAAOC,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQhE,EAAS,KAAK,CAAA,CACxD,GACEgE,CAAAA,CAAM,QAAA,CAAS,KAAK,GACpBA,CAAAA,CAAM,QAAA,CAAS,SAAS,CAAA,EACxBA,CAAAA,CAAM,QAAA,CAAS,SAAS,CAAA,EACxBA,CAAAA,CAAM,QAAA,CAAS,kBAAkB,CAAA,CAEjC,OAAOD,EAAM,OAAA,CAAQ,OAAA,CAAS,EAAE,CAAA,EAAK,IAAA,CAKzC,OAAO,OAAO,IAAA,CAAK/D,CAAAA,EAAU,KAAK,CAAA,GAAI,CAAC,CAAA,CAAE,QAAQ,OAAA,CAAS,EAAE,CAAA,EAAK,IACnE,CAEA,eAAsB6C,GAAoBtE,CAAAA,CAAa,CAOrD,OAAA,CANc,MAAM6C,EAAAA,CAAG,IAAA,CAAK,aAAc,CACxC,GAAA,CAAA7C,CAAAA,CACA,IAAA,CAAM,CAAA,CACN,MAAA,CAAQ6D,EACV,CAAC,CAAA,EAEY,MAAA,CAAS,CACxB,CA4BA,eAAsB6B,GACpB1F,CAAAA,CACA2F,CAAAA,CAAyC,IAAA,CACjB,CAExB,GAAM,CAACC,EAAgBC,CAAW,CAAA,CAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CACtDvE,GAAUtB,CAAG,CAAA,CACZ2F,CAAAA,CAEG,OAAA,CAAQ,OAAA,CAAQA,CAAkB,EADlCtC,CAAAA,CAAerD,CAAG,CAExB,CAAC,CAAA,CAED,GAAI4F,EACF,OAAOA,CAAAA,CAGT,GACE,CAACC,CAAAA,EACD,CAACA,EAAY,eAAA,EACZA,CAAAA,CAAY,eAAA,GAAoB,IAAA,EAAQ,CAACA,CAAAA,CAAY,mBAEtD,OAAO,IAAA,CAGT,IAAM/E,CAAAA,CAA0C,CAC9C,OAAA,CAAS,oCACT,GAAA,CAAK+E,CAAAA,CAAY,KAAA,CACjB,GAAA,CAAKA,CAAAA,CAAY,KAAA,CACjB,MAAO,UAAA,CACP,QAAA,CAAU,CACR,MAAA,CAAQA,CAAAA,CAAY,kBAAA,EAAsB,GAC1C,SAAA,CAAW,MAAA,CACX,GAAA,CAAKA,CAAAA,CAAY,eAAA,CACjB,YAAA,CAAc,KACd,MAAA,CAAQ,EACV,CAAA,CACA,WAAA,CAAa,QAAA,CACb,OAAA,CAAS,CACP,UAAA,CAAY,CAAA,EAAGA,CAAAA,CAAY,WAAW,CAAA,WAAA,CAAA,CACtC,EAAA,CAAI,GAAGA,CAAAA,CAAY,WAAW,CAAA,cAAA,CAAA,CAC9B,KAAA,CAAO,CAAA,EAAGA,CAAAA,CAAY,WAAW,CAAA,MAAA,CAAA,CACjC,GAAA,CAAK,CAAA,EAAGA,CAAAA,CAAY,WAAW,CAAA,IAAA,CAAA,CAC/B,MAAO,CAAA,EAAGA,CAAAA,CAAY,WAAW,CAAA,UAAA,CACnC,CACF,CAAA,CAEA,OAAO,MAAMrE,EAAAA,CAAmBxB,CAAAA,CAAKc,CAAM,CAC7C,CAEA,eAAsBgF,EAAAA,CAAoChF,CAAAA,CAE7B,CAC3B,GAAI,CAACA,CAAAA,CAAO,eAAe,GAAA,CACzB,OAAO,IAAA,CAGT,IAAM+E,CAAAA,CAAc,MAAMxC,EAAevC,CAAAA,CAAO,aAAA,CAAc,GAAG,CAAA,CAEjE,OAAK+E,CAAAA,EAAa,gBAIXA,CAAAA,CAAY,eAAA,CAHV,IAIX,CC3ZO,SAASE,EAAAA,CACdC,CAAAA,CACAC,CAAAA,CACA3H,CAAAA,CAEI,GACJ,CACA,GAAM,CAAE,aAAA,CAAA4H,CAAAA,CAAgB,KAAM,EAAI5H,CAAAA,CAG5B6H,CAAAA,CAAqBH,CAAAA,CAAgB,OAAA,CAAQ,OAAA,CAAS;AAAA,CAAI,EAAE,IAAA,EAAK,CACjEI,CAAAA,CAAgBH,CAAAA,CAAW,QAAQ,OAAA,CAAS;AAAA,CAAI,CAAA,CAAE,IAAA,EAAK,CAG7D,GAAIE,CAAAA,GAAuBC,CAAAA,CACzB,OAAO,KAAA,CAIT,GAAI,CAACF,CAAAA,CACH,OAAO,OAYT,IAAMG,CAAAA,CACJ,qHAAA,CAGIC,CAAAA,CAAoBC,CAAAA,EACjBA,CAAAA,CAAQ,OAAA,CACbF,CAAAA,CACA,CAACxI,CAAAA,CAAQ2I,CAAAA,CAAQ3F,CAAAA,CAAY4F,CAAAA,GAAW,CAEtC,GAAI5F,CAAAA,CAAW,UAAA,CAAW,GAAG,CAAA,CAC3B,OAAO,CAAA,EAAG2F,CAAM,CAAA,EAAG3F,CAAU,CAAA,EAAG4F,CAAM,CAAA,CAAA,CAKxC,IAAMC,CAAAA,CAAQ7F,CAAAA,CAAW,KAAA,CAAM,GAAG,CAAA,CAC5B8F,CAAAA,CAAWD,EAAMA,CAAAA,CAAM,MAAA,CAAS,CAAC,CAAA,CAGvC,OAAO,CAAA,EAAGF,CAAM,CAAA,YAAA,EAAeG,CAAQ,CAAA,EAAGF,CAAM,CAAA,CAClD,CACF,CAAA,CAGIG,CAAAA,CAAqBN,CAAAA,CAAiBH,CAAkB,EACxDU,CAAAA,CAAgBP,CAAAA,CAAiBF,CAAa,CAAA,CAEpD,OAAOQ,CAAAA,GAAuBC,CAChC,CCzDO,SAASC,EAAU1H,CAAAA,CAAkB,CAC1C,IAAM2H,CAAAA,CAAWnF,UAAAA,CAAK,QAAA,CAASxC,CAAQ,CAAA,CACvC,OAAO,cAAA,CAAe,IAAA,CAAK2H,CAAQ,CACrC,CAMO,SAASC,EAAAA,CAAoBC,CAAAA,CAAmB,CACrD,IAAMC,CAAAA,CAAW,CACf,YAAA,CACA,MAAA,CACA,wBAAA,CACA,kBACF,CAAA,CAEA,QAAWC,CAAAA,IAAWD,CAAAA,CAAU,CAC9B,IAAM9H,CAAAA,CAAWwC,UAAAA,CAAK,IAAA,CAAKqF,CAAAA,CAAWE,CAAO,CAAA,CAC7C,GAAIC,UAAAA,CAAWhI,CAAQ,CAAA,CACrB,OAAOA,CAEX,CAEA,OAAO,IACT,CAKO,SAASiI,EAAAA,CAAgBd,CAAAA,CAAiB,CAC/C,IAAMe,CAAAA,CAAQf,EAAQ,KAAA,CAAM;AAAA,CAAI,CAAA,CAC1BgB,CAAAA,CAA8B,EAAC,CAErC,IAAA,IAAWC,KAAQF,CAAAA,CAAO,CACxB,IAAMG,CAAAA,CAAUD,CAAAA,CAAK,IAAA,GAErB,GAAI,CAACC,CAAAA,EAAWA,CAAAA,CAAQ,UAAA,CAAW,GAAG,CAAA,CACpC,SAIF,IAAMC,CAAAA,CAAaD,CAAAA,CAAQ,OAAA,CAAQ,GAAG,CAAA,CACtC,GAAIC,CAAAA,GAAe,EAAA,CACjB,SAGF,IAAM5J,CAAAA,CAAM2J,CAAAA,CAAQ,SAAA,CAAU,CAAA,CAAGC,CAAU,CAAA,CAAE,IAAA,EAAK,CAC5C9J,CAAAA,CAAQ6J,CAAAA,CAAQ,UAAUC,CAAAA,CAAa,CAAC,CAAA,CAAE,IAAA,EAAK,CAEjD5J,CAAAA,GACFyJ,CAAAA,CAAIzJ,CAAG,CAAA,CAAIF,CAAAA,CAAM,OAAA,CAAQ,cAAA,CAAgB,EAAE,CAAA,EAE/C,CAEA,OAAO2J,CACT,CAKO,SAASI,EAAAA,CAAc3B,CAAAA,CAAyBC,EAAoB,CACzE,IAAM2B,CAAAA,CAAcP,EAAAA,CAAgBrB,CAAe,CAAA,CAC7C6B,EAASR,EAAAA,CAAgBpB,CAAU,CAAA,CAEnC6B,CAAAA,CAAU,EAAC,CACjB,IAAA,IAAWhK,CAAAA,IAAO,MAAA,CAAO,IAAA,CAAK+J,CAAM,CAAA,CAC5B/J,CAAAA,IAAO8J,CAAAA,EACXE,EAAQ,IAAA,CAAKhK,CAAG,CAAA,CAIpB,OAAOgK,CACT,CAMO,SAASC,EAAAA,CAAgB/B,CAAAA,CAAyBC,CAAAA,CAAoB,CAC3E,IAAM2B,CAAAA,CAAcP,EAAAA,CAAgBrB,CAAe,CAAA,CAC7C6B,CAAAA,CAASR,EAAAA,CAAgBpB,CAAU,CAAA,CAErC1D,CAAAA,CAASyD,CAAAA,CAAgB,OAAA,EAAQ,CACjCzD,CAAAA,EAAU,CAACA,CAAAA,CAAO,QAAA,CAAS;AAAA,CAAI,IACjCA,CAAAA,EAAU;AAAA,CAAA,CAAA,CAGZ,IAAMuF,CAAAA,CAAoB,EAAC,CAC3B,IAAA,GAAW,CAAChK,CAAAA,CAAKF,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQiK,CAAM,CAAA,CACxC/J,CAAAA,IAAO8J,CAAAA,EACXE,CAAAA,CAAQ,IAAA,CAAK,CAAA,EAAGhK,CAAG,CAAA,CAAA,EAAIF,CAAK,CAAA,CAAE,CAAA,CAIlC,OAAIkK,CAAAA,CAAQ,MAAA,CAAS,CAAA,EACfvF,CAAAA,GACFA,CAAAA,EAAU;AAAA,CAAA,CAAA,CAEZA,CAAAA,EAAUuF,EAAQ,IAAA,CAAK;AAAA,CAAI,EACpBvF,CAAAA,CAAS;AAAA,CAAA,EAIdA,CAAAA,EAAU,CAACA,CAAAA,CAAO,QAAA,CAAS;AAAA,CAAI,EAC1BA,CAAAA,CAAS;AAAA,CAAA,CAGXA,CACT,CC9GO,IAAMyF,CAAAA,CAAS,CACpB,KAAA,CAAA,GAASC,CAAAA,CAAiB,CACxB,OAAA,CAAQ,GAAA,CAAI1H,CAAAA,CAAY,MAAM0H,CAAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAC,EAC/C,EACA,IAAA,CAAA,GAAQA,CAAAA,CAAiB,CACvB,OAAA,CAAQ,GAAA,CAAI1H,CAAAA,CAAY,KAAK0H,CAAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAC,EAC9C,EACA,IAAA,CAAA,GAAQA,CAAAA,CAAiB,CACvB,OAAA,CAAQ,GAAA,CAAI1H,CAAAA,CAAY,KAAK0H,CAAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAC,EAC9C,EACA,OAAA,CAAA,GAAWA,CAAAA,CAAiB,CAC1B,OAAA,CAAQ,GAAA,CAAI1H,CAAAA,CAAY,QAAQ0H,CAAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAC,EACjD,CAAA,CACA,GAAA,CAAA,GAAOA,CAAAA,CAAiB,CACtB,OAAA,CAAQ,GAAA,CAAIA,EAAK,IAAA,CAAK,GAAG,CAAC,EAC5B,CAAA,CACA,KAAA,EAAQ,CACN,OAAA,CAAQ,GAAA,CAAI,EAAE,EAChB,CACF,ECnBO,SAASC,CAAAA,CACdC,CAAAA,CACA7J,CAAAA,CAGA,CACA,OAAO8J,EAAAA,CAAI,CACT,IAAA,CAAAD,CAAAA,CACA,QAAA,CAAU7J,GAAS,MACrB,CAAC,CACH,CCPO,IAAM+J,EAAAA,CAAgC,MAAO,CAClD,UAAA,CAAAC,CAAAA,CACA,MAAA,CAAAxH,EACA,SAAA,CAAAyH,CACF,CAAA,IAEMzH,CAAAA,CAAO,QAAA,EAAU,YAAA,EAAgB,CAACyH,CAAAA,EAAW,YAAA,EAqBjDD,CAAAA,CAAW,oBAAA,CAAqBE,UAAAA,CAAW,aAAa,EAAE,OAAA,CAASC,CAAAA,EAAS,CAC1E,IAAMC,CAAAA,CAAMD,CAAAA,CAAK,gBAAe,CAC1BE,CAAAA,CAASC,EAAAA,CAAkBF,CAAAA,CAAKH,CAAAA,CAAU,YAAY,EAAE,IAAA,EAAK,CAC/DI,CAAAA,GAAWD,CAAAA,EACbD,CAAAA,CAAK,eAAA,CAAgBE,CAAM,EAE/B,CAAC,CAAA,CAEML,CAAAA,CAAAA,CA+DF,SAASO,EAAAA,CAAeC,EAAsC,CACnE,GAAI,CAACA,CAAAA,CAAU,QAAA,CAAS,GAAG,GAAK,CAACA,CAAAA,CAAU,QAAA,CAAS,GAAG,CAAA,CACrD,OAAO,CAAC,IAAA,CAAMA,CAAAA,CAAW,IAAI,CAAA,CAG/B,IAAMpC,CAAAA,CAA2B,EAAC,CAE9B,CAACqC,CAAAA,CAAMC,CAAK,CAAA,CAAIF,CAAAA,CAAU,MAAM,GAAG,CAAA,CAGvC,GAAI,CAACC,CAAAA,CAAK,QAAA,CAAS,GAAG,CAAA,CACpB,OAAO,CAAC,IAAA,CAAMA,CAAAA,CAAMC,CAAK,EAI3B,IAAMC,CAAAA,CAAQF,CAAAA,CAAK,KAAA,CAAM,GAAG,CAAA,CAGtBjJ,EAAOmJ,CAAAA,CAAM,GAAA,EAAI,CAGjB9B,CAAAA,CAAU8B,CAAAA,CAAM,IAAA,CAAK,GAAG,CAAA,CAG9B,OAAAvC,EAAM,IAAA,CAAKS,CAAAA,EAAW,KAAMrH,CAAAA,EAAQ,IAAA,CAAMkJ,CAAAA,EAAS,IAAI,CAAA,CAEhDtC,CACT,CAEA,IAAMwC,EAAAA,CAAW,CAAC,KAAA,CAAO,OAAA,CAAS,SAAA,CAAW,eAAgB,OAAO,CAAA,CAE7D,SAASN,EAAAA,CACdO,CAAAA,CACAC,CAAAA,CACA,CAEID,CAAAA,CAAM,QAAA,CAAS,UAAU,CAAA,GAC3BA,CAAAA,CAAQA,CAAAA,CAAM,QAAQ,UAAA,CAAY,wBAAwB,CAAA,CAAA,CAI5D,IAAME,CAAAA,CAAaF,CAAAA,CAAM,MAAM,GAAG,CAAA,CAC5BG,CAAAA,CAAY,IAAI,GAAA,CAChBC,CAAAA,CAAW,IAAI,GAAA,CACrB,IAAA,IAAST,CAAAA,IAAaO,CAAAA,CAAY,CAChC,GAAM,CAAClC,CAAAA,CAASvJ,CAAAA,CAAO4L,CAAQ,CAAA,CAAIX,EAAAA,CAAeC,CAAS,CAAA,CACrDtC,CAAAA,CAAS0C,EAAAA,CAAS,IAAA,CAAM1C,CAAAA,EAAW5I,CAAAA,EAAO,WAAW4I,CAAM,CAAC,CAAA,CAClE,GAAI,CAACA,CAAAA,CAAQ,CACN8C,CAAAA,CAAU,GAAA,CAAIR,CAAS,CAAA,EAC1BQ,CAAAA,CAAU,GAAA,CAAIR,CAAS,CAAA,CAEzB,QACF,CAEA,IAAMW,CAAAA,CAAS7L,CAAAA,EAAO,QAAQ4I,CAAAA,CAAQ,EAAE,CAAA,CACxC,GAAIiD,CAAAA,EAAUA,CAAAA,IAAUL,EAAQ,KAAA,CAAO,CACrCE,CAAAA,CAAU,GAAA,CACR,CAACnC,CAAAA,CAAS,GAAGX,CAAM,CAAA,EAAG4C,CAAAA,CAAQ,KAAA,CAAMK,CAAM,CAAC,EAAE,CAAA,CAC1C,MAAA,CAAO,OAAO,CAAA,CACd,IAAA,CAAK,GAAG,CAAA,EAAKD,CAAAA,CAAW,CAAA,CAAA,EAAIA,CAAQ,CAAA,CAAA,CAAK,EAAA,CAC9C,EAEAD,CAAAA,CAAS,GAAA,CACP,CAAC,MAAA,CAAQpC,CAAAA,CAAS,CAAA,EAAGX,CAAM,CAAA,EAAG4C,CAAAA,CAAQ,IAAA,CAAKK,CAAM,CAAC,CAAA,CAAE,EACjD,MAAA,CAAO,OAAO,CAAA,CACd,IAAA,CAAK,GAAG,CAAA,EAAKD,EAAW,CAAA,CAAA,EAAIA,CAAQ,CAAA,CAAA,CAAK,EAAA,CAC9C,CAAA,CACA,QACF,CAEKF,CAAAA,CAAU,GAAA,CAAIR,CAAS,CAAA,EAC1BQ,CAAAA,CAAU,GAAA,CAAIR,CAAS,EAE3B,CAEA,OAAO,CAAC,GAAG,KAAA,CAAM,KAAKQ,CAAS,CAAA,CAAG,GAAG,KAAA,CAAM,IAAA,CAAKC,CAAQ,CAAC,CAAA,CAAE,IAAA,CAAK,GAAG,CAAA,CAAE,IAAA,EACvE,CCnLO,IAAMG,EAAAA,CAAiB,CAC5B,MAAA,CAAQ,CACN,IAAA,CAAM,eACN,OAAA,CAAS,cAAA,CACT,MAAA,CAAQ,cACV,CAAA,CACA,KAAA,CAAO,CACL,IAAA,CAAM,uBAAA,CACN,OAAA,CAAS,uBAAA,CACT,MAAA,CAAQ,uBACV,CACF,ECLA,IAAMC,GAAiB,QAAA,CAEVC,EAAAA,CAA8B,MAAO,CAAE,UAAA,CAAAtB,CAAAA,CAAY,OAAAxH,CAAO,CAAA,GAAM,CAE3E,GAAI,CAACA,CAAAA,CAAO,aAAe,EAAEA,CAAAA,CAAO,eAAe4I,EAAAA,CAAAA,CACjD,OAAOpB,EAGT,IAAMuB,CAAAA,CAAgB,MAAMC,EAAAA,EAAiB,CACvCC,CAAAA,CAAgBJ,GAChBK,CAAAA,CAAgBlJ,CAAAA,CAAO,WAAA,CAE7B,GAAIiJ,CAAAA,GAAkBC,CAAAA,CACpB,OAAO1B,CAAAA,CAGT,IAAI2B,CAAAA,CAA0B,EAAC,CAC/B,IAAA,IAAWC,KAAqB5B,CAAAA,CAAW,qBAAA,EAAsB,EAAK,EAAC,CACrE,GACE4B,EAAkB,kBAAA,EAAmB,EAAG,OAAA,EAAQ,GAChD,CAAA,CAAA,EAAIR,EAAAA,CAAeC,EAAc,CAAA,CAAE,MAAM,CAAA,CAAA,CAAA,CAK3C,CAAA,IAAA,IAAWQ,CAAAA,IAAaD,CAAAA,CAAkB,iBAAgB,EAAK,EAAC,CAAG,CACjE,IAAME,CAAAA,CAAWD,EAAU,OAAA,EAAQ,CAE7BE,EAAeR,CAAAA,CAAcO,CAAQ,IAAIJ,CAAa,CAAA,CAExD,CAACK,CAAAA,EAAgBJ,CAAAA,CAAc,QAAA,CAASI,CAAY,CAAA,GAIxDJ,CAAAA,CAAc,IAAA,CAAKI,CAAY,CAAA,CAG/BF,CAAAA,CAAU,QAAO,CAGjB7B,CAAAA,CACG,oBAAA,CAAqBE,UAAAA,CAAW,qBAAqB,CAAA,CACrD,OAAQC,CAAAA,EAASA,CAAAA,CAAK,cAAA,EAAe,EAAG,OAAA,EAAQ,GAAM2B,CAAQ,CAAA,CAC9D,OAAA,CAAS3B,CAAAA,EAASA,CAAAA,CAAK,cAAA,EAAe,EAAG,gBAAgB4B,CAAY,CAAC,CAAA,EAC3E,CAGIH,CAAAA,CAAkB,eAAA,IAAmB,MAAA,GAAW,CAAA,EAClDA,CAAAA,CAAkB,MAAA,GAAO,CAI7B,GAAID,EAAc,MAAA,CAAS,CAAA,CAAG,CAC5B,IAAMK,CAAAA,CAAwBhC,EAAW,oBAAA,CAAqB,CAC5D,eAAA,CACEoB,EAAAA,CAAeM,CAA4C,CAAA,EAAG,OAChE,YAAA,CAAcC,CAAAA,CAAc,GAAA,CAAKM,CAAAA,GAAU,CACzC,IAAA,CAAMA,CACR,CAAA,CAAE,CACJ,CAAC,CAAA,CAEIC,EAAAA,CAAclC,CAAU,GAC3BgC,CAAAA,CAAsB,eAAA,CACpBA,CAAAA,CAAsB,OAAA,EAAQ,CAAE,OAAA,CAAQ,IAAK,EAAE,CACjD,EAEJ,CAEA,OAAOhC,CACT,EAEA,SAASkC,EAAAA,CAAclC,CAAAA,CAAwB,CAC7C,OACEA,CAAAA,CAAW,uBAAsB,GAAI,CAAC,CAAA,EAAG,OAAA,EAAQ,CAAE,QAAA,CAAS,GAAG,CAAA,EAAK,KAExE,CC7EO,IAAMmC,EAAAA,CAA+B,MAAO,CACjD,UAAA,CAAAnC,CAAAA,CACA,OAAAxH,CAAAA,CACA,QAAA,CAAA4J,CACF,CAAA,GAAM,CAEJ,IAAMC,EAAc,CAAA,CAAA,EADG7J,CAAAA,CAAO,OAAA,EAAS,KAAA,EAAO,KAAA,CAAM,GAAG,EAAE,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAC9B,CAAA,UAAA,CAAA,CAEtC,GAAI,CAAC,CAAC,MAAA,CAAQ,KAAA,CAAO,MAAA,CAAQ,KAAK,EAAE,QAAA,CAASwH,CAAAA,CAAW,YAAA,EAAc,CAAA,CACpE,OAAOA,EAGT,IAAA,IAAW6B,CAAAA,IAAa7B,CAAAA,CAAW,uBAAA,EAAwB,CAAG,CAC5D,IAAMsC,CAAAA,CAAUC,EAAAA,CACdV,EAAU,eAAA,EAAgB,CAC1BrJ,EACA4J,CACF,CAAA,CAIA,GAHAP,CAAAA,CAAU,eAAA,CAAgBS,CAAO,EAG7BD,CAAAA,GAAgBC,CAAAA,EAAWA,CAAAA,GAAY,aAAA,CAAe,CAQxD,GAAI,CAPsBT,CAAAA,CAAU,sBAAA,CAClC3B,UAAAA,CAAW,iBACb,CAAA,EAEI,eAAA,GACD,IAAA,CAAMsC,CAAAA,EAAgBA,CAAAA,CAAY,OAAA,EAAQ,GAAM,IAAI,EAEtC,SAEjBX,CAAAA,CAAU,eAAA,CACRQ,CAAAA,GAAgBC,CAAAA,CACZA,CAAAA,CAAQ,QAAQD,CAAAA,CAAa7J,CAAAA,CAAO,OAAA,CAAQ,KAAK,CAAA,CACjDA,CAAAA,CAAO,QAAQ,KACrB,EACF,CACF,CAEA,OAAOwH,CACT,EAEA,SAASuC,EAAAA,CACPE,EACAjK,CAAAA,CACA4J,CAAAA,CAAoB,MACpB,CAEA,GAAI,CAACK,CAAAA,CAAgB,UAAA,CAAW,IAAI,GAAK,CAACL,CAAAA,CACxC,OAAOK,CAAAA,CAST,GALIL,CAAAA,EAAYK,EAAgB,UAAA,CAAW,IAAI,CAAA,GAC7CA,CAAAA,CAAkBA,CAAAA,CAAgB,OAAA,CAAQ,OAAQ,sBAAsB,CAAA,CAAA,CAItE,CAACA,CAAAA,CAAgB,UAAA,CAAW,aAAa,EAAG,CAE9C,IAAMvF,CAAAA,CAAQ1E,CAAAA,CAAO,OAAA,CAAQ,UAAA,CAAW,MAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CACpD,OAAOiK,CAAAA,CAAgB,QAAQ,MAAA,CAAQ,CAAA,EAAGvF,CAAK,CAAA,CAAA,CAAG,CACpD,CAEA,OAAIuF,CAAAA,CAAgB,KAAA,CAAM,wBAAwB,CAAA,CACzCA,CAAAA,CAAgB,QACrB,wBAAA,CACAjK,CAAAA,CAAO,OAAA,CAAQ,EAAA,EAAM,CAAA,EAAGA,CAAAA,CAAO,QAAQ,UAAU,CAAA,GAAA,CACnD,CAAA,CAIAA,CAAAA,CAAO,OAAA,CAAQ,UAAA,EACfiK,EAAgB,KAAA,CAAM,gCAAgC,CAAA,CAE/CA,CAAAA,CAAgB,OAAA,CACrB,gCAAA,CACAjK,EAAO,OAAA,CAAQ,UACjB,CAAA,CAGEA,CAAAA,CAAO,OAAA,CAAQ,GAAA,EAAOiK,EAAgB,KAAA,CAAM,yBAAyB,CAAA,CAChEA,CAAAA,CAAgB,OAAA,CACrB,yBAAA,CACAjK,EAAO,OAAA,CAAQ,GACjB,CAAA,CAIAA,CAAAA,CAAO,OAAA,CAAQ,KAAA,EACfiK,EAAgB,KAAA,CAAM,2BAA2B,CAAA,CAE1CA,CAAAA,CAAgB,OAAA,CACrB,2BAAA,CACAjK,EAAO,OAAA,CAAQ,KACjB,CAAA,CAGKiK,CAAAA,CAAgB,OAAA,CACrB,qBAAA,CACAjK,EAAO,OAAA,CAAQ,UACjB,CACF,CC3FA,IAAMkK,EAAAA,CAA+B,CACnC,UAAA,CAAY,QAAA,CACZ,2BAAA,CAA6B,IAAA,CAC7B,0BAAA,CAA4B,IAAA,CAC5B,UAAW,CAAA,CACX,MAAA,CAAQ,IAAA,CACR,OAAA,CAAS,CACP,iBAAA,CACA,SACA,qBAAA,CACA,wBAAA,CACA,iBAAA,CACA,kBAAA,CACA,SAAA,CACA,mBAAA,CACA,gBACA,eAAA,CACA,mBAAA,CACA,qBAAA,CACA,cAAA,CACA,cAAA,CACA,kBAAA,CACA,aACA,2BAAA,CACA,kBAAA,CACA,mBACA,sBAAA,CACA,kBAAA,CACA,CACE,kBAAA,CACA,CACE,QAAA,CAAU,SACZ,CACF,CAAA,CACA,CACE,gBAAA,CACA,CACE,UAAA,CAAY,MACd,CACF,CAAA,CACA,mBACA,eAAA,CACA,aAAA,CACA,YAAA,CACA,KACF,CACF,CAAA,CAEaC,GAAoC,MAAO,CACtD,UAAA,CAAA3C,CAAAA,CACA,MAAA,CAAAxH,CACF,IAAM,CACJ,IAAMoK,CAAAA,CAAS5C,CAAAA,CAAW,WAAA,EAAY,CAEtC,GAAIxH,CAAAA,CAAO,GAAA,CACT,OAAOoK,CAAAA,CAGT,IAAMC,CAAAA,CAAa,SAAMD,CAAAA,CAAQ,CAC/B,MAAA,CAAQ,CACN,KAAA,CAAQE,CAAAA,EACCC,MAAMD,CAAAA,CAAMJ,EAAa,CAEpC,CACF,CAAC,EAEKzI,CAAAA,CAAS+I,oBAAAA,CAAqBH,CAAAA,CAAKD,CAAAA,CAAQ,CAC/C,aAAA,CAAe,MACf,IAAA,CAAM,KAAA,CACN,GAAA,CAAK,IAAA,CACL,OAAA,CAAS,CAACK,EAAmB,CAAA,CAC7B,UAAA,CAAY,KACd,CAAC,CAAA,CAED,GAAI,CAAChJ,CAAAA,EAAU,CAACA,CAAAA,CAAO,GAAA,CACrB,MAAM,IAAI,MAAM,yBAAyB,CAAA,CAG3C,OAAc,EAAA,CAAA,KAAA,CAAMA,CAAAA,CAAO,GAAG,EAAE,IAClC,CAAA,CC3FA,IAAMiJ,EAAAA,CAAiB,uBAAA,CAEVC,EAAAA,CAA4B,MAAO,CAAE,UAAA,CAAAnD,EAAY,MAAA,CAAAxH,CAAO,IAAM,CACzE,GAAIA,EAAO,GAAA,CACT,OAAOwH,CAAAA,CAIT,IAAMoD,CAAAA,CAAQpD,CAAAA,CAAW,oBAAoBE,UAAAA,CAAW,mBAAmB,CAAA,CAC3E,OAAIkD,CAAAA,EAASF,EAAAA,CAAe,KAAKE,CAAAA,CAAM,OAAA,EAAS,CAAA,EAC9CA,CAAAA,CAAM,MAAA,GAGDpD,CACT,CAAA,CCRO,IAAMqD,GAAmC,MAAO,CACrD,UAAA,CAAArD,CAAAA,CACA,MAAA,CAAAxH,CACF,IAAM,CACJ,GAAI,CAACA,CAAAA,CAAO,QAAA,EAAU,MAAA,CACpB,OAAOwH,CAAAA,CAET,IAAMnE,CAAAA,CAAkB,MAAM2B,EAAAA,CAAoChF,CAAM,EAGxE,OAAAwH,CAAAA,CACG,oBAAA,CAAqBE,UAAAA,CAAW,cAAc,CAAA,CAC9C,OAAQC,CAAAA,EAASA,CAAAA,CAAK,aAAA,EAAc,CAAE,OAAA,EAAQ,GAAM,KAAK,CAAA,CACzD,OAAA,CAASA,CAAAA,EAAS,CAEjB,GAAIA,CAAAA,CAAK,cAAa,CAAE,CAAC,CAAA,EAAG,MAAA,CAAOD,UAAAA,CAAW,aAAa,EAAG,CAC5D,IAAMoD,CAAAA,CAAoBnD,CAAAA,CAAK,YAAA,EAAa,CAAE,CAAC,CAAA,CAC3CmD,CAAAA,EACFA,CAAAA,CAAkB,eAAA,CAChB,CAAA,CAAA,EAAIC,CAAAA,CACFD,EAAkB,OAAA,EAAQ,EAAG,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAA,CAC/C9K,EAAO,QAAA,CAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EAEJ,CAGIsE,CAAAA,CAAK,YAAA,EAAa,CAAE,CAAC,CAAA,EAAG,OAAOD,UAAAA,CAAW,uBAAuB,CAAA,EACnEC,CAAAA,CACG,YAAA,EAAa,CAAE,CAAC,CAAA,EACf,oBAAA,CAAqBD,UAAAA,CAAW,kBAAkB,CAAA,CACnD,IAAA,CAAMC,GAASA,CAAAA,CAAK,OAAA,EAAQ,GAAM,UAAU,CAAA,EAC3C,oBAAA,CAAqBD,WAAW,kBAAkB,CAAA,CACnD,OAAA,CAASC,CAAAA,EAAS,CACjBA,CAAAA,CACG,qBAAqBD,UAAAA,CAAW,kBAAkB,CAAA,CAClD,OAAA,CAASC,CAAAA,EAAS,CACjB,IAAMY,CAAAA,CAAaZ,CAAAA,CAAK,oBAAA,CACtBD,UAAAA,CAAW,aACb,CAAA,CACIa,GACFA,CAAAA,EAAY,eAAA,CACV,IAAIwC,CAAAA,CACFxC,CAAAA,CAAW,SAAQ,EAAG,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAA,CACxCvI,CAAAA,CAAO,SAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EAEJ,CAAC,EACL,CAAC,EAEP,CAAC,CAAA,CAGHmE,CAAAA,CAAW,oBAAA,CAAqBE,WAAW,YAAY,CAAA,CAAE,OAAA,CAASC,CAAAA,EAAS,CACzE,GAAIA,EAAK,WAAA,EAAY,CAAE,OAAA,EAAQ,GAAM,WAAA,CAAa,CAEhD,GAAIA,CAAAA,CAAK,cAAA,EAAe,EAAG,MAAA,CAAOD,UAAAA,CAAW,aAAa,EAAG,CAC3D,IAAM5K,CAAAA,CAAQ6K,CAAAA,CAAK,cAAA,EAAe,CAC9B7K,GACFA,CAAAA,CAAM,eAAA,CACJ,IAAIiO,CAAAA,CACFjO,CAAAA,CAAM,SAAQ,EAAG,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAA,CACnCkD,CAAAA,CAAO,SAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EAEJ,CAGA,GAAIsE,CAAAA,CAAK,cAAA,EAAe,EAAG,MAAA,CAAOD,UAAAA,CAAW,aAAa,EAAG,CAE3D,IAAMsD,CAAAA,CAAiBrD,CAAAA,CACpB,cAAA,EAAe,EACd,qBAAqBD,UAAAA,CAAW,cAAc,CAAA,CAC/C,IAAA,CAAMC,CAAAA,EAASA,CAAAA,CAAK,eAAc,CAAE,OAAA,EAAQ,GAAM,IAAI,CAAA,CACrDqD,CAAAA,EAEFA,EAAe,YAAA,EAAa,CAAE,OAAA,CAASrD,CAAAA,EAAS,CAAA,CAE5CA,CAAAA,CAAK,OAAOD,UAAAA,CAAW,qBAAqB,CAAA,EAC5CC,CAAAA,CAAK,MAAA,CAAOD,UAAAA,CAAW,gBAAgB,CAAA,GAEvCC,CAAAA,CACG,iBAAA,CAAkBD,UAAAA,CAAW,aAAa,CAAA,CAC1C,QAASC,CAAAA,EAAS,CACjBA,CAAAA,CAAK,eAAA,CACH,CAAA,CAAA,EAAIoD,CAAAA,CACFpD,EAAK,OAAA,EAAQ,EAAG,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAA,CAClC3H,EAAO,QAAA,CAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EACF,CAAC,CAAA,CAGDsE,CAAAA,CAAK,MAAA,CAAOD,UAAAA,CAAW,aAAa,CAAA,EACtCC,EAAK,eAAA,CACH,CAAA,CAAA,EAAIoD,CAAAA,CACFpD,CAAAA,CAAK,OAAA,EAAQ,EAAG,QAAQ,MAAA,CAAQ,EAAE,CAAA,CAClC3H,CAAAA,CAAO,QAAA,CAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EAEJ,CAAC,EAEL,CACF,CAGIsE,CAAAA,CAAK,WAAA,EAAY,CAAE,OAAA,EAAQ,GAAM,cAC/BA,CAAAA,CAAK,cAAA,EAAe,EAAG,MAAA,CAAOD,UAAAA,CAAW,aAAa,GACxDC,CAAAA,CACG,oBAAA,CAAqBD,UAAAA,CAAW,kBAAkB,CAAA,CAClD,OAAA,CAASC,GAAS,CACjB,GAAIA,CAAAA,CAAK,cAAA,EAAe,EAAG,MAAA,CAAOD,WAAW,cAAc,CAAA,CAAG,CAC5D,IAAMsD,CAAAA,CAAiBrD,CAAAA,CAAK,qBAC1BD,UAAAA,CAAW,cACb,CAAA,CACIsD,CAAAA,EAEFA,CAAAA,CAAe,YAAA,GAAe,OAAA,CAASC,CAAAA,EAAQ,CACzCA,CAAAA,CAAI,MAAA,CAAOvD,UAAAA,CAAW,qBAAqB,CAAA,EAC7CuD,CAAAA,CACG,kBAAkBvD,UAAAA,CAAW,aAAa,EAC1C,OAAA,CAASC,CAAAA,EAAS,CACjBA,CAAAA,CAAK,eAAA,CACH,CAAA,CAAA,EAAIoD,EACFpD,CAAAA,CAAK,OAAA,EAAQ,EAAG,OAAA,CAAQ,MAAA,CAAQ,EAAE,EAClC3H,CAAAA,CAAO,QAAA,CAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EACF,CAAC,CAAA,CAGD4H,CAAAA,CAAI,MAAA,CAAOvD,UAAAA,CAAW,aAAa,GACrCuD,CAAAA,CAAI,eAAA,CACF,CAAA,CAAA,EAAIF,CAAAA,CACFE,CAAAA,CAAI,OAAA,IAAW,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAA,CACjCjL,CAAAA,CAAO,QAAA,CAAS,OAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EAEJ,CAAC,EAEL,CAEA,GAAIsE,CAAAA,CAAK,gBAAe,EAAG,MAAA,CAAOD,WAAW,aAAa,CAAA,EACpDC,CAAAA,CAAK,WAAA,EAAY,CAAE,OAAA,KAAc,SAAA,CAAW,CAC9C,IAAMY,CAAAA,CAAaZ,CAAAA,CAAK,cAAA,GACpBY,CAAAA,EACFA,CAAAA,CAAW,eAAA,CACT,CAAA,CAAA,EAAIwC,CAAAA,CACFxC,CAAAA,CAAW,SAAQ,EAAG,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAA,CACxCvI,CAAAA,CAAO,SAAS,MAAA,CAChBqD,CACF,CAAC,CAAA,CAAA,CACH,EAEJ,CAEJ,CAAC,EAGT,CAAC,CAAA,CAEMmE,CACT,CAAA,CAEO,SAASuD,EACd1C,CAAAA,CACA3C,CAAAA,CAAiB,EAAA,CACjBrC,CAAAA,CACA,CACA,OAAIA,IAAoB,IAAA,CACfgF,CAAAA,CACJ,KAAA,CAAM,GAAG,CAAA,CACT,GAAA,CAAKL,GAAc,CAClB,GAAM,CAAC3B,CAAAA,CAASvJ,CAAAA,CAAO4L,CAAQ,EAAIX,EAAAA,CAAeC,CAAS,CAAA,CAC3D,OAAI3B,CAAAA,CACKqC,CAAAA,CACH,GAAGrC,CAAO,CAAA,CAAA,EAAIX,CAAM,CAAA,EAAG5I,CAAK,CAAA,CAAA,EAAI4L,CAAQ,CAAA,CAAA,CACxC,CAAA,EAAGrC,CAAO,CAAA,CAAA,EAAIX,CAAM,CAAA,EAAG5I,CAAK,CAAA,CAAA,CAEzB4L,CAAAA,CACH,CAAA,EAAGhD,CAAM,CAAA,EAAG5I,CAAK,IAAI4L,CAAQ,CAAA,CAAA,CAC7B,CAAA,EAAGhD,CAAM,CAAA,EAAG5I,CAAK,EAEzB,CAAC,CAAA,CACA,IAAA,CAAK,GAAG,CAAA,CAGNuL,CAAAA,CACJ,MAAM,GAAG,CAAA,CACT,IAAKL,CAAAA,EACJA,CAAAA,CAAU,QAAQ,CAAA,EAAGtC,CAAM,CAAA,CAAA,CAAG,CAAA,GAAM,CAAA,CAChCsC,CAAAA,CACA,GAAGtC,CAAM,CAAA,CAAA,EAAIsC,CAAAA,CAAU,IAAA,EAAM,CAAA,CACnC,EACC,IAAA,CAAK,GAAG,CACb,CC1LA,IAAMkD,EAAAA,CAAU,IAAIC,OAAAA,CAAQ,CAC1B,eAAA,CAAiB,EACnB,CAAC,EAED,eAAeC,EAAAA,CAAqBC,CAAAA,CAAkB,CACpD,IAAMC,CAAAA,CAAM,MAAMxI,QAAAA,CAAG,OAAA,CAAQhC,UAAAA,CAAK,IAAA,CAAKyK,MAAAA,EAAO,CAAG,SAAS,CAAC,CAAA,CAC3D,OAAOzK,UAAAA,CAAK,IAAA,CAAKwK,CAAAA,CAAKD,CAAQ,CAChC,CAEA,eAAsBG,EAAAA,CACpBC,CAAAA,CACAC,CAAAA,CAA8B,CAC5B/B,EAAAA,CACAgB,EAAAA,CACApD,EAAAA,CACAsD,EAAAA,CACA/B,EACF,CAAA,CACA,CACA,IAAM6C,CAAAA,CAAW,MAAMP,EAAAA,CAAqBK,CAAAA,CAAK,QAAQ,EACnDjE,CAAAA,CAAa0D,EAAAA,CAAQ,gBAAA,CAAiBS,CAAAA,CAAUF,CAAAA,CAAK,GAAA,CAAK,CAC9D,UAAA,CAAYG,UAAAA,CAAW,GACzB,CAAC,CAAA,CAED,IAAA,IAAWC,KAAeH,CAAAA,CACxB,MAAMG,CAAAA,CAAY,CAAE,UAAA,CAAArE,CAAAA,CAAY,GAAGiE,CAAK,CAAC,CAAA,CAG3C,OAAIA,CAAAA,CAAK,YAAA,CACA,MAAMtB,EAAAA,CAAa,CACxB,UAAA,CAAA3C,CAAAA,CACA,GAAGiE,CACL,CAAC,CAAA,CAGIjE,CAAAA,CAAW,SACpB,CChEO,IAAMsE,EAAAA,CAA6B,MAAO,CAAE,UAAA,CAAAtE,CAAW,CAAA,IAE5DA,EAAW,YAAA,EAAa,CAAE,OAAA,CAASuE,CAAAA,EAAS,CACtCA,CAAAA,CAAK,SAAQ,GAAM,YAAA,EACrBA,CAAAA,CAAK,MAAA,CAAO,OAAO,EAEvB,CAAC,CAAA,CAGDvE,CAAAA,CAAW,uBAAA,EAAwB,CAAE,OAAA,CAASwE,CAAAA,EAAa,CACrDA,CAAAA,CAAS,OAAA,EAAQ,GAAM,YAAA,EACzBA,CAAAA,CAAS,MAAA,CAAO,OAAO,EAE3B,CAAC,CAAA,CAGDxE,CAAAA,CAAW,qBAAA,EAAsB,CAAE,QAASyE,CAAAA,EAAe,CACpCA,CAAAA,CAAW,eAAA,EAAgB,CACnC,OAAA,CAASC,GAAgB,CAChCA,CAAAA,CAAY,OAAA,EAAQ,GAAM,YAAA,EAC5BA,CAAAA,CAAY,QAAQ,OAAO,CAAA,CAEXA,CAAAA,CAAY,YAAA,EAAa,EAC5B,OAAA,KAAc,YAAA,EAC3BA,CAAAA,CAAY,QAAA,CAAS,OAAO,EAEhC,CAAC,EACH,CAAC,CAAA,CAEM1E,CAAAA,CAAAA,CCAT,eAAsB2E,EAAAA,CACpB3H,CAAAA,CACAxE,CAAAA,CACAxC,EASA,CACA,GAAI,CAACgH,CAAAA,EAAO,MAAA,CACV,OAAO,CACL,YAAA,CAAc,EAAC,CACf,YAAA,CAAc,EAAC,CACf,aAAc,EAChB,EAEFhH,CAAAA,CAAU,CACR,UAAW,KAAA,CACX,KAAA,CAAO,KAAA,CACP,MAAA,CAAQ,KAAA,CACR,QAAA,CAAU,MACV,WAAA,CAAa,KAAA,CACb,GAAGA,CACL,CAAA,CACA,IAAM4O,EAAsBhF,CAAAA,CAAQ,iBAAA,CAAmB,CACrD,MAAA,CAAQ5J,CAAAA,CAAQ,MAClB,CAAC,CAAA,EAAG,KAAA,EAAM,CAEJ,CAACuH,CAAAA,CAAa0C,CAAS,EAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CACjDlF,CAAAA,CAAevC,CAAAA,CAAO,cAAc,GAAG,CAAA,CACvCA,CAAAA,CAAO,QAAA,CAAS,SAAA,CACZqM,EAAAA,CAAqBrM,EAAO,QAAA,CAAS,SAAS,CAAA,CAC9C,OAAA,CAAQ,OAAA,CAAQ,MAAS,CAC/B,CAAC,CAAA,CAEGsM,EAAyB,EAAC,CAC1BC,EAAyB,EAAC,CAC1BC,CAAAA,CAAyB,EAAC,CAC1BC,CAAAA,CAAyB,EAAC,CAC1BC,CAAAA,CAAyB,IAAA,CAE7B,IAAA,IAASC,CAAAA,CAAQ,CAAA,CAAGA,EAAQnI,CAAAA,CAAM,MAAA,CAAQmI,CAAAA,EAAAA,CAAS,CACjD,IAAM5I,CAAAA,CAAOS,EAAMmI,CAAK,CAAA,CACxB,GAAI,CAAC5I,CAAAA,CAAK,OAAA,CACR,SAGF,IAAIzF,CAAAA,CAAWsO,EAAAA,CAAgB7I,CAAAA,CAAM/D,CAAAA,CAAQ,CAC3C,SAAU+E,CAAAA,EAAa,QAAA,CACvB,SAAA,CAAWA,CAAAA,EAAa,SAAA,CAAU,IAAA,CAClC,WAAYnD,EAAAA,CACV4C,CAAAA,CAAM,GAAA,CAAKqI,CAAAA,EAAMA,CAAAA,CAAE,IAAI,EACvB9I,CAAAA,CAAK,IACP,EACA,IAAA,CAAMvG,CAAAA,CAAQ,KACd,SAAA,CAAWmP,CACb,CAAC,CAAA,CAED,GAAI,CAACrO,EACH,SAGF,IAAM2H,CAAAA,CAAW6G,QAAAA,CAAS/I,CAAAA,CAAK,IAAI,EAC7BoC,CAAAA,CAAYrF,UAAAA,CAAK,OAAA,CAAQxC,CAAQ,CAAA,CAQvC,GANK0B,EAAO,GAAA,GACV1B,CAAAA,CAAWA,CAAAA,CAAS,OAAA,CAAQ,SAAA,CAAYlB,CAAAA,EACtCA,IAAU,MAAA,CAAS,MAAA,CAAS,KAC9B,CAAA,CAAA,CAGE4I,CAAAA,CAAU1H,CAAQ,GAAK,CAACgI,UAAAA,CAAWhI,CAAQ,CAAA,CAAG,CAChD,IAAMyO,EAAqB7G,EAAAA,CAAoBC,CAAS,CAAA,CACpD4G,CAAAA,GACFzO,CAAAA,CAAWyO,CAAAA,EAEf,CAEA,IAAMC,CAAAA,CAAe1G,UAAAA,CAAWhI,CAAQ,CAAA,CAGxC,GAAI0O,GAAgBC,QAAAA,CAAS3O,CAAQ,CAAA,CAAE,WAAA,EAAY,CACjD,MAAM,IAAI,KAAA,CACR,CAAA,gBAAA,EAAmBA,CAAQ,CAAA,qEAAA,CAC7B,CAAA,CAKF,IAAMmH,EAAUO,CAAAA,CAAU1H,CAAQ,CAAA,CAC9ByF,CAAAA,CAAK,OAAA,CACL,MAAMyH,GACJ,CACE,QAAA,CAAUzH,CAAAA,CAAK,IAAA,CACf,GAAA,CAAKA,CAAAA,CAAK,QACV,MAAA,CAAA/D,CAAAA,CACA,SAAA,CAAAyH,CAAAA,CACA,YAAA,CAAc,CAACzH,EAAO,GAAA,CACtB,QAAA,CAAUxC,CAAAA,CAAQ,QACpB,CAAA,CACA,CACEmM,GACAgB,EAAAA,CACApD,EAAAA,CACAsD,EAAAA,CACA/B,EAAAA,CACA,GAAIoE,EAAAA,CAAoB5O,EAAUyG,CAAAA,CAAa/E,CAAM,EACjD,CAAC8L,EAAa,EACd,EACN,CACF,CAAA,CAIJ,GAAIkB,CAAAA,EAAgB,CAAChH,CAAAA,CAAU1H,CAAQ,CAAA,CAAG,CACxC,IAAM6O,CAAAA,CAAsB,MAAMrK,QAAAA,CAAG,QAAA,CAASxE,CAAAA,CAAU,OAAO,CAAA,CAE/D,GACE2G,GAAckI,CAAAA,CAAqB1H,CAAAA,CAAS,CAG1C,aAAA,CAAejI,CAAAA,CAAQ,WACzB,CAAC,CAAA,CACD,CACAgP,CAAAA,CAAa,IAAA,CAAK1L,UAAAA,CAAK,QAAA,CAASd,EAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,CAAA,CACnE,QACF,CACF,CAGA,GAAI0O,CAAAA,EAAgB,CAACxP,CAAAA,CAAQ,SAAA,EAAa,CAACwI,CAAAA,CAAU1H,CAAQ,EAAG,CAC9D8N,CAAAA,CAAoB,MAAK,CACrB5O,CAAAA,CAAQ,WAAA,EACVA,CAAAA,CAAQ,WAAA,CAAY,IAAA,GAEtB,GAAM,CAAE,SAAA,CAAA4P,CAAU,CAAA,CAAI,MAAMC,GAAQ,CAClC,IAAA,CAAM,SAAA,CACN,IAAA,CAAM,WAAA,CACN,OAAA,CAAS,YAAY5N,CAAAA,CAAY,IAAA,CAC/BwG,CACF,CAAC,CAAA,6CAAA,CAAA,CACD,OAAA,CAAS,KACX,CAAC,CAAA,CAED,GAAI,CAACmH,CAAAA,CAAW,CACdZ,EAAa,IAAA,CAAK1L,UAAAA,CAAK,QAAA,CAASd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,CAAA,CAC/Dd,CAAAA,CAAQ,WAAA,EACVA,CAAAA,CAAQ,WAAA,CAAY,OAAM,CAE5B,QACF,CACA4O,CAAAA,EAAqB,KAAA,GACjB5O,CAAAA,CAAQ,WAAA,EACVA,CAAAA,CAAQ,WAAA,CAAY,KAAA,GAExB,CAaA,GAVI0P,EAAAA,CAAoB5O,CAAAA,CAAUyG,CAAAA,CAAa/E,CAAM,CAAA,GACnD1B,EAAWA,CAAAA,CAAS,OAAA,CAAQ,sBAAA,CAAwB,UAAU,CAAA,CAAA,CAI3DgI,UAAAA,CAAWH,CAAS,CAAA,EACvB,MAAMrD,QAAAA,CAAG,KAAA,CAAMqD,CAAAA,CAAW,CAAE,UAAW,IAAK,CAAC,CAAA,CAI3CH,CAAAA,CAAU1H,CAAQ,CAAA,EAAK0O,EAAc,CACvC,IAAMG,CAAAA,CAAsB,MAAMrK,QAAAA,CAAG,QAAA,CAASxE,EAAU,OAAO,CAAA,CACzDgP,CAAAA,CAAgBrG,EAAAA,CAAgBkG,CAAAA,CAAqB1H,CAAO,EAIlE,GAHAgH,CAAAA,CAAe5F,EAAAA,CAAcsG,CAAAA,CAAqB1H,CAAO,CAAA,CACzDiH,EAAU5L,UAAAA,CAAK,QAAA,CAASd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,EAEtD,CAACmO,CAAAA,CAAa,MAAA,CAAQ,CACxBD,CAAAA,CAAa,IAAA,CAAK1L,WAAK,QAAA,CAASd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,EACnE,QACF,CAEA,MAAMwE,QAAAA,CAAG,SAAA,CAAUxE,CAAAA,CAAUgP,EAAe,OAAO,CAAA,CACnDf,CAAAA,CAAa,IAAA,CAAKzL,UAAAA,CAAK,QAAA,CAASd,EAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,CAAA,CACnE,QACF,CAEA,MAAMwE,QAAAA,CAAG,SAAA,CAAUxE,CAAAA,CAAUmH,CAAAA,CAAS,OAAO,EAGxCuH,CAAAA,CAQHT,CAAAA,CAAa,KAAKzL,UAAAA,CAAK,QAAA,CAASd,EAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,CAAA,EAPnEgO,CAAAA,CAAa,KAAKxL,UAAAA,CAAK,QAAA,CAASd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,CAAA,CAE/D0H,CAAAA,CAAU1H,CAAQ,CAAA,GACpBmO,CAAAA,CAAe,MAAA,CAAO,KAAKlG,EAAAA,CAAgBd,CAAO,CAAC,CAAA,CACnDiH,CAAAA,CAAU5L,UAAAA,CAAK,SAASd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAA,CAAA,EAKhE,CAEA,IAAMiP,CAAAA,CAAW,CAAC,GAAGjB,CAAAA,CAAc,GAAGC,CAAAA,CAAc,GAAGC,CAAY,CAAA,CAC7DgB,CAAAA,CAAe,MAAMC,EAAAA,CAAeF,CAAAA,CAAUvN,CAAM,CAAA,CAkB1D,GAfAuM,EAAa,IAAA,CAAK,GAAGiB,CAAY,CAAA,CAGjCjB,CAAAA,CAAeA,CAAAA,CAAa,MAAA,CAAQxI,CAAAA,EAAS,CAACuI,EAAa,QAAA,CAASvI,CAAI,CAAC,CAAA,CAGrE,EADoBuI,CAAAA,CAAa,QAAUC,CAAAA,CAAa,MAAA,CAAA,EACpC,CAACC,CAAAA,CAAa,MAAA,EACpCJ,CAAAA,EAAqB,KAAK,mBAAmB,CAAA,CAI/CE,CAAAA,CAAe,KAAA,CAAM,IAAA,CAAK,IAAI,IAAIA,CAAY,CAAC,CAAA,CAC/CC,CAAAA,CAAe,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIA,CAAY,CAAC,CAAA,CAC/CC,CAAAA,CAAe,KAAA,CAAM,KAAK,IAAI,GAAA,CAAIA,CAAY,CAAC,CAAA,CAE3CF,CAAAA,CAAa,QAMf,GALAF,CAAAA,EAAqB,QACnB,CAAA,QAAA,EAAWE,CAAAA,CAAa,MAAM,CAAA,CAAA,EAC5BA,CAAAA,CAAa,MAAA,GAAW,CAAA,CAAI,MAAA,CAAS,OACvC,GACF,CAAA,CACI,CAAC9O,CAAAA,CAAQ,MAAA,CACX,IAAA,IAAWuG,CAAAA,IAAQuI,EACjBpF,CAAAA,CAAO,GAAA,CAAI,CAAA,IAAA,EAAOnD,CAAI,CAAA,CAAE,EAAA,CAAA,KAI5BqI,GAAqB,IAAA,EAAK,CAG5B,GAAIG,CAAAA,CAAa,MAAA,GACfnF,CAAAA,CACE,WAAWmF,CAAAA,CAAa,MAAM,CAAA,CAAA,EAC5BA,CAAAA,CAAa,MAAA,GAAW,CAAA,CAAI,OAAS,OACvC,CAAA,CAAA,CAAA,CACA,CACE,MAAA,CAAQ/O,CAAAA,CAAQ,MAClB,CACF,CAAA,EAAG,IAAA,EAAK,CACJ,CAACA,CAAAA,CAAQ,MAAA,CAAA,CACX,QAAWuG,CAAAA,IAAQwI,CAAAA,CACjBrF,CAAAA,CAAO,GAAA,CAAI,CAAA,IAAA,EAAOnD,CAAI,EAAE,CAAA,CAK9B,GAAIyI,CAAAA,CAAa,MAAA,GACfpF,CAAAA,CACE,CAAA,QAAA,EAAWoF,EAAa,MAAM,CAAA,CAAA,EAC5BD,CAAAA,CAAa,MAAA,GAAW,CAAA,CAAI,MAAA,CAAS,OACvC,CAAA,0DAAA,CAAA,CACA,CACE,MAAA,CAAQ/O,CAAAA,CAAQ,MAClB,CACF,GAAG,IAAA,EAAK,CACJ,CAACA,CAAAA,CAAQ,MAAA,CAAA,CACX,IAAA,IAAWuG,KAAQyI,CAAAA,CACjBtF,CAAAA,CAAO,GAAA,CAAI,CAAA,IAAA,EAAOnD,CAAI,CAAA,CAAE,EAK9B,GAAI0I,CAAAA,CAAa,MAAA,EAAUC,CAAAA,GACzBtF,CAAAA,CACE,CAAA,iCAAA,EAAoC3H,EAAY,IAAA,CAAKiN,CAAO,CAAC,CAAA,CAAA,CAC/D,CAAA,EAAG,IAAA,GACC,CAAClP,CAAAA,CAAQ,QACX,IAAA,IAAWR,CAAAA,IAAOyP,EAChBvF,CAAAA,CAAO,GAAA,CAAI,CAAA,EAAA,EAAKzH,CAAAA,CAAY,OAAA,CAAQ,GAAG,CAAC,CAAA,CAAA,EAAIzC,CAAG,CAAA,CAAE,CAAA,CAKvD,OAAKQ,CAAAA,CAAQ,QACX0J,CAAAA,CAAO,KAAA,EAAM,CAGR,CACL,YAAA,CAAAoF,CAAAA,CACA,aAAAC,CAAAA,CACA,YAAA,CAAAC,CACF,CACF,CAEO,SAASI,GACd7I,CAAAA,CACA/D,CAAAA,CACAxC,CAAAA,CAOA,CAEA,GAAIA,CAAAA,CAAQ,KAAM,CAChB,IAAM8D,CAAAA,CAAeR,UAAAA,CAAK,UAAA,CAAWtD,CAAAA,CAAQ,IAAI,CAAA,CAC7CA,CAAAA,CAAQ,IAAA,CACRsD,UAAAA,CAAK,IAAA,CAAKd,CAAAA,CAAO,cAAc,GAAA,CAAKxC,CAAAA,CAAQ,IAAI,CAAA,CAIpD,GAFmB,aAAa,IAAA,CAAK8D,CAAY,CAAA,CAAA,CAK/C,GAAI9D,CAAAA,CAAQ,SAAA,GAAc,EACxB,OAAO8D,CAAAA,CAAAA,KAEJ,CAGL,IAAM2E,CAAAA,CAAWnF,UAAAA,CAAK,SAASiD,CAAAA,CAAK,IAAI,CAAA,CACxC,OAAOjD,UAAAA,CAAK,IAAA,CAAKQ,EAAc2E,CAAQ,CACzC,CACF,CAEA,GAAIlC,CAAAA,CAAK,OAAQ,CACf,GAAIA,CAAAA,CAAK,MAAA,CAAO,UAAA,CAAW,IAAI,EAC7B,OAAOjD,UAAAA,CAAK,IAAA,CAAKd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK+D,EAAK,MAAA,CAAO,OAAA,CAAQ,IAAA,CAAM,EAAE,CAAC,CAAA,CAG1E,IAAI2J,CAAAA,CAAS3J,CAAAA,CAAK,OAElB,OAAIA,CAAAA,CAAK,OAAS,eAAA,GAChB2J,CAAAA,CAASC,EAAAA,CAAkBD,CAAAA,CAAQlQ,CAAAA,CAAQ,SAAS,EAChD,CAACkQ,CAAAA,CAAAA,CACI,EAAA,CAIJlQ,CAAAA,CAAQ,QAAA,CACXsD,UAAAA,CAAK,KAAKd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK,KAAA,CAAO0N,CAAAA,CAAO,OAAA,CAAQ,OAAQ,EAAE,CAAC,CAAA,CACrE5M,UAAAA,CAAK,IAAA,CAAKd,CAAAA,CAAO,cAAc,GAAA,CAAK0N,CAAAA,CAAO,OAAA,CAAQ,MAAA,CAAQ,EAAE,CAAC,CACpE,CAEA,IAAMvH,CAAAA,CAAYyH,EAAAA,CAA2B7J,CAAAA,CAAM/D,CAAM,EAEnD6B,CAAAA,CAAegM,EAAAA,CAAsB9J,CAAAA,CAAK,IAAA,CAAMoC,CAAS,CAAA,CAC/D,OAAOrF,UAAAA,CAAK,IAAA,CAAKqF,CAAAA,CAAWtE,CAAY,CAC1C,CAEA,SAAS+L,EAAAA,CACP7J,CAAAA,CACA/D,CAAAA,CACA,CACA,OAAI+D,CAAAA,CAAK,OAAS,aAAA,CACT/D,CAAAA,CAAO,aAAA,CAAc,EAAA,CAG1B+D,CAAAA,CAAK,IAAA,GAAS,eACT/D,CAAAA,CAAO,aAAA,CAAc,GAAA,CAG1B+D,CAAAA,CAAK,IAAA,GAAS,gBAAA,EAAoBA,EAAK,IAAA,GAAS,oBAAA,CAC3C/D,CAAAA,CAAO,aAAA,CAAc,UAAA,CAG1B+D,CAAAA,CAAK,OAAS,eAAA,CACT/D,CAAAA,CAAO,aAAA,CAAc,KAAA,CAGvBA,CAAAA,CAAO,aAAA,CAAc,UAC9B,CAEO,SAAS4B,EAAAA,CAAe+C,CAAAA,CAAiBgE,CAAAA,CAAwB,CAEtE,IAAMmF,CAAAA,CAAkBnJ,CAAAA,CAAM,GAAA,CAAKoJ,CAAAA,EAAMA,CAAAA,CAAE,OAAA,CAAQ,MAAO,EAAE,CAAC,EACvDC,CAAAA,CAAmBrF,CAAAA,CAAO,QAAQ,KAAA,CAAO,EAAE,CAAA,CAG3CsF,CAAAA,CAAYD,CAAAA,CAAiB,KAAA,CAAM,GAAG,CAAA,CAAE,KAAA,CAAM,CAAA,CAAG,EAAE,CAAA,CAAE,IAAA,CAAK,GAAG,CAAA,CAGnE,GAAI,CAACC,CAAAA,CACH,OAAO,EAAA,CAIT,IAAMC,CAAAA,CAAiBD,CAAAA,CAAU,KAAA,CAAM,GAAG,CAAA,CAG1C,IAAA,IAASE,EAAID,CAAAA,CAAe,MAAA,CAAQC,CAAAA,CAAI,CAAA,CAAGA,CAAAA,EAAAA,CAAK,CAC9C,IAAMC,CAAAA,CAAWF,CAAAA,CAAe,KAAA,CAAM,CAAA,CAAGC,CAAC,CAAA,CAAE,KAAK,GAAG,CAAA,CAKpD,GAHwBL,CAAAA,CAAgB,IAAA,CACrChN,CAAAA,EAASA,IAASkN,CAAAA,EAAoBlN,CAAAA,CAAK,WAAWsN,CAAAA,CAAW,GAAG,CACvE,CAAA,CAEE,OAAO,GAAA,CAAMA,CAEjB,CAGA,OAAO,IAAMH,CACf,CAEO,SAASJ,EAAAA,CACdvP,CAAAA,CACA6H,CAAAA,CACQ,CAER,IAAMkI,CAAAA,CAAqB/P,CAAAA,CAAS,OAAA,CAAQ,UAAA,CAAY,EAAE,EACpDgQ,CAAAA,CAAsBnI,CAAAA,CAAU,OAAA,CAAQ,UAAA,CAAY,EAAE,CAAA,CAGtDoI,EAAeF,CAAAA,CAAmB,KAAA,CAAM,GAAG,CAAA,CAC3CG,CAAAA,CAAiBF,CAAAA,CAAoB,MAAM,GAAG,CAAA,CAG9CG,CAAAA,CAAoBD,CAAAA,CAAeA,CAAAA,CAAe,MAAA,CAAS,CAAC,CAAA,CAC5DE,CAAAA,CAAiBH,CAAAA,CAAa,SAAA,CACjCI,CAAAA,EAAYA,CAAAA,GAAYF,CAC3B,CAAA,CAEA,OAAIC,IAAmB,EAAA,CAEdH,CAAAA,CAAaA,EAAa,MAAA,CAAS,CAAC,CAAA,CAItCA,CAAAA,CAAa,KAAA,CAAMG,CAAAA,CAAiB,CAAC,CAAA,CAAE,IAAA,CAAK,GAAG,CACxD,CAEO,SAASf,GACdD,CAAAA,CACAvJ,CAAAA,CACA,CACA,GAAI,CAACA,CAAAA,CACH,OAAO,EAAA,CAGT,GAAIA,CAAAA,GAAc,UAAA,CAChB,OAAOuJ,CAAAA,CAGT,GAAIvJ,CAAAA,GAAc,YAAA,CAAc,CAC9B,IAAI1C,CAAAA,CAASiM,CAAAA,CAAO,QAAQ,QAAA,CAAU,QAAQ,CAAA,CAC9C,OAAAjM,CAAAA,CAASA,CAAAA,CAAO,QAAQ,oBAAA,CAAsB,IAAI,CAAA,CAE3CA,CACT,CAEA,GAAI0C,IAAc,cAAA,CAAgB,CAChC,IAAI1C,CAAAA,CAASiM,CAAAA,CAAO,OAAA,CAAQ,SAAU,aAAa,CAAA,CACnD,OAAAjM,CAAAA,CAASA,CAAAA,CAAO,OAAA,CAAQ,qBAAsB,IAAI,CAAA,CAE3CA,CACT,CAEA,GAAI0C,CAAAA,GAAc,UAAW,CAC3B,IAAI1C,CAAAA,CAASiM,CAAAA,CAAO,OAAA,CAAQ,QAAA,CAAU,qBAAqB,CAAA,CAC3D,OAAAjM,CAAAA,CAASA,CAAAA,CAAO,OAAA,CAAQ,oBAAA,CAAsB,IAAI,CAAA,CAE3CA,CACT,CAEA,OAAO,EACT,CAEA,eAAegM,EAAAA,CAAemB,CAAAA,CAAqB5O,CAAAA,CAAgB,CACjE,IAAMkL,CAAAA,CAAU,IAAIC,OAAAA,CAAQ,CAC1B,eAAA,CAAiB,EACnB,CAAC,EACKpG,CAAAA,CAAc,MAAMxC,EAAevC,CAAAA,CAAO,aAAA,CAAc,GAAG,CAAA,CAC3DW,CAAAA,CAAWC,UAAAA,CAAWZ,CAAAA,CAAO,aAAA,CAAc,GAAG,EAC9CwN,CAAAA,CAAe,EAAC,CAEtB,GAAI,CAACzI,CAAAA,EAAepE,EAAS,UAAA,GAAe,QAAA,CAC1C,OAAO,EAAC,CAGV,IAAA,IAAWkO,KAAYD,CAAAA,CAAW,CAChC,IAAMtN,CAAAA,CAAeR,UAAAA,CAAK,OAAA,CAAQd,EAAO,aAAA,CAAc,GAAA,CAAK6O,CAAQ,CAAA,CAGpE,GAAI,CAACvI,WAAWhF,CAAY,CAAA,CAC1B,SAGF,IAAMmE,CAAAA,CAAU,MAAM3C,SAAG,QAAA,CAASxB,CAAAA,CAAc,OAAO,CAAA,CAEjDgK,CAAAA,CAAM,MAAMxI,SAAG,OAAA,CAAQhC,UAAAA,CAAK,KAAKyK,MAAAA,EAAO,CAAG,SAAS,CAAC,CAAA,CACrD/D,CAAAA,CAAa0D,CAAAA,CAAQ,gBAAA,CACzBpK,UAAAA,CAAK,KAAKwK,CAAAA,CAAKwB,QAAAA,CAASxL,CAAY,CAAC,CAAA,CACrCmE,CAAAA,CACA,CACE,UAAA,CAAYmG,UAAAA,CAAW,GACzB,CACF,CAAA,CAGA,GAAI,CAAC,CAAC,MAAA,CAAQ,KAAA,CAAO,MAAA,CAAQ,KAAK,CAAA,CAAE,SAASpE,CAAAA,CAAW,YAAA,EAAc,CAAA,CACpE,SAGF,IAAMsH,EAAqBtH,CAAAA,CAAW,qBAAA,EAAsB,CAC5D,IAAA,IAAW4B,CAAAA,IAAqB0F,CAAAA,CAAoB,CAClD,IAAM7E,CAAAA,CAAkBb,CAAAA,CAAkB,uBAAA,EAAwB,CAGlE,GACErE,GAAa,WAAA,EACb,CAACkF,EAAgB,UAAA,CAAW,CAAA,EAAGlF,EAAY,WAAW,CAAA,CAAA,CAAG,CAAA,CAEzD,SAKF,IAAMgK,CAAAA,CAAyB,MAAMjP,CAAAA,CACnCmK,CAAAA,CACAtJ,CACF,CAAA,CAEA,GAAI,CAACoO,EACH,SAKF,IAAMC,CAAAA,CAAyBC,EAAAA,CAC7BF,CAAAA,CACAH,CAAAA,CACA5O,CACF,CAAA,CAEA,GAAI,CAACgP,CAAAA,CACH,SAIF,IAAME,EAAYC,EAAAA,CAChBH,CAAAA,CACAhP,CAAAA,CACA+E,CACF,CAAA,CAEI,CAACmK,GAAaA,CAAAA,GAAcjF,CAAAA,GAIhCb,CAAAA,CAAkB,kBAAA,CAAmB8F,CAAS,CAAA,CAG9C,MAAMpM,QAAAA,CAAG,SAAA,CAAUxB,CAAAA,CAAckG,CAAAA,CAAW,WAAA,EAAY,CAAG,OAAO,CAAA,CAGlEgG,CAAAA,CAAa,IAAA,CAAKqB,CAAQ,CAAA,EAC5B,CACF,CAEA,OAAOrB,CACT,CAOO,SAASyB,EAAAA,CACdF,CAAAA,CACAvK,EACAxE,CAAAA,CACAoP,CAAAA,CAAuB,CAAC,MAAA,CAAQ,KAAA,CAAO,KAAA,CAAO,OAAQ,MAAM,CAAA,CAC5D,CACA,IAAMlQ,CAAAA,CAAM4B,UAAAA,CAAK,UAAUd,CAAAA,CAAO,aAAA,CAAc,GAAG,CAAA,CAG7CqP,CAAAA,CAAgB7K,CAAAA,CAAM,IAAKqI,CAAAA,EAAMA,CAAAA,CAAE,KAAA,CAAM/L,UAAAA,CAAK,GAAG,CAAA,CAAE,KAAKA,UAAAA,CAAK,KAAA,CAAM,GAAG,CAAC,CAAA,CACvEwO,CAAAA,CAAU,IAAI,GAAA,CAAID,CAAa,CAAA,CAG/BE,CAAAA,CAAYzO,UAAAA,CAAK,OAAA,CAAQiO,CAAsB,CAAA,CAC/CS,CAAAA,CAASD,IAAc,EAAA,CACvBE,CAAAA,CAAUD,EACZT,CAAAA,CAAuB,KAAA,CAAM,CAAA,CAAG,CAACQ,CAAAA,CAAU,MAAM,EACjDR,CAAAA,CAIEW,CAAAA,CADa5O,UAAAA,CAAK,QAAA,CAAS5B,CAAAA,CAAKuQ,CAAO,EAClB,KAAA,CAAM3O,UAAAA,CAAK,GAAG,CAAA,CAAE,IAAA,CAAKA,UAAAA,CAAK,MAAM,GAAG,CAAA,CAGxD6O,CAAAA,CAAUH,CAAAA,CAAS,CAACD,CAAS,EAAIH,CAAAA,CAGjCQ,CAAAA,CAAa,IAAI,GAAA,CAGvB,IAAA,IAAWjR,CAAAA,IAAKgR,EAAS,CACvB,IAAME,CAAAA,CAAUJ,CAAAA,CAAU9Q,CAAAA,CACpBmR,CAAAA,CAAUhP,WAAK,KAAA,CAAM,SAAA,CAAUA,UAAAA,CAAK,QAAA,CAAS5B,CAAAA,CAAK2Q,CAAO,CAAC,CAAA,CAAA,CAC5DP,CAAAA,CAAQ,IAAIQ,CAAO,CAAA,EAAKxJ,WAAWuJ,CAAO,CAAA,GAC5CD,CAAAA,CAAW,GAAA,CAAIE,CAAO,CAAA,CAGxB,IAAMC,CAAAA,CAASjP,UAAAA,CAAK,IAAA,CAAK2O,CAAAA,CAAS,CAAA,KAAA,EAAQ9Q,CAAC,EAAE,CAAA,CACvCqR,CAAAA,CAASlP,UAAAA,CAAK,KAAA,CAAM,SAAA,CAAUA,UAAAA,CAAK,SAAS5B,CAAAA,CAAK6Q,CAAM,CAAC,CAAA,CAAA,CAC1DT,CAAAA,CAAQ,GAAA,CAAIU,CAAM,CAAA,EAAK1J,UAAAA,CAAWyJ,CAAM,CAAA,GAC1CH,CAAAA,CAAW,GAAA,CAAII,CAAM,EAEzB,CAGA,IAAMhR,CAAAA,CAAO8B,UAAAA,CAAK,QAAA,CAAS2O,CAAO,CAAA,CAClC,IAAA,IAAW5C,CAAAA,IAAKwC,CAAAA,CACVM,CAAAA,CAAQ,IAAA,CAAMhR,GAAMkO,CAAAA,CAAE,QAAA,CAAS,IAAI7N,CAAI,CAAA,EAAGL,CAAC,CAAA,CAAE,CAAC,CAAA,EAChDiR,CAAAA,CAAW,GAAA,CAAI/C,CAAC,EAKpB,OAAI+C,CAAAA,CAAW,IAAA,GAAS,CAAA,CAAU,IAAA,CAGnB,KAAA,CAAM,KAAKA,CAAU,CAAA,CAAE,IAAA,CAAK,CAACK,CAAAA,CAAGC,CAAAA,GAAM,CAEnD,IAAMC,CAAAA,CAAOrP,UAAAA,CAAK,KAAA,CAAM,OAAA,CAAQmP,CAAC,EAC3BG,CAAAA,CAAOtP,UAAAA,CAAK,KAAA,CAAM,OAAA,CAAQoP,CAAC,CAAA,CAC3BG,EAAMV,CAAAA,CAAQ,OAAA,CAAQQ,CAAI,CAAA,CAAIR,CAAAA,CAAQ,OAAA,CAAQS,CAAI,CAAA,CACxD,GAAIC,CAAAA,GAAQ,CAAA,CAAG,OAAOA,CAAAA,CAEtB,IAAMC,CAAAA,CAAUZ,CAAAA,EAAWO,CAAAA,CAAE,UAAA,CAAWP,CAAO,CAAA,CAAI,GAAK,CAAA,CAClDa,CAAAA,CAAUb,CAAAA,EAAWQ,CAAAA,CAAE,UAAA,CAAWR,CAAO,EAAI,EAAA,CAAK,CAAA,CACxD,OAAOY,CAAAA,CAAUC,CACnB,CAAC,EAGa,CAAC,CACjB,CAEO,SAASpB,EAAAA,CACd7Q,CAAAA,CACA0B,EACA+E,CAAAA,CACe,CACf,IAAMyL,CAAAA,CAAM1P,UAAAA,CAAK,SAAA,CAAUA,WAAK,IAAA,CAAKd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK1B,CAAQ,CAAC,EAIlEmS,CAAAA,CAAU,MAAA,CAAO,OAAA,CAAQzQ,CAAAA,CAAO,aAAa,CAAA,CAChD,OACC,CAAC,EAAG0Q,CAAI,CAAA,GAAMA,CAAAA,EAAQF,EAAI,UAAA,CAAW1P,UAAAA,CAAK,UAAU4P,CAAAA,CAAO5P,UAAAA,CAAK,GAAG,CAAC,CACtE,CAAA,CACC,IAAA,CAAK,CAACmP,CAAAA,CAAGC,IAAMA,CAAAA,CAAE,CAAC,CAAA,CAAE,MAAA,CAASD,CAAAA,CAAE,CAAC,EAAE,MAAM,CAAA,CAE3C,GAAIQ,CAAAA,CAAQ,MAAA,GAAW,CAAA,CACrB,OAAO,IAAA,CAET,GAAM,CAACE,CAAAA,CAAUC,CAAO,CAAA,CAAIH,EAAQ,CAAC,CAAA,CAGjCI,CAAAA,CAAM/P,UAAAA,CAAK,QAAA,CAAS8P,CAAAA,CAASJ,CAAG,CAAA,CAEpCK,CAAAA,CAAMA,CAAAA,CAAI,KAAA,CAAM/P,UAAAA,CAAK,GAAG,EAAE,IAAA,CAAK,GAAG,CAAA,CAGlC,IAAMgQ,CAAAA,CAAMhQ,UAAAA,CAAK,MAAM,OAAA,CAAQ+P,CAAG,CAAA,CAE5BE,CAAAA,CADW,CAAC,KAAA,CAAO,OAAQ,KAAA,CAAO,MAAM,CAAA,CACrB,QAAA,CAASD,CAAG,CAAA,CAAI,GAAKA,CAAAA,CAC1CE,CAAAA,CAAQH,CAAAA,CAAI,KAAA,CAAM,CAAA,CAAGA,CAAAA,CAAI,OAASC,CAAAA,CAAI,MAAM,CAAA,CAG5CE,CAAAA,CAAM,QAAA,CAAS,QAAQ,IACzBA,CAAAA,CAAQA,CAAAA,CAAM,KAAA,CAAM,CAAA,CAAG,EAAgB,CAAA,CAAA,CAKzC,IAAMC,CAAAA,CACJN,CAAAA,GAAa,KAAA,CACT5L,CAAAA,CAAY,WAAA,CACZ/E,CAAAA,CAAO,QAAQ2Q,CAAuC,CAAA,CAC5D,GAAI,CAACM,CAAAA,CACH,OAAO,KAGT,IAAItL,CAAAA,CAASqL,CAAAA,GAAU,EAAA,CAAK,EAAA,CAAK,CAAA,CAAA,EAAIA,CAAK,CAAA,CAAA,CAI1C,OAAArL,EAASA,CAAAA,CAAO,OAAA,CAAQ,OAAQ,EAAE,CAAA,CAI3B,CAAA,EAAGsL,CAAS,CAAA,EAAGtL,CAAM,GAAGoL,CAAO,CAAA,CACxC,CAEA,SAAS7D,EAAAA,CACP5O,CAAAA,CACAyG,EACA/E,CAAAA,CACA,CACA,IAAMkR,CAAAA,CACJ5S,CAAAA,GAAawC,UAAAA,CAAK,KAAKd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK,eAAe,CAAA,EAChE1B,CAAAA,GAAawC,WAAK,IAAA,CAAKd,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAK,eAAe,CAAA,CAE5DmR,EACJpM,CAAAA,EAAa,SAAA,CAAU,IAAA,GAAS,UAAA,EAChCA,CAAAA,EAAa,SAAA,CAAU,OAAS,YAAA,CAElC,GAAI,CAACmM,CAAAA,EAAoB,CAACC,CAAAA,EAAY,CAACpM,CAAAA,EAAa,gBAAA,CAClD,OAAO,MAAA,CAGT,IAAMqM,CAAAA,CAAe,SAASrM,CAAAA,CAAY,gBAAA,CAAiB,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC,CAAA,CAGxE,OAFqB,CAAC,KAAA,CAAMqM,CAAY,CAAA,EAAKA,GAAgB,EAG/D,CCztBA,IAAMC,EAAAA,CAA6B,CAAC,MAAA,CAAQ,MAAO,MAAA,CAAQ,KAAA,CAAO,MAAM,CAAA,CAClEC,EAAAA,CAAsB,CAAC,cAAc,CAAA,CACrCC,EAAAA,CAAuB,CAC3B,iCAAA,CACA,qBACF,CAAA,CAEMrG,GAAU,IAAIC,OAAAA,CAAQ,CAC1B,eAAA,CAAiB,EACnB,CAAC,CAAA,CAIM,SAASqG,EAAAA,CACdvH,CAAAA,CACe,CAEf,GAAIsH,EAAAA,CAAqB,IAAA,CAAME,CAAAA,EAAYA,CAAAA,CAAQ,IAAA,CAAKxH,CAAe,CAAC,CAAA,CACtE,OAAO,IAAA,CAWT,GANI,CAACA,CAAAA,CAAgB,WAAW,GAAG,CAAA,EAAKA,CAAAA,CAAgB,QAAA,CAAS,GAAG,CAAA,GAClEA,EAAkBA,CAAAA,CAAgB,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAA,CAK5CA,EAAgB,UAAA,CAAW,GAAG,CAAA,CAAG,CACnC,IAAMrE,CAAAA,CAAQqE,EAAgB,KAAA,CAAM,GAAG,CAAA,CACnCrE,CAAAA,CAAM,MAAA,CAAS,CAAA,GACjBqE,EAAkBrE,CAAAA,CAAM,KAAA,CAAM,EAAG,CAAC,CAAA,CAAE,KAAK,GAAG,CAAA,EAEhD,CAEA,OAAOqE,CACT,CAEA,eAAsByH,EAAAA,CACpBpT,CAAAA,CACA0B,CAAAA,CACA+E,CAAAA,CACA4M,CAAAA,CAA8B,IAAI,IAC2C,CAC7E,IAAMC,CAAAA,CAAwBC,CAAA,CAAA,OAAA,CAAQ7R,CAAAA,CAAO,aAAA,CAAc,IAAK1B,CAAQ,CAAA,CAClEwT,CAAAA,CAAgCD,CAAA,CAAA,QAAA,CACpC7R,CAAAA,CAAO,aAAA,CAAc,IACrB4R,CACF,CAAA,CAGA,GAAIN,EAAAA,CAAoB,QAAA,CAASQ,CAAwB,EACvD,OAAO,CAAE,YAAA,CAAc,EAAC,CAAG,KAAA,CAAO,EAAG,CAAA,CAIvC,IAAMC,CAAAA,CAAqBF,CAAA,CAAA,OAAA,CAAQvT,CAAQ,EAC3C,GAAI,CAAC+S,GAA2B,QAAA,CAASU,CAAa,EACpD,OAAO,CAAE,YAAA,CAAc,EAAC,CAAG,KAAA,CAAO,EAAG,CAAA,CAIvC,GAAIJ,CAAAA,CAAe,GAAA,CAAIG,CAAwB,EAC7C,OAAO,CAAE,YAAA,CAAc,EAAC,CAAG,KAAA,CAAO,EAAG,CAAA,CAKvC,GAHAH,CAAAA,CAAe,GAAA,CAAIG,CAAwB,EAGvC,CAAA,CADS,MAAS,CAAA,CAAA,IAAA,CAAKF,CAAgB,CAAA,EACjC,MAAA,GAER,OAAO,CAAE,YAAA,CAAc,EAAC,CAAG,KAAA,CAAO,EAAG,CAAA,CAGvC,IAAMnM,CAAAA,CAAU,MAAS,CAAA,CAAA,QAAA,CAASmM,EAAkB,OAAO,CAAA,CACrDjG,CAAAA,CAAW,MAAMP,EAAAA,CAA0ByG,CAAA,CAAA,QAAA,CAASD,CAAgB,CAAC,CAAA,CACrEpK,CAAAA,CAAa0D,EAAAA,CAAQ,gBAAA,CAAiBS,CAAAA,CAAUlG,EAAS,CAC7D,UAAA,CAAYmG,UAAAA,CAAW,GACzB,CAAC,CAAA,CACKjL,EAAW,MAAMC,UAAAA,CAAWZ,CAAAA,CAAO,aAAA,CAAc,GAAG,CAAA,CAC1D,GAAIW,CAAAA,CAAS,UAAA,GAAe,QAAA,CAC1B,OAAO,CAAE,YAAA,CAAc,EAAC,CAAG,KAAA,CAAO,EAAG,CAAA,CAGvC,IAAM6D,EAAqD,EAAC,CACtDwN,CAAAA,CAAe,IAAI,GAAA,CAGnBC,CAAAA,CAAWC,GAAkB5T,CAAQ,CAAA,CACrC6T,CAAAA,CAAe,CACnB,IAAA,CAAML,CAAAA,CACN,KAAMG,CAAAA,CACN,MAAA,CAAQ,EACV,CAAA,CACAzN,CAAAA,CAAM,KAAK2N,CAAY,CAAA,CAGvB,IAAMC,CAAAA,CAAmB5K,CAAAA,CAAW,qBAAA,GACpC,IAAA,IAAW6K,CAAAA,IAAmBD,CAAAA,CAAkB,CAC9C,IAAMnI,CAAAA,CAAkBoI,EAAgB,uBAAA,EAAwB,CAE1DC,CAAAA,CAAmBrI,CAAAA,CAAgB,UAAA,CAAW,GAAG,EAMvD,GAAI,CALkBA,CAAAA,CAAgB,UAAA,CACpC,CAAA,EAAGlF,CAAAA,CAAY,WAAW,CAAA,CAAA,CAC5B,CAAA,EAGsB,CAACuN,CAAAA,CAAkB,CACvC,IAAMC,EAAaf,EAAAA,CAAiCvH,CAAe,CAAA,CAC/DsI,CAAAA,EACFP,CAAAA,CAAa,GAAA,CAAIO,CAAU,CAAA,CAE7B,QACF,CAEA,IAAIxD,CAAAA,CAAyB,MAAMjP,EAAcmK,CAAAA,CAAiBtJ,CAAQ,EAS1E,GAPI2R,CAAAA,GACFvD,EAA8B8C,CAAA,CAAA,OAAA,CACvBA,CAAA,CAAA,OAAA,CAAQD,CAAgB,CAAA,CAC7B3H,CACF,CAAA,CAAA,CAGE,CAAC8E,CAAAA,CACH,SAMF,GAAI,CADsB8C,CAAA,CAAA,OAAA,CAAQ9C,CAAsB,EAEtD,IAAA,IAAW+B,CAAAA,IAAOO,EAAAA,CAA4B,CAC5C,IAAMmB,EAAAA,CAAsB,GAAGzD,CAAsB,CAAA,EAAG+B,CAAG,CAAA,CAAA,CAC3D,GAAI,CACF,MAAS,CAAA,CAAA,MAAA,CAAO0B,EAAW,CAAA,CAC3BzD,CAAAA,CAAyByD,EAAAA,CACzB,KACF,MAAQ,CACN,QACF,CACF,CAGF,IAAMC,CAAAA,CAAsCZ,WAC1C7R,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrB+O,CACF,CAAA,CAGA,GACE4C,EAAe,GAAA,CAAIc,CAA8B,GACjDnB,EAAAA,CAAoB,QAAA,CAASmB,CAA8B,CAAA,CAE3D,SAGF,IAAMR,EAAAA,CAAWC,EAAAA,CAAkBjI,CAAe,EAC5ClG,EAAAA,CAAO,CACX,IAAA,CAAM0O,CAAAA,CACN,IAAA,CAAMR,EAAAA,CACN,OAAQ,EACV,CAAA,CAAA,CAGIA,EAAAA,GAAa,eAAA,EAAmBA,EAAAA,GAAa,eAAA,IAC/ClO,GAAK,MAAA,CAASkG,CAAAA,CAAAA,CAGhBzF,CAAAA,CAAM,IAAA,CAAKT,EAAI,CAAA,CAGf,IAAM2O,EAAAA,CAAgB,MAAMhB,EAAAA,CAC1Be,CAAAA,CACAzS,CAAAA,CACA+E,CAAAA,CACA4M,CACF,CAAA,CAEA,GAAIe,EAAAA,CAAc,KAAA,CAEhB,IAAA,IAAW3O,CAAAA,IAAQ2O,GAAc,KAAA,CAC1Bf,CAAAA,CAAe,GAAA,CAAI5N,CAAAA,CAAK,IAAI,CAAA,GAC/B4N,EAAe,GAAA,CAAI5N,CAAAA,CAAK,IAAI,CAAA,CAC5BS,CAAAA,CAAM,IAAA,CAAKT,CAAI,CAAA,CAAA,CAKjB2O,EAAAA,CAAc,YAAA,EAChBA,EAAAA,CAAc,YAAA,CAAa,OAAA,CAASzO,GAAQ+N,CAAAA,CAAa,GAAA,CAAI/N,CAAG,CAAC,EAErE,CAGA,IAAM0O,CAAAA,CAAc,KAAA,CAAM,IAAA,CACxB,IAAI,GAAA,CAAInO,CAAAA,CAAM,IAAKT,CAAAA,EAAS,CAACA,CAAAA,CAAK,IAAA,CAAMA,CAAI,CAAC,CAAC,CAAA,CAAE,MAAA,EAClD,CAAA,CAEA,OAAO,CACL,aAAc,KAAA,CAAM,IAAA,CAAKiO,CAAY,CAAA,CACrC,KAAA,CAAOW,CACT,CACF,CAEA,eAAevH,EAAAA,CAAqBC,CAAAA,CAAkB,CACpD,IAAMC,EAAM,MAAS,CAAA,CAAA,OAAA,CAAauG,OAAKtG,MAAAA,EAAO,CAAG,SAAS,CAAC,CAAA,CAC3D,OAAYsG,CAAA,CAAA,IAAA,CAAKvG,CAAAA,CAAKD,CAAQ,CAChC,CAIA,SAAS6G,EAAAA,CACPjI,CAAAA,CAC4C,CAC5C,OAAIA,EAAgB,QAAA,CAAS,MAAM,CAAA,CAC1B,aAAA,CAGLA,CAAAA,CAAgB,QAAA,CAAS,OAAO,CAAA,CAC3B,cAAA,CAGLA,CAAAA,CAAgB,QAAA,CAAS,SAAS,CAAA,CAC7B,iBAGLA,CAAAA,CAAgB,QAAA,CAAS,cAAc,CAAA,CAClC,oBAAA,CAIX,CAGO,SAAS2I,CAAAA,CAAM9R,CAAAA,CAAc,CAClC,GAAI,CACF,OAAA,IAAI,IAAIA,CAAI,CAAA,CACL,CAAA,CACT,CAAA,KAAgB,CACd,OAAO,MACT,CACF,CAEO,SAAS+R,EAAAA,CAAY/R,CAAAA,CAAc,CACxC,OAAOA,CAAAA,CAAK,QAAA,CAAS,OAAO,CAAA,EAAK,CAAC8R,EAAM9R,CAAI,CAC9C,CASO,SAASgS,EAAAA,CACdC,CAAAA,CAIS,CAKT,OAJI,CAACA,CAAAA,EAKHA,CAAAA,CAAa,IAAA,GAAS,eAAA,EACtBA,EAAa,IAAA,GAAS,eAAA,CAEf,KAAA,CAAA,CAGKA,CAAAA,CAAa,KAAA,EAAS,IAGvB,KAAA,CACVhP,CAAAA,EACC,CAAC,CAACA,CAAAA,CAAK,MAAA,GACNA,EAAK,IAAA,GAAS,eAAA,EAAmBA,CAAAA,CAAK,IAAA,GAAS,eAAA,CACpD,CACF,CAIA,eAAsBiP,EAAAA,CACpBC,CAAAA,CACAjT,CAAAA,CACA,CAEA,GAAI,CAACkT,EAAAA,CAAoBlT,CAAM,EAC7B,OAAOtB,CAAAA,CACJ,MAAMyU,GAAsB,CAAA,CAC5B,KAAA,CAAMF,CAAAA,CAAY,IAAA,EAAK,CAAE,OAAO,OAAO,CAAC,CAAA,CAI7C,IAAMlO,CAAAA,CAAc,MAAMxC,EAAevC,CAAAA,CAAO,aAAA,CAAc,GAAG,CAAA,CAC3DoT,CAAAA,CAAY,IAAI,IAChB7F,CAAAA,CAAW7O,CAAAA,CACd,KAAA,CAAMyU,GAAsB,CAAA,CAC5B,KAAA,CAAMF,EAAY,IAAA,EAAK,CAAE,MAAA,CAAO,OAAO,CAAC,CAAA,CAE3C,OAAA1F,CAAAA,CAAS,OAAA,CAASxJ,CAAAA,EAAS,CACzB,IAAMzC,CAAAA,CAAesL,GAAgB7I,CAAAA,CAAM/D,CAAAA,CAAQ,CACjD,QAAA,CAAU+E,CAAAA,EAAa,QAAA,CACvB,UAAWA,CAAAA,EAAa,SAAA,CAAU,IAAA,CAClC,UAAA,CAAYnD,EAAAA,CACV2L,CAAAA,CAAS,IAAKV,CAAAA,EAAMA,CAAAA,CAAE,IAAI,CAAA,CAC1B9I,CAAAA,CAAK,IACP,CACF,CAAC,CAAA,CAEGzC,CAAAA,EAEF8R,CAAAA,CAAU,GAAA,CAAI9R,CAAAA,CAAcyC,CAAI,EAEpC,CAAC,CAAA,CAEM,KAAA,CAAM,IAAA,CAAKqP,CAAAA,CAAU,QAAQ,CACtC,CAGO,SAASF,EAAAA,CAAoBlT,CAAAA,CAAgB,CAClD,OAAO,CAAC,EACNA,CAAAA,EAAQ,aAAA,EAAe,GAAA,GACtBA,GAAQ,aAAA,EAAe,EAAA,EACtBA,CAAAA,EAAQ,aAAA,EAAe,GAAA,EACvBA,CAAAA,EAAQ,eAAe,UAAA,EACvBA,CAAAA,EAAQ,aAAA,EAAe,KAAA,CAAA,CAE7B,CCxVA,SAASqT,GAAuBrT,CAAAA,CAAkC,CAChE,OAAKA,CAAAA,CAAO,KAAA,CAMRA,CAAAA,CAAO,KAAA,GAAU,UAAA,EAAcA,CAAAA,CAAO,UAAU,MAAA,GAAW,EAAA,CACtDvD,EAAAA,CAGFuD,CAAAA,CAAO,KAAA,CATLvD,EAUX,CAEO,SAAS6W,EAAAA,CAAmBtT,CAAAA,CAAmC,CACpE,IAAMuT,CAAAA,CAAa/Q,GAAa,CAC9B,KAAA,CAAO/F,EAAAA,CACP,UAAA,CAAYE,CACd,CAAC,EAED,OAAKqD,CAAAA,CAIEa,GAAAA,CAAa,KAAA,CAClB2S,EAAAA,CAAUD,CAAAA,CAAY,CACpB,GAAGvT,CAAAA,CACH,KAAA,CAAOqT,EAAAA,CAAuBrT,CAAM,CAAA,CACpC,WAAY,CAAE,GAAGrD,CAAAA,CAAoB,GAAGqD,CAAAA,CAAO,UAAW,CAC5D,CAAC,CACH,EATSuT,CAUX,CChCA,IAAIE,EAAAA,CAA2B,CAC7B,OAAA,CAAS,EACX,CAAA,CAEO,SAASC,EAAAA,CACdC,CAAAA,CACA,CAEAF,EAAAA,CAAQ,OAAA,CAAU,CAAE,GAAGA,EAAAA,CAAQ,OAAA,CAAS,GAAGE,CAAQ,EACrD,CAEO,SAASC,EAAAA,CACdlW,CAAAA,CACwB,CACxB,OAAO+V,EAAAA,CAAQ,OAAA,CAAQ/V,CAAG,CAAA,EAAK,EACjC,CAEO,SAASmW,EAAAA,EAAuB,CACrCJ,EAAAA,CAAQ,OAAA,CAAU,GACpB,CCdO,SAASK,GACd9T,CAAAA,CACU,CACV,IAAM9C,CAAAA,CAAO,IAAI,GAAA,CAEjB,OAAI,OAAO8C,CAAAA,EAAW,SACpB/C,EAAAA,CAAe+C,CAAM,EAAE,OAAA,CAASlB,CAAAA,EAAM5B,CAAAA,CAAK,GAAA,CAAI4B,CAAC,CAAC,GAEjD7B,EAAAA,CAAe+C,CAAAA,CAAO,GAAG,CAAA,CAAE,OAAA,CAASlB,CAAAA,EAAM5B,EAAK,GAAA,CAAI4B,CAAC,CAAC,CAAA,CAEjDkB,CAAAA,CAAO,MAAA,EACT,OAAO,MAAA,CAAOA,CAAAA,CAAO,MAAM,CAAA,CAAE,OAAA,CAASlD,CAAAA,EAAU,CAC9CG,EAAAA,CAAeH,CAAK,CAAA,CAAE,OAAA,CAASgC,CAAAA,EAAM5B,CAAAA,CAAK,IAAI4B,CAAC,CAAC,EAClD,CAAC,CAAA,CAGCkB,CAAAA,CAAO,SACT,MAAA,CAAO,MAAA,CAAOA,CAAAA,CAAO,OAAO,CAAA,CAAE,OAAA,CAASlD,GAAU,CAC/CG,EAAAA,CAAeH,CAAK,CAAA,CAAE,OAAA,CAASgC,CAAAA,EAAM5B,EAAK,GAAA,CAAI4B,CAAC,CAAC,EAClD,CAAC,CAAA,CAAA,CAIE,MAAM,IAAA,CAAK5B,CAAI,CACxB,CAEO,SAAS6W,EAAAA,CACd3V,EACA4B,CAAAA,CACM,CAEN,IAAMgU,CAAAA,CADeF,EAAAA,CAAiC9T,CAAM,EAC/B,MAAA,CAAQlB,CAAAA,EAAM,CAAC,OAAA,CAAQ,GAAA,CAAIA,CAAC,CAAC,CAAA,CAE1D,GAAIkV,CAAAA,CAAQ,MAAA,CAAS,CAAA,CACnB,MAAM,IAAIpV,EAAAA,CAAyCR,CAAAA,CAAc4V,CAAO,CAE5E,CAEO,SAASC,GACdC,CAAAA,CACAlU,CAAAA,CACM,CACN,IAAA,IAAWxB,CAAAA,IAAQ0V,CAAAA,CACjBC,GAAkC3V,CAAAA,CAAM8U,EAAAA,CAAmBtT,CAAM,CAAC,CAAA,CAIpE6T,KACF,CC/CA,IAAMO,EAAAA,CAAmB,QAAA,CACnBC,EAAAA,CAAoB,UACpBC,EAAAA,CAAkB,YAAA,CAClBC,EAAAA,CAAwB,GAAA,CACxBC,EAAAA,CAAwB,GAAA,CAEvB,SAASL,EAAAA,CACdnV,CAAAA,CACAgB,CAAAA,CACA,CACA,GAAM,CAAE,SAAAyU,CAAAA,CAAU,IAAA,CAAAjW,CAAK,CAAA,CAAIe,EAAAA,CAA+BP,CAAI,EAE9D,GAAI,CAACyV,CAAAA,CACH,OAAO,IAAA,CAIT,IAAMC,GADa1U,CAAAA,EAAQ,UAAA,EAAc,EAAC,EACRyU,CAAQ,CAAA,CAC1C,GAAI,CAACC,CAAAA,CACH,MAAM,IAAIvW,CAAAA,CAA2BsW,CAAQ,EAK/C,OAAAV,EAAAA,CAAuBU,EAAUC,CAAc,CAAA,CAExC,CACL,GAAA,CAAKC,EAAAA,CAA2BnW,CAAAA,CAAMkW,CAAAA,CAAgB1U,CAAM,CAAA,CAC5D,QAAS4U,EAAAA,CAA+BF,CAAc,CACxD,CACF,CAEO,SAASC,GACdnW,CAAAA,CACAkW,CAAAA,CACA1U,CAAAA,CACA,CACA,GAAI,OAAO0U,GAAmB,QAAA,CAAU,CACtC,IAAIhX,CAAAA,CAAMgX,CAAAA,CAAe,OAAA,CAAQN,GAAkB5V,CAAI,CAAA,CACvD,OAAIwB,CAAAA,EAAQ,KAAA,EAAStC,CAAAA,CAAI,SAAS2W,EAAiB,CAAA,GACjD3W,CAAAA,CAAMA,CAAAA,CAAI,OAAA,CAAQ2W,EAAAA,CAAmBrU,EAAO,KAAK,CAAA,CAAA,CAE5CnD,EAAAA,CAAca,CAAG,CAC1B,CAEA,IAAImX,CAAAA,CAAUH,CAAAA,CAAe,IAAI,OAAA,CAAQN,EAAAA,CAAkB5V,CAAI,CAAA,CAM/D,OALIwB,CAAAA,EAAQ,KAAA,EAAS6U,CAAAA,CAAQ,QAAA,CAASR,EAAiB,CAAA,GACrDQ,CAAAA,CAAUA,CAAAA,CAAQ,OAAA,CAAQR,EAAAA,CAAmBrU,CAAAA,CAAO,KAAK,CAAA,CAAA,CAE3D6U,CAAAA,CAAUhY,EAAAA,CAAcgY,CAAO,CAAA,CAE1BH,CAAAA,CAAe,OAIbI,EAAAA,CAAkBD,CAAAA,CAASH,CAAAA,CAAe,MAAM,CAAA,CAH9CG,CAIX,CAEO,SAASD,EAAAA,CACd5U,CAAAA,CACA,CACA,GAAI,OAAOA,GAAW,QAAA,EAAY,CAACA,CAAAA,CAAO,OAAA,CACxC,OAAO,GAGT,IAAM2T,CAAAA,CAAkC,EAAC,CAEzC,IAAA,GAAW,CAAC3W,EAAKF,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQkD,CAAAA,CAAO,OAAO,EAAG,CACzD,IAAM+U,CAAAA,CAAgBlY,EAAAA,CAAcC,CAAK,CAAA,CAErCkY,GAAoBlY,CAAAA,CAAOiY,CAAa,CAAA,GAC1CpB,CAAAA,CAAQ3W,CAAG,CAAA,CAAI+X,GAEnB,CAEA,OAAOpB,CACT,CAEA,SAASmB,EAAAA,CAAkBD,EAAiBI,CAAAA,CAAgC,CAC1E,IAAMC,CAAAA,CAAY,IAAI,eAAA,CAEtB,OAAW,CAAClY,CAAAA,CAAKF,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQmY,CAAM,CAAA,CAAG,CACjD,IAAMF,CAAAA,CAAgBlY,EAAAA,CAAcC,CAAK,EACrCiY,CAAAA,EACFG,CAAAA,CAAU,MAAA,CAAOlY,CAAAA,CAAK+X,CAAa,EAEvC,CAEA,IAAMI,CAAAA,CAAcD,EAAU,QAAA,EAAS,CACvC,GAAI,CAACC,CAAAA,CACH,OAAON,CAAAA,CAGT,IAAMO,CAAAA,CAAYP,EAAQ,QAAA,CAASN,EAAqB,CAAA,CACpDC,EAAAA,CACAD,EAAAA,CAEJ,OAAO,GAAGM,CAAO,CAAA,EAAGO,CAAS,CAAA,EAAGD,CAAW,CAAA,CAC7C,CAEA,SAASH,EAAAA,CAAoBK,CAAAA,CAAuBN,CAAAA,CAAuB,CACzE,IAAMO,EAAkBP,CAAAA,CAAc,IAAA,EAAK,CAE3C,GAAI,CAACO,CAAAA,CACH,OAAO,MAAA,CAIT,GAAID,CAAAA,CAAc,QAAA,CAAS,IAAI,CAAA,EAEbA,EAAc,KAAA,CAAMf,EAAe,CAAA,CACtC,CACX,IAAMiB,CAAAA,CAAsBF,EACzB,OAAA,CAAQf,EAAAA,CAAiB,EAAE,CAAA,CAC3B,IAAA,GACH,OAAOgB,CAAAA,GAAoBC,CAC7B,CAGF,OAAO,KACT,CASO,SAASC,EAAAA,CAAmBC,CAAAA,CAAmB,CACpD,GAAI7C,CAAAA,CAAM6C,CAAS,CAAA,CAAG,CAGpB,IAAM/X,CAAAA,CAAM,IAAI,GAAA,CAAI+X,CAAS,CAAA,CAC7B,OAAI/X,CAAAA,CAAI,QAAA,CAAS,KAAA,CAAM,aAAa,GAAK,CAACA,CAAAA,CAAI,QAAA,CAAS,QAAA,CAAS,OAAO,CAAA,GACrEA,EAAI,QAAA,CAAW,CAAA,EAAGA,CAAAA,CAAI,QAAQ,CAAA,KAAA,CAAA,CAAA,CAGzBA,CAAAA,CAAI,UACb,CAEA,OAAO,CAAA,EAAGlB,EAAY,CAAA,CAAA,EAAIiZ,CAAS,CAAA,CACrC,CCtIA,IAAMC,EAAAA,CAAQ,QAAQ,GAAA,CAAI,WAAA,CACtB,IAAIC,eAAAA,CAAgB,OAAA,CAAQ,GAAA,CAAI,WAAW,CAAA,CAC3C,MAAA,CAEEC,EAAAA,CAAgB,IAAI,GAAA,CAM1B,eAAsBC,EACpBlR,CAAAA,CACAnH,CAAAA,CAAkC,EAAC,CACnC,CACAA,CAAAA,CAAU,CACR,QAAA,CAAU,IAAA,CACV,GAAGA,CACL,CAAA,CAEA,GAAI,CA+EF,OA9EgB,MAAM,OAAA,CAAQ,GAAA,CAC5BmH,CAAAA,CAAM,GAAA,CAAI,MAAO7D,CAAAA,EAAS,CACxB,IAAMpD,CAAAA,CAAM8X,EAAAA,CAAmB1U,CAAI,EAGnC,GAAItD,CAAAA,CAAQ,QAAA,EAAYoY,EAAAA,CAAc,GAAA,CAAIlY,CAAG,EAC3C,OAAOkY,EAAAA,CAAc,GAAA,CAAIlY,CAAG,CAAA,CAI9B,IAAMoY,GAAgB,SAAY,CAEhC,IAAMnC,CAAAA,CAAUC,EAAAA,CAA8BlW,CAAG,EAE3CqY,CAAAA,CAAW,MAAMC,EAAAA,CAAMtY,CAAAA,CAAK,CAChC,KAAA,CAAAgY,GACA,OAAA,CAAS,CACP,GAAG/B,CACL,CACF,CAAC,EAED,GAAI,CAACoC,CAAAA,CAAS,EAAA,CAAI,CAChB,IAAIE,EAEJ,GACEF,CAAAA,CAAS,QAAQ,GAAA,CAAI,cAAc,GAAG,QAAA,CAAS,kBAAkB,CAAA,CACjE,CACA,IAAMG,CAAAA,CAAO,MAAMH,CAAAA,CAAS,IAAA,EAAK,CAC3BI,CAAAA,CAASzX,CAAAA,CACZ,MAAA,CAAO,CAEN,MAAA,CAAQA,CAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC5B,MAAOA,CAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAE3B,OAAA,CAASA,EAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC7B,KAAA,CAAOA,CAAAA,CAAE,QAAO,CAAE,QAAA,EACpB,CAAC,CAAA,CACA,SAAA,CAAUwX,CAAI,CAAA,CAEbC,CAAAA,CAAO,OAAA,GAETF,CAAAA,CAAoBE,CAAAA,CAAO,IAAA,CAAK,QAAUA,CAAAA,CAAO,IAAA,CAAK,QAElDA,CAAAA,CAAO,IAAA,CAAK,QACdF,CAAAA,CAAoB,CAAA,CAAA,EAAIE,CAAAA,CAAO,IAAA,CAAK,KAAK,CAAA,EAAA,EAAKF,CAAiB,CAAA,CAAA,CAAA,EAGrE,CAEA,MAAIF,CAAAA,CAAS,MAAA,GAAW,GAAA,CAChB,IAAInY,EAAAA,CAA0BF,CAAAA,CAAKuY,CAAiB,CAAA,CAGxDF,CAAAA,CAAS,MAAA,GAAW,IAChB,IAAItY,CAAAA,CAAsBC,CAAAA,CAAKuY,CAAiB,CAAA,CAGpDF,CAAAA,CAAS,SAAW,GAAA,CAChB,IAAIlY,EAAAA,CAAuBH,CAAAA,CAAKuY,CAAiB,CAAA,CAGnD,IAAInY,EAAAA,CACRJ,CAAAA,CACAqY,CAAAA,CAAS,MAAA,CACTE,CACF,CACF,CAEA,OAAOF,CAAAA,CAAS,IAAA,EAClB,CAAA,GAAG,CAEH,OAAIvY,CAAAA,CAAQ,QAAA,EACVoY,GAAc,GAAA,CAAIlY,CAAAA,CAAKoY,CAAY,CAAA,CAE9BA,CACT,CAAC,CACH,CAGF,CAAA,MAAS7U,EAAO,CACd,MAAMA,CACR,CACF,CAEA,eAAsBmV,GAAmB9X,CAAAA,CAAkB,CACzD,GAAI,CAEF,IAAI+X,CAAAA,CAAe/X,EACfA,CAAAA,CAAS,UAAA,CAAW,IAAI,CAAA,GAC1B+X,CAAAA,CAAevV,UAAAA,CAAK,KAAKwV,OAAAA,EAAQ,CAAGhY,CAAAA,CAAS,KAAA,CAAM,CAAC,CAAC,GAGvD,IAAMgD,CAAAA,CAAeR,UAAAA,CAAK,OAAA,CAAQuV,CAAY,CAAA,CACxC5Q,EAAU,MAAM3C,QAAAA,CAAG,QAAA,CAASxB,CAAAA,CAAc,MAAM,CAAA,CAChD6U,EAAS,IAAA,CAAK,KAAA,CAAM1Q,CAAO,CAAA,CAEjC,GAAI,CACF,OAAO8Q,CAAAA,CAAmB,KAAA,CAAMJ,CAAM,CACxC,CAAA,MAASlV,CAAAA,CAAO,CACd,MAAM,IAAI1C,CAAAA,CAAmBD,CAAAA,CAAU2C,CAAK,CAC9C,CACF,CAAA,MAASA,CAAAA,CAAO,CAEd,MACEA,CAAAA,YAAiB,KAAA,GAChBA,EAAM,OAAA,CAAQ,QAAA,CAAS,QAAQ,CAAA,EAC9BA,CAAAA,CAAM,OAAA,CAAQ,SAAS,cAAc,CAAA,CAAA,CAEjC,IAAI5C,CAAAA,CAAuBC,CAAAA,CAAU2C,CAAK,EAG9CA,CAAAA,YAAiB1C,CAAAA,CACb0C,CAAAA,CAGF,IAAI5C,CAAAA,CAAuBC,CAAAA,CAAU2C,CAAK,CAClD,CACF,CC5HA,eAAsBuV,EAAAA,CACpBC,CAAAA,CAGAzW,EACAxC,CAAAA,CAIA,CAYA,GAXI,CAACiZ,CAAAA,GAILjZ,CAAAA,CAAU,CACR,MAAA,CAAQ,KAAA,CACR,eAAA,CAAiB,IAAA,CACjB,GAAGA,CACL,EAGIA,CAAAA,CAAQ,eAAA,GAAoB,IAAA,CAAA,CAC9B,OAGF,IAAMkZ,CAAAA,CAA2B5V,WAAK,QAAA,CACpCd,CAAAA,CAAO,aAAA,CAAc,GAAA,CACrBA,CAAAA,CAAO,aAAA,CAAc,cACvB,CAAA,CACM2W,CAAAA,CAAkBvP,CAAAA,CACtB,CAAA,SAAA,EAAY3H,CAAAA,CAAY,IAAA,CAAKiX,CAAwB,CAAC,CAAA,CAAA,CACtD,CACE,MAAA,CAAQlZ,CAAAA,CAAQ,MAClB,CACF,CAAA,CAAE,KAAA,EAAM,CACFoK,CAAAA,CAAM,MAAM9E,QAAAA,CAAG,SAAS9C,CAAAA,CAAO,aAAA,CAAc,cAAA,CAAgB,MAAM,CAAA,CACnEoK,CAAAA,CAAS,MAAMwM,EAAAA,CAAwBhP,CAAAA,CAAK6O,CAAAA,CAAgBzW,CAAM,CAAA,CACxE,MAAM8C,SAAG,SAAA,CAAU9C,CAAAA,CAAO,aAAA,CAAc,cAAA,CAAgBoK,CAAAA,CAAQ,MAAM,EACtEuM,CAAAA,EAAiB,OAAA,GACnB,CAEA,eAAsBC,EAAAA,CACpBvO,EACAoO,CAAAA,CACAzW,CAAAA,CACA,CACA,IAAMwH,CAAAA,CAAa,MAAMqP,EAAAA,CAAkBxO,CAAAA,CAAOrI,CAAM,CAAA,CAIlD8W,CAAAA,CAAetP,CAAAA,CAClB,qBAAqBE,UAAAA,CAAW,uBAAuB,CAAA,CACvD,IAAA,CAAMC,CAAAA,EACLA,CAAAA,CACG,eAAc,CACd,IAAA,CACEoP,CAAAA,EACCA,CAAAA,CAAS,MAAA,CAAOrP,UAAAA,CAAW,kBAAkB,CAAA,EAC7CqP,CAAAA,CAAS,OAAA,EAAQ,GAAM,SAC3B,CACJ,EAGF,GAAI,CAACD,CAAAA,CACH,OAAOzO,CAAAA,CAGT,IAAM2O,EAAYC,EAAAA,CAAcH,CAAY,CAAA,CAG5C,OAAAI,EAAAA,CACEJ,CAAAA,CACA,CACE,IAAA,CAAM,UAAA,CACN,KAAA,CAAO,OACT,CAAA,CACA,CAAE,UAAAE,CAAU,CACd,CAAA,CAGAP,CAAAA,CAAe,OAAA,EAAS,OAAA,CAASU,GAAW,CAC1CC,EAAAA,CAAwBN,CAAAA,CAAcK,CAAM,EAC9C,CAAC,EAGGV,CAAAA,CAAe,KAAA,EACjB,MAAMY,EAAAA,CAAuBP,CAAAA,CAAcL,CAAAA,CAAe,KAAK,CAAA,CAG1DjP,CAAAA,CAAW,WAAA,EACpB,CAEA,SAAS0P,GACPJ,CAAAA,CACAC,CAAAA,CAIA,CACE,SAAA,CAAAC,CACF,CAAA,CAGA,CACA,IAAMM,CAAAA,CAAmBR,CAAAA,CAAa,WAAA,CAAY,UAAU,CAAA,CAE5D,GAAI,CAACQ,CAAAA,CAAkB,CACrB,IAAMC,CAAAA,CAAc,CAClB,KAAMR,CAAAA,CAAS,IAAA,CACf,WAAA,CAAa,CAAA,CAAA,EAAIC,CAAS,CAAA,EAAGD,EAAS,KAAK,CAAA,EAAGC,CAAS,CAAA,CAAA,CACzD,CAAA,CAGA,OAAID,CAAAA,CAAS,IAAA,GAAS,UAAA,EACpBD,CAAAA,CAAa,wBAAA,CAAyB,CAAA,CAAGS,CAAW,CAAA,CAC7CT,CAAAA,GAGTA,CAAAA,CAAa,qBAAA,CAAsBS,CAAW,CAAA,CAEvCT,EACT,CAEA,GAAIQ,CAAAA,CAAiB,MAAA,CAAO5P,UAAAA,CAAW,kBAAkB,EAAG,CAC1D,IAAM8P,CAAAA,CAAcF,CAAAA,CAAiB,cAAA,EAAe,CAC9CG,EAAW,CAAA,EAAGT,CAAS,CAAA,EAAGD,CAAAA,CAAS,KAAK,CAAA,EAAGC,CAAS,CAAA,CAAA,CAG1D,GAAIQ,CAAAA,EAAa,MAAA,CAAO9P,UAAAA,CAAW,aAAa,EAAG,CACjD,IAAMgQ,CAAAA,CAAkBF,CAAAA,CAAY,OAAA,EAAQ,CAC5C,OAAAA,CAAAA,CAAY,eAAA,CAAgB,IAAIE,CAAe,CAAA,EAAA,EAAKD,CAAQ,CAAA,CAAA,CAAG,CAAA,CACxDX,CACT,CAGA,GAAIU,CAAAA,EAAa,OAAO9P,UAAAA,CAAW,sBAAsB,CAAA,CAAG,CAE1D,GACE8P,CAAAA,CACG,aAAY,CACZ,GAAA,CAAKG,CAAAA,EAAYA,CAAAA,CAAQ,OAAA,EAAS,EAClC,QAAA,CAASF,CAAQ,CAAA,CAEpB,OAAOX,CAAAA,CAETU,CAAAA,CAAY,WAAWC,CAAQ,EACjC,CAEA,OAAOX,CACT,CAEA,OAAOA,CACT,CAEA,eAAeO,EAAAA,CACbP,CAAAA,CACAc,CAAAA,CACA,CAEKd,CAAAA,CAAa,WAAA,CAAY,OAAO,CAAA,EACnCA,CAAAA,CAAa,qBAAA,CAAsB,CACjC,IAAA,CAAM,OAAA,CACN,YAAa,IACf,CAAC,EAIHe,EAAAA,CAAqBf,CAAY,CAAA,CAMjC,IAAMgB,CAAAA,CAAAA,CAJgBhB,CAAAA,CACnB,mBAAmB,OAAO,CAAA,EACzB,aAAA,CAAcpP,UAAAA,CAAW,kBAAkB,CAAA,EAER,gBAAe,CACtD,GAAIoQ,CAAAA,EAAkB,MAAA,CAAOpQ,UAAAA,CAAW,uBAAuB,EAAG,CAChE,IAAMqQ,CAAAA,CAAoBD,CAAAA,CAAiB,OAAA,EAAQ,CAC7CE,EAAc,MAAMC,EAAAA,CAAmBF,CAAiB,CAAA,CACxDtW,CAAAA,CAAS+R,EAAAA,CAAUwE,EAAaJ,CAAAA,CAAO,CAC3C,UAAA,CAAY,CAACM,CAAAA,CAAKC,CAAAA,GAAQA,CAC5B,CAAC,CAAA,CACKC,CAAAA,CAAeC,EAAAA,CAAe5W,CAAM,CAAA,CACvC,QAAQ,iBAAA,CAAmB,OAAO,CAAA,CAClC,OAAA,CAAQ,OAAA,CAAS,GAAG,EACpB,OAAA,CAAQ,OAAA,CAAS,GAAG,CAAA,CACpB,OAAA,CAAQ,OAAA,CAAS,GAAG,CAAA,CACpB,OAAA,CAAQ,OAAA,CAAS,GAAG,CAAA,CACpB,OAAA,CAAQ,UAAW,GAAG,CAAA,CACtB,OAAA,CAAQ,OAAA,CAAS,GAAG,CAAA,CACpB,QAAQ,SAAA,CAAW,GAAG,CAAA,CACtB,OAAA,CAAQ,OAAA,CAAS,GAAG,EACvBqW,CAAAA,CAAiB,eAAA,CAAgBM,CAAY,EAC/C,CAGAE,EAAAA,CAAuBxB,CAAY,EACrC,CAEA,SAASM,EAAAA,CACPN,CAAAA,CACAK,CAAAA,CACA,CACA,IAAMoB,CAAAA,CAAkBzB,CAAAA,CAAa,WAAA,CAAY,SAAS,CAAA,CAE1D,GAAI,CAACyB,CAAAA,CACH,OAAAzB,CAAAA,CAAa,qBAAA,CAAsB,CACjC,IAAA,CAAM,SAAA,CACN,WAAA,CAAa,CAAA,CAAA,EAAIK,CAAM,CAAA,CAAA,CACzB,CAAC,CAAA,CAEML,CAAAA,CAGT,GAAIyB,CAAAA,CAAgB,MAAA,CAAO7Q,UAAAA,CAAW,kBAAkB,CAAA,CAAG,CACzD,IAAM8P,CAAAA,CAAce,CAAAA,CAAgB,cAAA,GAEpC,GAAIf,CAAAA,EAAa,MAAA,CAAO9P,UAAAA,CAAW,sBAAsB,CAAA,CAAG,CAC1D,GACE8P,CAAAA,CACG,WAAA,EAAY,CACZ,GAAA,CAAKG,CAAAA,EACGA,EAAQ,OAAA,EAAQ,CAAE,OAAA,CAAQ,OAAA,CAAS,EAAE,CAC7C,EACA,QAAA,CAASR,CAAAA,CAAO,OAAA,CAAQ,OAAA,CAAS,EAAE,CAAC,EAEvC,OAAOL,CAAAA,CAETU,EAAY,UAAA,CAAWL,CAAM,EAC/B,CAEA,OAAOL,CACT,CAEA,OAAOA,CACT,CAEA,eAAsBD,EAAAA,CAAkBxO,CAAAA,CAAerI,CAAAA,CAAuB,CAC5E,IAAMsL,EAAM,MAAMxI,QAAAA,CAAG,OAAA,CAAQhC,UAAAA,CAAK,IAAA,CAAKyK,MAAAA,GAAU,SAAS,CAAC,CAAA,CACrDjK,CAAAA,CACJtB,CAAAA,EAAQ,aAAA,EAAe,gBAAkB,oBAAA,CACrC2L,CAAAA,CAAW7K,UAAAA,CAAK,IAAA,CAAKwK,CAAAA,CAAK,CAAA,OAAA,EAAUxK,WAAK,QAAA,CAASQ,CAAY,CAAC,CAAA,CAAE,CAAA,CAYvE,OAVgB,IAAI6J,OAAAA,CAAQ,CAC1B,eAAA,CAAiB,EACnB,CAAC,EAC0B,gBAAA,CAAiBQ,CAAAA,CAAUtD,EAAO,CAG3D,UAAA,CACEvH,WAAK,OAAA,CAAQQ,CAAY,CAAA,GAAM,KAAA,CAAQsK,UAAAA,CAAW,EAAA,CAAKA,WAAW,EACtE,CAAC,CAGH,CAEO,SAASqL,EAAAA,CAAcH,EAAuC,CACnE,OAAOA,CAAAA,CACJ,wBAAA,CAAyBpP,UAAAA,CAAW,aAAa,GAChD,YAAA,EAAa,GAAM8Q,SAAAA,CAAU,MAAA,CAC7B,GAAA,CACA,GACN,CAEO,SAASX,EAAAA,CAAqBY,CAAAA,CAA8B,CACjE,IAAMC,CAAAA,CAAaD,EAAI,aAAA,EAAc,CAErC,IAAA,IAAStK,CAAAA,CAAI,CAAA,CAAGA,CAAAA,CAAIuK,EAAW,MAAA,CAAQvK,CAAAA,EAAAA,CAAK,CAC1C,IAAMwK,CAAAA,CAAOD,CAAAA,CAAWvK,CAAC,CAAA,CACzB,GAAIwK,CAAAA,CAAK,MAAA,CAAOjR,UAAAA,CAAW,gBAAgB,EAAG,CAC5C,IAAMkR,CAAAA,CAAmBD,CAAAA,CAAK,aAAA,CAAcjR,UAAAA,CAAW,gBAAgB,CAAA,CACjEmR,CAAAA,CAAaD,CAAAA,CAAiB,aAAA,EAAc,CAAE,OAAA,GAGpDH,CAAAA,CAAI,wBAAA,CAAyBtK,CAAAA,CAAG,CAE9B,IAAA,CAAM,CAAA,IAAA,EAAO0K,EAAW,OAAA,CAAQ,SAAA,CAAW,EAAE,CAAC,CAAA,CAAA,CAAA,CAC9C,WAAA,CAAa,OAAOA,CAAAA,CAAW,OAAA,CAAQ,SAAA,CAAW,EAAE,CAAC,CAAA,CAAA,CACvD,CAAC,CAAA,CAGDD,CAAAA,CAAiB,MAAA,GACnB,CAAA,KAAA,GAAWD,CAAAA,CAAK,OAAOjR,UAAAA,CAAW,kBAAkB,CAAA,CAAG,CAErD,IAAM8P,CAAAA,CADiBmB,EAAK,aAAA,CAAcjR,UAAAA,CAAW,kBAAkB,CAAA,CACpC,cAAA,GAGjC8P,CAAAA,EACAA,CAAAA,CAAY,MAAA,CAAO9P,UAAAA,CAAW,uBAAuB,CAAA,CAGrDmQ,GACEL,CAAAA,CAAY,aAAA,CAAc9P,UAAAA,CAAW,uBAAuB,CAC9D,CAAA,CAEA8P,GACAA,CAAAA,CAAY,MAAA,CAAO9P,UAAAA,CAAW,sBAAsB,CAAA,EAEpDoR,EAAAA,CACEtB,EAAY,aAAA,CAAc9P,UAAAA,CAAW,sBAAsB,CAC7D,EAEJ,CACF,CACF,CAEO,SAASoR,EAAAA,CAAmBzZ,CAAAA,CAA6B,CAC9D,IAAM0Z,EAAW1Z,CAAAA,CAAI,WAAA,EAAY,CACjC,IAAA,IAAS2Z,CAAAA,CAAI,CAAA,CAAGA,EAAID,CAAAA,CAAS,MAAA,CAAQC,CAAAA,EAAAA,CAAK,CACxC,IAAMrB,CAAAA,CAAUoB,EAASC,CAAC,CAAA,CAC1B,GAAIrB,CAAAA,CAAQ,MAAA,CAAOjQ,WAAW,uBAAuB,CAAA,CAEnDmQ,EAAAA,CACEF,CAAAA,CAAQ,aAAA,CAAcjQ,UAAAA,CAAW,uBAAuB,CAC1D,CAAA,CAAA,KAAA,GACSiQ,CAAAA,CAAQ,MAAA,CAAOjQ,UAAAA,CAAW,sBAAsB,EAEzDoR,EAAAA,CACEnB,CAAAA,CAAQ,aAAA,CAAcjQ,UAAAA,CAAW,sBAAsB,CACzD,UACSiQ,CAAAA,CAAQ,MAAA,CAAOjQ,UAAAA,CAAW,aAAa,CAAA,CAAG,CACnD,IAAMmR,CAAAA,CAAalB,CAAAA,CAAQ,OAAA,EAAQ,CAEnCtY,CAAAA,CAAI,aAAA,CAAc2Z,CAAC,CAAA,CACnB3Z,CAAAA,CAAI,aAAA,CAAc2Z,CAAAA,CAAG,CAAA,CAAA,EAAIH,CAAU,GAAG,EACxC,CACF,CACF,CAEO,SAASP,EAAAA,CAAuBG,EAA8B,CACnE,IAAMC,EAAaD,CAAAA,CAAI,aAAA,GAEvB,IAAA,IAAStK,CAAAA,CAAI,CAAA,CAAGA,CAAAA,CAAIuK,CAAAA,CAAW,MAAA,CAAQvK,IAAK,CAC1C,IAAMwK,CAAAA,CAAOD,CAAAA,CAAWvK,CAAC,CAAA,CACzB,GAAIwK,CAAAA,CAAK,MAAA,CAAOjR,UAAAA,CAAW,kBAAkB,CAAA,CAAG,CAC9C,IAAMuR,CAAAA,CAAiBN,CAAAA,CACjBnB,CAAAA,CAAcyB,CAAAA,CAAe,cAAA,EAAe,CAElD,GAAIzB,CAAAA,EAAeA,CAAAA,CAAY,MAAA,CAAO9P,UAAAA,CAAW,aAAa,CAAA,CAAG,CAC/D,IAAM5K,CAAAA,CAAQ0a,CAAAA,CACX,aAAA,CAAc9P,UAAAA,CAAW,aAAa,EACtC,eAAA,EAAgB,CACf5K,CAAAA,CAAM,UAAA,CAAW,KAAK,CAAA,GACxB2b,EAAI,sBAAA,CAAuBtK,CAAAA,CAAG,CAAE,UAAA,CAAYrR,CAAAA,CAAM,KAAA,CAAM,CAAC,CAAE,CAAC,CAAA,CAC5Dmc,CAAAA,CAAe,MAAA,EAAO,EAE1B,MAAWzB,CAAAA,EAAa,MAAA,CAAO9P,UAAAA,CAAW,uBAAuB,CAAA,CAC/D4Q,EAAAA,CAAuBd,CAAsC,CAAA,CAE7DA,CAAAA,EACAA,CAAAA,CAAY,MAAA,CAAO9P,UAAAA,CAAW,sBAAsB,GAEpDwR,EAAAA,CACE1B,CAAAA,CAAY,aAAA,CAAc9P,UAAAA,CAAW,sBAAsB,CAC7D,EAEJ,CACF,CACF,CAEO,SAASwR,EAAAA,CAAoB7Z,CAAAA,CAA6B,CAC/D,IAAM0Z,CAAAA,CAAW1Z,CAAAA,CAAI,WAAA,EAAY,CACjC,IAAA,IAAS2Z,EAAI,CAAA,CAAGA,CAAAA,CAAID,CAAAA,CAAS,MAAA,CAAQC,CAAAA,EAAAA,CAAK,CACxC,IAAMrB,CAAAA,CAAUoB,CAAAA,CAASC,CAAC,CAAA,CAC1B,GAAIrB,EAAQ,MAAA,CAAOjQ,UAAAA,CAAW,uBAAuB,CAAA,CAEnD4Q,EAAAA,CACEX,CAAAA,CAAQ,cAAcjQ,UAAAA,CAAW,uBAAuB,CAC1D,CAAA,CAAA,KAAA,GACSiQ,CAAAA,CAAQ,MAAA,CAAOjQ,WAAW,sBAAsB,CAAA,CAEzDwR,EAAAA,CACEvB,CAAAA,CAAQ,aAAA,CAAcjQ,UAAAA,CAAW,sBAAsB,CACzD,CAAA,CAAA,KAAA,GACSiQ,CAAAA,CAAQ,MAAA,CAAOjQ,UAAAA,CAAW,aAAa,EAAG,CACnD,IAAMmR,CAAAA,CAAalB,CAAAA,CAAQ,OAAA,EAAQ,CAE7BwB,EAAa,+BAAA,CACfA,CAAAA,CAAW,IAAA,CAAKN,CAAU,CAAA,GAC5BxZ,CAAAA,CAAI,cAAc2Z,CAAC,CAAA,CACnB3Z,CAAAA,CAAI,aAAA,CAAc2Z,CAAAA,CAAGH,CAAAA,CAAW,QAAQM,CAAAA,CAAY,IAAI,CAAC,CAAA,EAE7D,CACF,CACF,CAEA,eAAelB,EAAAA,CAAmBmB,CAAAA,CAA2C,CAM3E,IAAMC,GALa,MAAMxC,EAAAA,CACvB,CAAA,cAAA,EAAiBuC,CAAmB,CAAA,CAAA,CACpC,IACF,GAE6B,aAAA,EAAc,CAAE,CAAC,CAAA,CAC9C,GAAIC,CAAAA,EAAW,SAAQ,GAAM3R,UAAAA,CAAW,iBAAA,CAAmB,CAIzD,IAAM8P,CAAAA,CAAAA,CAHe6B,EAClB,kBAAA,EAAmB,EAClB,eAAA,EAAgB,CAAE,CAAC,CAAA,EACS,gBAAe,CAC/C,GAAI7B,CAAAA,EAAa,MAAA,CAAO9P,UAAAA,CAAW,uBAAuB,EACxD,OAAO,MAAM4R,EAAAA,CAA6B9B,CAAW,CAEzD,CAEA,MAAM,IAAI,KAAA,CAAM,sCAAsC,CACxD,CAEA,SAAS8B,EAAAA,CAA6B3R,CAAAA,CAAoC,CACxE,IAAMlG,CAAAA,CAAc,GACpB,IAAA,IAAWsV,CAAAA,IAAYpP,CAAAA,CAAK,aAAA,EAAc,CACxC,GAAIoP,EAAS,MAAA,CAAOrP,UAAAA,CAAW,kBAAkB,CAAA,CAAG,CAClD,IAAM1I,EAAO+X,CAAAA,CAAS,OAAA,EAAQ,CAAE,OAAA,CAAQ,KAAA,CAAO,EAAE,EAE/CA,CAAAA,CAAS,cAAA,EAAe,EAAG,MAAA,CAAOrP,UAAAA,CAAW,uBAAuB,EAEpEjG,CAAAA,CAAOzC,CAAI,CAAA,CAAIsa,EAAAA,CACbvC,CAAAA,CAAS,cAAA,EACX,CAAA,CAEAA,CAAAA,CAAS,cAAA,EAAe,EAAG,MAAA,CAAOrP,UAAAA,CAAW,sBAAsB,CAAA,CAEnEjG,CAAAA,CAAOzC,CAAI,CAAA,CAAIua,EAAAA,CACbxC,CAAAA,CAAS,gBACX,CAAA,CAEAtV,CAAAA,CAAOzC,CAAI,CAAA,CAAIwa,EAAAA,CAAWzC,EAAS,cAAA,EAAgB,EAEvD,CAEF,OAAOtV,CACT,CAEA,SAAS8X,EAAAA,CAA4B5R,CAAAA,CAAqC,CACxE,IAAMlG,CAAAA,CAAgB,EAAC,CACvB,IAAA,IAAWkW,CAAAA,IAAWhQ,CAAAA,CAAK,WAAA,EAAY,CACjCgQ,EAAQ,MAAA,CAAOjQ,UAAAA,CAAW,uBAAuB,CAAA,CACnDjG,CAAAA,CAAO,IAAA,CACL6X,GACE3B,CAAAA,CAAQ,aAAA,CAAcjQ,UAAAA,CAAW,uBAAuB,CAC1D,CACF,EACSiQ,CAAAA,CAAQ,MAAA,CAAOjQ,UAAAA,CAAW,sBAAsB,CAAA,CACzDjG,CAAAA,CAAO,KACL8X,EAAAA,CACE5B,CAAAA,CAAQ,cAAcjQ,UAAAA,CAAW,sBAAsB,CACzD,CACF,CAAA,CAEAjG,CAAAA,CAAO,IAAA,CAAK+X,EAAAA,CAAW7B,CAAO,CAAC,CAAA,CAGnC,OAAOlW,CACT,CAEA,SAAS+X,EAAAA,CAAW7R,EAAgB,CAClC,OAAQA,CAAAA,CAAK,OAAA,EAAQ,EACnB,KAAKD,UAAAA,CAAW,aAAA,CACd,OAAOC,CAAAA,CAAK,OAAA,EAAQ,CACtB,KAAKD,UAAAA,CAAW,cAAA,CACd,OAAO,MAAA,CAAOC,CAAAA,CAAK,OAAA,EAAS,CAAA,CAC9B,KAAKD,UAAAA,CAAW,WAAA,CACd,OAAO,KAAA,CACT,KAAKA,UAAAA,CAAW,YAAA,CACd,OAAO,MAAA,CACT,KAAKA,UAAAA,CAAW,YACd,OAAO,IAAA,CACT,KAAKA,UAAAA,CAAW,sBAAA,CACd,OAAOC,CAAAA,CAAK,WAAA,EAAY,CAAE,GAAA,CAAI6R,EAAU,CAAA,CAC1C,KAAK9R,UAAAA,CAAW,uBAAA,CACd,OAAO4R,EAAAA,CAA6B3R,CAAI,CAAA,CAC1C,QACE,OAAOA,CAAAA,CAAK,OAAA,EAChB,CACF,CAEO,SAAS8R,EAAAA,CACdC,CAAAA,CACA,CACA,IAAMjY,CAAAA,CAA8B,GAEpC,IAAA,IAAWzE,CAAAA,IAAO,MAAA,CAAO,IAAA,CAAK0c,CAAO,CAAA,CAAG,CACtC,IAAM9T,CAAAA,CAAQ5I,CAAAA,CAAI,KAAA,CAAM,GAAG,CAAA,CACrB2c,EAAY/T,CAAAA,CAAM,CAAC,CAAA,CACnBgU,CAAAA,CAAUhU,CAAAA,CAAM,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA,CAAK,GAAG,CAAA,CAEnCgU,CAAAA,GAAY,GACV,OAAOnY,CAAAA,CAAOkY,CAAS,CAAA,EAAM,QAAA,CAC/BlY,CAAAA,CAAOkY,CAAS,CAAA,CAAE,OAAA,CAAU,CAAA,UAAA,EAAa3c,CAAG,CAAA,EAAA,CAAA,CAE5CyE,CAAAA,CAAOkY,CAAS,CAAA,CAAI,CAAA,UAAA,EAAa3c,CAAG,CAAA,EAAA,CAAA,EAGlC,OAAOyE,CAAAA,CAAOkY,CAAS,CAAA,EAAM,QAAA,GAC/BlY,CAAAA,CAAOkY,CAAS,CAAA,CAAI,CAAE,QAAS,CAAA,UAAA,EAAaA,CAAS,CAAA,EAAA,CAAK,CAAA,CAAA,CAE5DlY,CAAAA,CAAOkY,CAAS,EAAEC,CAAO,CAAA,CAAI,CAAA,UAAA,EAAa5c,CAAG,CAAA,EAAA,CAAA,EAEjD,CAGA,OAAW,CAAC2c,CAAAA,CAAW7c,CAAK,CAAA,GAAK,MAAA,CAAO,OAAA,CAAQ2E,CAAM,CAAA,CAElD,OAAO3E,CAAAA,EAAU,QAAA,EACjBA,CAAAA,CAAM,OAAA,GAAY,aAAa6c,CAAS,CAAA,EAAA,CAAA,EACxC,EAAEA,CAAAA,IAAaD,CAAAA,CAAAA,EAEf,OAAO5c,EAAM,OAAA,CAIjB,OAAO2E,CACT,CC1fO,SAASoY,EAAAA,CACd3F,EACAlU,CAAAA,CACA,CACA,IAAM8Z,CAAAA,CAA0D,EAAC,CAC3DC,EAAgB,CAAC,GAAG7F,CAAK,CAAA,CAE/B,GAAI,CAAClU,GAAQ,UAAA,CACX,OAAA0T,EAAAA,CAAmB,EAAE,CAAA,CACdqG,EAGT,IAAA,IAAS5L,CAAAA,CAAI,CAAA,CAAGA,CAAAA,CAAI4L,CAAAA,CAAc,MAAA,CAAQ5L,IAAK,CAC7C,IAAM6L,EAAW7F,EAAAA,CAAkC4F,CAAAA,CAAc5L,CAAC,CAAA,CAAGnO,CAAM,CAAA,CAEvEga,CAAAA,GACFD,CAAAA,CAAc5L,CAAC,EAAI6L,CAAAA,CAAS,GAAA,CAExB,MAAA,CAAO,IAAA,CAAKA,CAAAA,CAAS,OAAO,EAAE,MAAA,CAAS,CAAA,GACzCF,CAAAA,CAAgBE,CAAAA,CAAS,GAAG,CAAA,CAAIA,EAAS,OAAA,CAAA,EAG/C,CAEA,OAAAtG,EAAAA,CAAmBoG,CAAe,CAAA,CAE3BC,CACT,CAIA,eAAsBE,CAAAA,CACpB/F,CAAAA,CACAlU,CAAAA,CACAxC,CAAAA,CAAkC,EAAC,CACnC,CAoCA,OAnCgB,MAAM,OAAA,CAAQ,GAAA,CAC5B0W,EAAM,GAAA,CAAI,MAAO1V,CAAAA,EAAS,CACxB,GAAIqU,EAAAA,CAAYrU,CAAI,CAAA,CAClB,OAAO4X,GAAmB5X,CAAI,CAAA,CAGhC,GAAIoU,CAAAA,CAAMpU,CAAI,CAAA,CAAG,CACf,GAAM,CAACiD,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAACrX,CAAI,CAAA,CAAGhB,CAAO,CAAA,CACpD,GAAI,CACF,OAAO+Y,CAAAA,CAAmB,KAAA,CAAM9U,CAAM,CACxC,CAAA,MAASR,CAAAA,CAAO,CACd,MAAM,IAAI1C,EAAmBC,CAAAA,CAAMyC,CAAK,CAC1C,CACF,CAEA,GAAIzC,EAAK,UAAA,CAAW,GAAG,CAAA,EAAKwB,CAAAA,EAAQ,UAAA,CAAY,CAC9C,IAAM2E,CAAAA,CAAQkV,EAAAA,CAAmC,CAACrb,CAAI,CAAA,CAAGwB,CAAM,EACzD,CAACyB,CAAM,EAAI,MAAMoU,CAAAA,CAAclR,EAAOnH,CAAO,CAAA,CACnD,GAAI,CACF,OAAO+Y,CAAAA,CAAmB,MAAM9U,CAAM,CACxC,CAAA,MAASR,CAAAA,CAAO,CACd,MAAM,IAAI1C,CAAAA,CAAmBC,CAAAA,CAAMyC,CAAK,CAC1C,CACF,CAEA,IAAMH,CAAAA,CAAO,CAAA,OAAA,EAAUd,CAAAA,EAAQ,KAAA,EAAS,aAAa,CAAA,CAAA,EAAIxB,CAAI,CAAA,KAAA,CAAA,CACvD,CAACiD,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAAC/U,CAAI,CAAA,CAAGtD,CAAO,CAAA,CACpD,GAAI,CACF,OAAO+Y,CAAAA,CAAmB,KAAA,CAAM9U,CAAM,CACxC,CAAA,MAASR,CAAAA,CAAO,CACd,MAAM,IAAI1C,CAAAA,CAAmBC,CAAAA,CAAMyC,CAAK,CAC1C,CACF,CAAC,CACH,CAGF,CAGqCsV,EAAmB,MAAA,CAAO,CAC7D,OAAA,CAAS7X,CAAAA,CAAE,MAAA,EAAO,CAAE,UACtB,CAAC,EAID,eAAsBwb,EAAAA,CACpBC,CAAAA,CACAna,EACAxC,CAAAA,CAAkC,EAAC,CACnC,CACAA,CAAAA,CAAU,CACR,SAAU,IAAA,CACV,GAAGA,CACL,CAAA,CAEA,IAAI4c,CAAAA,CAA0D,EAAC,CAC3DC,CAAAA,CAAqE,EAAC,CACtEC,CAAAA,CAAuC,GAErCC,CAAAA,CAAc,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIJ,CAAK,CAAC,CAAA,CAEvCK,CAAAA,CAAU,MAAMP,CAAAA,CAAmBM,CAAAA,CAAava,EAAQxC,CAAO,CAAA,CAE/Did,CAAAA,CAAY,IAAI,GAAA,CACtB,IAAA,IAAStM,EAAI,CAAA,CAAGA,CAAAA,CAAIqM,CAAAA,CAAQ,MAAA,CAAQrM,CAAAA,EAAAA,CAC9BqM,CAAAA,CAAQrM,CAAC,CAAA,EACXsM,CAAAA,CAAU,GAAA,CAAIF,CAAAA,CAAYpM,CAAC,CAAA,CAAGqM,EAAQrM,CAAC,CAAC,CAAA,CAI5C,IAAA,GAAW,CAACuM,CAAAA,CAAYlc,CAAI,CAAA,GAAK,KAAA,CAAM,IAAA,CAAKic,CAAAA,CAAU,OAAA,EAAS,EAAG,CAEhE,IAAME,CAAAA,CAA+D,CACnE,GAAGnc,CAAAA,CACH,QAASkc,CACX,CAAA,CAGA,GAFAN,CAAAA,CAAQ,IAAA,CAAKO,CAAc,EAEvBnc,CAAAA,CAAK,oBAAA,CAAsB,CAE7B,IAAIoc,CAAAA,CAAuBpc,EAAK,oBAAA,CAGhC,GAAKwB,CAAAA,EAAQ,UAAA,CASX4a,CAAAA,CAAuBf,EAAAA,CACrBrb,EAAK,oBAAA,CACLwB,CACF,CAAA,CAAA,KAZuB,CACvB,IAAM6a,CAAAA,CAAiBrc,EAAK,oBAAA,CAAqB,MAAA,CAAQyF,CAAAA,EACvDA,CAAAA,CAAI,UAAA,CAAW,GAAG,CACpB,CAAA,CACA,GAAI4W,CAAAA,CAAe,MAAA,CAAS,CAAA,CAAG,CAC7B,GAAM,CAAE,QAAA,CAAApG,CAAS,CAAA,CAAIlV,EAAAA,CAA+Bsb,CAAAA,CAAe,CAAC,CAAC,CAAA,CACrE,MAAM,IAAI1c,CAAAA,CAA2BsW,CAAQ,CAC/C,CACF,CAOA,GAAM,CAAE,KAAA,CAAAP,CAAAA,CAAO,cAAA4G,CAAc,CAAA,CAAI,MAAMC,EAAAA,CACrCH,CAAAA,CACA5a,EACAxC,CAAAA,CACA,IAAI,GAAA,CAAI+c,CAAW,CACrB,CAAA,CACAF,EAAmB,IAAA,CAAK,GAAGnG,CAAK,CAAA,CAChCoG,CAAAA,CAA2B,IAAA,CAAK,GAAGQ,CAAa,EAClD,CACF,CAKA,GAHAV,CAAAA,CAAQ,KAAK,GAAGC,CAAkB,CAAA,CAG9BC,CAAAA,CAA2B,MAAA,CAAS,CAAA,CAAG,CAEzC,IAAMU,CAAAA,CAAsB,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIV,CAA0B,CAAC,CAAA,CAGpEW,CAAAA,CAAqBD,CAAAA,CAAoB,MAAA,CAC5Chc,CAAAA,EAAS,CAACA,CAAAA,CAAK,UAAA,CAAW,GAAG,CAChC,CAAA,CACMkc,CAAAA,CAAqBF,EAAoB,MAAA,CAAQhc,CAAAA,EACrDA,CAAAA,CAAK,UAAA,CAAW,GAAG,CACrB,EAGA,GAAIkc,CAAAA,CAAmB,MAAA,CAAS,CAAA,CAAG,CAEjC,IAAMC,EAAa,MAAMlB,CAAAA,CACvBiB,CAAAA,CACAlb,CAAAA,CACAxC,CACF,CAAA,CAEA,QAAS2Q,CAAAA,CAAI,CAAA,CAAGA,CAAAA,CAAIgN,CAAAA,CAAW,MAAA,CAAQhN,CAAAA,EAAAA,CAAK,CAE1C,IAAMwM,CAAAA,CAA+D,CACnE,GAFWQ,CAAAA,CAAWhN,CAAC,EAGvB,OAAA,CAAS+M,CAAAA,CAAmB/M,CAAC,CAC/B,CAAA,CACAiM,CAAAA,CAAQ,KAAKO,CAAc,EAC7B,CACF,CAGA,GAAIM,CAAAA,CAAmB,OAAS,CAAA,CAAG,CACjC,IAAMtO,CAAAA,CAAQ,MAAMyO,EAAAA,GACpB,GAAI,CAACzO,GAASyN,CAAAA,CAAQ,MAAA,GAAW,EAC/B,OAAO,IAAA,CAGT,GAAIzN,CAAAA,CAAO,CAELsO,CAAAA,CAAmB,SAAS,OAAO,CAAA,EACrCA,CAAAA,CAAmB,OAAA,CAAQ,OAAO,CAAA,CAKpC,IAAMI,CAAAA,CAAyB,EAAC,CAChC,IAAA,IAAWrc,EAAAA,IAAQic,CAAAA,CAAoB,CACrC,IAAMK,EAAAA,CAAmB,MAAMC,EAAAA,CAC7Bvc,EAAAA,CACAgB,CAAAA,CACAxC,CACF,CAAA,CACA6d,CAAAA,CAAa,IAAA,CAAK,GAAGC,EAAgB,EACvC,CAGA,IAAME,CAAAA,CAAa,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIH,CAAY,CAAC,CAAA,CAC/C5Z,CAAAA,CAAS,MAAMoU,CAAAA,CAAc2F,CAAAA,CAAYhe,CAAO,CAAA,CAC9Cie,CAAAA,CAAkB/c,CAAAA,CAAE,KAAA,CAAM6X,CAAkB,CAAA,CAAE,MAAM9U,CAAM,CAAA,CAChE2Y,CAAAA,CAAQ,IAAA,CAAK,GAAGqB,CAAe,EACjC,CACF,CACF,CAEA,GAAI,CAACrB,CAAAA,CAAQ,OACX,OAAO,IAAA,CAST,GAAA,CACEG,CAAAA,CAAY,QAAA,CAAS,OAAO,GAC5BD,CAAAA,CAA2B,QAAA,CAAS,OAAO,CAAA,GAEvCta,CAAAA,CAAO,QAAA,CAAS,UAAW,CAC7B,IAAM4X,CAAAA,CAAQ,MAAM8D,EAAAA,CAAiB1b,CAAAA,CAAO,SAAS,SAAA,CAAWA,CAAM,CAAA,CAClE4X,CAAAA,EACFwC,CAAAA,CAAQ,OAAA,CAAQxC,CAAK,EAEzB,CAIF,IAAM+D,CAAAA,CAAY,IAAI,GAAA,CACtBvB,EAAQ,OAAA,CAAS5b,CAAAA,EAAS,CAExB,IAAMod,CAAAA,CAASpd,EAAK,OAAA,EAAWA,CAAAA,CAAK,IAAA,CACpCmd,CAAAA,CAAU,GAAA,CAAInd,CAAAA,CAAMod,CAAM,EAC5B,CAAC,CAAA,CAGDxB,CAAAA,CAAUyB,EAAAA,CAA6BzB,CAAAA,CAASuB,CAAS,CAAA,CAIzDvB,CAAAA,CAAQ,IAAA,CAAK,CAACnK,CAAAA,CAAGC,CAAAA,GACXD,EAAE,IAAA,GAAS,gBAAA,EAAoBC,CAAAA,CAAE,IAAA,GAAS,gBAAA,CACrC,EAAA,CAELD,EAAE,IAAA,GAAS,gBAAA,EAAoBC,CAAAA,CAAE,IAAA,GAAS,gBAAA,CACrC,CAAA,CAEF,CACR,CAAA,CAED,IAAI4L,CAAAA,CAAW,EAAC,CAChB1B,CAAAA,CAAQ,QAAS5b,CAAAA,EAAS,CACxBsd,CAAAA,CAAWtI,EAAAA,CAAUsI,CAAAA,CAAUtd,CAAAA,CAAK,UAAY,EAAE,EACpD,CAAC,CAAA,CAED,IAAIkb,EAAU,EAAC,CACfU,CAAAA,CAAQ,OAAA,CAAS5b,CAAAA,EAAS,CACxBkb,EAAUlG,EAAAA,CAAUkG,CAAAA,CAASlb,CAAAA,CAAK,OAAA,EAAW,EAAE,EACjD,CAAC,CAAA,CAED,IAAIud,CAAAA,CAAM,EAAC,CACX3B,EAAQ,OAAA,CAAS5b,CAAAA,EAAS,CACxBud,CAAAA,CAAMvI,EAAAA,CAAUuI,CAAAA,CAAKvd,EAAK,GAAA,EAAO,EAAE,EACrC,CAAC,CAAA,CAED,IAAIwd,CAAAA,CAAO,EAAA,CACX5B,CAAAA,CAAQ,OAAA,CAAS5b,CAAAA,EAAS,CACpBA,EAAK,IAAA,GACPwd,CAAAA,EAAQ,CAAA,EAAGxd,CAAAA,CAAK,IAAI;AAAA,CAAA,EAExB,CAAC,CAAA,CAED,IAAIyd,CAAAA,CAAU,EAAC,CACf7B,CAAAA,CAAQ,OAAA,CAAS5b,CAAAA,EAAS,CACxByd,CAAAA,CAAUzI,EAAAA,CAAUyI,EAASzd,CAAAA,CAAK,OAAA,EAAW,EAAE,EACjD,CAAC,CAAA,CAGD,IAAM0d,CAAAA,CAAoB,MAAMlJ,EAAAA,CAC9BoH,CAAAA,CAAQ,GAAA,CAAK5b,CAAAA,EAASA,CAAAA,CAAK,KAAA,EAAS,EAAE,CAAA,CACtCwB,CACF,CAAA,CAEMmW,GAAAA,CAASgG,CAAAA,CAAgC,KAAA,CAAM,CACnD,YAAA,CAAc3I,EAAAA,CAAU,GAAA,CAAI4G,CAAAA,CAAQ,GAAA,CAAK5b,CAAAA,EAASA,CAAAA,CAAK,YAAA,EAAgB,EAAE,CAAC,CAAA,CAC1E,eAAA,CAAiBgV,EAAAA,CAAU,GAAA,CACzB4G,CAAAA,CAAQ,GAAA,CAAK5b,GAASA,CAAAA,CAAK,eAAA,EAAmB,EAAE,CAClD,CAAA,CACA,KAAA,CAAO0d,CAAAA,CACP,SAAAJ,CAAAA,CACA,OAAA,CAAApC,CAAAA,CACA,GAAA,CAAAqC,CAAAA,CACA,IAAA,CAAAC,CACF,CAAC,CAAA,CAED,OAAI,MAAA,CAAO,IAAA,CAAKC,CAAO,CAAA,CAAE,MAAA,CAAS,CAAA,GAChC9F,IAAO,OAAA,CAAU8F,CAAAA,CAAAA,CAGZ9F,GACT,CAEA,eAAe4E,EAAAA,CACb/I,CAAAA,CACAhS,CAAAA,CACAxC,EAAkC,EAAC,CACnC4e,CAAAA,CAAuB,IAAI,GAAA,CAC3B,CACA,IAAMlI,CAAAA,CAA8C,EAAC,CAC/C4G,CAAAA,CAA0B,EAAC,CAEjC,IAAA,IAAW7W,CAAAA,IAAO+N,CAAAA,CAChB,GAAI,CAAAoK,CAAAA,CAAQ,GAAA,CAAInY,CAAG,CAAA,CAAA,CAMnB,GAHAmY,CAAAA,CAAQ,GAAA,CAAInY,CAAG,CAAA,CAGX2O,CAAAA,CAAM3O,CAAG,CAAA,EAAK4O,EAAAA,CAAY5O,CAAG,CAAA,CAAG,CAClC,GAAM,CAACzF,CAAI,CAAA,CAAI,MAAMyb,CAAAA,CAAmB,CAAChW,CAAG,CAAA,CAAGjE,EAAQxC,CAAO,CAAA,CAC9D,GAAIgB,CAAAA,GACF0V,CAAAA,CAAM,IAAA,CAAK1V,CAAI,CAAA,CACXA,CAAAA,CAAK,oBAAA,CAAA,CAAsB,CAE7B,IAAM6d,CAAAA,CAAerc,CAAAA,EAAQ,UAAA,CACzB6Z,EAAAA,CACErb,EAAK,oBAAA,CACLwB,CACF,CAAA,CACAxB,CAAAA,CAAK,oBAAA,CAEH8d,CAAAA,CAAS,MAAMvB,EAAAA,CACnBsB,EACArc,CAAAA,CACAxC,CAAAA,CACA4e,CACF,CAAA,CACAlI,CAAAA,CAAM,IAAA,CAAK,GAAGoI,CAAAA,CAAO,KAAK,CAAA,CAC1BxB,CAAAA,CAAc,IAAA,CAAK,GAAGwB,CAAAA,CAAO,aAAa,EAC5C,CAEJ,CAAA,KAAA,GAESrY,CAAAA,CAAI,UAAA,CAAW,GAAG,CAAA,EAAKjE,CAAAA,EAAQ,UAAA,CAAY,CAElD,GAAM,CAAE,QAAA,CAAAyU,CAAS,CAAA,CAAIlV,EAAAA,CAA+B0E,CAAG,CAAA,CACvD,GAAIwQ,GAAY,EAAEA,CAAAA,IAAYzU,CAAAA,CAAO,UAAA,CAAA,CACnC,MAAM,IAAI7B,CAAAA,CAA2BsW,CAAQ,EAK/C,GAAM,CAACjW,CAAI,CAAA,CAAI,MAAMyb,CAAAA,CAAmB,CAAChW,CAAG,CAAA,CAAGjE,CAAAA,CAAQxC,CAAO,CAAA,CAC9D,GAAIgB,CAAAA,GACF0V,CAAAA,CAAM,IAAA,CAAK1V,CAAI,CAAA,CACXA,CAAAA,CAAK,oBAAA,CAAA,CAAsB,CAE7B,IAAM6d,CAAAA,CAAerc,CAAAA,EAAQ,UAAA,CACzB6Z,GACErb,CAAAA,CAAK,oBAAA,CACLwB,CACF,CAAA,CACAxB,CAAAA,CAAK,oBAAA,CAEH8d,CAAAA,CAAS,MAAMvB,GACnBsB,CAAAA,CACArc,CAAAA,CACAxC,CAAAA,CACA4e,CACF,CAAA,CACAlI,CAAAA,CAAM,IAAA,CAAK,GAAGoI,CAAAA,CAAO,KAAK,CAAA,CAC1BxB,CAAAA,CAAc,IAAA,CAAK,GAAGwB,CAAAA,CAAO,aAAa,EAC5C,CAEJ,CAAA,KAAA,GAGExB,CAAAA,CAAc,IAAA,CAAK7W,CAAG,CAAA,CAElBjE,CAAAA,CACF,GAAI,CACF,GAAM,CAACxB,CAAI,CAAA,CAAI,MAAMyb,CAAAA,CAAmB,CAAChW,CAAG,EAAGjE,CAAAA,CAAQxC,CAAO,CAAA,CAC9D,GAAIgB,CAAAA,EAAQA,CAAAA,CAAK,oBAAA,CAAsB,CAErC,IAAM6d,CAAAA,CAAerc,CAAAA,EAAQ,UAAA,CACzB6Z,EAAAA,CACErb,CAAAA,CAAK,oBAAA,CACLwB,CACF,EACAxB,CAAAA,CAAK,oBAAA,CAEH8d,CAAAA,CAAS,MAAMvB,EAAAA,CACnBsB,CAAAA,CACArc,CAAAA,CACAxC,CAAAA,CACA4e,CACF,CAAA,CACAlI,CAAAA,CAAM,IAAA,CAAK,GAAGoI,CAAAA,CAAO,KAAK,CAAA,CAC1BxB,EAAc,IAAA,CAAK,GAAGwB,CAAAA,CAAO,aAAa,EAC5C,CACF,CAAA,KAAgB,CAGhB,CAAA,CAKN,OAAO,CAAE,KAAA,CAAApI,CAAAA,CAAO,aAAA,CAAA4G,CAAc,CAChC,CAEA,eAAeS,EAAAA,CACb7d,CAAAA,CACAsC,CAAAA,CACAxC,CAAAA,CAAkC,EAAC,CACnC,CACA,GAAIoV,CAAAA,CAAMlV,CAAG,CAAA,CACX,OAAO,CAACA,CAAG,CAAA,CAGb,GAAM,CAAE,aAAA,CAAAod,CAAc,CAAA,CAAI,MAAMC,EAAAA,CAC9B,CAACrd,CAAG,CAAA,CACJsC,CAAAA,CACAxC,CAAAA,CACA,IAAI,GACN,CAAA,CAEM+e,CAAAA,CAAQvc,CAAAA,CAAO,aAAA,EAAe,IAChC,MAAMqC,EAAAA,CAAyBrC,CAAAA,CAAO,aAAA,CAAc,GAAA,CAAKA,CAAAA,CAAO,KAAK,CAAA,CACrEA,EAAO,KAAA,CAELwc,CAAAA,CAAO1B,CAAAA,CAAc,GAAA,CAAK9b,CAAAA,EAC9BwW,EAAAA,CAAmB5C,CAAAA,CAAM5T,CAAI,EAAIA,CAAAA,CAAO,CAAA,OAAA,EAAUud,CAAK,CAAA,CAAA,EAAIvd,CAAI,CAAA,KAAA,CAAO,CACxE,CAAA,CAEA,OAAO,KAAA,CAAM,IAAA,CAAK,IAAI,GAAA,CAAIwd,CAAI,CAAC,CACjC,CAEA,eAAed,EAAAA,CAAiB1c,CAAAA,CAAcgB,CAAAA,CAAgB,CAC5D,GAAM,CAACyH,CAAAA,CAAWpE,CAAe,CAAA,CAAI,MAAM,OAAA,CAAQ,GAAA,CAAI,CACrDgJ,EAAAA,CAAqBrN,CAAI,CAAA,CACzBgG,GAAoChF,CAAM,CAC5C,CAAC,CAAA,CACD,GAAI,CAACyH,CAAAA,CACH,OAAO,IAAA,CAIT,IAAMmQ,CAAAA,CAAQ,CACZ,IAAA,CAAA5Y,CAAAA,CACA,IAAA,CAAM,gBAAA,CACN,SAAU,CACR,MAAA,CAAQ,CACN,KAAA,CAAO,CACL,MAAA,CAAQ,CACN,YAAA,CAAc,CACZ,EAAA,CAAI,eAAA,CACJ,EAAA,CAAI,2BAAA,CACJ,EAAA,CAAI,2BACN,CAAA,CACA,MAAA,CAAQ,EACV,CACF,CACF,CACF,CAAA,CACA,OAAA,CAAS,CACP,KAAA,CAAO,EAAC,CACR,KAAA,CAAO,CACL,MAAA,CAAQ,QACV,CAAA,CACA,IAAA,CAAM,EACR,CACF,CAAA,CAEA,OAAIgB,CAAAA,CAAO,QAAA,CAAS,YAAA,GAClB4X,CAAAA,CAAM,SAAS,MAAA,CAAO,KAAA,CAAM,MAAA,CAAO,MAAA,CAAS,CAC1C,GAAGA,CAAAA,CAAM,QAAA,CAAS,OAAO,KAAA,CAAM,MAAA,CAAO,MAAA,CACtC,GAAG6B,EAAAA,CAAoChS,CAAAA,CAAU,OAAA,CAAQ,IAAA,EAAQ,EAAE,CACrE,CAAA,CACAmQ,CAAAA,CAAM,OAAA,CAAU,CACd,KAAA,CAAO,CACL,GAAGnQ,CAAAA,CAAU,OAAA,CAAQ,KAAA,CACrB,GAAGmQ,CAAAA,CAAM,OAAA,CAAQ,KACnB,EACA,KAAA,CAAO,CACL,GAAGnQ,CAAAA,CAAU,OAAA,CAAQ,KAAA,CACrB,GAAGmQ,CAAAA,CAAM,QAAQ,KACnB,CAAA,CACA,IAAA,CAAM,CACJ,GAAGnQ,CAAAA,CAAU,OAAA,CAAQ,IAAA,CACrB,GAAGmQ,CAAAA,CAAM,OAAA,CAAQ,IACnB,CACF,CAAA,CAEIvU,CAAAA,GAAoB,IAAA,EAAQoE,EAAU,SAAA,GACxCmQ,CAAAA,CAAM,OAAA,CAAU,CACd,KAAA,CAAO,CACL,GAAGnQ,CAAAA,CAAU,UAAU,KAAA,CACvB,GAAGmQ,CAAAA,CAAM,OAAA,CAAQ,KACnB,CAAA,CACA,KAAA,CAAO,CACL,OAAQ,UAAA,CACR,GAAGnQ,CAAAA,CAAU,SAAA,CAAU,KACzB,CAAA,CACA,IAAA,CAAM,CACJ,GAAGA,CAAAA,CAAU,SAAA,CAAU,IACzB,CACF,CAAA,CAAA,CAAA,CAIGmQ,CACT,CAEA,SAAS6E,CAAAA,CACPje,CAAAA,CACAod,CAAAA,CACA,CACA,IAAMc,CAAAA,CAAad,CAAAA,EAAUpd,CAAAA,CAAK,IAAA,CAE5Bme,CAAAA,CAAOC,UAAAA,CAAW,QAAQ,CAAA,CAC7B,MAAA,CAAOF,CAAU,CAAA,CACjB,OAAO,KAAK,CAAA,CACZ,SAAA,CAAU,CAAA,CAAG,CAAC,CAAA,CAEjB,OAAO,CAAA,EAAGle,CAAAA,CAAK,IAAI,CAAA,EAAA,EAAKme,CAAI,CAAA,CAC9B,CAEA,SAASE,EAAAA,CAAoCtK,EAAoB,CAC/D,GAAIK,CAAAA,CAAML,CAAU,CAAA,CAAG,CAErB,IAAMuK,CAAAA,CADM,IAAI,GAAA,CAAIvK,CAAU,CAAA,CACT,QAAA,CACfnV,CAAAA,CAAQ0f,CAAAA,CAAS,KAAA,CAAM,kBAAkB,EACzC9d,CAAAA,CAAO5B,CAAAA,CAAQA,CAAAA,CAAM,CAAC,CAAA,CAAI0D,UAAAA,CAAK,QAAA,CAASgc,CAAAA,CAAU,OAAO,CAAA,CAE/D,OAAO,CACL,IAAA,CAAA9d,CAAAA,CACA,IAAA,CAAMyd,CAAAA,CAAgB,CAAE,IAAA,CAAAzd,CAAK,CAAA,CAAGuT,CAAU,CAC5C,CACF,CAEA,GAAIM,GAAYN,CAAU,CAAA,CAAG,CAC3B,IAAMnV,CAAAA,CAAQmV,CAAAA,CAAW,KAAA,CAAM,kBAAkB,EAC3CvT,CAAAA,CAAO5B,CAAAA,CAAQA,CAAAA,CAAM,CAAC,CAAA,CAAI0D,UAAAA,CAAK,QAAA,CAASyR,CAAAA,CAAY,OAAO,CAAA,CAEjE,OAAO,CACL,IAAA,CAAAvT,CAAAA,CACA,IAAA,CAAMyd,CAAAA,CAAgB,CAAE,IAAA,CAAAzd,CAAK,CAAA,CAAGuT,CAAU,CAC5C,CACF,CAEA,GAAM,CAAE,IAAA,CAAA/T,CAAK,CAAA,CAAIe,EAAAA,CAA+BgT,CAAU,CAAA,CAC1D,OAAO,CACL,KAAM/T,CAAAA,CACN,IAAA,CAAMie,CAAAA,CAAgB,CAAE,IAAA,CAAMje,CAAK,CAAA,CAAG+T,CAAU,CAClD,CACF,CAEA,SAASsJ,EAAAA,CACP3H,CAAAA,CACAyH,CAAAA,CACA,CACA,IAAMoB,CAAAA,CAAU,IAAI,GAAA,CACdC,CAAAA,CAAa,IAAI,GAAA,CACjBC,CAAAA,CAAW,IAAI,IACfC,CAAAA,CAAgB,IAAI,GAAA,CAE1BhJ,CAAAA,CAAM,OAAA,CAAS1V,CAAAA,EAAS,CACtB,IAAMod,EAASD,CAAAA,CAAU,GAAA,CAAInd,CAAI,CAAA,EAAKA,CAAAA,CAAK,IAAA,CACrCme,CAAAA,CAAOF,CAAAA,CAAgBje,CAAAA,CAAMod,CAAM,CAAA,CAEzCmB,CAAAA,CAAQ,GAAA,CAAIJ,CAAAA,CAAMne,CAAI,CAAA,CACtBwe,EAAW,GAAA,CAAIL,CAAAA,CAAMne,CAAI,CAAA,CACzBye,CAAAA,CAAS,GAAA,CAAIN,CAAAA,CAAM,CAAC,EACpBO,CAAAA,CAAc,GAAA,CAAIP,CAAAA,CAAM,EAAE,EAC5B,CAAC,CAAA,CAGD,IAAMQ,CAAAA,CAAc,IAAI,GAAA,CACxBjJ,CAAAA,CAAM,OAAA,CAAS1V,CAAAA,EAAS,CACtB,IAAMod,CAAAA,CAASD,CAAAA,CAAU,GAAA,CAAInd,CAAI,CAAA,EAAKA,CAAAA,CAAK,IAAA,CACrCme,CAAAA,CAAOF,EAAgBje,CAAAA,CAAMod,CAAM,CAAA,CAEpCuB,CAAAA,CAAY,GAAA,CAAI3e,CAAAA,CAAK,IAAI,CAAA,EAC5B2e,EAAY,GAAA,CAAI3e,CAAAA,CAAK,IAAA,CAAM,EAAE,CAAA,CAE/B2e,CAAAA,CAAY,GAAA,CAAI3e,EAAK,IAAI,CAAA,CAAG,IAAA,CAAKme,CAAI,CAAA,CAEjCf,CAAAA,GAAWpd,CAAAA,CAAK,IAAA,GACb2e,CAAAA,CAAY,GAAA,CAAIvB,CAAM,CAAA,EACzBuB,CAAAA,CAAY,GAAA,CAAIvB,CAAAA,CAAQ,EAAE,CAAA,CAE5BuB,CAAAA,CAAY,GAAA,CAAIvB,CAAM,CAAA,CAAG,IAAA,CAAKe,CAAI,CAAA,EAEtC,CAAC,CAAA,CAEDzI,CAAAA,CAAM,OAAA,CAAS1V,CAAAA,EAAS,CACtB,IAAM4e,CAAAA,CAAazB,CAAAA,CAAU,IAAInd,CAAI,CAAA,EAAKA,CAAAA,CAAK,IAAA,CACzC6e,CAAAA,CAAWZ,CAAAA,CAAgBje,CAAAA,CAAM4e,CAAU,CAAA,CAE7C5e,CAAAA,CAAK,oBAAA,EACPA,CAAAA,CAAK,oBAAA,CAAqB,OAAA,CAASyF,CAAAA,EAAQ,CACzC,IAAIqZ,CAAAA,CAEEC,CAAAA,CAAeJ,CAAAA,CAAY,GAAA,CAAIlZ,CAAG,CAAA,EAAK,EAAC,CAC9C,GAAIsZ,CAAAA,CAAa,MAAA,GAAW,CAAA,CAC1BD,CAAAA,CAAUC,CAAAA,CAAa,CAAC,CAAA,CAAA,KAAA,GACfA,EAAa,MAAA,CAAS,CAAA,CAG/BD,CAAAA,CAAUC,CAAAA,CAAa,CAAC,CAAA,CAAA,KACnB,CACL,GAAM,CAAE,IAAA,CAAAve,CAAK,CAAA,CAAI6d,EAAAA,CAAoC5Y,CAAG,CAAA,CAClDuZ,CAAAA,CAAcL,EAAY,GAAA,CAAIne,CAAI,CAAA,EAAK,EAAC,CAC1Cwe,CAAAA,CAAY,MAAA,CAAS,CAAA,GACvBF,EAAUE,CAAAA,CAAY,CAAC,CAAA,EAE3B,CAEIF,CAAAA,EAAWP,CAAAA,CAAQ,GAAA,CAAIO,CAAO,IAChCJ,CAAAA,CAAc,GAAA,CAAII,CAAO,CAAA,CAAG,IAAA,CAAKD,CAAQ,CAAA,CACzCJ,CAAAA,CAAS,GAAA,CAAII,CAAAA,CAAUJ,CAAAA,CAAS,GAAA,CAAII,CAAQ,CAAA,CAAK,CAAC,CAAA,EAEtD,CAAC,EAEL,CAAC,CAAA,CAGD,IAAMI,CAAAA,CAAkB,EAAC,CACnBC,CAAAA,CAA+C,EAAC,CAQtD,IANAT,CAAAA,CAAS,OAAA,CAAQ,CAACU,CAAAA,CAAQhB,CAAAA,GAAS,CAC7BgB,IAAW,CAAA,EACbF,CAAAA,CAAM,IAAA,CAAKd,CAAI,EAEnB,CAAC,CAAA,CAEMc,CAAAA,CAAM,MAAA,CAAS,CAAA,EAAG,CACvB,IAAMG,CAAAA,CAAcH,CAAAA,CAAM,KAAA,EAAM,CAC1Bjf,EAAOue,CAAAA,CAAQ,GAAA,CAAIa,CAAW,CAAA,CACpCF,CAAAA,CAAO,IAAA,CAAKlf,CAAI,CAAA,CAEhB0e,EAAc,GAAA,CAAIU,CAAW,CAAA,CAAG,OAAA,CAASC,CAAAA,EAAkB,CACzD,IAAMC,CAAAA,CAAYb,EAAS,GAAA,CAAIY,CAAa,CAAA,CAAK,CAAA,CACjDZ,CAAAA,CAAS,GAAA,CAAIY,CAAAA,CAAeC,CAAS,CAAA,CAEjCA,CAAAA,GAAc,CAAA,EAChBL,CAAAA,CAAM,IAAA,CAAKI,CAAa,EAE5B,CAAC,EACH,CAEA,GAAIH,CAAAA,CAAO,MAAA,GAAWxJ,CAAAA,CAAM,MAAA,CAAQ,CAClC,OAAA,CAAQ,KAAK,gDAAgD,CAAA,CAG7D,IAAM6J,CAAAA,CAAe,IAAI,GAAA,CACvBL,CAAAA,CAAO,GAAA,CAAKlf,GAAS,CACnB,IAAMod,CAAAA,CAASD,CAAAA,CAAU,GAAA,CAAInd,CAAI,CAAA,EAAKA,CAAAA,CAAK,IAAA,CAC3C,OAAOie,CAAAA,CAAgBje,CAAAA,CAAMod,CAAM,CACrC,CAAC,CACH,EAEA1H,CAAAA,CAAM,OAAA,CAAS1V,CAAAA,EAAS,CACtB,IAAMod,CAAAA,CAASD,CAAAA,CAAU,GAAA,CAAInd,CAAI,CAAA,EAAKA,CAAAA,CAAK,IAAA,CACrCme,CAAAA,CAAOF,CAAAA,CAAgBje,CAAAA,CAAMod,CAAM,CAAA,CACpCmC,EAAa,GAAA,CAAIpB,CAAI,CAAA,EACxBe,CAAAA,CAAO,IAAA,CAAKlf,CAAI,EAEpB,CAAC,EACH,CAEA,OAAOkf,CACT,CCrsBO,SAASM,EAAAA,CAAY/c,CAAAA,CAAgB,CAgC1C,GA/BAiG,CAAAA,CAAO,KAAA,EAAM,CACbA,EAAO,KAAA,CACL,sEACF,CAAA,CACAA,CAAAA,CAAO,KAAA,CAAM,0DAA0D,CAAA,CACvEA,CAAAA,CAAO,MAAM,EAAE,CAAA,CACX,OAAOjG,CAAAA,EAAU,QAAA,GACnBiG,CAAAA,CAAO,KAAA,CAAMjG,CAAK,CAAA,CAClBiG,CAAAA,CAAO,KAAA,EAAM,CACb,OAAA,CAAQ,IAAA,CAAK,CAAC,CAAA,CAAA,CAGZjG,aAAiB3D,CAAAA,GACf2D,CAAAA,CAAM,OAAA,GACRiG,CAAAA,CAAO,KAAA,CAAMjG,CAAAA,CAAM,KAAA,CAAQ,QAAA,CAAW,UAAU,CAAA,CAChDiG,CAAAA,CAAO,KAAA,CAAMjG,CAAAA,CAAM,OAAO,CAAA,CAAA,CAGxBA,CAAAA,CAAM,KAAA,GACRiG,EAAO,KAAA,CAAM;AAAA,QAAA,CAAY,CAAA,CACzBA,EAAO,KAAA,CAAMjG,CAAAA,CAAM,KAAK,CAAA,CAAA,CAGtBA,CAAAA,CAAM,UAAA,GACRiG,CAAAA,CAAO,KAAA,CAAM;AAAA,WAAA,CAAe,EAC5BA,CAAAA,CAAO,KAAA,CAAMjG,EAAM,UAAU,CAAA,CAAA,CAE/BiG,EAAO,KAAA,EAAM,CACb,QAAQ,IAAA,CAAK,CAAC,GAGZjG,CAAAA,YAAiBvC,CAAAA,CAAE,SAAU,CAC/BwI,CAAAA,CAAO,MAAM,oBAAoB,CAAA,CACjC,OAAW,CAAClK,CAAAA,CAAKF,CAAK,CAAA,GAAK,MAAA,CAAO,QAAQmE,CAAAA,CAAM,OAAA,GAAU,WAAW,CAAA,CACnEiG,EAAO,KAAA,CAAM,CAAA,EAAA,EAAKzH,EAAY,IAAA,CAAKzC,CAAG,CAAC,CAAA,EAAA,EAAKF,CAAK,EAAE,CAAA,CAErDoK,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,KAAK,CAAC,EAChB,CAEIjG,CAAAA,YAAiB,KAAA,GACnBiG,EAAO,KAAA,CAAMjG,CAAAA,CAAM,OAAO,CAAA,CAC1BiG,CAAAA,CAAO,OAAM,CACb,OAAA,CAAQ,KAAK,CAAC,CAAA,CAAA,CAGhBA,EAAO,KAAA,EAAM,CACb,QAAQ,IAAA,CAAK,CAAC,EAChB,CCdA,eAAsB+W,EAAAA,CACpBjf,CAAAA,CACAxB,EAIA,CACA,GAAM,CAAE,MAAA,CAAAwC,CAAAA,CAAQ,SAAAke,CAAS,CAAA,CAAI1gB,GAAW,EAAC,CAEzC,GAAIoV,CAAAA,CAAM5T,CAAI,EAAG,CACf,GAAM,CAACyC,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAAC7W,CAAI,CAAA,CAAG,CAAE,SAAAkf,CAAS,CAAC,EACzD,GAAI,CACF,OAAOC,GAAAA,CAAe,KAAA,CAAM1c,CAAM,CACpC,CAAA,MAASR,EAAO,CACd,MAAM,IAAI1C,CAAAA,CAAmBS,CAAAA,CAAMiC,CAAK,CAC1C,CACF,CAEA,GAAI,CAACjC,EAAK,UAAA,CAAW,GAAG,EACtB,MAAM,IAAID,GAA8BC,CAAI,CAAA,CAG9C,IAAIZ,CAAAA,CAAeY,CAAAA,CACdZ,EAAa,QAAA,CAAS,WAAW,IACpCA,CAAAA,CAAe,CAAA,EAAGA,CAAY,CAAA,SAAA,CAAA,CAAA,CAGhC,IAAMggB,EAAgBjK,EAAAA,CACpB/V,CAAAA,CACAkV,GAAmBtT,CAAM,CAC3B,EAEA,GAAI,CAACoe,GAAe,GAAA,CAClB,MAAM,IAAI3gB,CAAAA,CAAsBW,CAAY,EAG1CggB,CAAAA,CAAc,OAAA,EAAW,OAAO,IAAA,CAAKA,CAAAA,CAAc,OAAO,CAAA,CAAE,MAAA,CAAS,GACvE1K,EAAAA,CAAmB,CACjB,CAAC0K,CAAAA,CAAc,GAAG,EAAGA,CAAAA,CAAc,OACrC,CAAC,CAAA,CAGH,GAAM,CAAC3c,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAACuI,EAAc,GAAG,CAAA,CAAG,CAAE,QAAA,CAAAF,CAAS,CAAC,CAAA,CAEtE,GAAI,CACF,OAAOC,GAAAA,CAAe,MAAM1c,CAAM,CACpC,OAASR,CAAAA,CAAO,CACd,MAAM,IAAI1C,CAAAA,CAAmBH,EAAc6C,CAAK,CAClD,CACF,CAEA,eAAsBod,GACpBnK,CAAAA,CACA1W,CAAAA,CAIA,CACA,GAAM,CAAE,OAAAwC,CAAAA,CAAQ,QAAA,CAAAke,EAAW,KAAM,CAAA,CAAI1gB,GAAW,EAAC,CAEjD,OAAAqW,EAAAA,EAAqB,CAEdoG,EAAmB/F,CAAAA,CAAOZ,EAAAA,CAAmBtT,CAAM,CAAA,CAAG,CAAE,SAAAke,CAAS,CAAC,CAC3E,CAEA,eAAsBI,GACpBpK,CAAAA,CACA1W,CAAAA,CAIA,CACA,GAAM,CAAE,OAAAwC,CAAAA,CAAQ,QAAA,CAAAke,EAAW,KAAM,CAAA,CAAI1gB,GAAW,EAAC,CAEjD,OAAAqW,EAAAA,EAAqB,CACdqG,GAAoBhG,CAAAA,CAAOZ,EAAAA,CAAmBtT,CAAM,CAAA,CAAG,CAAE,SAAAke,CAAS,CAAC,CAC5E,CAEA,eAAsBK,GACpBrf,CAAAA,CACA1B,CAAAA,CACA,CACA,GAAM,CAAE,SAAA0gB,CAAAA,CAAW,IAAK,EAAI1gB,CAAAA,EAAW,GAGlC0gB,CAAAA,EACH5d,EAAAA,CAAS,aAAY,CAGvB,IAAMS,EAAe,MAAMT,EAAAA,CAAS,OAAOpB,CAAG,CAAA,CAE9C,GAAI,CAAC6B,CAAAA,CAGH,OAAO,CACL,UAAA,CAAYpE,CACd,CAAA,CAIF,IAAM6hB,EAAmB9f,CAAAA,CACtB,MAAA,CAAO,CACN,UAAA,CAAY+f,CAAAA,CAAqB,UACnC,CAAC,EACA,SAAA,CAAU1d,CAAAA,CAAa,MAAM,CAAA,CAEhC,GAAI,CAACyd,CAAAA,CAAiB,OAAA,CACpB,MAAM,IAAIvf,EAAAA,CAAiBC,EAAKsf,CAAAA,CAAiB,KAAK,EAIxD,OAAO,CACL,WAAY,CACV,GAAG7hB,EACH,GAAI6hB,CAAAA,CAAiB,KAAK,UAAA,EAAc,EAC1C,CACF,CACF,CAEA,eAAsBpD,EAAAA,EAAyB,CAC7C,GAAI,CACF,GAAM,CAAC3Z,CAAM,EAAI,MAAMoU,CAAAA,CAAc,CAAC,YAAY,CAAC,EAEnD,OAAO6I,CAAAA,CAAoB,MAAMjd,CAAM,CACzC,OAASR,CAAAA,CAAO,CACdiG,EAAO,KAAA,CAAM;AAAA,CAAI,CAAA,CACjB8W,EAAAA,CAAY/c,CAAK,EACnB,CACF,CAEA,eAAsB0d,EAAAA,EAAoB,CACxC,GAAI,CACF,GAAM,CAACld,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAAC,mBAAmB,CAAC,CAAA,CAE1D,OAAO+I,GAAAA,CAAa,KAAA,CAAMnd,CAAM,CAClC,CAAA,MAASR,CAAAA,CAAO,CACd,OAAAiG,EAAO,KAAA,CAAM;AAAA,CAAI,EACjB8W,EAAAA,CAAY/c,CAAK,CAAA,CACV,EACT,CACF,CAEA,eAAsB+H,EAAAA,EAAmB,CACvC,GAAI,CACF,GAAM,CAACvH,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAAC,kBAAkB,CAAC,CAAA,CACzD,OAAOgJ,GAAAA,CAAY,MAAMpd,CAAM,CACjC,CAAA,MAASR,CAAAA,CAAO,CACd,OAAA+c,EAAAA,CAAY/c,CAAK,CAAA,CACV,EACT,CACF,CAEA,eAAsB6d,IAAwB,CAC5C,OAAOpiB,EACT,CAEA,eAAsB2P,EAAAA,CAAqB5E,CAAAA,CAAmB,CAC5D,GAAI,CACF,GAAM,CAAChG,CAAM,CAAA,CAAI,MAAMoU,CAAAA,CAAc,CAAC,CAAA,OAAA,EAAUpO,CAAS,OAAO,CAAC,CAAA,CAEjE,OAAOsX,CAAAA,CAAwB,MAAMtd,CAAM,CAC7C,CAAA,MAASR,CAAAA,CAAO,CACd+c,EAAAA,CAAY/c,CAAK,EACnB,CACF,CAKA,eAAsB+d,EAAAA,CACpBrS,CAAAA,CACAwN,CAAAA,CACA,CACA,IAAM8E,CAAAA,CAA4C,EAAC,CAEnD,IAAA,IAAWjgB,KAAQmb,CAAAA,CAAO,CACxB,IAAM+E,CAAAA,CAAQvS,EAAM,IAAA,CAAMuS,CAAAA,EAAUA,CAAAA,CAAM,IAAA,GAASlgB,CAAI,CAAA,CAEvD,GAAKkgB,CAAAA,GAILD,CAAAA,CAAK,KAAKC,CAAK,CAAA,CAEXA,CAAAA,CAAM,oBAAA,CAAA,CAAsB,CAC9B,IAAMlN,CAAAA,CAAe,MAAMgN,EAAAA,CAAYrS,EAAOuS,CAAAA,CAAM,oBAAoB,EACxED,CAAAA,CAAK,IAAA,CAAK,GAAGjN,CAAY,EAC3B,CACF,CAEA,OAAOiN,CAAAA,CAAK,MAAA,CACV,CAACE,CAAAA,CAAWxS,EAAOyS,CAAAA,GACjBA,CAAAA,CAAK,SAAA,CAAWC,CAAAA,EAAMA,EAAE,IAAA,GAASF,CAAAA,CAAU,IAAI,CAAA,GAAMxS,CACzD,CACF,CAKA,eAAsB2S,EAAAA,CACpB/C,EACA0C,CAAAA,CACA,CACA,GAAI,CACF,IAAMta,CAAAA,CAAQsa,CAAAA,CAAK,GAAA,CAAKzgB,CAAAA,EAAS,UAAU+d,CAAK,CAAA,CAAA,EAAI/d,EAAK,IAAI,CAAA,KAAA,CAAO,EAEpE,OAAA,CADgB,MAAMqX,CAAAA,CAAclR,CAAK,GAC1B,GAAA,CAAKlD,CAAAA,EAAW8U,CAAAA,CAAmB,KAAA,CAAM9U,CAAM,CAAC,CACjE,CAAA,MAASR,CAAAA,CAAO,CACd,OAAA+c,EAAAA,CAAY/c,CAAK,CAAA,CACV,EACT,CACF,CAKA,eAAsBse,GACpBvf,CAAAA,CACAxB,CAAAA,CACAghB,CAAAA,CACA,CACA,GAAIA,CAAAA,CACF,OAAOA,CAAAA,CAGT,GAAIhhB,EAAK,IAAA,GAAS,aAAA,CAChB,OAAOwB,CAAAA,CAAO,cAAc,EAAA,EAAMA,CAAAA,CAAO,aAAA,CAAc,UAAA,CAGzD,GAAM,CAACyf,CAAAA,CAAQ3b,CAAI,CAAA,CAAItF,EAAK,IAAA,EAAM,KAAA,CAAM,GAAG,CAAA,EAAK,EAAC,CACjD,OAAMihB,CAAAA,IAAUzf,CAAAA,CAAO,cAIhBc,UAAAA,CAAK,IAAA,CACVd,CAAAA,CAAO,aAAA,CAAcyf,CAA2C,CAAA,CAChE3b,CACF,CAAA,CANS,IAOX,CAEA,eAAsB4b,EAAAA,CAAmBliB,CAAAA,CAAkC,CACzEA,EAAU,CACR,QAAA,CAAU,KACV,GAAGA,CACL,EAEA,IAAME,CAAAA,CAAM,CAAA,EAAGlB,EAAY,mBACrB,CAACmjB,CAAI,CAAA,CAAI,MAAM9J,EAAc,CAACnY,CAAG,CAAA,CAAG,CACxC,SAAUF,CAAAA,CAAQ,QACpB,CAAC,CAAA,CAED,GAAI,CACF,OAAOoiB,CAAAA,CAAsB,KAAA,CAAMD,CAAI,CACzC,CAAA,MAAS1e,CAAAA,CAAO,CACd,MAAIA,CAAAA,YAAiBvC,CAAAA,CAAE,QAAA,CACf,IAAIS,GAA0B8B,CAAK,CAAA,CAGrCA,CACR,CACF,CCrSA,eAAsB4e,EAAAA,CACpBC,CAAAA,CACAtiB,IAOA,CACA,GAAM,CAAE,KAAA,CAAAuiB,EAAO,KAAA,CAAAC,CAAAA,CAAO,MAAA,CAAAC,CAAAA,CAAQ,OAAAjgB,CAAAA,CAAQ,QAAA,CAAAke,CAAS,CAAA,CAAI1gB,KAAW,EAAC,CAE3D0iB,CAAAA,CAAqD,GAEzD,IAAA,IAAWzL,CAAAA,IAAYqL,CAAAA,CAAY,CAGjC,IAAMK,CAAAA,CAAAA,CAAAA,CAFe,MAAMlC,GAAYxJ,CAAAA,CAAU,CAAE,OAAAzU,CAAAA,CAAQ,QAAA,CAAAke,CAAS,CAAC,GAE7B,KAAA,EAAS,EAAC,EAAG,GAAA,CAAK1f,IAAU,CAClE,IAAA,CAAMA,CAAAA,CAAK,IAAA,CACX,KAAMA,CAAAA,CAAK,IAAA,CACX,WAAA,CAAaA,CAAAA,CAAK,YAClB,QAAA,CAAUiW,CAAAA,CACV,kBAAA,CAAoB2L,EAAAA,CAClB5hB,EAAK,IAAA,CACLiW,CACF,CACF,CAAA,CAAE,EAEFyL,CAAAA,CAAWA,CAAAA,CAAS,MAAA,CAAOC,CAAiB,EAC9C,CAEIJ,CAAAA,GACFG,CAAAA,CAAWG,EAAAA,CAAYH,EAAU,CAC/B,KAAA,CAAAH,CAAAA,CACA,KAAA,CAAOG,EAAS,MAAA,CAChB,IAAA,CAAM,CAAC,MAAA,CAAQ,aAAa,CAC9B,CAAC,CAAA,CAAA,CAGH,IAAMI,EAAmBL,CAAAA,EAAU,CAAA,CAC7BM,CAAAA,CAAkBP,CAAAA,EAASE,EAAS,MAAA,CACpCM,CAAAA,CAAaN,CAAAA,CAAS,MAAA,CAEtBze,EAA8C,CAClD,UAAA,CAAY,CACV,KAAA,CAAO+e,EACP,MAAA,CAAQF,CAAAA,CACR,KAAA,CAAOC,CAAAA,CACP,QAASD,CAAAA,CAAmBC,CAAAA,CAAkBC,CAChD,CAAA,CACA,KAAA,CAAON,EAAS,KAAA,CAAMI,CAAAA,CAAkBA,CAAAA,CAAmBC,CAAe,CAC5E,CAAA,CAEA,OAAOE,CAAAA,CAAoB,KAAA,CAAMhf,CAAM,CACzC,CAEA,IAAMif,EAAAA,CAAuBhiB,EAC1B,MAAA,CAAO,CACN,IAAA,CAAMA,CAAAA,CAAE,QAAO,CACf,IAAA,CAAMA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC1B,WAAA,CAAaA,CAAAA,CAAE,QAAO,CAAE,QAAA,EAAS,CACjC,QAAA,CAAUA,EAAE,MAAA,EAAO,CAAE,UAAS,CAC9B,kBAAA,CAAoBA,EAAE,MAAA,EAAO,CAAE,QAAA,EACjC,CAAC,CAAA,CACA,WAAA,EAAY,CAIf,SAAS2hB,GASPnM,CAAAA,CACA1W,CAAAA,CAGA,CACAA,CAAAA,CAAU,CACR,KAAA,CAAO,GAAA,CACP,SAAA,CAAW,IAAA,CACX,GAAGA,CACL,CAAA,CAQA,IAAMgd,CAAAA,CANgBmG,GAAU,EAAA,CAAGnjB,CAAAA,CAAQ,KAAA,CAAO0W,CAAAA,CAAO,CACvD,IAAA,CAAM1W,CAAAA,CAAQ,IAAA,CACd,SAAA,CAAWA,EAAQ,SAAA,CACnB,KAAA,CAAOA,EAAQ,KACjB,CAAC,EAE6B,GAAA,CAAKiE,CAAAA,EAAWA,CAAAA,CAAO,GAAG,EAExD,OAAO/C,CAAAA,CAAE,KAAA,CAAMgiB,EAAoB,EAAE,KAAA,CAAMlG,CAAO,CACpD,CAEA,SAAS5H,EAAAA,CAAMgO,CAAAA,CAAyB,CACtC,GAAI,CACF,OAAA,IAAI,GAAA,CAAIA,CAAM,CAAA,CACP,EACT,CAAA,KAAQ,CACN,OAAO,MACT,CACF,CAKO,SAASR,EAAAA,CACdphB,CAAAA,CACAyV,EACA,CAEA,GAAI,CAAC7B,EAAAA,CAAM6B,CAAQ,EACjB,OAAO,CAAA,EAAGA,CAAQ,CAAA,CAAA,EAAIzV,CAAI,CAAA,CAAA,CAI5B,IAAM6hB,CAAAA,CAAcpM,CAAAA,CAAS,QAAQ,KAAK,CAAA,CAAI,CAAA,CACxCqM,CAAAA,CAAUrM,EAAS,OAAA,CAAQ,GAAA,CAAKoM,CAAW,CAAA,CAEjD,GAAIC,CAAAA,GAAY,EAAA,CAAI,CAElB,IAAMC,EAAatM,CAAAA,CAAS,OAAA,CAAQ,GAAA,CAAKoM,CAAW,EACpD,GAAIE,CAAAA,GAAe,EAAA,CAAI,CAErB,IAAMC,CAAAA,CAAcvM,CAAAA,CAAS,UAAU,CAAA,CAAGsM,CAAU,EAG9CE,CAAAA,CAFgBxM,CAAAA,CAAS,SAAA,CAAUsM,CAAU,EAEhB,OAAA,CAAQ,eAAA,CAAiB/hB,CAAI,CAAA,CAChE,OAAOgiB,CAAAA,CAAcC,CACvB,CAEA,OAAOxM,CACT,CAGA,IAAMyM,CAAAA,CAAWzM,CAAAA,CAAS,UAAU,CAAA,CAAGqM,CAAO,CAAA,CACxCK,CAAAA,CAAe1M,EAAS,SAAA,CAAUqM,CAAO,CAAA,CAIzCM,CAAAA,CACJD,EAAa,OAAA,CAAQ,GAAG,CAAA,GAAM,EAAA,CAC1BA,EAAa,OAAA,CAAQ,GAAG,EACxBA,CAAAA,CAAa,MAAA,CACbE,EAAWF,CAAAA,CAAa,SAAA,CAAU,CAAA,CAAGC,CAAO,EAC5CE,CAAAA,CAAgBH,CAAAA,CAAa,SAAA,CAAUC,CAAO,EAG9CG,CAAAA,CAAYF,CAAAA,CAAS,WAAA,CAAY,UAAU,EAC7CG,CAAAA,CAAcH,CAAAA,CACdE,CAAAA,GAAc,EAAA,GAChBC,EACEH,CAAAA,CAAS,SAAA,CAAU,CAAA,CAAGE,CAAS,EAC/BviB,CAAAA,CACAqiB,CAAAA,CAAS,SAAA,CAAUE,CAAAA,CAAY,CAAiB,CAAA,CAAA,CAIpD,IAAMN,CAAAA,CAAeK,CAAAA,CAAc,QAAQ,eAAA,CAAiBtiB,CAAI,EAEhE,OAAOkiB,CAAAA,CAAWM,EAAcP,CAClC", "file": "chunk-OYVM23Y7.js", "sourcesContent": ["import { registryConfigSchema } from \"@/src/schema\"\nimport { z } from \"zod\"\n\nexport const REGISTRY_URL =\n  process.env.REGISTRY_URL ?? \"https://ui.shadcn.com/r\"\n\nexport const FALLBACK_STYLE = \"new-york-v4\"\n\nexport const BASE_COLORS = [\n  {\n    name: \"neutral\",\n    label: \"Neutral\",\n  },\n  {\n    name: \"gray\",\n    label: \"Gray\",\n  },\n  {\n    name: \"zinc\",\n    label: \"Zinc\",\n  },\n  {\n    name: \"stone\",\n    label: \"Stone\",\n  },\n  {\n    name: \"slate\",\n    label: \"Slate\",\n  },\n] as const\n\n// Built-in registries that are always available and cannot be overridden\nexport const BUILTIN_REGISTRIES: z.infer<typeof registryConfigSchema> = {\n  \"@shadcn\": `${REGISTRY_URL}/styles/{style}/{name}.json`,\n}\n\nexport const BUILTIN_MODULES = new Set([\n  [\n    // Node.js built-in modules\n    // From https://github.com/sindresorhus/builtin-modules.\n    \"node:assert\",\n    \"assert\",\n    \"node:assert/strict\",\n    \"assert/strict\",\n    \"node:async_hooks\",\n    \"async_hooks\",\n    \"node:buffer\",\n    \"buffer\",\n    \"node:child_process\",\n    \"child_process\",\n    \"node:cluster\",\n    \"cluster\",\n    \"node:console\",\n    \"console\",\n    \"node:constants\",\n    \"constants\",\n    \"node:crypto\",\n    \"crypto\",\n    \"node:dgram\",\n    \"dgram\",\n    \"node:diagnostics_channel\",\n    \"diagnostics_channel\",\n    \"node:dns\",\n    \"dns\",\n    \"node:dns/promises\",\n    \"dns/promises\",\n    \"node:domain\",\n    \"domain\",\n    \"node:events\",\n    \"events\",\n    \"node:fs\",\n    \"fs\",\n    \"node:fs/promises\",\n    \"fs/promises\",\n    \"node:http\",\n    \"http\",\n    \"node:http2\",\n    \"http2\",\n    \"node:https\",\n    \"https\",\n    \"node:inspector\",\n    \"inspector\",\n    \"node:inspector/promises\",\n    \"inspector/promises\",\n    \"node:module\",\n    \"module\",\n    \"node:net\",\n    \"net\",\n    \"node:os\",\n    \"os\",\n    \"node:path\",\n    \"path\",\n    \"node:path/posix\",\n    \"path/posix\",\n    \"node:path/win32\",\n    \"path/win32\",\n    \"node:perf_hooks\",\n    \"perf_hooks\",\n    \"node:process\",\n    \"process\",\n    \"node:querystring\",\n    \"querystring\",\n    \"node:quic\",\n    \"node:readline\",\n    \"readline\",\n    \"node:readline/promises\",\n    \"readline/promises\",\n    \"node:repl\",\n    \"repl\",\n    \"node:sea\",\n    \"node:sqlite\",\n    \"node:stream\",\n    \"stream\",\n    \"node:stream/consumers\",\n    \"stream/consumers\",\n    \"node:stream/promises\",\n    \"stream/promises\",\n    \"node:stream/web\",\n    \"stream/web\",\n    \"node:string_decoder\",\n    \"string_decoder\",\n    \"node:test\",\n    \"node:test/reporters\",\n    \"node:timers\",\n    \"timers\",\n    \"node:timers/promises\",\n    \"timers/promises\",\n    \"node:tls\",\n    \"tls\",\n    \"node:trace_events\",\n    \"trace_events\",\n    \"node:tty\",\n    \"tty\",\n    \"node:url\",\n    \"url\",\n    \"node:util\",\n    \"util\",\n    \"node:util/types\",\n    \"util/types\",\n    \"node:v8\",\n    \"v8\",\n    \"node:vm\",\n    \"vm\",\n    \"node:wasi\",\n    \"wasi\",\n    \"node:worker_threads\",\n    \"worker_threads\",\n    \"node:zlib\",\n    \"zlib\",\n\n    // Bun built-in modules.\n    \"bun\",\n    \"bun:test\",\n    \"bun:sqlite\",\n    \"bun:ffi\",\n    \"bun:jsc\",\n    \"bun:internal\",\n  ],\n])\n\nexport const DEPRECATED_COMPONENTS = [\n  {\n    name: \"toast\",\n    deprecatedBy: \"sonner\",\n    message:\n      \"The toast component is deprecated. Use the sonner component instead.\",\n  },\n  {\n    name: \"toaster\",\n    deprecatedBy: \"sonner\",\n    message:\n      \"The toaster component is deprecated. Use the sonner component instead.\",\n  },\n]\n", "export function expandEnvVars(value: string) {\n  return value.replace(/\\${(\\w+)}/g, (_match, key) => process.env[key] || \"\")\n}\n\nexport function extractEnvVars(value: string) {\n  const vars: string[] = []\n  const regex = /\\${(\\w+)}/g\n  let match: RegExpExecArray | null\n\n  while ((match = regex.exec(value)) !== null) {\n    vars.push(match[1])\n  }\n\n  return vars\n}\n", "import { z } from \"zod\"\n\n// Error codes for programmatic error handling\nexport const RegistryErrorCode = {\n  // Network errors\n  NETWORK_ERROR: \"NETWORK_ERROR\",\n  NOT_FOUND: \"NOT_FOUND\",\n  UNAUTHORIZED: \"UNAUTHORIZ<PERSON>\",\n  FORBIDDEN: \"FORBIDDEN\",\n  FETCH_ERROR: \"FETCH_ERROR\",\n\n  // Configuration errors\n  NOT_CONFIGURED: \"NOT_CONFIGURED\",\n  INVALID_CONFIG: \"INVALID_CONFIG\",\n  MISSING_ENV_VARS: \"MISSING_ENV_VARS\",\n\n  // File system errors\n  LOCAL_FILE_ERROR: \"LOCAL_FILE_ERROR\",\n\n  // Parsing errors\n  PARSE_ERROR: \"PARSE_ERROR\",\n  VALIDATION_ERROR: \"VALIDATION_ERROR\",\n\n  // Generic errors\n  UNKNOWN_ERROR: \"UNKNOWN_ERROR\",\n} as const\n\nexport type RegistryErrorCode =\n  (typeof RegistryErrorCode)[keyof typeof RegistryErrorCode]\n\nexport class RegistryError extends Error {\n  public readonly code: RegistryErrorCode\n  public readonly statusCode?: number\n  public readonly context?: Record<string, unknown>\n  public readonly suggestion?: string\n  public readonly timestamp: Date\n  public readonly cause?: unknown\n\n  constructor(\n    message: string,\n    options: {\n      code?: RegistryErrorCode\n      statusCode?: number\n      cause?: unknown\n      context?: Record<string, unknown>\n      suggestion?: string\n    } = {}\n  ) {\n    super(message)\n    this.name = \"RegistryError\"\n    this.code = options.code || RegistryErrorCode.UNKNOWN_ERROR\n    this.statusCode = options.statusCode\n    this.cause = options.cause\n    this.context = options.context\n    this.suggestion = options.suggestion\n    this.timestamp = new Date()\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      code: this.code,\n      statusCode: this.statusCode,\n      context: this.context,\n      suggestion: this.suggestion,\n      timestamp: this.timestamp,\n      stack: this.stack,\n    }\n  }\n}\n\nexport class RegistryNotFoundError extends RegistryError {\n  constructor(public readonly url: string, cause?: unknown) {\n    const message = `The item at ${url} was not found. It may not exist at the registry.`\n\n    super(message, {\n      code: RegistryErrorCode.NOT_FOUND,\n      statusCode: 404,\n      cause,\n      context: { url },\n      suggestion:\n        \"Check if the item name is correct and the registry URL is accessible.\",\n    })\n    this.name = \"RegistryNotFoundError\"\n  }\n}\n\nexport class RegistryUnauthorizedError extends RegistryError {\n  constructor(public readonly url: string, cause?: unknown) {\n    const message = `You are not authorized to access the item at ${url}. If this is a remote registry, you may need to authenticate.`\n\n    super(message, {\n      code: RegistryErrorCode.UNAUTHORIZED,\n      statusCode: 401,\n      cause,\n      context: { url },\n      suggestion:\n        \"Check your authentication credentials and environment variables.\",\n    })\n    this.name = \"RegistryUnauthorizedError\"\n  }\n}\n\nexport class RegistryForbiddenError extends RegistryError {\n  constructor(public readonly url: string, cause?: unknown) {\n    const message = `You are not authorized to access the item at ${url}. If this is a remote registry, you may need to authenticate.`\n\n    super(message, {\n      code: RegistryErrorCode.FORBIDDEN,\n      statusCode: 403,\n      cause,\n      context: { url },\n      suggestion:\n        \"Check your authentication credentials and environment variables.\",\n    })\n    this.name = \"RegistryForbiddenError\"\n  }\n}\n\nexport class RegistryFetchError extends RegistryError {\n  constructor(\n    public readonly url: string,\n    statusCode?: number,\n    public readonly responseBody?: string,\n    cause?: unknown\n  ) {\n    // Use the error detail from the server if available\n    const baseMessage = statusCode\n      ? `Failed to fetch from registry (${statusCode}): ${url}`\n      : `Failed to fetch from registry: ${url}`\n\n    const message =\n      typeof cause === \"string\" && cause\n        ? `${baseMessage} - ${cause}`\n        : baseMessage\n\n    let suggestion = \"Check your network connection and try again.\"\n    if (statusCode === 404) {\n      suggestion =\n        \"The requested resource was not found. Check the URL or item name.\"\n    } else if (statusCode === 500) {\n      suggestion = \"The registry server encountered an error. Try again later.\"\n    } else if (statusCode && statusCode >= 400 && statusCode < 500) {\n      suggestion = \"There was a client error. Check your request parameters.\"\n    }\n\n    super(message, {\n      code: RegistryErrorCode.FETCH_ERROR,\n      statusCode,\n      cause,\n      context: { url, responseBody },\n      suggestion,\n    })\n    this.name = \"RegistryFetchError\"\n  }\n}\n\nexport class RegistryNotConfiguredError extends RegistryError {\n  constructor(public readonly registryName: string | null) {\n    const message = registryName\n      ? `Unknown registry \"${registryName}\". Make sure it is defined in components.json as follows:\n{\n  \"registries\": {\n    \"${registryName}\": \"[URL_TO_REGISTRY]\"\n  }\n}`\n      : `Unknown registry. Make sure it is defined in components.json under \"registries\".`\n\n    super(message, {\n      code: RegistryErrorCode.NOT_CONFIGURED,\n      context: { registryName },\n      suggestion:\n        \"Add the registry configuration to your components.json file. Consult the registry documentation for the correct format.\",\n    })\n    this.name = \"RegistryNotConfiguredError\"\n  }\n}\n\nexport class RegistryLocalFileError extends RegistryError {\n  constructor(public readonly filePath: string, cause?: unknown) {\n    super(`Failed to read local registry file: ${filePath}`, {\n      code: RegistryErrorCode.LOCAL_FILE_ERROR,\n      cause,\n      context: { filePath },\n      suggestion: \"Check if the file exists and you have read permissions.\",\n    })\n    this.name = \"RegistryLocalFileError\"\n  }\n}\n\nexport class RegistryParseError extends RegistryError {\n  public readonly parseError: unknown\n\n  constructor(public readonly item: string, parseError: unknown) {\n    let message = `Failed to parse registry item: ${item}`\n\n    if (parseError instanceof z.ZodError) {\n      message = `Failed to parse registry item: ${item}\\n${parseError.errors\n        .map((e) => `  - ${e.path.join(\".\")}: ${e.message}`)\n        .join(\"\\n\")}`\n    }\n\n    super(message, {\n      code: RegistryErrorCode.PARSE_ERROR,\n      cause: parseError,\n      context: { item },\n      suggestion:\n        \"The registry item may be corrupted or have an invalid format. Please make sure it returns a valid JSON object. See https://ui.shadcn.com/schema/registry-item.json.\",\n    })\n\n    this.parseError = parseError\n    this.name = \"RegistryParseError\"\n  }\n}\n\nexport class RegistryMissingEnvironmentVariablesError extends RegistryError {\n  constructor(\n    public readonly registryName: string,\n    public readonly missingVars: string[]\n  ) {\n    const message =\n      `Registry \"${registryName}\" requires the following environment variables:\\n\\n` +\n      missingVars.map((v) => `  • ${v}`).join(\"\\n\")\n\n    super(message, {\n      code: RegistryErrorCode.MISSING_ENV_VARS,\n      context: { registryName, missingVars },\n      suggestion:\n        \"Set the required environment variables to your .env or .env.local file.\",\n    })\n    this.name = \"RegistryMissingEnvironmentVariablesError\"\n  }\n}\n\nexport class RegistryInvalidNamespaceError extends RegistryError {\n  constructor(public readonly name: string) {\n    const message = `Invalid registry namespace: \"${name}\". Registry names must start with @ (e.g., @shadcn, @v0).`\n\n    super(message, {\n      code: RegistryErrorCode.VALIDATION_ERROR,\n      context: { name },\n      suggestion:\n        \"Use a valid registry name starting with @ or provide a direct URL to the registry.\",\n    })\n    this.name = \"RegistryInvalidNamespaceError\"\n  }\n}\n\nexport class ConfigMissingError extends RegistryError {\n  constructor(public readonly cwd: string) {\n    const message = `No components.json found in ${cwd} or parent directories.`\n\n    super(message, {\n      code: RegistryErrorCode.NOT_CONFIGURED,\n      context: { cwd },\n      suggestion:\n        \"Run 'npx shadcn@latest init' to create a components.json file, or check that you're in the correct directory.\",\n    })\n    this.name = \"ConfigMissingError\"\n  }\n}\n\nexport class ConfigParseError extends RegistryError {\n  constructor(public readonly cwd: string, parseError: unknown) {\n    let message = `Invalid components.json configuration in ${cwd}.`\n\n    if (parseError instanceof z.ZodError) {\n      message = `Invalid components.json configuration in ${cwd}:\\n${parseError.errors\n        .map((e) => `  - ${e.path.join(\".\")}: ${e.message}`)\n        .join(\"\\n\")}`\n    }\n\n    super(message, {\n      code: RegistryErrorCode.INVALID_CONFIG,\n      cause: parseError,\n      context: { cwd },\n      suggestion:\n        \"Check your components.json file for syntax errors or invalid configuration. Run 'npx shadcn@latest init' to regenerate a valid configuration.\",\n    })\n    this.name = \"ConfigParseError\"\n  }\n}\n\nexport class RegistriesIndexParseError extends RegistryError {\n  public readonly parseError: unknown\n\n  constructor(parseError: unknown) {\n    let message = \"Failed to parse registries index\"\n\n    if (parseError instanceof z.ZodError) {\n      const invalidNamespaces = parseError.errors\n        .filter((e) => e.path.length > 0)\n        .map((e) => `\"${e.path[0]}\"`)\n        .filter((v, i, arr) => arr.indexOf(v) === i) // remove duplicates\n\n      if (invalidNamespaces.length > 0) {\n        message = `Failed to parse registries index. Invalid registry namespace(s): ${invalidNamespaces.join(\n          \", \"\n        )}\\n${parseError.errors\n          .map((e) => `  - ${e.path.join(\".\")}: ${e.message}`)\n          .join(\"\\n\")}`\n      } else {\n        message = `Failed to parse registries index:\\n${parseError.errors\n          .map((e) => `  - ${e.path.join(\".\")}: ${e.message}`)\n          .join(\"\\n\")}`\n      }\n    }\n\n    super(message, {\n      code: RegistryErrorCode.PARSE_ERROR,\n      cause: parseError,\n      context: { parseError },\n      suggestion:\n        \"The registries index may be corrupted or have invalid registry namespace format. Registry names must start with @ (e.g., @shadcn, @example).\",\n    })\n\n    this.parseError = parseError\n    this.name = \"RegistriesIndexParseError\"\n  }\n}\n", "// Valid registry name pattern: @namespace where namespace is alphanumeric with hyphens/underscores\nconst REGISTRY_PATTERN = /^(@[a-zA-Z0-9](?:[a-zA-Z0-9-_]*[a-zA-Z0-9])?)\\/(.+)$/\n\nexport function parseRegistryAndItemFromString(name: string) {\n  if (!name.startsWith(\"@\")) {\n    return {\n      registry: null,\n      item: name,\n    }\n  }\n\n  const match = name.match(REGISTRY_PATTERN)\n  if (match) {\n    return {\n      registry: match[1],\n      item: match[2],\n    }\n  }\n\n  return {\n    registry: null,\n    item: name,\n  }\n}\n", "export const FRAMEWORKS = {\n  \"next-app\": {\n    name: \"next-app\",\n    label: \"Next.js\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/next\",\n      tailwind: \"https://tailwindcss.com/docs/guides/nextjs\",\n    },\n  },\n  \"next-pages\": {\n    name: \"next-pages\",\n    label: \"Next.js\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/next\",\n      tailwind: \"https://tailwindcss.com/docs/guides/nextjs\",\n    },\n  },\n  remix: {\n    name: \"remix\",\n    label: \"Remix\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/remix\",\n      tailwind: \"https://tailwindcss.com/docs/guides/remix\",\n    },\n  },\n  \"react-router\": {\n    name: \"react-router\",\n    label: \"React Router\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/react-router\",\n      tailwind:\n        \"https://tailwindcss.com/docs/installation/framework-guides/react-router\",\n    },\n  },\n  vite: {\n    name: \"vite\",\n    label: \"Vite\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/vite\",\n      tailwind: \"https://tailwindcss.com/docs/guides/vite\",\n    },\n  },\n  astro: {\n    name: \"astro\",\n    label: \"Astro\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/astro\",\n      tailwind: \"https://tailwindcss.com/docs/guides/astro\",\n    },\n  },\n  laravel: {\n    name: \"laravel\",\n    label: \"Laravel\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/laravel\",\n      tailwind: \"https://tailwindcss.com/docs/guides/laravel\",\n    },\n  },\n  \"tanstack-start\": {\n    name: \"tanstack-start\",\n    label: \"TanStack Start\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/tanstack\",\n      tailwind: \"https://tailwindcss.com/docs/installation/using-postcss\",\n    },\n  },\n  gatsby: {\n    name: \"gatsby\",\n    label: \"Gatsby\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/gatsby\",\n      tailwind: \"https://tailwindcss.com/docs/guides/gatsby\",\n    },\n  },\n  expo: {\n    name: \"expo\",\n    label: \"Expo\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/expo\",\n      tailwind: \"https://www.nativewind.dev/docs/getting-started/installation\",\n    },\n  },\n  manual: {\n    name: \"manual\",\n    label: \"Manual\",\n    links: {\n      installation: \"https://ui.shadcn.com/docs/installation/manual\",\n      tailwind: \"https://tailwindcss.com/docs/installation\",\n    },\n  },\n} as const\n\nexport type Framework = (typeof FRAMEWORKS)[keyof typeof FRAMEWORKS]\n", "import { cyan, green, red, yellow } from \"kleur/colors\"\n\nexport const highlighter = {\n  error: red,\n  warn: yellow,\n  info: cyan,\n  success: green,\n}\n", "import { createMatchPath, type ConfigLoaderSuccessResult } from \"tsconfig-paths\"\n\nexport async function resolveImport(\n  importPath: string,\n  config: Pick<ConfigLoaderSuccessResult, \"absoluteBaseUrl\" | \"paths\">\n) {\n  return createMatchPath(config.absoluteBaseUrl, config.paths)(\n    importPath,\n    undefined,\n    () => true,\n    [\".ts\", \".tsx\", \".jsx\", \".js\", \".css\"]\n  )\n}\n", "import path from \"path\"\nimport { BUILTIN_REGISTRIES } from \"@/src/registry/constants\"\nimport {\n  configSchema,\n  rawConfigSchema,\n  workspaceConfigSchema,\n} from \"@/src/schema\"\nimport { getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { resolveImport } from \"@/src/utils/resolve-import\"\nimport { cosmiconfig } from \"cosmiconfig\"\nimport fg from \"fast-glob\"\nimport { loadConfig } from \"tsconfig-paths\"\nimport { z } from \"zod\"\n\nexport const DEFAULT_STYLE = \"default\"\nexport const DEFAULT_COMPONENTS = \"@/components\"\nexport const DEFAULT_UTILS = \"@/lib/utils\"\nexport const DEFAULT_TAILWIND_CSS = \"app/globals.css\"\nexport const DEFAULT_TAILWIND_CONFIG = \"tailwind.config.js\"\nexport const DEFAULT_TAILWIND_BASE_COLOR = \"slate\"\n\n// TODO: Figure out if we want to support all cosmiconfig formats.\n// A simple components.json file would be nice.\nexport const explorer = cosmiconfig(\"components\", {\n  searchPlaces: [\"components.json\"],\n})\n\nexport type Config = z.infer<typeof configSchema>\n\nexport async function getConfig(cwd: string) {\n  const config = await getRawConfig(cwd)\n\n  if (!config) {\n    return null\n  }\n\n  // Set default icon library if not provided.\n  if (!config.iconLibrary) {\n    config.iconLibrary = config.style === \"new-york\" ? \"radix\" : \"lucide\"\n  }\n\n  return await resolveConfigPaths(cwd, config)\n}\n\nexport async function resolveConfigPaths(\n  cwd: string,\n  config: z.infer<typeof rawConfigSchema>\n) {\n  // Merge built-in registries with user registries\n  config.registries = {\n    ...BUILTIN_REGISTRIES,\n    ...(config.registries || {}),\n  }\n\n  // Read tsconfig.json.\n  const tsConfig = await loadConfig(cwd)\n\n  if (tsConfig.resultType === \"failed\") {\n    throw new Error(\n      `Failed to load ${config.tsx ? \"tsconfig\" : \"jsconfig\"}.json. ${\n        tsConfig.message ?? \"\"\n      }`.trim()\n    )\n  }\n\n  return configSchema.parse({\n    ...config,\n    resolvedPaths: {\n      cwd,\n      tailwindConfig: config.tailwind.config\n        ? path.resolve(cwd, config.tailwind.config)\n        : \"\",\n      tailwindCss: path.resolve(cwd, config.tailwind.css),\n      utils: await resolveImport(config.aliases[\"utils\"], tsConfig),\n      components: await resolveImport(config.aliases[\"components\"], tsConfig),\n      ui: config.aliases[\"ui\"]\n        ? await resolveImport(config.aliases[\"ui\"], tsConfig)\n        : path.resolve(\n            (await resolveImport(config.aliases[\"components\"], tsConfig)) ??\n              cwd,\n            \"ui\"\n          ),\n      // TODO: Make this configurable.\n      // For now, we assume the lib and hooks directories are one level up from the components directory.\n      lib: config.aliases[\"lib\"]\n        ? await resolveImport(config.aliases[\"lib\"], tsConfig)\n        : path.resolve(\n            (await resolveImport(config.aliases[\"utils\"], tsConfig)) ?? cwd,\n            \"..\"\n          ),\n      hooks: config.aliases[\"hooks\"]\n        ? await resolveImport(config.aliases[\"hooks\"], tsConfig)\n        : path.resolve(\n            (await resolveImport(config.aliases[\"components\"], tsConfig)) ??\n              cwd,\n            \"..\",\n            \"hooks\"\n          ),\n    },\n  })\n}\n\nexport async function getRawConfig(\n  cwd: string\n): Promise<z.infer<typeof rawConfigSchema> | null> {\n  try {\n    const configResult = await explorer.search(cwd)\n\n    if (!configResult) {\n      return null\n    }\n\n    const config = rawConfigSchema.parse(configResult.config)\n\n    // Check if user is trying to override built-in registries\n    if (config.registries) {\n      for (const registryName of Object.keys(config.registries)) {\n        if (registryName in BUILTIN_REGISTRIES) {\n          throw new Error(\n            `\"${registryName}\" is a built-in registry and cannot be overridden.`\n          )\n        }\n      }\n    }\n\n    return config\n  } catch (error) {\n    const componentPath = `${cwd}/components.json`\n    if (error instanceof Error && error.message.includes(\"reserved registry\")) {\n      throw error\n    }\n    throw new Error(\n      `Invalid configuration found in ${highlighter.info(componentPath)}.`\n    )\n  }\n}\n\n// Note: we can check for -workspace.yaml or \"workspace\" in package.json.\n// Since cwd is not necessarily the root of the project.\n// We'll instead check if ui aliases resolve to a different root.\nexport async function getWorkspaceConfig(config: Config) {\n  let resolvedAliases: any = {}\n\n  for (const key of Object.keys(config.aliases)) {\n    if (!isAliasKey(key, config)) {\n      continue\n    }\n\n    const resolvedPath = config.resolvedPaths[key]\n    const packageRoot = await findPackageRoot(\n      config.resolvedPaths.cwd,\n      resolvedPath\n    )\n\n    if (!packageRoot) {\n      resolvedAliases[key] = config\n      continue\n    }\n\n    resolvedAliases[key] = await getConfig(packageRoot)\n  }\n\n  const result = workspaceConfigSchema.safeParse(resolvedAliases)\n  if (!result.success) {\n    return null\n  }\n\n  return result.data\n}\n\nexport async function findPackageRoot(cwd: string, resolvedPath: string) {\n  const commonRoot = findCommonRoot(cwd, resolvedPath)\n  const relativePath = path.relative(commonRoot, resolvedPath)\n\n  const packageRoots = await fg.glob(\"**/package.json\", {\n    cwd: commonRoot,\n    deep: 3,\n    ignore: [\"**/node_modules/**\", \"**/dist/**\", \"**/build/**\", \"**/public/**\"],\n  })\n\n  const matchingPackageRoot = packageRoots\n    .map((pkgPath) => path.dirname(pkgPath))\n    .find((pkgDir) => relativePath.startsWith(pkgDir))\n\n  return matchingPackageRoot ? path.join(commonRoot, matchingPackageRoot) : null\n}\n\nfunction isAliasKey(\n  key: string,\n  config: Config\n): key is keyof Config[\"aliases\"] {\n  return Object.keys(config.resolvedPaths)\n    .filter((key) => key !== \"utils\")\n    .includes(key)\n}\n\nexport function findCommonRoot(cwd: string, resolvedPath: string) {\n  const parts1 = cwd.split(path.sep)\n  const parts2 = resolvedPath.split(path.sep)\n  const commonParts = []\n\n  for (let i = 0; i < Math.min(parts1.length, parts2.length); i++) {\n    if (parts1[i] !== parts2[i]) {\n      break\n    }\n    commonParts.push(parts1[i])\n  }\n\n  return commonParts.join(path.sep)\n}\n\n// TODO: Cache this call.\nexport async function getTargetStyleFromConfig(cwd: string, fallback: string) {\n  const projectInfo = await getProjectInfo(cwd)\n  return projectInfo?.tailwindVersion === \"v4\" ? \"new-york-v4\" : fallback\n}\n\ntype DeepPartial<T> = {\n  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]\n}\n\n/**\n * Creates a config object with sensible defaults.\n * Useful for universal registry items that bypass framework detection.\n *\n * @param partial - Partial config values to override defaults\n * @returns A complete Config object\n */\nexport function createConfig(partial?: DeepPartial<Config>): Config {\n  const defaultConfig: Config = {\n    resolvedPaths: {\n      cwd: process.cwd(),\n      tailwindConfig: \"\",\n      tailwindCss: \"\",\n      utils: \"\",\n      components: \"\",\n      ui: \"\",\n      lib: \"\",\n      hooks: \"\",\n    },\n    style: \"\",\n    tailwind: {\n      config: \"\",\n      css: \"\",\n      baseColor: \"\",\n      cssVariables: false,\n    },\n    rsc: false,\n    tsx: true,\n    aliases: {\n      components: \"\",\n      utils: \"\",\n    },\n    registries: {\n      ...BUILTIN_REGISTRIES,\n    },\n  }\n\n  // Deep merge the partial config with defaults\n  if (partial) {\n    return {\n      ...defaultConfig,\n      ...partial,\n      resolvedPaths: {\n        ...defaultConfig.resolvedPaths,\n        ...(partial.resolvedPaths || {}),\n      },\n      tailwind: {\n        ...defaultConfig.tailwind,\n        ...(partial.tailwind || {}),\n      },\n      aliases: {\n        ...defaultConfig.aliases,\n        ...(partial.aliases || {}),\n      },\n      registries: {\n        ...defaultConfig.registries,\n        ...(partial.registries || {}),\n      },\n    }\n  }\n\n  return defaultConfig\n}\n", "import path from \"path\"\nimport fs from \"fs-extra\"\nimport { type PackageJson } from \"type-fest\"\n\nexport function getPackageInfo(\n  cwd: string = \"\",\n  shouldThrow: boolean = true\n): PackageJson | null {\n  const packageJsonPath = path.join(cwd, \"package.json\")\n\n  return fs.readJSONSync(packageJsonPath, {\n    throws: shouldThrow,\n  }) as PackageJson\n}\n", "import path from \"path\"\nimport { rawConfigSchema } from \"@/src/schema\"\nimport { FRAMEWORKS, Framework } from \"@/src/utils/frameworks\"\nimport { Config, getConfig, resolveConfigPaths } from \"@/src/utils/get-config\"\nimport { getPackageInfo } from \"@/src/utils/get-package-info\"\nimport fg from \"fast-glob\"\nimport fs from \"fs-extra\"\nimport { loadConfig } from \"tsconfig-paths\"\nimport { z } from \"zod\"\n\nexport type TailwindVersion = \"v3\" | \"v4\" | null\n\nexport type ProjectInfo = {\n  framework: Framework\n  isSrcDir: boolean\n  isRSC: boolean\n  isTsx: boolean\n  tailwindConfigFile: string | null\n  tailwindCssFile: string | null\n  tailwindVersion: TailwindVersion\n  frameworkVersion: string | null\n  aliasPrefix: string | null\n}\n\nconst PROJECT_SHARED_IGNORE = [\n  \"**/node_modules/**\",\n  \".next\",\n  \"public\",\n  \"dist\",\n  \"build\",\n]\n\nconst TS_CONFIG_SCHEMA = z.object({\n  compilerOptions: z.object({\n    paths: z.record(z.string().or(z.array(z.string()))),\n  }),\n})\n\nexport async function getProjectInfo(cwd: string): Promise<ProjectInfo | null> {\n  const [\n    configFiles,\n    isSrcDir,\n    isTsx,\n    tailwindConfigFile,\n    tailwindCssFile,\n    tailwindVersion,\n    aliasPrefix,\n    packageJson,\n  ] = await Promise.all([\n    fg.glob(\n      \"**/{next,vite,astro,app}.config.*|gatsby-config.*|composer.json|react-router.config.*\",\n      {\n        cwd,\n        deep: 3,\n        ignore: PROJECT_SHARED_IGNORE,\n      }\n    ),\n    fs.pathExists(path.resolve(cwd, \"src\")),\n    isTypeScriptProject(cwd),\n    getTailwindConfigFile(cwd),\n    getTailwindCssFile(cwd),\n    getTailwindVersion(cwd),\n    getTsConfigAliasPrefix(cwd),\n    getPackageInfo(cwd, false),\n  ])\n\n  const isUsingAppDir = await fs.pathExists(\n    path.resolve(cwd, `${isSrcDir ? \"src/\" : \"\"}app`)\n  )\n\n  const type: ProjectInfo = {\n    framework: FRAMEWORKS[\"manual\"],\n    isSrcDir,\n    isRSC: false,\n    isTsx,\n    tailwindConfigFile,\n    tailwindCssFile,\n    tailwindVersion,\n    frameworkVersion: null,\n    aliasPrefix,\n  }\n\n  // Next.js.\n  if (configFiles.find((file) => file.startsWith(\"next.config.\"))?.length) {\n    type.framework = isUsingAppDir\n      ? FRAMEWORKS[\"next-app\"]\n      : FRAMEWORKS[\"next-pages\"]\n    type.isRSC = isUsingAppDir\n    type.frameworkVersion = await getFrameworkVersion(\n      type.framework,\n      packageJson\n    )\n    return type\n  }\n\n  // Astro.\n  if (configFiles.find((file) => file.startsWith(\"astro.config.\"))?.length) {\n    type.framework = FRAMEWORKS[\"astro\"]\n    return type\n  }\n\n  // Gatsby.\n  if (configFiles.find((file) => file.startsWith(\"gatsby-config.\"))?.length) {\n    type.framework = FRAMEWORKS[\"gatsby\"]\n    return type\n  }\n\n  // Laravel.\n  if (configFiles.find((file) => file.startsWith(\"composer.json\"))?.length) {\n    type.framework = FRAMEWORKS[\"laravel\"]\n    return type\n  }\n\n  // Remix.\n  if (\n    Object.keys(packageJson?.dependencies ?? {}).find((dep) =>\n      dep.startsWith(\"@remix-run/\")\n    )\n  ) {\n    type.framework = FRAMEWORKS[\"remix\"]\n    return type\n  }\n\n  // TanStack Start.\n  if (\n    [\n      ...Object.keys(packageJson?.dependencies ?? {}),\n      ...Object.keys(packageJson?.devDependencies ?? {}),\n    ].find((dep) => dep.startsWith(\"@tanstack/react-start\"))\n  ) {\n    type.framework = FRAMEWORKS[\"tanstack-start\"]\n    return type\n  }\n\n  // React Router.\n  if (\n    configFiles.find((file) => file.startsWith(\"react-router.config.\"))?.length\n  ) {\n    type.framework = FRAMEWORKS[\"react-router\"]\n    return type\n  }\n\n  // Vite.\n  // Some Remix templates also have a vite.config.* file.\n  // We'll assume that it got caught by the Remix check above.\n  if (configFiles.find((file) => file.startsWith(\"vite.config.\"))?.length) {\n    type.framework = FRAMEWORKS[\"vite\"]\n    return type\n  }\n\n  // Vinxi-based (such as @tanstack/start and @solidjs/solid-start)\n  // They are vite-based, and the same configurations used for Vite should work flawlessly\n  const appConfig = configFiles.find((file) => file.startsWith(\"app.config\"))\n  if (appConfig?.length) {\n    const appConfigContents = await fs.readFile(\n      path.resolve(cwd, appConfig),\n      \"utf8\"\n    )\n    if (appConfigContents.includes(\"defineConfig\")) {\n      type.framework = FRAMEWORKS[\"vite\"]\n      return type\n    }\n  }\n\n  // Expo.\n  if (packageJson?.dependencies?.expo) {\n    type.framework = FRAMEWORKS[\"expo\"]\n    return type\n  }\n\n  return type\n}\n\nexport async function getFrameworkVersion(\n  framework: Framework,\n  packageJson: ReturnType<typeof getPackageInfo>\n) {\n  if (!packageJson) {\n    return null\n  }\n\n  // Only detect Next.js version for now.\n  if (![\"next-app\", \"next-pages\"].includes(framework.name)) {\n    return null\n  }\n\n  const version =\n    packageJson.dependencies?.next || packageJson.devDependencies?.next\n\n  if (!version) {\n    return null\n  }\n\n  // Extract full semver (major.minor.patch), handling ^, ~, etc.\n  const versionMatch = version.match(/^[\\^~]?(\\d+\\.\\d+\\.\\d+)/)\n  if (versionMatch) {\n    return versionMatch[1] // e.g., \"16.0.0\"\n  }\n\n  // For ranges like \">=15.0.0 <16.0.0\", extract the first version.\n  const rangeMatch = version.match(/(\\d+\\.\\d+\\.\\d+)/)\n  if (rangeMatch) {\n    return rangeMatch[1]\n  }\n\n  // For \"latest\", \"canary\", \"rc\", etc., return the tag as-is.\n  return version\n}\n\nexport async function getTailwindVersion(\n  cwd: string\n): Promise<ProjectInfo[\"tailwindVersion\"]> {\n  const [packageInfo, config] = await Promise.all([\n    getPackageInfo(cwd, false),\n    getConfig(cwd),\n  ])\n\n  // If the config file is empty, we can assume that it's a v4 project.\n  if (config?.tailwind?.config === \"\") {\n    return \"v4\"\n  }\n\n  if (\n    !packageInfo?.dependencies?.tailwindcss &&\n    !packageInfo?.devDependencies?.tailwindcss\n  ) {\n    return null\n  }\n\n  if (\n    /^(?:\\^|~)?3(?:\\.\\d+)*(?:-.*)?$/.test(\n      packageInfo?.dependencies?.tailwindcss ||\n        packageInfo?.devDependencies?.tailwindcss ||\n        \"\"\n    )\n  ) {\n    return \"v3\"\n  }\n\n  return \"v4\"\n}\n\nexport async function getTailwindCssFile(cwd: string) {\n  const [files, tailwindVersion] = await Promise.all([\n    fg.glob([\"**/*.css\", \"**/*.scss\"], {\n      cwd,\n      deep: 5,\n      ignore: PROJECT_SHARED_IGNORE,\n    }),\n    getTailwindVersion(cwd),\n  ])\n\n  if (!files.length) {\n    return null\n  }\n\n  const needle =\n    tailwindVersion === \"v4\" ? `@import \"tailwindcss\"` : \"@tailwind base\"\n  for (const file of files) {\n    const contents = await fs.readFile(path.resolve(cwd, file), \"utf8\")\n    if (\n      contents.includes(`@import \"tailwindcss\"`) ||\n      contents.includes(`@import 'tailwindcss'`) ||\n      contents.includes(`@tailwind base`)\n    ) {\n      return file\n    }\n  }\n\n  return null\n}\n\nexport async function getTailwindConfigFile(cwd: string) {\n  const files = await fg.glob(\"tailwind.config.*\", {\n    cwd,\n    deep: 3,\n    ignore: PROJECT_SHARED_IGNORE,\n  })\n\n  if (!files.length) {\n    return null\n  }\n\n  return files[0]\n}\n\nexport async function getTsConfigAliasPrefix(cwd: string) {\n  const tsConfig = await loadConfig(cwd)\n\n  if (\n    tsConfig?.resultType === \"failed\" ||\n    !Object.entries(tsConfig?.paths).length\n  ) {\n    return null\n  }\n\n  // This assume that the first alias is the prefix.\n  for (const [alias, paths] of Object.entries(tsConfig.paths)) {\n    if (\n      paths.includes(\"./*\") ||\n      paths.includes(\"./src/*\") ||\n      paths.includes(\"./app/*\") ||\n      paths.includes(\"./resources/js/*\") // Laravel.\n    ) {\n      return alias.replace(/\\/\\*$/, \"\") ?? null\n    }\n  }\n\n  // Use the first alias as the prefix.\n  return Object.keys(tsConfig?.paths)?.[0].replace(/\\/\\*$/, \"\") ?? null\n}\n\nexport async function isTypeScriptProject(cwd: string) {\n  const files = await fg.glob(\"tsconfig.*\", {\n    cwd,\n    deep: 1,\n    ignore: PROJECT_SHARED_IGNORE,\n  })\n\n  return files.length > 0\n}\n\nexport async function getTsConfig(cwd: string) {\n  for (const fallback of [\n    \"tsconfig.json\",\n    \"tsconfig.web.json\",\n    \"tsconfig.app.json\",\n  ]) {\n    const filePath = path.resolve(cwd, fallback)\n    if (!(await fs.pathExists(filePath))) {\n      continue\n    }\n\n    // We can't use fs.readJSON because it doesn't support comments.\n    const contents = await fs.readFile(filePath, \"utf8\")\n    const cleanedContents = contents.replace(/\\/\\*\\s*\\*\\//g, \"\")\n    const result = TS_CONFIG_SCHEMA.safeParse(JSON.parse(cleanedContents))\n\n    if (result.error) {\n      continue\n    }\n\n    return result.data\n  }\n\n  return null\n}\n\nexport async function getProjectConfig(\n  cwd: string,\n  defaultProjectInfo: ProjectInfo | null = null\n): Promise<Config | null> {\n  // Check for existing component config.\n  const [existingConfig, projectInfo] = await Promise.all([\n    getConfig(cwd),\n    !defaultProjectInfo\n      ? getProjectInfo(cwd)\n      : Promise.resolve(defaultProjectInfo),\n  ])\n\n  if (existingConfig) {\n    return existingConfig\n  }\n\n  if (\n    !projectInfo ||\n    !projectInfo.tailwindCssFile ||\n    (projectInfo.tailwindVersion === \"v3\" && !projectInfo.tailwindConfigFile)\n  ) {\n    return null\n  }\n\n  const config: z.infer<typeof rawConfigSchema> = {\n    $schema: \"https://ui.shadcn.com/schema.json\",\n    rsc: projectInfo.isRSC,\n    tsx: projectInfo.isTsx,\n    style: \"new-york\",\n    tailwind: {\n      config: projectInfo.tailwindConfigFile ?? \"\",\n      baseColor: \"zinc\",\n      css: projectInfo.tailwindCssFile,\n      cssVariables: true,\n      prefix: \"\",\n    },\n    iconLibrary: \"lucide\",\n    aliases: {\n      components: `${projectInfo.aliasPrefix}/components`,\n      ui: `${projectInfo.aliasPrefix}/components/ui`,\n      hooks: `${projectInfo.aliasPrefix}/hooks`,\n      lib: `${projectInfo.aliasPrefix}/lib`,\n      utils: `${projectInfo.aliasPrefix}/lib/utils`,\n    },\n  }\n\n  return await resolveConfigPaths(cwd, config)\n}\n\nexport async function getProjectTailwindVersionFromConfig(config: {\n  resolvedPaths: Pick<Config[\"resolvedPaths\"], \"cwd\">\n}): Promise<TailwindVersion> {\n  if (!config.resolvedPaths?.cwd) {\n    return \"v3\"\n  }\n\n  const projectInfo = await getProjectInfo(config.resolvedPaths.cwd)\n\n  if (!projectInfo?.tailwindVersion) {\n    return null\n  }\n\n  return projectInfo.tailwindVersion\n}\n", "export function isContentSame(\n  existingContent: string,\n  newContent: string,\n  options: {\n    ignoreImports?: boolean\n  } = {}\n) {\n  const { ignoreImports = false } = options\n\n  // Normalize line endings and whitespace.\n  const normalizedExisting = existingContent.replace(/\\r\\n/g, \"\\n\").trim()\n  const normalizedNew = newContent.replace(/\\r\\n/g, \"\\n\").trim()\n\n  // First, try exact match after normalization.\n  if (normalizedExisting === normalizedNew) {\n    return true\n  }\n\n  // If not ignoring imports or exact match failed, return false\n  if (!ignoreImports) {\n    return false\n  }\n\n  // Compare with import statements normalized.\n  // This regex matches various import patterns including:\n  // - import defaultExport from \"module\"\n  // - import * as name from \"module\"\n  // - import { export1, export2 } from \"module\"\n  // - import { export1 as alias1 } from \"module\"\n  // - import defaultExport, { export1 } from \"module\"\n  // - import type { Type } from \"module\"\n  // - This Regex written by Claude <PERSON>.\n  const importRegex =\n    /^(import\\s+(?:type\\s+)?(?:\\*\\s+as\\s+\\w+|\\{[^}]*\\}|\\w+)?(?:\\s*,\\s*(?:\\{[^}]*\\}|\\w+))?\\s+from\\s+[\"'])([^\"']+)([\"'])/gm\n\n  // Function to normalize import paths - remove alias differences.\n  const normalizeImports = (content: string) => {\n    return content.replace(\n      importRegex,\n      (_match, prefix, importPath, suffix) => {\n        // Keep relative imports as-is.\n        if (importPath.startsWith(\".\")) {\n          return `${prefix}${importPath}${suffix}`\n        }\n\n        // For aliased imports, normalize to a common format.\n        // Extract the last meaningful part of the path.\n        const parts = importPath.split(\"/\")\n        const lastPart = parts[parts.length - 1]\n\n        // Normalize to a consistent format.\n        return `${prefix}@normalized/${lastPart}${suffix}`\n      }\n    )\n  }\n\n  const existingNormalized = normalizeImports(normalizedExisting)\n  const newNormalized = normalizeImports(normalizedNew)\n\n  return existingNormalized === newNormalized\n}\n", "import { existsSync } from \"fs\"\nimport path from \"path\"\n\nexport function isEnvFile(filePath: string) {\n  const fileName = path.basename(filePath)\n  return /^\\.env(\\.|$)/.test(fileName)\n}\n\n/**\n * Finds a file variant in the project.\n * TODO: abstract this to a more generic function.\n */\nexport function findExistingEnvFile(targetDir: string) {\n  const variants = [\n    \".env.local\",\n    \".env\",\n    \".env.development.local\",\n    \".env.development\",\n  ]\n\n  for (const variant of variants) {\n    const filePath = path.join(targetDir, variant)\n    if (existsSync(filePath)) {\n      return filePath\n    }\n  }\n\n  return null\n}\n\n/**\n * Parse .env content into key-value pairs.\n */\nexport function parseEnvContent(content: string) {\n  const lines = content.split(\"\\n\")\n  const env: Record<string, string> = {}\n\n  for (const line of lines) {\n    const trimmed = line.trim()\n\n    if (!trimmed || trimmed.startsWith(\"#\")) {\n      continue\n    }\n\n    // Find the first = and split there\n    const equalIndex = trimmed.indexOf(\"=\")\n    if (equalIndex === -1) {\n      continue\n    }\n\n    const key = trimmed.substring(0, equalIndex).trim()\n    const value = trimmed.substring(equalIndex + 1).trim()\n\n    if (key) {\n      env[key] = value.replace(/^[\"']|[\"']$/g, \"\")\n    }\n  }\n\n  return env\n}\n\n/**\n * Get the list of new keys that would be added when merging env content.\n */\nexport function getNewEnvKeys(existingContent: string, newContent: string) {\n  const existingEnv = parseEnvContent(existingContent)\n  const newEnv = parseEnvContent(newContent)\n\n  const newKeys = []\n  for (const key of Object.keys(newEnv)) {\n    if (!(key in existingEnv)) {\n      newKeys.push(key)\n    }\n  }\n\n  return newKeys\n}\n\n/**\n * Merge env content by appending ONLY new keys that don't exist in the existing content.\n * Existing keys are preserved with their original values.\n */\nexport function mergeEnvContent(existingContent: string, newContent: string) {\n  const existingEnv = parseEnvContent(existingContent)\n  const newEnv = parseEnvContent(newContent)\n\n  let result = existingContent.trimEnd()\n  if (result && !result.endsWith(\"\\n\")) {\n    result += \"\\n\"\n  }\n\n  const newKeys: string[] = []\n  for (const [key, value] of Object.entries(newEnv)) {\n    if (!(key in existingEnv)) {\n      newKeys.push(`${key}=${value}`)\n    }\n  }\n\n  if (newKeys.length > 0) {\n    if (result) {\n      result += \"\\n\"\n    }\n    result += newKeys.join(\"\\n\")\n    return result + \"\\n\"\n  }\n\n  // Ensure existing content ends with newline.\n  if (result && !result.endsWith(\"\\n\")) {\n    return result + \"\\n\"\n  }\n\n  return result\n}\n", "import { highlighter } from \"@/src/utils/highlighter\"\n\nexport const logger = {\n  error(...args: unknown[]) {\n    console.log(highlighter.error(args.join(\" \")))\n  },\n  warn(...args: unknown[]) {\n    console.log(highlighter.warn(args.join(\" \")))\n  },\n  info(...args: unknown[]) {\n    console.log(highlighter.info(args.join(\" \")))\n  },\n  success(...args: unknown[]) {\n    console.log(highlighter.success(args.join(\" \")))\n  },\n  log(...args: unknown[]) {\n    console.log(args.join(\" \"))\n  },\n  break() {\n    console.log(\"\")\n  },\n}\n", "import ora, { type Options } from \"ora\"\n\nexport function spinner(\n  text: Options[\"text\"],\n  options?: {\n    silent?: boolean\n  }\n) {\n  return ora({\n    text,\n    isSilent: options?.silent,\n  })\n}\n", "import { registryBaseColorSchema } from \"@/src/schema\"\nimport { Transformer } from \"@/src/utils/transformers\"\nimport { ScriptKind, SyntaxKind } from \"ts-morph\"\nimport { z } from \"zod\"\n\nexport const transformCssVars: Transformer = async ({\n  sourceFile,\n  config,\n  baseColor,\n}) => {\n  // No transform if using css variables.\n  if (config.tailwind?.cssVariables || !baseColor?.inlineColors) {\n    return sourceFile\n  }\n\n  // Find jsx attributes with the name className.\n  // const openingElements = sourceFile.getDescendantsOfKind(SyntaxKind.JsxElement)\n  // console.log(openingElements)\n  // const jsxAttributes = sourceFile\n  //   .getDescendantsOfKind(SyntaxKind.JsxAttribute)\n  //   .filter((node) => node.getName() === \"className\")\n\n  // for (const jsxAttribute of jsxAttributes) {\n  //   const value = jsxAttribute.getInitializer()?.getText()\n  //   if (value) {\n  //     const valueWithColorMapping = applyColorMapping(\n  //       value.replace(/\"/g, \"\"),\n  //       baseColor.inlineColors\n  //     )\n  //     jsxAttribute.setInitializer(`\"${valueWithColorMapping}\"`)\n  //   }\n  // }\n  sourceFile.getDescendantsOfKind(SyntaxKind.StringLiteral).forEach((node) => {\n    const raw = node.getLiteralText()\n    const mapped = applyColorMapping(raw, baseColor.inlineColors).trim()\n    if (mapped !== raw) {\n      node.setLiteralValue(mapped)\n    }\n  })\n\n  return sourceFile\n}\n\n// export default function transformer(file: FileInfo, api: API) {\n//   const j = api.jscodeshift.withParser(\"tsx\")\n\n//   // Replace bg-background with \"bg-white dark:bg-slate-950\"\n//   const $j = j(file.source)\n//   return $j\n//     .find(j.JSXAttribute, {\n//       name: {\n//         name: \"className\",\n//       },\n//     })\n//     .forEach((path) => {\n//       const { node } = path\n//       if (node?.value?.type) {\n//         if (node.value.type === \"StringLiteral\") {\n//           node.value.value = applyColorMapping(node.value.value)\n//           console.log(node.value.value)\n//         }\n\n//         if (\n//           node.value.type === \"JSXExpressionContainer\" &&\n//           node.value.expression.type === \"CallExpression\"\n//         ) {\n//           const callee = node.value.expression.callee\n//           if (callee.type === \"Identifier\" && callee.name === \"cn\") {\n//             node.value.expression.arguments.forEach((arg) => {\n//               if (arg.type === \"StringLiteral\") {\n//                 arg.value = applyColorMapping(arg.value)\n//               }\n\n//               if (\n//                 arg.type === \"LogicalExpression\" &&\n//                 arg.right.type === \"StringLiteral\"\n//               ) {\n//                 arg.right.value = applyColorMapping(arg.right.value)\n//               }\n//             })\n//           }\n//         }\n//       }\n//     })\n//     .toSource()\n// }\n\n// // export function splitClassName(input: string): (string | null)[] {\n// //   const parts = input.split(\":\")\n// //   const classNames = parts.map((part) => {\n// //     const match = part.match(/^\\[?(.+)\\]$/)\n// //     if (match) {\n// //       return match[1]\n// //     } else {\n// //       return null\n// //     }\n// //   })\n\n// //   return classNames\n// // }\n\n// Splits a className into variant-name-alpha.\n// eg. hover:bg-primary-100 -> [hover, bg-primary, 100]\nexport function splitClassName(className: string): (string | null)[] {\n  if (!className.includes(\"/\") && !className.includes(\":\")) {\n    return [null, className, null]\n  }\n\n  const parts: (string | null)[] = []\n  // First we split to find the alpha.\n  let [rest, alpha] = className.split(\"/\")\n\n  // Check if rest has a colon.\n  if (!rest.includes(\":\")) {\n    return [null, rest, alpha]\n  }\n\n  // Next we split the rest by the colon.\n  const split = rest.split(\":\")\n\n  // We take the last item from the split as the name.\n  const name = split.pop()\n\n  // We glue back the rest of the split.\n  const variant = split.join(\":\")\n\n  // Finally we push the variant, name and alpha.\n  parts.push(variant ?? null, name ?? null, alpha ?? null)\n\n  return parts\n}\n\nconst PREFIXES = [\"bg-\", \"text-\", \"border-\", \"ring-offset-\", \"ring-\"]\n\nexport function applyColorMapping(\n  input: string,\n  mapping: z.infer<typeof registryBaseColorSchema>[\"inlineColors\"]\n) {\n  // Handle border classes.\n  if (input.includes(\" border \")) {\n    input = input.replace(\" border \", \" border border-border \")\n  }\n\n  // Build color mappings.\n  const classNames = input.split(\" \")\n  const lightMode = new Set<string>()\n  const darkMode = new Set<string>()\n  for (let className of classNames) {\n    const [variant, value, modifier] = splitClassName(className)\n    const prefix = PREFIXES.find((prefix) => value?.startsWith(prefix))\n    if (!prefix) {\n      if (!lightMode.has(className)) {\n        lightMode.add(className)\n      }\n      continue\n    }\n\n    const needle = value?.replace(prefix, \"\")\n    if (needle && needle in mapping.light) {\n      lightMode.add(\n        [variant, `${prefix}${mapping.light[needle]}`]\n          .filter(Boolean)\n          .join(\":\") + (modifier ? `/${modifier}` : \"\")\n      )\n\n      darkMode.add(\n        [\"dark\", variant, `${prefix}${mapping.dark[needle]}`]\n          .filter(Boolean)\n          .join(\":\") + (modifier ? `/${modifier}` : \"\")\n      )\n      continue\n    }\n\n    if (!lightMode.has(className)) {\n      lightMode.add(className)\n    }\n  }\n\n  return [...Array.from(lightMode), ...Array.from(darkMode)].join(\" \").trim()\n}\n", "export const ICON_LIBRARIES = {\n  lucide: {\n    name: \"lucide-react\",\n    package: \"lucide-react\",\n    import: \"lucide-react\",\n  },\n  radix: {\n    name: \"@radix-ui/react-icons\",\n    package: \"@radix-ui/react-icons\",\n    import: \"@radix-ui/react-icons\",\n  },\n}\n", "import { getRegistryIcons } from \"@/src/registry/api\"\nimport { ICON_LIBRARIES } from \"@/src/utils/icon-libraries\"\nimport { Transformer } from \"@/src/utils/transformers\"\nimport { SourceFile, SyntaxKind } from \"ts-morph\"\n\n// Lucide is the default icon library in the registry.\nconst SOURCE_LIBRARY = \"lucide\"\n\nexport const transformIcons: Transformer = async ({ sourceFile, config }) => {\n  // No transform if we cannot read the icon library.\n  if (!config.iconLibrary || !(config.iconLibrary in ICON_LIBRARIES)) {\n    return sourceFile\n  }\n\n  const registryIcons = await getRegistryIcons()\n  const sourceLibrary = SOURCE_LIBRARY\n  const targetLibrary = config.iconLibrary\n\n  if (sourceLibrary === targetLibrary) {\n    return sourceFile\n  }\n\n  let targetedIcons: string[] = []\n  for (const importDeclaration of sourceFile.getImportDeclarations() ?? []) {\n    if (\n      importDeclaration.getModuleSpecifier()?.getText() !==\n      `\"${ICON_LIBRARIES[SOURCE_LIBRARY].import}\"`\n    ) {\n      continue\n    }\n\n    for (const specifier of importDeclaration.getNamedImports() ?? []) {\n      const iconName = specifier.getName()\n\n      const targetedIcon = registryIcons[iconName]?.[targetLibrary]\n\n      if (!targetedIcon || targetedIcons.includes(targetedIcon)) {\n        continue\n      }\n\n      targetedIcons.push(targetedIcon)\n\n      // Remove the named import.\n      specifier.remove()\n\n      // Replace with the targeted icon.\n      sourceFile\n        .getDescendantsOfKind(SyntaxKind.JsxSelfClosingElement)\n        .filter((node) => node.getTagNameNode()?.getText() === iconName)\n        .forEach((node) => node.getTagNameNode()?.replaceWithText(targetedIcon))\n    }\n\n    // If the named import is empty, remove the import declaration.\n    if (importDeclaration.getNamedImports()?.length === 0) {\n      importDeclaration.remove()\n    }\n  }\n\n  if (targetedIcons.length > 0) {\n    const iconImportDeclaration = sourceFile.addImportDeclaration({\n      moduleSpecifier:\n        ICON_LIBRARIES[targetLibrary as keyof typeof ICON_LIBRARIES]?.import,\n      namedImports: targetedIcons.map((icon) => ({\n        name: icon,\n      })),\n    })\n\n    if (!_useSemicolon(sourceFile)) {\n      iconImportDeclaration.replaceWithText(\n        iconImportDeclaration.getText().replace(\";\", \"\")\n      )\n    }\n  }\n\n  return sourceFile\n}\n\nfunction _useSemicolon(sourceFile: SourceFile) {\n  return (\n    sourceFile.getImportDeclarations()?.[0]?.getText().endsWith(\";\") ?? false\n  )\n}\n", "import { Config } from \"@/src/utils/get-config\"\nimport { Transformer } from \"@/src/utils/transformers\"\nimport { SyntaxKind } from \"ts-morph\"\n\nexport const transformImport: Transformer = async ({\n  sourceFile,\n  config,\n  isRemote,\n}) => {\n  const workspaceAlias = config.aliases?.utils?.split(\"/\")[0]?.slice(1)\n  const utilsImport = `@${workspaceAlias}/lib/utils`\n\n  if (![\".tsx\", \".ts\", \".jsx\", \".js\"].includes(sourceFile.getExtension())) {\n    return sourceFile\n  }\n\n  for (const specifier of sourceFile.getImportStringLiterals()) {\n    const updated = updateImportAliases(\n      specifier.getLiteralValue(),\n      config,\n      isRemote\n    )\n    specifier.setLiteralValue(updated)\n\n    // Replace `import { cn } from \"@/lib/utils\"`\n    if (utilsImport === updated || updated === \"@/lib/utils\") {\n      const importDeclaration = specifier.getFirstAncestorByKind(\n        SyntaxKind.ImportDeclaration\n      )\n      const isCnImport = importDeclaration\n        ?.getNamedImports()\n        .some((namedImport) => namedImport.getName() === \"cn\")\n\n      if (!isCnImport) continue\n\n      specifier.setLiteralValue(\n        utilsImport === updated\n          ? updated.replace(utilsImport, config.aliases.utils)\n          : config.aliases.utils\n      )\n    }\n  }\n\n  return sourceFile\n}\n\nfunction updateImportAliases(\n  moduleSpecifier: string,\n  config: Config,\n  isRemote: boolean = false\n) {\n  // Not a local import.\n  if (!moduleSpecifier.startsWith(\"@/\") && !isRemote) {\n    return moduleSpecifier\n  }\n\n  // This treats the remote as coming from a faux registry.\n  if (isRemote && moduleSpecifier.startsWith(\"@/\")) {\n    moduleSpecifier = moduleSpecifier.replace(/^@\\//, `@/registry/new-york/`)\n  }\n\n  // Not a registry import.\n  if (!moduleSpecifier.startsWith(\"@/registry/\")) {\n    // We fix the alias and return.\n    const alias = config.aliases.components.split(\"/\")[0]\n    return moduleSpecifier.replace(/^@\\//, `${alias}/`)\n  }\n\n  if (moduleSpecifier.match(/^@\\/registry\\/(.+)\\/ui/)) {\n    return moduleSpecifier.replace(\n      /^@\\/registry\\/(.+)\\/ui/,\n      config.aliases.ui ?? `${config.aliases.components}/ui`\n    )\n  }\n\n  if (\n    config.aliases.components &&\n    moduleSpecifier.match(/^@\\/registry\\/(.+)\\/components/)\n  ) {\n    return moduleSpecifier.replace(\n      /^@\\/registry\\/(.+)\\/components/,\n      config.aliases.components\n    )\n  }\n\n  if (config.aliases.lib && moduleSpecifier.match(/^@\\/registry\\/(.+)\\/lib/)) {\n    return moduleSpecifier.replace(\n      /^@\\/registry\\/(.+)\\/lib/,\n      config.aliases.lib\n    )\n  }\n\n  if (\n    config.aliases.hooks &&\n    moduleSpecifier.match(/^@\\/registry\\/(.+)\\/hooks/)\n  ) {\n    return moduleSpecifier.replace(\n      /^@\\/registry\\/(.+)\\/hooks/,\n      config.aliases.hooks\n    )\n  }\n\n  return moduleSpecifier.replace(\n    /^@\\/registry\\/[^/]+/,\n    config.aliases.components\n  )\n}\n", "import { type Transformer } from \"@/src/utils/transformers\"\nimport { transformFromAstSync } from \"@babel/core\"\nimport { ParserOptions, parse } from \"@babel/parser\"\n// @ts-ignore\nimport transformTypescript from \"@babel/plugin-transform-typescript\"\nimport * as recast from \"recast\"\n\n// TODO.\n// I'm using recast for the AST here.\n// Figure out if ts-morph AST is compatible with Babel.\n\n// This is a copy of the babel options from recast/parser.\n// The goal here is to tolerate as much syntax as possible.\n// We want to be able to parse any valid tsx code.\n// See https://github.com/benjamn/recast/blob/master/parsers/_babel_options.ts.\nconst PARSE_OPTIONS: ParserOptions = {\n  sourceType: \"module\",\n  allowImportExportEverywhere: true,\n  allowReturnOutsideFunction: true,\n  startLine: 1,\n  tokens: true,\n  plugins: [\n    \"asyncGenerators\",\n    \"bigInt\",\n    \"classPrivateMethods\",\n    \"classPrivateProperties\",\n    \"classProperties\",\n    \"classStaticBlock\",\n    \"decimal\",\n    \"decorators-legacy\",\n    \"doExpressions\",\n    \"dynamicImport\",\n    \"exportDefaultFrom\",\n    \"exportNamespaceFrom\",\n    \"functionBind\",\n    \"functionSent\",\n    \"importAssertions\",\n    \"importMeta\",\n    \"nullishCoalescingOperator\",\n    \"numericSeparator\",\n    \"objectRestSpread\",\n    \"optionalCatchBinding\",\n    \"optionalChaining\",\n    [\n      \"pipelineOperator\",\n      {\n        proposal: \"minimal\",\n      },\n    ],\n    [\n      \"recordAndTuple\",\n      {\n        syntaxType: \"hash\",\n      },\n    ],\n    \"throwExpressions\",\n    \"topLevelAwait\",\n    \"v8intrinsic\",\n    \"typescript\",\n    \"jsx\",\n  ],\n}\n\nexport const transformJsx: Transformer<string> = async ({\n  sourceFile,\n  config,\n}) => {\n  const output = sourceFile.getFullText()\n\n  if (config.tsx) {\n    return output\n  }\n\n  const ast = recast.parse(output, {\n    parser: {\n      parse: (code: string) => {\n        return parse(code, PARSE_OPTIONS)\n      },\n    },\n  })\n\n  const result = transformFromAstSync(ast, output, {\n    cloneInputAst: false,\n    code: false,\n    ast: true,\n    plugins: [transformTypescript],\n    configFile: false,\n  })\n\n  if (!result || !result.ast) {\n    throw new Error(\"Failed to transform JSX\")\n  }\n\n  return recast.print(result.ast).code\n}\n", "import { Transformer } from \"@/src/utils/transformers\"\nimport { SyntaxKind } from \"ts-morph\"\n\nconst directiveRegex = /^[\"']use client[\"']$/g\n\nexport const transformRsc: Transformer = async ({ sourceFile, config }) => {\n  if (config.rsc) {\n    return sourceFile\n  }\n\n  // Remove \"use client\" from the top of the file.\n  const first = sourceFile.getFirstChildByKind(SyntaxKind.ExpressionStatement)\n  if (first && directiveRegex.test(first.getText())) {\n    first.remove()\n  }\n\n  return sourceFile\n}\n", "import { Transformer } from \"@/src/utils/transformers\"\nimport { SyntaxKind } from \"ts-morph\"\n\nimport {\n  Tai<PERSON>windVersion,\n  getProjectTailwindVersionFromConfig,\n} from \"../get-project-info\"\nimport { splitClassName } from \"./transform-css-vars\"\n\nexport const transformTwPrefixes: Transformer = async ({\n  sourceFile,\n  config,\n}) => {\n  if (!config.tailwind?.prefix) {\n    return sourceFile\n  }\n  const tailwindVersion = await getProjectTailwindVersionFromConfig(config)\n\n  // Find the cva function calls.\n  sourceFile\n    .getDescendantsOfKind(SyntaxKind.CallExpression)\n    .filter((node) => node.getExpression().getText() === \"cva\")\n    .forEach((node) => {\n      // cva(base, ...)\n      if (node.getArguments()[0]?.isKind(SyntaxKind.StringLiteral)) {\n        const defaultClassNames = node.getArguments()[0]\n        if (defaultClassNames) {\n          defaultClassNames.replaceWithText(\n            `\"${applyPrefix(\n              defaultClassNames.getText()?.replace(/\"|'/g, \"\"),\n              config.tailwind.prefix,\n              tailwindVersion\n            )}\"`\n          )\n        }\n      }\n\n      // cva(..., { variants: { ... } })\n      if (node.getArguments()[1]?.isKind(SyntaxKind.ObjectLiteralExpression)) {\n        node\n          .getArguments()[1]\n          ?.getDescendantsOfKind(SyntaxKind.PropertyAssignment)\n          .find((node) => node.getName() === \"variants\")\n          ?.getDescendantsOfKind(SyntaxKind.PropertyAssignment)\n          .forEach((node) => {\n            node\n              .getDescendantsOfKind(SyntaxKind.PropertyAssignment)\n              .forEach((node) => {\n                const classNames = node.getInitializerIfKind(\n                  SyntaxKind.StringLiteral\n                )\n                if (classNames) {\n                  classNames?.replaceWithText(\n                    `\"${applyPrefix(\n                      classNames.getText()?.replace(/\"|'/g, \"\"),\n                      config.tailwind.prefix,\n                      tailwindVersion\n                    )}\"`\n                  )\n                }\n              })\n          })\n      }\n    })\n\n  // Find all jsx attributes with the name className.\n  sourceFile.getDescendantsOfKind(SyntaxKind.JsxAttribute).forEach((node) => {\n    if (node.getNameNode().getText() === \"className\") {\n      // className=\"...\"\n      if (node.getInitializer()?.isKind(SyntaxKind.StringLiteral)) {\n        const value = node.getInitializer()\n        if (value) {\n          value.replaceWithText(\n            `\"${applyPrefix(\n              value.getText()?.replace(/\"|'/g, \"\"),\n              config.tailwind.prefix,\n              tailwindVersion\n            )}\"`\n          )\n        }\n      }\n\n      // className={...}\n      if (node.getInitializer()?.isKind(SyntaxKind.JsxExpression)) {\n        // Check if it's a call to cn().\n        const callExpression = node\n          .getInitializer()\n          ?.getDescendantsOfKind(SyntaxKind.CallExpression)\n          .find((node) => node.getExpression().getText() === \"cn\")\n        if (callExpression) {\n          // Loop through the arguments.\n          callExpression.getArguments().forEach((node) => {\n            if (\n              node.isKind(SyntaxKind.ConditionalExpression) ||\n              node.isKind(SyntaxKind.BinaryExpression)\n            ) {\n              node\n                .getChildrenOfKind(SyntaxKind.StringLiteral)\n                .forEach((node) => {\n                  node.replaceWithText(\n                    `\"${applyPrefix(\n                      node.getText()?.replace(/\"|'/g, \"\"),\n                      config.tailwind.prefix,\n                      tailwindVersion\n                    )}\"`\n                  )\n                })\n            }\n\n            if (node.isKind(SyntaxKind.StringLiteral)) {\n              node.replaceWithText(\n                `\"${applyPrefix(\n                  node.getText()?.replace(/\"|'/g, \"\"),\n                  config.tailwind.prefix,\n                  tailwindVersion\n                )}\"`\n              )\n            }\n          })\n        }\n      }\n    }\n\n    // classNames={...}\n    if (node.getNameNode().getText() === \"classNames\") {\n      if (node.getInitializer()?.isKind(SyntaxKind.JsxExpression)) {\n        node\n          .getDescendantsOfKind(SyntaxKind.PropertyAssignment)\n          .forEach((node) => {\n            if (node.getInitializer()?.isKind(SyntaxKind.CallExpression)) {\n              const callExpression = node.getInitializerIfKind(\n                SyntaxKind.CallExpression\n              )\n              if (callExpression) {\n                // Loop through the arguments.\n                callExpression.getArguments().forEach((arg) => {\n                  if (arg.isKind(SyntaxKind.ConditionalExpression)) {\n                    arg\n                      .getChildrenOfKind(SyntaxKind.StringLiteral)\n                      .forEach((node) => {\n                        node.replaceWithText(\n                          `\"${applyPrefix(\n                            node.getText()?.replace(/\"|'/g, \"\"),\n                            config.tailwind.prefix,\n                            tailwindVersion\n                          )}\"`\n                        )\n                      })\n                  }\n\n                  if (arg.isKind(SyntaxKind.StringLiteral)) {\n                    arg.replaceWithText(\n                      `\"${applyPrefix(\n                        arg.getText()?.replace(/\"|'/g, \"\"),\n                        config.tailwind.prefix,\n                        tailwindVersion\n                      )}\"`\n                    )\n                  }\n                })\n              }\n            }\n\n            if (node.getInitializer()?.isKind(SyntaxKind.StringLiteral)) {\n              if (node.getNameNode().getText() !== \"variant\") {\n                const classNames = node.getInitializer()\n                if (classNames) {\n                  classNames.replaceWithText(\n                    `\"${applyPrefix(\n                      classNames.getText()?.replace(/\"|'/g, \"\"),\n                      config.tailwind.prefix,\n                      tailwindVersion\n                    )}\"`\n                  )\n                }\n              }\n            }\n          })\n      }\n    }\n  })\n\n  return sourceFile\n}\n\nexport function applyPrefix(\n  input: string,\n  prefix: string = \"\",\n  tailwindVersion: TailwindVersion\n) {\n  if (tailwindVersion === \"v3\") {\n    return input\n      .split(\" \")\n      .map((className) => {\n        const [variant, value, modifier] = splitClassName(className)\n        if (variant) {\n          return modifier\n            ? `${variant}:${prefix}${value}/${modifier}`\n            : `${variant}:${prefix}${value}`\n        } else {\n          return modifier\n            ? `${prefix}${value}/${modifier}`\n            : `${prefix}${value}`\n        }\n      })\n      .join(\" \")\n  }\n\n  return input\n    .split(\" \")\n    .map((className) =>\n      className.indexOf(`${prefix}:`) === 0\n        ? className\n        : `${prefix}:${className.trim()}`\n    )\n    .join(\" \")\n}\n\nexport function applyPrefixesCss(\n  css: string,\n  prefix: string,\n  tailwindVersion: TailwindVersion\n) {\n  const lines = css.split(\"\\n\")\n  for (let line of lines) {\n    if (line.includes(\"@apply\")) {\n      const originalTWCls = line.replace(\"@apply\", \"\").trim()\n      const prefixedTwCls = applyPrefix(originalTWCls, prefix, tailwindVersion)\n      css = css.replace(originalTWCls, prefixedTwCls)\n    }\n  }\n  return css\n}\n", "import { promises as fs } from \"fs\"\nimport { tmpdir } from \"os\"\nimport path from \"path\"\nimport { registryBaseColorSchema } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { transformCssVars } from \"@/src/utils/transformers/transform-css-vars\"\nimport { transformIcons } from \"@/src/utils/transformers/transform-icons\"\nimport { transformImport } from \"@/src/utils/transformers/transform-import\"\nimport { transformJsx } from \"@/src/utils/transformers/transform-jsx\"\nimport { transformRsc } from \"@/src/utils/transformers/transform-rsc\"\nimport { Project, ScriptKind, type SourceFile } from \"ts-morph\"\nimport { z } from \"zod\"\n\nimport { transformTwPrefixes } from \"./transform-tw-prefix\"\n\nexport type TransformOpts = {\n  filename: string\n  raw: string\n  config: Config\n  baseColor?: z.infer<typeof registryBaseColorSchema>\n  transformJsx?: boolean\n  isRemote?: boolean\n}\n\nexport type Transformer<Output = SourceFile> = (\n  opts: TransformOpts & {\n    sourceFile: SourceFile\n  }\n) => Promise<Output>\n\nconst project = new Project({\n  compilerOptions: {},\n})\n\nasync function createTempSourceFile(filename: string) {\n  const dir = await fs.mkdtemp(path.join(tmpdir(), \"shadcn-\"))\n  return path.join(dir, filename)\n}\n\nexport async function transform(\n  opts: TransformOpts,\n  transformers: Transformer[] = [\n    transformImport,\n    transformRsc,\n    transformCssVars,\n    transformTwPrefixes,\n    transformIcons,\n  ]\n) {\n  const tempFile = await createTempSourceFile(opts.filename)\n  const sourceFile = project.createSourceFile(tempFile, opts.raw, {\n    scriptKind: ScriptKind.TSX,\n  })\n\n  for (const transformer of transformers) {\n    await transformer({ sourceFile, ...opts })\n  }\n\n  if (opts.transformJsx) {\n    return await transformJsx({\n      sourceFile,\n      ...opts,\n    })\n  }\n\n  return sourceFile.getText()\n}\n", "import { Transformer } from \"@/src/utils/transformers\"\n\nexport const transformNext: Transformer = async ({ sourceFile }) => {\n  // export function middleware.\n  sourceFile.getFunctions().forEach((func) => {\n    if (func.getName() === \"middleware\") {\n      func.rename(\"proxy\")\n    }\n  })\n\n  // export const middleware.\n  sourceFile.getVariableDeclarations().forEach((variable) => {\n    if (variable.getName() === \"middleware\") {\n      variable.rename(\"proxy\")\n    }\n  })\n\n  // export { handler as middleware }.\n  sourceFile.getExportDeclarations().forEach((exportDecl) => {\n    const namedExports = exportDecl.getNamedExports()\n    namedExports.forEach((namedExport) => {\n      if (namedExport.getName() === \"middleware\") {\n        namedExport.setName(\"proxy\")\n      }\n      const aliasNode = namedExport.getAliasNode()\n      if (aliasNode?.getText() === \"middleware\") {\n        namedExport.setAlias(\"proxy\")\n      }\n    })\n  })\n\n  return sourceFile\n}\n", "import { existsSync, promises as fs, statSync } from \"fs\"\nimport { tmpdir } from \"os\"\nimport path, { basename } from \"path\"\nimport { getRegistryBaseColor } from \"@/src/registry/api\"\nimport { RegistryItem, registryItemFileSchema } from \"@/src/schema\"\nimport { isContentSame } from \"@/src/utils/compare\"\nimport {\n  findExistingEnvFile,\n  getNewEnvKeys,\n  isEnvFile,\n  mergeEnvContent,\n  parseEnvContent,\n} from \"@/src/utils/env-helpers\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { ProjectInfo, getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { resolveImport } from \"@/src/utils/resolve-import\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport { transform } from \"@/src/utils/transformers\"\nimport { transformCssVars } from \"@/src/utils/transformers/transform-css-vars\"\nimport { transformIcons } from \"@/src/utils/transformers/transform-icons\"\nimport { transformImport } from \"@/src/utils/transformers/transform-import\"\nimport { transformNext } from \"@/src/utils/transformers/transform-next\"\nimport { transformRsc } from \"@/src/utils/transformers/transform-rsc\"\nimport { transformTwPrefixes } from \"@/src/utils/transformers/transform-tw-prefix\"\nimport prompts from \"prompts\"\nimport { Project, ScriptKind } from \"ts-morph\"\nimport { loadConfig } from \"tsconfig-paths\"\nimport { z } from \"zod\"\n\nexport async function updateFiles(\n  files: RegistryItem[\"files\"],\n  config: Config,\n  options: {\n    overwrite?: boolean\n    force?: boolean\n    silent?: boolean\n    rootSpinner?: ReturnType<typeof spinner>\n    isRemote?: boolean\n    isWorkspace?: boolean\n    path?: string\n  }\n) {\n  if (!files?.length) {\n    return {\n      filesCreated: [],\n      filesUpdated: [],\n      filesSkipped: [],\n    }\n  }\n  options = {\n    overwrite: false,\n    force: false,\n    silent: false,\n    isRemote: false,\n    isWorkspace: false,\n    ...options,\n  }\n  const filesCreatedSpinner = spinner(`Updating files.`, {\n    silent: options.silent,\n  })?.start()\n\n  const [projectInfo, baseColor] = await Promise.all([\n    getProjectInfo(config.resolvedPaths.cwd),\n    config.tailwind.baseColor\n      ? getRegistryBaseColor(config.tailwind.baseColor)\n      : Promise.resolve(undefined),\n  ])\n\n  let filesCreated: string[] = []\n  let filesUpdated: string[] = []\n  let filesSkipped: string[] = []\n  let envVarsAdded: string[] = []\n  let envFile: string | null = null\n\n  for (let index = 0; index < files.length; index++) {\n    const file = files[index]\n    if (!file.content) {\n      continue\n    }\n\n    let filePath = resolveFilePath(file, config, {\n      isSrcDir: projectInfo?.isSrcDir,\n      framework: projectInfo?.framework.name,\n      commonRoot: findCommonRoot(\n        files.map((f) => f.path),\n        file.path\n      ),\n      path: options.path,\n      fileIndex: index,\n    })\n\n    if (!filePath) {\n      continue\n    }\n\n    const fileName = basename(file.path)\n    const targetDir = path.dirname(filePath)\n\n    if (!config.tsx) {\n      filePath = filePath.replace(/\\.tsx?$/, (match) =>\n        match === \".tsx\" ? \".jsx\" : \".js\"\n      )\n    }\n\n    if (isEnvFile(filePath) && !existsSync(filePath)) {\n      const alternativeEnvFile = findExistingEnvFile(targetDir)\n      if (alternativeEnvFile) {\n        filePath = alternativeEnvFile\n      }\n    }\n\n    const existingFile = existsSync(filePath)\n\n    // Check if the path exists and is a directory - we can't write to directories.\n    if (existingFile && statSync(filePath).isDirectory()) {\n      throw new Error(\n        `Cannot write to ${filePath}: path exists and is a directory. Please provide a file path instead.`\n      )\n    }\n\n    // Run our transformers.\n    // Skip transformers for .env files to preserve exact content\n    const content = isEnvFile(filePath)\n      ? file.content\n      : await transform(\n          {\n            filename: file.path,\n            raw: file.content,\n            config,\n            baseColor,\n            transformJsx: !config.tsx,\n            isRemote: options.isRemote,\n          },\n          [\n            transformImport,\n            transformRsc,\n            transformCssVars,\n            transformTwPrefixes,\n            transformIcons,\n            ...(_isNext16Middleware(filePath, projectInfo, config)\n              ? [transformNext]\n              : []),\n          ]\n        )\n\n    // Skip the file if it already exists and the content is the same.\n    // Exception: Don't skip .env files as we merge content instead of replacing\n    if (existingFile && !isEnvFile(filePath)) {\n      const existingFileContent = await fs.readFile(filePath, \"utf-8\")\n\n      if (\n        isContentSame(existingFileContent, content, {\n          // Ignore import differences for workspace components.\n          // TODO: figure out if we always want this.\n          ignoreImports: options.isWorkspace,\n        })\n      ) {\n        filesSkipped.push(path.relative(config.resolvedPaths.cwd, filePath))\n        continue\n      }\n    }\n\n    // Skip overwrite prompt for .env files - we'll handle them specially\n    if (existingFile && !options.overwrite && !isEnvFile(filePath)) {\n      filesCreatedSpinner.stop()\n      if (options.rootSpinner) {\n        options.rootSpinner.stop()\n      }\n      const { overwrite } = await prompts({\n        type: \"confirm\",\n        name: \"overwrite\",\n        message: `The file ${highlighter.info(\n          fileName\n        )} already exists. Would you like to overwrite?`,\n        initial: false,\n      })\n\n      if (!overwrite) {\n        filesSkipped.push(path.relative(config.resolvedPaths.cwd, filePath))\n        if (options.rootSpinner) {\n          options.rootSpinner.start()\n        }\n        continue\n      }\n      filesCreatedSpinner?.start()\n      if (options.rootSpinner) {\n        options.rootSpinner.start()\n      }\n    }\n\n    // Rename middleware.ts to proxy.ts for Next.js 16+.\n    if (_isNext16Middleware(filePath, projectInfo, config)) {\n      filePath = filePath.replace(/middleware\\.(ts|js)$/, \"proxy.$1\")\n    }\n\n    // Create the target directory if it doesn't exist.\n    if (!existsSync(targetDir)) {\n      await fs.mkdir(targetDir, { recursive: true })\n    }\n\n    // Special handling for .env files - append only new keys\n    if (isEnvFile(filePath) && existingFile) {\n      const existingFileContent = await fs.readFile(filePath, \"utf-8\")\n      const mergedContent = mergeEnvContent(existingFileContent, content)\n      envVarsAdded = getNewEnvKeys(existingFileContent, content)\n      envFile = path.relative(config.resolvedPaths.cwd, filePath)\n\n      if (!envVarsAdded.length) {\n        filesSkipped.push(path.relative(config.resolvedPaths.cwd, filePath))\n        continue\n      }\n\n      await fs.writeFile(filePath, mergedContent, \"utf-8\")\n      filesUpdated.push(path.relative(config.resolvedPaths.cwd, filePath))\n      continue\n    }\n\n    await fs.writeFile(filePath, content, \"utf-8\")\n\n    // Handle file creation logging\n    if (!existingFile) {\n      filesCreated.push(path.relative(config.resolvedPaths.cwd, filePath))\n\n      if (isEnvFile(filePath)) {\n        envVarsAdded = Object.keys(parseEnvContent(content))\n        envFile = path.relative(config.resolvedPaths.cwd, filePath)\n      }\n    } else {\n      filesUpdated.push(path.relative(config.resolvedPaths.cwd, filePath))\n    }\n  }\n\n  const allFiles = [...filesCreated, ...filesUpdated, ...filesSkipped]\n  const updatedFiles = await resolveImports(allFiles, config)\n\n  // Let's update filesUpdated with the updated files.\n  filesUpdated.push(...updatedFiles)\n\n  // If a file is in filesCreated and filesUpdated, we should remove it from filesUpdated.\n  filesUpdated = filesUpdated.filter((file) => !filesCreated.includes(file))\n\n  const hasUpdatedFiles = filesCreated.length || filesUpdated.length\n  if (!hasUpdatedFiles && !filesSkipped.length) {\n    filesCreatedSpinner?.info(\"No files updated.\")\n  }\n\n  // Remove duplicates.\n  filesCreated = Array.from(new Set(filesCreated))\n  filesUpdated = Array.from(new Set(filesUpdated))\n  filesSkipped = Array.from(new Set(filesSkipped))\n\n  if (filesCreated.length) {\n    filesCreatedSpinner?.succeed(\n      `Created ${filesCreated.length} ${\n        filesCreated.length === 1 ? \"file\" : \"files\"\n      }:`\n    )\n    if (!options.silent) {\n      for (const file of filesCreated) {\n        logger.log(`  - ${file}`)\n      }\n    }\n  } else {\n    filesCreatedSpinner?.stop()\n  }\n\n  if (filesUpdated.length) {\n    spinner(\n      `Updated ${filesUpdated.length} ${\n        filesUpdated.length === 1 ? \"file\" : \"files\"\n      }:`,\n      {\n        silent: options.silent,\n      }\n    )?.info()\n    if (!options.silent) {\n      for (const file of filesUpdated) {\n        logger.log(`  - ${file}`)\n      }\n    }\n  }\n\n  if (filesSkipped.length) {\n    spinner(\n      `Skipped ${filesSkipped.length} ${\n        filesUpdated.length === 1 ? \"file\" : \"files\"\n      }: (files might be identical, use --overwrite to overwrite)`,\n      {\n        silent: options.silent,\n      }\n    )?.info()\n    if (!options.silent) {\n      for (const file of filesSkipped) {\n        logger.log(`  - ${file}`)\n      }\n    }\n  }\n\n  if (envVarsAdded.length && envFile) {\n    spinner(\n      `Added the following variables to ${highlighter.info(envFile)}:`\n    )?.info()\n    if (!options.silent) {\n      for (const key of envVarsAdded) {\n        logger.log(`  ${highlighter.success(\"+\")} ${key}`)\n      }\n    }\n  }\n\n  if (!options.silent) {\n    logger.break()\n  }\n\n  return {\n    filesCreated,\n    filesUpdated,\n    filesSkipped,\n  }\n}\n\nexport function resolveFilePath(\n  file: z.infer<typeof registryItemFileSchema>,\n  config: Config,\n  options: {\n    isSrcDir?: boolean\n    commonRoot?: string\n    framework?: ProjectInfo[\"framework\"][\"name\"]\n    path?: string\n    fileIndex?: number\n  }\n) {\n  // Handle custom path if provided.\n  if (options.path) {\n    const resolvedPath = path.isAbsolute(options.path)\n      ? options.path\n      : path.join(config.resolvedPaths.cwd, options.path)\n\n    const isFilePath = /\\.[^/\\\\]+$/.test(resolvedPath)\n\n    if (isFilePath) {\n      // We'll only use the custom path for the first file.\n      // This is for registry items with multiple files.\n      if (options.fileIndex === 0) {\n        return resolvedPath\n      }\n    } else {\n      // If the custom path is a directory,\n      // We'll place all files in the directory.\n      const fileName = path.basename(file.path)\n      return path.join(resolvedPath, fileName)\n    }\n  }\n\n  if (file.target) {\n    if (file.target.startsWith(\"~/\")) {\n      return path.join(config.resolvedPaths.cwd, file.target.replace(\"~/\", \"\"))\n    }\n\n    let target = file.target\n\n    if (file.type === \"registry:page\") {\n      target = resolvePageTarget(target, options.framework)\n      if (!target) {\n        return \"\"\n      }\n    }\n\n    return options.isSrcDir\n      ? path.join(config.resolvedPaths.cwd, \"src\", target.replace(\"src/\", \"\"))\n      : path.join(config.resolvedPaths.cwd, target.replace(\"src/\", \"\"))\n  }\n\n  const targetDir = resolveFileTargetDirectory(file, config)\n\n  const relativePath = resolveNestedFilePath(file.path, targetDir)\n  return path.join(targetDir, relativePath)\n}\n\nfunction resolveFileTargetDirectory(\n  file: z.infer<typeof registryItemFileSchema>,\n  config: Config\n) {\n  if (file.type === \"registry:ui\") {\n    return config.resolvedPaths.ui\n  }\n\n  if (file.type === \"registry:lib\") {\n    return config.resolvedPaths.lib\n  }\n\n  if (file.type === \"registry:block\" || file.type === \"registry:component\") {\n    return config.resolvedPaths.components\n  }\n\n  if (file.type === \"registry:hook\") {\n    return config.resolvedPaths.hooks\n  }\n\n  return config.resolvedPaths.components\n}\n\nexport function findCommonRoot(paths: string[], needle: string): string {\n  // Remove leading slashes for consistent handling\n  const normalizedPaths = paths.map((p) => p.replace(/^\\//, \"\"))\n  const normalizedNeedle = needle.replace(/^\\//, \"\")\n\n  // Get the directory path of the needle by removing the file name\n  const needleDir = normalizedNeedle.split(\"/\").slice(0, -1).join(\"/\")\n\n  // If needle is at root level, return empty string\n  if (!needleDir) {\n    return \"\"\n  }\n\n  // Split the needle directory into segments\n  const needleSegments = needleDir.split(\"/\")\n\n  // Start from the full path and work backwards\n  for (let i = needleSegments.length; i > 0; i--) {\n    const testPath = needleSegments.slice(0, i).join(\"/\")\n    // Check if this is a common root by verifying if any other paths start with it\n    const hasRelatedPaths = normalizedPaths.some(\n      (path) => path !== normalizedNeedle && path.startsWith(testPath + \"/\")\n    )\n    if (hasRelatedPaths) {\n      return \"/\" + testPath // Add leading slash back for the result\n    }\n  }\n\n  // If no common root found with other files, return the parent directory of the needle\n  return \"/\" + needleDir // Add leading slash back for the result\n}\n\nexport function resolveNestedFilePath(\n  filePath: string,\n  targetDir: string\n): string {\n  // Normalize paths by removing leading/trailing slashes\n  const normalizedFilePath = filePath.replace(/^\\/|\\/$/g, \"\")\n  const normalizedTargetDir = targetDir.replace(/^\\/|\\/$/g, \"\")\n\n  // Split paths into segments\n  const fileSegments = normalizedFilePath.split(\"/\")\n  const targetSegments = normalizedTargetDir.split(\"/\")\n\n  // Find the last matching segment from targetDir in filePath\n  const lastTargetSegment = targetSegments[targetSegments.length - 1]\n  const commonDirIndex = fileSegments.findIndex(\n    (segment) => segment === lastTargetSegment\n  )\n\n  if (commonDirIndex === -1) {\n    // Return just the filename if no common directory is found\n    return fileSegments[fileSegments.length - 1]\n  }\n\n  // Return everything after the common directory\n  return fileSegments.slice(commonDirIndex + 1).join(\"/\")\n}\n\nexport function resolvePageTarget(\n  target: string,\n  framework?: ProjectInfo[\"framework\"][\"name\"]\n) {\n  if (!framework) {\n    return \"\"\n  }\n\n  if (framework === \"next-app\") {\n    return target\n  }\n\n  if (framework === \"next-pages\") {\n    let result = target.replace(/^app\\//, \"pages/\")\n    result = result.replace(/\\/page(\\.[jt]sx?)$/, \"$1\")\n\n    return result\n  }\n\n  if (framework === \"react-router\") {\n    let result = target.replace(/^app\\//, \"app/routes/\")\n    result = result.replace(/\\/page(\\.[jt]sx?)$/, \"$1\")\n\n    return result\n  }\n\n  if (framework === \"laravel\") {\n    let result = target.replace(/^app\\//, \"resources/js/pages/\")\n    result = result.replace(/\\/page(\\.[jt]sx?)$/, \"$1\")\n\n    return result\n  }\n\n  return \"\"\n}\n\nasync function resolveImports(filePaths: string[], config: Config) {\n  const project = new Project({\n    compilerOptions: {},\n  })\n  const projectInfo = await getProjectInfo(config.resolvedPaths.cwd)\n  const tsConfig = loadConfig(config.resolvedPaths.cwd)\n  const updatedFiles = []\n\n  if (!projectInfo || tsConfig.resultType === \"failed\") {\n    return []\n  }\n\n  for (const filepath of filePaths) {\n    const resolvedPath = path.resolve(config.resolvedPaths.cwd, filepath)\n\n    // Check if the file exists.\n    if (!existsSync(resolvedPath)) {\n      continue\n    }\n\n    const content = await fs.readFile(resolvedPath, \"utf-8\")\n\n    const dir = await fs.mkdtemp(path.join(tmpdir(), \"shadcn-\"))\n    const sourceFile = project.createSourceFile(\n      path.join(dir, basename(resolvedPath)),\n      content,\n      {\n        scriptKind: ScriptKind.TSX,\n      }\n    )\n\n    // Skip if the file extension is not one of the supported extensions.\n    if (![\".tsx\", \".ts\", \".jsx\", \".js\"].includes(sourceFile.getExtension())) {\n      continue\n    }\n\n    const importDeclarations = sourceFile.getImportDeclarations()\n    for (const importDeclaration of importDeclarations) {\n      const moduleSpecifier = importDeclaration.getModuleSpecifierValue()\n\n      // Filter out non-local imports.\n      if (\n        projectInfo?.aliasPrefix &&\n        !moduleSpecifier.startsWith(`${projectInfo.aliasPrefix}/`)\n      ) {\n        continue\n      }\n\n      // Find the probable import file path.\n      // This is where we expect to find the file on disk.\n      const probableImportFilePath = await resolveImport(\n        moduleSpecifier,\n        tsConfig\n      )\n\n      if (!probableImportFilePath) {\n        continue\n      }\n\n      // Find the actual import file path.\n      // This is the path where the file has been installed.\n      const resolvedImportFilePath = resolveModuleByProbablePath(\n        probableImportFilePath,\n        filePaths,\n        config\n      )\n\n      if (!resolvedImportFilePath) {\n        continue\n      }\n\n      // Convert the resolved import file path to an aliased import.\n      const newImport = toAliasedImport(\n        resolvedImportFilePath,\n        config,\n        projectInfo\n      )\n\n      if (!newImport || newImport === moduleSpecifier) {\n        continue\n      }\n\n      importDeclaration.setModuleSpecifier(newImport)\n\n      // Write the updated content to the file.\n      await fs.writeFile(resolvedPath, sourceFile.getFullText(), \"utf-8\")\n\n      // Track the updated file.\n      updatedFiles.push(filepath)\n    }\n  }\n\n  return updatedFiles\n}\n\n/**\n * Given an absolute \"probable\" import path (no ext),\n * plus an array of absolute file paths you already know about,\n * return 0–N matches (best match first), and also check disk for any missing ones.\n */\nexport function resolveModuleByProbablePath(\n  probableImportFilePath: string,\n  files: string[],\n  config: Config,\n  extensions: string[] = [\".tsx\", \".ts\", \".js\", \".jsx\", \".css\"]\n) {\n  const cwd = path.normalize(config.resolvedPaths.cwd)\n\n  // 1) Build a set of POSIX-normalized, project-relative files\n  const relativeFiles = files.map((f) => f.split(path.sep).join(path.posix.sep))\n  const fileSet = new Set(relativeFiles)\n\n  // 2) Strip any existing extension off the absolute base path\n  const extInPath = path.extname(probableImportFilePath)\n  const hasExt = extInPath !== \"\"\n  const absBase = hasExt\n    ? probableImportFilePath.slice(0, -extInPath.length)\n    : probableImportFilePath\n\n  // 3) Compute the project-relative \"base\" directory for strong matching\n  const relBaseRaw = path.relative(cwd, absBase)\n  const relBase = relBaseRaw.split(path.sep).join(path.posix.sep)\n\n  // 4) Decide which extensions to try\n  const tryExts = hasExt ? [extInPath] : extensions\n\n  // 5) Collect candidates\n  const candidates = new Set<string>()\n\n  // 5a) Fast‑path: [base + ext] and [base/index + ext]\n  for (const e of tryExts) {\n    const absCand = absBase + e\n    const relCand = path.posix.normalize(path.relative(cwd, absCand))\n    if (fileSet.has(relCand) || existsSync(absCand)) {\n      candidates.add(relCand)\n    }\n\n    const absIdx = path.join(absBase, `index${e}`)\n    const relIdx = path.posix.normalize(path.relative(cwd, absIdx))\n    if (fileSet.has(relIdx) || existsSync(absIdx)) {\n      candidates.add(relIdx)\n    }\n  }\n\n  // 5b) Fallback: scan known files by basename\n  const name = path.basename(absBase)\n  for (const f of relativeFiles) {\n    if (tryExts.some((e) => f.endsWith(`/${name}${e}`))) {\n      candidates.add(f)\n    }\n  }\n\n  // 6) If no matches, bail\n  if (candidates.size === 0) return null\n\n  // 7) Sort by (1) extension priority, then (2) \"strong\" base match\n  const sorted = Array.from(candidates).sort((a, b) => {\n    // a) extension order\n    const aExt = path.posix.extname(a)\n    const bExt = path.posix.extname(b)\n    const ord = tryExts.indexOf(aExt) - tryExts.indexOf(bExt)\n    if (ord !== 0) return ord\n    // b) strong match if path starts with relBase\n    const aStrong = relBase && a.startsWith(relBase) ? -1 : 1\n    const bStrong = relBase && b.startsWith(relBase) ? -1 : 1\n    return aStrong - bStrong\n  })\n\n  // 8) Return the first (best) candidate\n  return sorted[0]\n}\n\nexport function toAliasedImport(\n  filePath: string,\n  config: Config,\n  projectInfo: ProjectInfo\n): string | null {\n  const abs = path.normalize(path.join(config.resolvedPaths.cwd, filePath))\n\n  // 1️⃣ Find the longest matching alias root in resolvedPaths\n  //    e.g. key=\"ui\", root=\"/…/components/ui\" beats key=\"components\"\n  const matches = Object.entries(config.resolvedPaths)\n    .filter(\n      ([, root]) => root && abs.startsWith(path.normalize(root + path.sep))\n    )\n    .sort((a, b) => b[1].length - a[1].length)\n\n  if (matches.length === 0) {\n    return null\n  }\n  const [aliasKey, rootDir] = matches[0]\n\n  // 2️⃣ Compute the path UNDER that root\n  let rel = path.relative(rootDir, abs)\n  // force POSIX-style separators\n  rel = rel.split(path.sep).join(\"/\") // e.g. \"button/index.tsx\"\n\n  // 3️⃣ Strip code-file extensions, keep others (css, json, etc.)\n  const ext = path.posix.extname(rel)\n  const codeExts = [\".ts\", \".tsx\", \".js\", \".jsx\"]\n  const keepExt = codeExts.includes(ext) ? \"\" : ext\n  let noExt = rel.slice(0, rel.length - ext.length)\n\n  // 4️⃣ Collapse \"/index\" to its directory\n  if (noExt.endsWith(\"/index\")) {\n    noExt = noExt.slice(0, -\"/index\".length)\n  }\n\n  // 5️⃣ Build the aliased path\n  //    config.aliases[aliasKey] is e.g. \"@/components/ui\"\n  const aliasBase =\n    aliasKey === \"cwd\"\n      ? projectInfo.aliasPrefix\n      : config.aliases[aliasKey as keyof typeof config.aliases]\n  if (!aliasBase) {\n    return null\n  }\n  // if noExt is empty (i.e. file was exactly at the root), we import the root\n  let suffix = noExt === \"\" ? \"\" : `/${noExt}`\n\n  // Rremove /src from suffix.\n  // Alias will handle this.\n  suffix = suffix.replace(\"/src\", \"\")\n\n  // 6️⃣ Prepend the prefix from projectInfo (e.g. \"@\") if needed\n  //    but usually config.aliases already include it.\n  return `${aliasBase}${suffix}${keepExt}`\n}\n\nfunction _isNext16Middleware(\n  filePath: string,\n  projectInfo: ProjectInfo | null,\n  config: Config\n) {\n  const isRootMiddleware =\n    filePath === path.join(config.resolvedPaths.cwd, \"middleware.ts\") ||\n    filePath === path.join(config.resolvedPaths.cwd, \"middleware.js\")\n\n  const isNextJs =\n    projectInfo?.framework.name === \"next-app\" ||\n    projectInfo?.framework.name === \"next-pages\"\n\n  if (!isRootMiddleware || !isNextJs || !projectInfo?.frameworkVersion) {\n    return false\n  }\n\n  const majorVersion = parseInt(projectInfo.frameworkVersion.split(\".\")[0])\n  const isNext16Plus = !isNaN(majorVersion) && majorVersion >= 16\n\n  return isNext16Plus\n}\n", "import * as fs from \"fs/promises\"\nimport { tmpdir } from \"os\"\nimport * as path from \"path\"\nimport {\n  configSchema,\n  registryItemFileSchema,\n  registryItemSchema,\n} from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { ProjectInfo, getProjectInfo } from \"@/src/utils/get-project-info\"\nimport { resolveImport } from \"@/src/utils/resolve-import\"\nimport {\n  findCommonRoot,\n  resolveFilePath,\n} from \"@/src/utils/updaters/update-files\"\nimport { Project, ScriptKind } from \"ts-morph\"\nimport { loadConfig } from \"tsconfig-paths\"\nimport { z } from \"zod\"\n\nconst FILE_EXTENSIONS_FOR_LOOKUP = [\".tsx\", \".ts\", \".jsx\", \".js\", \".css\"]\nconst FILE_PATH_SKIP_LIST = [\"lib/utils.ts\"]\nconst DEPENDENCY_SKIP_LIST = [\n  /^(react|react-dom|next)(\\/.*)?$/, // Matches react, react-dom, next and their submodules\n  /^(node|jsr|npm):.*$/, // Matches node:, jsr:, and npm: prefixed modules\n]\n\nconst project = new Project({\n  compilerOptions: {},\n})\n\n// This returns the dependency from the module specifier.\n// Here dependency means an npm package.\nexport function getDependencyFromModuleSpecifier(\n  moduleSpecifier: string\n): string | null {\n  // Skip if the dependency matches any pattern in the skip list\n  if (DEPENDENCY_SKIP_LIST.some((pattern) => pattern.test(moduleSpecifier))) {\n    return null\n  }\n\n  // If the module specifier does not start with `@` and has a /, add the dependency first part only.\n  // E.g. `foo/bar` -> `foo`\n  if (!moduleSpecifier.startsWith(\"@\") && moduleSpecifier.includes(\"/\")) {\n    moduleSpecifier = moduleSpecifier.split(\"/\")[0]\n  }\n\n  // For scoped packages, we want to keep the first two parts\n  // E.g. `@types/react/dom` -> `@types/react`\n  if (moduleSpecifier.startsWith(\"@\")) {\n    const parts = moduleSpecifier.split(\"/\")\n    if (parts.length > 2) {\n      moduleSpecifier = parts.slice(0, 2).join(\"/\")\n    }\n  }\n\n  return moduleSpecifier\n}\n\nexport async function recursivelyResolveFileImports(\n  filePath: string,\n  config: z.infer<typeof configSchema>,\n  projectInfo: ProjectInfo,\n  processedFiles: Set<string> = new Set()\n): Promise<Pick<z.infer<typeof registryItemSchema>, \"files\" | \"dependencies\">> {\n  const resolvedFilePath = path.resolve(config.resolvedPaths.cwd, filePath)\n  const relativeRegistryFilePath = path.relative(\n    config.resolvedPaths.cwd,\n    resolvedFilePath\n  )\n\n  // Skip if the file is in the skip list\n  if (FILE_PATH_SKIP_LIST.includes(relativeRegistryFilePath)) {\n    return { dependencies: [], files: [] }\n  }\n\n  // Skip if the file extension is not one of the supported extensions\n  const fileExtension = path.extname(filePath)\n  if (!FILE_EXTENSIONS_FOR_LOOKUP.includes(fileExtension)) {\n    return { dependencies: [], files: [] }\n  }\n\n  // Prevent infinite loop: skip if already processed\n  if (processedFiles.has(relativeRegistryFilePath)) {\n    return { dependencies: [], files: [] }\n  }\n  processedFiles.add(relativeRegistryFilePath)\n\n  const stat = await fs.stat(resolvedFilePath)\n  if (!stat.isFile()) {\n    // Optionally log or handle this case\n    return { dependencies: [], files: [] }\n  }\n\n  const content = await fs.readFile(resolvedFilePath, \"utf-8\")\n  const tempFile = await createTempSourceFile(path.basename(resolvedFilePath))\n  const sourceFile = project.createSourceFile(tempFile, content, {\n    scriptKind: ScriptKind.TSX,\n  })\n  const tsConfig = await loadConfig(config.resolvedPaths.cwd)\n  if (tsConfig.resultType === \"failed\") {\n    return { dependencies: [], files: [] }\n  }\n\n  const files: z.infer<typeof registryItemSchema>[\"files\"] = []\n  const dependencies = new Set<string>()\n\n  // Add the original file first\n  const fileType = determineFileType(filePath)\n  const originalFile = {\n    path: relativeRegistryFilePath,\n    type: fileType,\n    target: \"\",\n  }\n  files.push(originalFile)\n\n  // 1. Find all import statements in the file.\n  const importStatements = sourceFile.getImportDeclarations()\n  for (const importStatement of importStatements) {\n    const moduleSpecifier = importStatement.getModuleSpecifierValue()\n\n    const isRelativeImport = moduleSpecifier.startsWith(\".\")\n    const isAliasImport = moduleSpecifier.startsWith(\n      `${projectInfo.aliasPrefix}/`\n    )\n\n    // If not a local import, add to the dependencies array.\n    if (!isAliasImport && !isRelativeImport) {\n      const dependency = getDependencyFromModuleSpecifier(moduleSpecifier)\n      if (dependency) {\n        dependencies.add(dependency)\n      }\n      continue\n    }\n\n    let probableImportFilePath = await resolveImport(moduleSpecifier, tsConfig)\n\n    if (isRelativeImport) {\n      probableImportFilePath = path.resolve(\n        path.dirname(resolvedFilePath),\n        moduleSpecifier\n      )\n    }\n\n    if (!probableImportFilePath) {\n      continue\n    }\n\n    // Check if the probable import path has a file extension.\n    // Try each extension until we find a file that exists.\n    const hasExtension = path.extname(probableImportFilePath)\n    if (!hasExtension) {\n      for (const ext of FILE_EXTENSIONS_FOR_LOOKUP) {\n        const pathWithExt: string = `${probableImportFilePath}${ext}`\n        try {\n          await fs.access(pathWithExt)\n          probableImportFilePath = pathWithExt\n          break\n        } catch {\n          continue\n        }\n      }\n    }\n\n    const nestedRelativeRegistryFilePath = path.relative(\n      config.resolvedPaths.cwd,\n      probableImportFilePath\n    )\n\n    // Skip if we've already processed this file or if it's in the skip list\n    if (\n      processedFiles.has(nestedRelativeRegistryFilePath) ||\n      FILE_PATH_SKIP_LIST.includes(nestedRelativeRegistryFilePath)\n    ) {\n      continue\n    }\n\n    const fileType = determineFileType(moduleSpecifier)\n    const file = {\n      path: nestedRelativeRegistryFilePath,\n      type: fileType,\n      target: \"\",\n    }\n\n    // TODO (shadcn): fix this.\n    if (fileType === \"registry:page\" || fileType === \"registry:file\") {\n      file.target = moduleSpecifier\n    }\n\n    files.push(file)\n\n    // Recursively process the imported file, passing the shared processedFiles set\n    const nestedResults = await recursivelyResolveFileImports(\n      nestedRelativeRegistryFilePath,\n      config,\n      projectInfo,\n      processedFiles\n    )\n\n    if (nestedResults.files) {\n      // Only add files that haven't been processed yet\n      for (const file of nestedResults.files) {\n        if (!processedFiles.has(file.path)) {\n          processedFiles.add(file.path)\n          files.push(file)\n        }\n      }\n    }\n\n    if (nestedResults.dependencies) {\n      nestedResults.dependencies.forEach((dep) => dependencies.add(dep))\n    }\n  }\n\n  // Deduplicate files by path\n  const uniqueFiles = Array.from(\n    new Map(files.map((file) => [file.path, file])).values()\n  )\n\n  return {\n    dependencies: Array.from(dependencies),\n    files: uniqueFiles,\n  }\n}\n\nasync function createTempSourceFile(filename: string) {\n  const dir = await fs.mkdtemp(path.join(tmpdir(), \"shadcn-\"))\n  return path.join(dir, filename)\n}\n\n// This is a bit tricky to accurately determine.\n// For now we'll use the module specifier to determine the type.\nfunction determineFileType(\n  moduleSpecifier: string\n): z.infer<typeof registryItemSchema>[\"type\"] {\n  if (moduleSpecifier.includes(\"/ui/\")) {\n    return \"registry:ui\"\n  }\n\n  if (moduleSpecifier.includes(\"/lib/\")) {\n    return \"registry:lib\"\n  }\n\n  if (moduleSpecifier.includes(\"/hooks/\")) {\n    return \"registry:hook\"\n  }\n\n  if (moduleSpecifier.includes(\"/components/\")) {\n    return \"registry:component\"\n  }\n\n  return \"registry:component\"\n}\n\n// Additional utility functions for local file support\nexport function isUrl(path: string) {\n  try {\n    new URL(path)\n    return true\n  } catch (error) {\n    return false\n  }\n}\n\nexport function isLocalFile(path: string) {\n  return path.endsWith(\".json\") && !isUrl(path)\n}\n\n/**\n * Check if a registry item is universal (framework-agnostic).\n * A universal registry item must:\n * 1. Have type \"registry:item\" or \"registry:file\"\n * 2. If it has files, all files must have explicit targets and be type \"registry:file\" or \"registry:item\"\n * It can be installed without framework detection or components.json.\n */\nexport function isUniversalRegistryItem(\n  registryItem:\n    | Pick<z.infer<typeof registryItemSchema>, \"files\" | \"type\">\n    | null\n    | undefined\n): boolean {\n  if (!registryItem) {\n    return false\n  }\n\n  if (\n    registryItem.type !== \"registry:item\" &&\n    registryItem.type !== \"registry:file\"\n  ) {\n    return false\n  }\n\n  const files = registryItem.files ?? []\n\n  // If there are files, all must have targets and be of type registry:file or registry:item.\n  return files.every(\n    (file) =>\n      !!file.target &&\n      (file.type === \"registry:file\" || file.type === \"registry:item\")\n  )\n}\n\n// Deduplicates files based on their resolved target paths.\n// When multiple files resolve to the same target path, the last one wins.\nexport async function deduplicateFilesByTarget(\n  filesArrays: Array<z.infer<typeof registryItemFileSchema>[] | undefined>,\n  config: Config\n) {\n  // Fallback to simple concatenation when we don't have complete config.\n  if (!canDeduplicateFiles(config)) {\n    return z\n      .array(registryItemFileSchema)\n      .parse(filesArrays.flat().filter(Boolean))\n  }\n\n  // Get project info for file resolution.\n  const projectInfo = await getProjectInfo(config.resolvedPaths.cwd)\n  const targetMap = new Map<string, z.infer<typeof registryItemFileSchema>>()\n  const allFiles = z\n    .array(registryItemFileSchema)\n    .parse(filesArrays.flat().filter(Boolean))\n\n  allFiles.forEach((file) => {\n    const resolvedPath = resolveFilePath(file, config, {\n      isSrcDir: projectInfo?.isSrcDir,\n      framework: projectInfo?.framework.name,\n      commonRoot: findCommonRoot(\n        allFiles.map((f) => f.path),\n        file.path\n      ),\n    })\n\n    if (resolvedPath) {\n      // Last one wins - overwrites previous entry.\n      targetMap.set(resolvedPath, file)\n    }\n  })\n\n  return Array.from(targetMap.values())\n}\n\n// Checks if the config has the minimum required paths for file deduplication.\nexport function canDeduplicateFiles(config: Config) {\n  return !!(\n    config?.resolvedPaths?.cwd &&\n    (config?.resolvedPaths?.ui ||\n      config?.resolvedPaths?.lib ||\n      config?.resolvedPaths?.components ||\n      config?.resolvedPaths?.hooks)\n  )\n}\n", "import { BUILTIN_REGISTRIES, FALLBACK_STYLE } from \"@/src/registry/constants\"\nimport { configSchema } from \"@/src/schema\"\nimport { Config, createConfig } from \"@/src/utils/get-config\"\nimport deepmerge from \"deepmerge\"\n\nfunction resolveStyleFromConfig(config: Partial<Config> | Config) {\n  if (!config.style) {\n    return FALLBACK_STYLE\n  }\n\n  // Check if we should use new-york-v4 for Tailwind v4.\n  // We assume that if tailwind.config is empty, we're using Tailwind v4.\n  if (config.style === \"new-york\" && config.tailwind?.config === \"\") {\n    return FALLBACK_STYLE\n  }\n\n  return config.style\n}\n\nexport function configWithDefaults(config?: Partial<Config> | Config) {\n  const baseConfig = createConfig({\n    style: FALLBACK_STYLE,\n    registries: BUILTIN_REGISTRIES,\n  })\n\n  if (!config) {\n    return baseConfig\n  }\n\n  return configSchema.parse(\n    deepmerge(baseConfig, {\n      ...config,\n      style: resolveStyleFromConfig(config),\n      registries: { ...BUILTIN_REGISTRIES, ...config.registries },\n    })\n  )\n}\n", "interface RegistryContext {\n  headers: Record<string, Record<string, string>>\n}\n\nlet context: RegistryContext = {\n  headers: {},\n}\n\nexport function setRegistryHeaders(\n  headers: Record<string, Record<string, string>>\n) {\n  // Merge new headers with existing ones to preserve headers for nested dependencies\n  context.headers = { ...context.headers, ...headers }\n}\n\nexport function getRegistryHeadersFromContext(\n  url: string\n): Record<string, string> {\n  return context.headers[url] || {}\n}\n\nexport function clearRegistryContext() {\n  context.headers = {}\n}\n", "import { buildUrlAndHeadersForRegistryItem } from \"@/src/registry/builder\"\nimport { configWithDefaults } from \"@/src/registry/config\"\nimport { clearRegistryContext } from \"@/src/registry/context\"\nimport { extractEnvVars } from \"@/src/registry/env\"\nimport { RegistryMissingEnvironmentVariablesError } from \"@/src/registry/errors\"\nimport { registryConfigItemSchema } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { z } from \"zod\"\n\nexport function extractEnvVarsFromRegistryConfig(\n  config: z.infer<typeof registryConfigItemSchema>\n): string[] {\n  const vars = new Set<string>()\n\n  if (typeof config === \"string\") {\n    extractEnvVars(config).forEach((v) => vars.add(v))\n  } else {\n    extractEnvVars(config.url).forEach((v) => vars.add(v))\n\n    if (config.params) {\n      Object.values(config.params).forEach((value) => {\n        extractEnvVars(value).forEach((v) => vars.add(v))\n      })\n    }\n\n    if (config.headers) {\n      Object.values(config.headers).forEach((value) => {\n        extractEnvVars(value).forEach((v) => vars.add(v))\n      })\n    }\n  }\n\n  return Array.from(vars)\n}\n\nexport function validateRegistryConfig(\n  registryName: string,\n  config: z.infer<typeof registryConfigItemSchema>\n): void {\n  const requiredVars = extractEnvVarsFromRegistryConfig(config)\n  const missing = requiredVars.filter((v) => !process.env[v])\n\n  if (missing.length > 0) {\n    throw new RegistryMissingEnvironmentVariablesError(registryName, missing)\n  }\n}\n\nexport function validateRegistryConfigForItems(\n  items: string[],\n  config?: Config\n): void {\n  for (const item of items) {\n    buildUrlAndHeadersForRegistryItem(item, configWithDefaults(config))\n  }\n\n  // Clear the registry context after validation.\n  clearRegistryContext()\n}\n", "import { REGISTRY_URL } from \"@/src/registry/constants\"\nimport { expandEnvVars } from \"@/src/registry/env\"\nimport { RegistryNotConfiguredError } from \"@/src/registry/errors\"\nimport { parseRegistryAndItemFromString } from \"@/src/registry/parser\"\nimport { isUrl } from \"@/src/registry/utils\"\nimport { validateRegistryConfig } from \"@/src/registry/validator\"\nimport { registryConfigItemSchema } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { z } from \"zod\"\n\nconst NAME_PLACEHOLDER = \"{name}\"\nconst STYLE_PLACEHOLDER = \"{style}\"\nconst ENV_VAR_PATTERN = /\\${(\\w+)}/g\nconst QUERY_PARAM_SEPARATOR = \"?\"\nconst QUERY_PARAM_DELIMITER = \"&\"\n\nexport function buildUrlAndHeadersForRegistryItem(\n  name: string,\n  config?: Config\n) {\n  const { registry, item } = parseRegistryAndItemFromString(name)\n\n  if (!registry) {\n    return null\n  }\n\n  const registries = config?.registries || {}\n  const registryConfig = registries[registry]\n  if (!registryConfig) {\n    throw new RegistryNotConfiguredError(registry)\n  }\n\n  // TODO: I don't like this here.\n  // But this will do for now.\n  validateRegistryConfig(registry, registryConfig)\n\n  return {\n    url: buildUrlFromRegistryConfig(item, registryConfig, config),\n    headers: buildHeadersFromRegistryConfig(registryConfig),\n  }\n}\n\nexport function buildUrlFromRegistryConfig(\n  item: string,\n  registryConfig: z.infer<typeof registryConfigItemSchema>,\n  config?: Config\n) {\n  if (typeof registryConfig === \"string\") {\n    let url = registryConfig.replace(NAME_PLACEHOLDER, item)\n    if (config?.style && url.includes(STYLE_PLACEHOLDER)) {\n      url = url.replace(STYLE_PLACEHOLDER, config.style)\n    }\n    return expandEnvVars(url)\n  }\n\n  let baseUrl = registryConfig.url.replace(NAME_PLACEHOLDER, item)\n  if (config?.style && baseUrl.includes(STYLE_PLACEHOLDER)) {\n    baseUrl = baseUrl.replace(STYLE_PLACEHOLDER, config.style)\n  }\n  baseUrl = expandEnvVars(baseUrl)\n\n  if (!registryConfig.params) {\n    return baseUrl\n  }\n\n  return appendQueryParams(baseUrl, registryConfig.params)\n}\n\nexport function buildHeadersFromRegistryConfig(\n  config: z.infer<typeof registryConfigItemSchema>\n) {\n  if (typeof config === \"string\" || !config.headers) {\n    return {}\n  }\n\n  const headers: Record<string, string> = {}\n\n  for (const [key, value] of Object.entries(config.headers)) {\n    const expandedValue = expandEnvVars(value)\n\n    if (shouldIncludeHeader(value, expandedValue)) {\n      headers[key] = expandedValue\n    }\n  }\n\n  return headers\n}\n\nfunction appendQueryParams(baseUrl: string, params: Record<string, string>) {\n  const urlParams = new URLSearchParams()\n\n  for (const [key, value] of Object.entries(params)) {\n    const expandedValue = expandEnvVars(value)\n    if (expandedValue) {\n      urlParams.append(key, expandedValue)\n    }\n  }\n\n  const queryString = urlParams.toString()\n  if (!queryString) {\n    return baseUrl\n  }\n\n  const separator = baseUrl.includes(QUERY_PARAM_SEPARATOR)\n    ? QUERY_PARAM_DELIMITER\n    : QUERY_PARAM_SEPARATOR\n\n  return `${baseUrl}${separator}${queryString}`\n}\n\nfunction shouldIncludeHeader(originalValue: string, expandedValue: string) {\n  const trimmedExpanded = expandedValue.trim()\n\n  if (!trimmedExpanded) {\n    return false\n  }\n\n  // If the original value contains valid env vars, only include if expansion changed the value.\n  if (originalValue.includes(\"${\")) {\n    // Check if there are actual env vars in the string\n    const envVars = originalValue.match(ENV_VAR_PATTERN)\n    if (envVars) {\n      const templateWithoutVars = originalValue\n        .replace(ENV_VAR_PATTERN, \"\")\n        .trim()\n      return trimmedExpanded !== templateWithoutVars\n    }\n  }\n\n  return true\n}\n\n/**\n * Resolves a registry URL from a path or URL string.\n * Handles special cases like v0 registry URLs that need /json suffix.\n *\n * @param pathOrUrl - Either a relative path or a full URL\n * @returns The resolved registry URL\n */\nexport function resolveRegistryUrl(pathOrUrl: string) {\n  if (isUrl(pathOrUrl)) {\n    // If the url contains /chat/b/, we assume it's the v0 registry.\n    // We need to add the /json suffix if it's missing.\n    const url = new URL(pathOrUrl)\n    if (url.pathname.match(/\\/chat\\/b\\//) && !url.pathname.endsWith(\"/json\")) {\n      url.pathname = `${url.pathname}/json`\n    }\n\n    return url.toString()\n  }\n\n  return `${REGISTRY_URL}/${pathOrUrl}`\n}\n", "import { promises as fs } from \"fs\"\nimport { homedir } from \"os\"\nimport path from \"path\"\nimport { resolveRegistryUrl } from \"@/src/registry/builder\"\nimport { getRegistryHeadersFromContext } from \"@/src/registry/context\"\nimport {\n  RegistryFetchError,\n  RegistryForbiddenError,\n  RegistryLocalFileError,\n  RegistryNotFoundError,\n  RegistryParseError,\n  RegistryUnauthorizedError,\n} from \"@/src/registry/errors\"\nimport { registryItemSchema } from \"@/src/schema\"\nimport { HttpsProxyAgent } from \"https-proxy-agent\"\nimport fetch from \"node-fetch\"\nimport { z } from \"zod\"\n\nconst agent = process.env.https_proxy\n  ? new HttpsProxyAgent(process.env.https_proxy)\n  : undefined\n\nconst registryCache = new Map<string, Promise<any>>()\n\nexport function clearRegistryCache() {\n  registryCache.clear()\n}\n\nexport async function fetchRegistry(\n  paths: string[],\n  options: { useCache?: boolean } = {}\n) {\n  options = {\n    useCache: true,\n    ...options,\n  }\n\n  try {\n    const results = await Promise.all(\n      paths.map(async (path) => {\n        const url = resolveRegistryUrl(path)\n\n        // Check cache first if caching is enabled\n        if (options.useCache && registryCache.has(url)) {\n          return registryCache.get(url)\n        }\n\n        // Store the promise in the cache before awaiting if caching is enabled.\n        const fetchPromise = (async () => {\n          // Get headers from context for this URL.\n          const headers = getRegistryHeadersFromContext(url)\n\n          const response = await fetch(url, {\n            agent,\n            headers: {\n              ...headers,\n            },\n          })\n\n          if (!response.ok) {\n            let messageFromServer = undefined\n\n            if (\n              response.headers.get(\"content-type\")?.includes(\"application/json\")\n            ) {\n              const json = await response.json()\n              const parsed = z\n                .object({\n                  // RFC 7807.\n                  detail: z.string().optional(),\n                  title: z.string().optional(),\n                  // Standard error response.\n                  message: z.string().optional(),\n                  error: z.string().optional(),\n                })\n                .safeParse(json)\n\n              if (parsed.success) {\n                // Prefer RFC 7807 detail field, then message field.\n                messageFromServer = parsed.data.detail || parsed.data.message\n\n                if (parsed.data.error) {\n                  messageFromServer = `[${parsed.data.error}] ${messageFromServer}`\n                }\n              }\n            }\n\n            if (response.status === 401) {\n              throw new RegistryUnauthorizedError(url, messageFromServer)\n            }\n\n            if (response.status === 404) {\n              throw new RegistryNotFoundError(url, messageFromServer)\n            }\n\n            if (response.status === 403) {\n              throw new RegistryForbiddenError(url, messageFromServer)\n            }\n\n            throw new RegistryFetchError(\n              url,\n              response.status,\n              messageFromServer\n            )\n          }\n\n          return response.json()\n        })()\n\n        if (options.useCache) {\n          registryCache.set(url, fetchPromise)\n        }\n        return fetchPromise\n      })\n    )\n\n    return results\n  } catch (error) {\n    throw error\n  }\n}\n\nexport async function fetchRegistryLocal(filePath: string) {\n  try {\n    // Handle tilde expansion for home directory\n    let expandedPath = filePath\n    if (filePath.startsWith(\"~/\")) {\n      expandedPath = path.join(homedir(), filePath.slice(2))\n    }\n\n    const resolvedPath = path.resolve(expandedPath)\n    const content = await fs.readFile(resolvedPath, \"utf8\")\n    const parsed = JSON.parse(content)\n\n    try {\n      return registryItemSchema.parse(parsed)\n    } catch (error) {\n      throw new RegistryParseError(filePath, error)\n    }\n  } catch (error) {\n    // Check if this is a file not found error\n    if (\n      error instanceof Error &&\n      (error.message.includes(\"ENOENT\") ||\n        error.message.includes(\"no such file\"))\n    ) {\n      throw new RegistryLocalFileError(filePath, error)\n    }\n    // Re-throw parse errors as-is\n    if (error instanceof RegistryParseError) {\n      throw error\n    }\n    // For other errors (like JSON parse errors), throw as local file error\n    throw new RegistryLocalFileError(filePath, error)\n  }\n}\n", "import { promises as fs } from \"fs\"\nimport { tmpdir } from \"os\"\nimport path from \"path\"\nimport {\n  registryItemCssVarsSchema,\n  registryItemTailwindSchema,\n} from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport { TailwindVersion } from \"@/src/utils/get-project-info\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { spinner } from \"@/src/utils/spinner\"\nimport deepmerge from \"deepmerge\"\nimport objectToString from \"stringify-object\"\nimport { type Config as TailwindConfig } from \"tailwindcss\"\nimport {\n  ArrayLiteralExpression,\n  ObjectLiteralExpression,\n  Project,\n  PropertyAssignment,\n  QuoteKind,\n  ScriptKind,\n  SyntaxKind,\n  VariableStatement,\n} from \"ts-morph\"\nimport { z } from \"zod\"\n\nexport type UpdaterTailwindConfig = Omit<TailwindConfig, \"plugins\"> & {\n  // We only want string plugins for now.\n  plugins?: string[]\n}\n\nexport async function updateTailwindConfig(\n  tailwindConfig:\n    | z.infer<typeof registryItemTailwindSchema>[\"config\"]\n    | undefined,\n  config: Config,\n  options: {\n    silent?: boolean\n    tailwindVersion?: TailwindVersion\n  }\n) {\n  if (!tailwindConfig) {\n    return\n  }\n\n  options = {\n    silent: false,\n    tailwindVersion: \"v3\",\n    ...options,\n  }\n\n  // No tailwind config in v4.\n  if (options.tailwindVersion === \"v4\") {\n    return\n  }\n\n  const tailwindFileRelativePath = path.relative(\n    config.resolvedPaths.cwd,\n    config.resolvedPaths.tailwindConfig\n  )\n  const tailwindSpinner = spinner(\n    `Updating ${highlighter.info(tailwindFileRelativePath)}`,\n    {\n      silent: options.silent,\n    }\n  ).start()\n  const raw = await fs.readFile(config.resolvedPaths.tailwindConfig, \"utf8\")\n  const output = await transformTailwindConfig(raw, tailwindConfig, config)\n  await fs.writeFile(config.resolvedPaths.tailwindConfig, output, \"utf8\")\n  tailwindSpinner?.succeed()\n}\n\nexport async function transformTailwindConfig(\n  input: string,\n  tailwindConfig: UpdaterTailwindConfig,\n  config: Config\n) {\n  const sourceFile = await _createSourceFile(input, config)\n  // Find the object with content property.\n  // This is faster than traversing the default export.\n  // TODO: maybe we do need to traverse the default export?\n  const configObject = sourceFile\n    .getDescendantsOfKind(SyntaxKind.ObjectLiteralExpression)\n    .find((node) =>\n      node\n        .getProperties()\n        .some(\n          (property) =>\n            property.isKind(SyntaxKind.PropertyAssignment) &&\n            property.getName() === \"content\"\n        )\n    )\n\n  // We couldn't find the config object, so we return the input as is.\n  if (!configObject) {\n    return input\n  }\n\n  const quoteChar = _getQuoteChar(configObject)\n\n  // Add darkMode.\n  addTailwindConfigProperty(\n    configObject,\n    {\n      name: \"darkMode\",\n      value: \"class\",\n    },\n    { quoteChar }\n  )\n\n  // Add Tailwind config plugins.\n  tailwindConfig.plugins?.forEach((plugin) => {\n    addTailwindConfigPlugin(configObject, plugin)\n  })\n\n  // Add Tailwind config theme.\n  if (tailwindConfig.theme) {\n    await addTailwindConfigTheme(configObject, tailwindConfig.theme)\n  }\n\n  return sourceFile.getFullText()\n}\n\nfunction addTailwindConfigProperty(\n  configObject: ObjectLiteralExpression,\n  property: {\n    name: string\n    value: string\n  },\n  {\n    quoteChar,\n  }: {\n    quoteChar: string\n  }\n) {\n  const existingProperty = configObject.getProperty(\"darkMode\")\n\n  if (!existingProperty) {\n    const newProperty = {\n      name: property.name,\n      initializer: `[${quoteChar}${property.value}${quoteChar}]`,\n    }\n\n    // We need to add darkMode as the first property.\n    if (property.name === \"darkMode\") {\n      configObject.insertPropertyAssignment(0, newProperty)\n      return configObject\n    }\n\n    configObject.addPropertyAssignment(newProperty)\n\n    return configObject\n  }\n\n  if (existingProperty.isKind(SyntaxKind.PropertyAssignment)) {\n    const initializer = existingProperty.getInitializer()\n    const newValue = `${quoteChar}${property.value}${quoteChar}`\n\n    // If property is a string, change it to an array and append.\n    if (initializer?.isKind(SyntaxKind.StringLiteral)) {\n      const initializerText = initializer.getText()\n      initializer.replaceWithText(`[${initializerText}, ${newValue}]`)\n      return configObject\n    }\n\n    // If property is an array, append.\n    if (initializer?.isKind(SyntaxKind.ArrayLiteralExpression)) {\n      // Check if the array already contains the value.\n      if (\n        initializer\n          .getElements()\n          .map((element) => element.getText())\n          .includes(newValue)\n      ) {\n        return configObject\n      }\n      initializer.addElement(newValue)\n    }\n\n    return configObject\n  }\n\n  return configObject\n}\n\nasync function addTailwindConfigTheme(\n  configObject: ObjectLiteralExpression,\n  theme: UpdaterTailwindConfig[\"theme\"]\n) {\n  // Ensure there is a theme property.\n  if (!configObject.getProperty(\"theme\")) {\n    configObject.addPropertyAssignment({\n      name: \"theme\",\n      initializer: \"{}\",\n    })\n  }\n\n  // Nest all spread properties.\n  nestSpreadProperties(configObject)\n\n  const themeProperty = configObject\n    .getPropertyOrThrow(\"theme\")\n    ?.asKindOrThrow(SyntaxKind.PropertyAssignment)\n\n  const themeInitializer = themeProperty.getInitializer()\n  if (themeInitializer?.isKind(SyntaxKind.ObjectLiteralExpression)) {\n    const themeObjectString = themeInitializer.getText()\n    const themeObject = await parseObjectLiteral(themeObjectString)\n    const result = deepmerge(themeObject, theme, {\n      arrayMerge: (dst, src) => src,\n    })\n    const resultString = objectToString(result)\n      .replace(/\\'\\.\\.\\.(.*)\\'/g, \"...$1\") // Remove quote around spread element\n      .replace(/\\'\\\"/g, \"'\") // Replace `\\\" with \"\n      .replace(/\\\"\\'/g, \"'\") // Replace `\\\" with \"\n      .replace(/\\'\\[/g, \"[\") // Replace `[ with [\n      .replace(/\\]\\'/g, \"]\") // Replace `] with ]\n      .replace(/\\'\\\\\\'/g, \"'\") // Replace `\\' with '\n      .replace(/\\\\\\'/g, \"'\") // Replace \\' with '\n      .replace(/\\\\\\'\\'/g, \"'\")\n      .replace(/\\'\\'/g, \"'\")\n    themeInitializer.replaceWithText(resultString)\n  }\n\n  // Unnest all spread properties.\n  unnestSpreadProperties(configObject)\n}\n\nfunction addTailwindConfigPlugin(\n  configObject: ObjectLiteralExpression,\n  plugin: string\n) {\n  const existingPlugins = configObject.getProperty(\"plugins\")\n\n  if (!existingPlugins) {\n    configObject.addPropertyAssignment({\n      name: \"plugins\",\n      initializer: `[${plugin}]`,\n    })\n\n    return configObject\n  }\n\n  if (existingPlugins.isKind(SyntaxKind.PropertyAssignment)) {\n    const initializer = existingPlugins.getInitializer()\n\n    if (initializer?.isKind(SyntaxKind.ArrayLiteralExpression)) {\n      if (\n        initializer\n          .getElements()\n          .map((element) => {\n            return element.getText().replace(/[\"']/g, \"\")\n          })\n          .includes(plugin.replace(/[\"']/g, \"\"))\n      ) {\n        return configObject\n      }\n      initializer.addElement(plugin)\n    }\n\n    return configObject\n  }\n\n  return configObject\n}\n\nexport async function _createSourceFile(input: string, config: Config | null) {\n  const dir = await fs.mkdtemp(path.join(tmpdir(), \"shadcn-\"))\n  const resolvedPath =\n    config?.resolvedPaths?.tailwindConfig || \"tailwind.config.ts\"\n  const tempFile = path.join(dir, `shadcn-${path.basename(resolvedPath)}`)\n\n  const project = new Project({\n    compilerOptions: {},\n  })\n  const sourceFile = project.createSourceFile(tempFile, input, {\n    // Note: .js and .mjs can still be valid for TS projects.\n    // We can't infer TypeScript from config.tsx.\n    scriptKind:\n      path.extname(resolvedPath) === \".ts\" ? ScriptKind.TS : ScriptKind.JS,\n  })\n\n  return sourceFile\n}\n\nexport function _getQuoteChar(configObject: ObjectLiteralExpression) {\n  return configObject\n    .getFirstDescendantByKind(SyntaxKind.StringLiteral)\n    ?.getQuoteKind() === QuoteKind.Single\n    ? \"'\"\n    : '\"'\n}\n\nexport function nestSpreadProperties(obj: ObjectLiteralExpression) {\n  const properties = obj.getProperties()\n\n  for (let i = 0; i < properties.length; i++) {\n    const prop = properties[i]\n    if (prop.isKind(SyntaxKind.SpreadAssignment)) {\n      const spreadAssignment = prop.asKindOrThrow(SyntaxKind.SpreadAssignment)\n      const spreadText = spreadAssignment.getExpression().getText()\n\n      // Replace spread with a property assignment\n      obj.insertPropertyAssignment(i, {\n        // Need to escape the name with \" so that deepmerge doesn't mishandle the key\n        name: `\"___${spreadText.replace(/^\\.\\.\\./, \"\")}\"`,\n        initializer: `\"...${spreadText.replace(/^\\.\\.\\./, \"\")}\"`,\n      })\n\n      // Remove the original spread assignment\n      spreadAssignment.remove()\n    } else if (prop.isKind(SyntaxKind.PropertyAssignment)) {\n      const propAssignment = prop.asKindOrThrow(SyntaxKind.PropertyAssignment)\n      const initializer = propAssignment.getInitializer()\n\n      if (\n        initializer &&\n        initializer.isKind(SyntaxKind.ObjectLiteralExpression)\n      ) {\n        // Recursively process nested object literals\n        nestSpreadProperties(\n          initializer.asKindOrThrow(SyntaxKind.ObjectLiteralExpression)\n        )\n      } else if (\n        initializer &&\n        initializer.isKind(SyntaxKind.ArrayLiteralExpression)\n      ) {\n        nestSpreadElements(\n          initializer.asKindOrThrow(SyntaxKind.ArrayLiteralExpression)\n        )\n      }\n    }\n  }\n}\n\nexport function nestSpreadElements(arr: ArrayLiteralExpression) {\n  const elements = arr.getElements()\n  for (let j = 0; j < elements.length; j++) {\n    const element = elements[j]\n    if (element.isKind(SyntaxKind.ObjectLiteralExpression)) {\n      // Recursive check on objects within arrays\n      nestSpreadProperties(\n        element.asKindOrThrow(SyntaxKind.ObjectLiteralExpression)\n      )\n    } else if (element.isKind(SyntaxKind.ArrayLiteralExpression)) {\n      // Recursive check on nested arrays\n      nestSpreadElements(\n        element.asKindOrThrow(SyntaxKind.ArrayLiteralExpression)\n      )\n    } else if (element.isKind(SyntaxKind.SpreadElement)) {\n      const spreadText = element.getText()\n      // Spread element within an array\n      arr.removeElement(j)\n      arr.insertElement(j, `\"${spreadText}\"`)\n    }\n  }\n}\n\nexport function unnestSpreadProperties(obj: ObjectLiteralExpression) {\n  const properties = obj.getProperties()\n\n  for (let i = 0; i < properties.length; i++) {\n    const prop = properties[i]\n    if (prop.isKind(SyntaxKind.PropertyAssignment)) {\n      const propAssignment = prop as PropertyAssignment\n      const initializer = propAssignment.getInitializer()\n\n      if (initializer && initializer.isKind(SyntaxKind.StringLiteral)) {\n        const value = initializer\n          .asKindOrThrow(SyntaxKind.StringLiteral)\n          .getLiteralValue()\n        if (value.startsWith(\"...\")) {\n          obj.insertSpreadAssignment(i, { expression: value.slice(3) })\n          propAssignment.remove()\n        }\n      } else if (initializer?.isKind(SyntaxKind.ObjectLiteralExpression)) {\n        unnestSpreadProperties(initializer as ObjectLiteralExpression)\n      } else if (\n        initializer &&\n        initializer.isKind(SyntaxKind.ArrayLiteralExpression)\n      ) {\n        unsetSpreadElements(\n          initializer.asKindOrThrow(SyntaxKind.ArrayLiteralExpression)\n        )\n      }\n    }\n  }\n}\n\nexport function unsetSpreadElements(arr: ArrayLiteralExpression) {\n  const elements = arr.getElements()\n  for (let j = 0; j < elements.length; j++) {\n    const element = elements[j]\n    if (element.isKind(SyntaxKind.ObjectLiteralExpression)) {\n      // Recursive check on objects within arrays\n      unnestSpreadProperties(\n        element.asKindOrThrow(SyntaxKind.ObjectLiteralExpression)\n      )\n    } else if (element.isKind(SyntaxKind.ArrayLiteralExpression)) {\n      // Recursive check on nested arrays\n      unsetSpreadElements(\n        element.asKindOrThrow(SyntaxKind.ArrayLiteralExpression)\n      )\n    } else if (element.isKind(SyntaxKind.StringLiteral)) {\n      const spreadText = element.getText()\n      // check if spread element\n      const spreadTest = /(?:^['\"])(\\.\\.\\..*)(?:['\"]$)/g\n      if (spreadTest.test(spreadText)) {\n        arr.removeElement(j)\n        arr.insertElement(j, spreadText.replace(spreadTest, \"$1\"))\n      }\n    }\n  }\n}\n\nasync function parseObjectLiteral(objectLiteralString: string): Promise<any> {\n  const sourceFile = await _createSourceFile(\n    `const theme = ${objectLiteralString}`,\n    null\n  )\n\n  const statement = sourceFile.getStatements()[0]\n  if (statement?.getKind() === SyntaxKind.VariableStatement) {\n    const declaration = (statement as VariableStatement)\n      .getDeclarationList()\n      ?.getDeclarations()[0]\n    const initializer = declaration.getInitializer()\n    if (initializer?.isKind(SyntaxKind.ObjectLiteralExpression)) {\n      return await parseObjectLiteralExpression(initializer)\n    }\n  }\n\n  throw new Error(\"Invalid input: not an object literal\")\n}\n\nfunction parseObjectLiteralExpression(node: ObjectLiteralExpression): any {\n  const result: any = {}\n  for (const property of node.getProperties()) {\n    if (property.isKind(SyntaxKind.PropertyAssignment)) {\n      const name = property.getName().replace(/\\'/g, \"\")\n      if (\n        property.getInitializer()?.isKind(SyntaxKind.ObjectLiteralExpression)\n      ) {\n        result[name] = parseObjectLiteralExpression(\n          property.getInitializer() as ObjectLiteralExpression\n        )\n      } else if (\n        property.getInitializer()?.isKind(SyntaxKind.ArrayLiteralExpression)\n      ) {\n        result[name] = parseArrayLiteralExpression(\n          property.getInitializer() as ArrayLiteralExpression\n        )\n      } else {\n        result[name] = parseValue(property.getInitializer())\n      }\n    }\n  }\n  return result\n}\n\nfunction parseArrayLiteralExpression(node: ArrayLiteralExpression): any[] {\n  const result: any[] = []\n  for (const element of node.getElements()) {\n    if (element.isKind(SyntaxKind.ObjectLiteralExpression)) {\n      result.push(\n        parseObjectLiteralExpression(\n          element.asKindOrThrow(SyntaxKind.ObjectLiteralExpression)\n        )\n      )\n    } else if (element.isKind(SyntaxKind.ArrayLiteralExpression)) {\n      result.push(\n        parseArrayLiteralExpression(\n          element.asKindOrThrow(SyntaxKind.ArrayLiteralExpression)\n        )\n      )\n    } else {\n      result.push(parseValue(element))\n    }\n  }\n  return result\n}\n\nfunction parseValue(node: any): any {\n  switch (node.getKind()) {\n    case SyntaxKind.StringLiteral:\n      return node.getText()\n    case SyntaxKind.NumericLiteral:\n      return Number(node.getText())\n    case SyntaxKind.TrueKeyword:\n      return true\n    case SyntaxKind.FalseKeyword:\n      return false\n    case SyntaxKind.NullKeyword:\n      return null\n    case SyntaxKind.ArrayLiteralExpression:\n      return node.getElements().map(parseValue)\n    case SyntaxKind.ObjectLiteralExpression:\n      return parseObjectLiteralExpression(node)\n    default:\n      return node.getText()\n  }\n}\n\nexport function buildTailwindThemeColorsFromCssVars(\n  cssVars: z.infer<typeof registryItemCssVarsSchema>\n) {\n  const result: Record<string, any> = {}\n\n  for (const key of Object.keys(cssVars)) {\n    const parts = key.split(\"-\")\n    const colorName = parts[0]\n    const subType = parts.slice(1).join(\"-\")\n\n    if (subType === \"\") {\n      if (typeof result[colorName] === \"object\") {\n        result[colorName].DEFAULT = `hsl(var(--${key}))`\n      } else {\n        result[colorName] = `hsl(var(--${key}))`\n      }\n    } else {\n      if (typeof result[colorName] !== \"object\") {\n        result[colorName] = { DEFAULT: `hsl(var(--${colorName}))` }\n      }\n      result[colorName][subType] = `hsl(var(--${key}))`\n    }\n  }\n\n  // Remove DEFAULT if it's not in the original cssVars\n  for (const [colorName, value] of Object.entries(result)) {\n    if (\n      typeof value === \"object\" &&\n      value.DEFAULT === `hsl(var(--${colorName}))` &&\n      !(colorName in cssVars)\n    ) {\n      delete value.DEFAULT\n    }\n  }\n\n  return result\n}\n", "import { createHash } from \"crypto\"\nimport path from \"path\"\nimport {\n  getRegistryBaseColor,\n  getShadcnRegistryIndex,\n} from \"@/src/registry/api\"\nimport {\n  buildUrlAndHeadersForRegistryItem,\n  resolveRegistryUrl,\n} from \"@/src/registry/builder\"\nimport { setRegistryHeaders } from \"@/src/registry/context\"\nimport {\n  RegistryNotConfiguredError,\n  RegistryParseError,\n} from \"@/src/registry/errors\"\nimport { fetchRegistry, fetchRegistryLocal } from \"@/src/registry/fetcher\"\nimport { parseRegistryAndItemFromString } from \"@/src/registry/parser\"\nimport {\n  deduplicateFilesByTarget,\n  isLocalFile,\n  isUrl,\n} from \"@/src/registry/utils\"\nimport {\n  registryItemSchema,\n  registryResolvedItemsTreeSchema,\n} from \"@/src/schema\"\nimport { Config, getTargetStyleFromConfig } from \"@/src/utils/get-config\"\nimport { getProjectTailwindVersionFromConfig } from \"@/src/utils/get-project-info\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { buildTailwindThemeColorsFromCssVars } from \"@/src/utils/updaters/update-tailwind-config\"\nimport deepmerge from \"deepmerge\"\nimport { z } from \"zod\"\n\nexport function resolveRegistryItemsFromRegistries(\n  items: string[],\n  config: Config\n) {\n  const registryHeaders: Record<string, Record<string, string>> = {}\n  const resolvedItems = [...items]\n\n  if (!config?.registries) {\n    setRegistryHeaders({})\n    return resolvedItems\n  }\n\n  for (let i = 0; i < resolvedItems.length; i++) {\n    const resolved = buildUrlAndHeadersForRegistryItem(resolvedItems[i], config)\n\n    if (resolved) {\n      resolvedItems[i] = resolved.url\n\n      if (Object.keys(resolved.headers).length > 0) {\n        registryHeaders[resolved.url] = resolved.headers\n      }\n    }\n  }\n\n  setRegistryHeaders(registryHeaders)\n\n  return resolvedItems\n}\n\n// Internal function that fetches registry items without clearing context.\n// This is used for recursive dependency resolution.\nexport async function fetchRegistryItems(\n  items: string[],\n  config: Config,\n  options: { useCache?: boolean } = {}\n) {\n  const results = await Promise.all(\n    items.map(async (item) => {\n      if (isLocalFile(item)) {\n        return fetchRegistryLocal(item)\n      }\n\n      if (isUrl(item)) {\n        const [result] = await fetchRegistry([item], options)\n        try {\n          return registryItemSchema.parse(result)\n        } catch (error) {\n          throw new RegistryParseError(item, error)\n        }\n      }\n\n      if (item.startsWith(\"@\") && config?.registries) {\n        const paths = resolveRegistryItemsFromRegistries([item], config)\n        const [result] = await fetchRegistry(paths, options)\n        try {\n          return registryItemSchema.parse(result)\n        } catch (error) {\n          throw new RegistryParseError(item, error)\n        }\n      }\n\n      const path = `styles/${config?.style ?? \"new-york-v4\"}/${item}.json`\n      const [result] = await fetchRegistry([path], options)\n      try {\n        return registryItemSchema.parse(result)\n      } catch (error) {\n        throw new RegistryParseError(item, error)\n      }\n    })\n  )\n\n  return results\n}\n\n// Helper schema for items with source tracking\nconst registryItemWithSourceSchema = registryItemSchema.extend({\n  _source: z.string().optional(),\n})\n\n// Resolves a list of registry items with all their dependencies and returns\n// a complete installation bundle with merged configuration.\nexport async function resolveRegistryTree(\n  names: z.infer<typeof registryItemSchema>[\"name\"][],\n  config: Config,\n  options: { useCache?: boolean } = {}\n) {\n  options = {\n    useCache: true,\n    ...options,\n  }\n\n  let payload: z.infer<typeof registryItemWithSourceSchema>[] = []\n  let allDependencyItems: z.infer<typeof registryItemWithSourceSchema>[] = []\n  let allDependencyRegistryNames: string[] = []\n\n  const uniqueNames = Array.from(new Set(names))\n\n  const results = await fetchRegistryItems(uniqueNames, config, options)\n\n  const resultMap = new Map<string, z.infer<typeof registryItemSchema>>()\n  for (let i = 0; i < results.length; i++) {\n    if (results[i]) {\n      resultMap.set(uniqueNames[i], results[i])\n    }\n  }\n\n  for (const [sourceName, item] of Array.from(resultMap.entries())) {\n    // Add source tracking\n    const itemWithSource: z.infer<typeof registryItemWithSourceSchema> = {\n      ...item,\n      _source: sourceName,\n    }\n    payload.push(itemWithSource)\n\n    if (item.registryDependencies) {\n      // Resolve namespace syntax and set headers for dependencies\n      let resolvedDependencies = item.registryDependencies\n\n      // Check for namespaced dependencies when no registries are configured\n      if (!config?.registries) {\n        const namespacedDeps = item.registryDependencies.filter((dep: string) =>\n          dep.startsWith(\"@\")\n        )\n        if (namespacedDeps.length > 0) {\n          const { registry } = parseRegistryAndItemFromString(namespacedDeps[0])\n          throw new RegistryNotConfiguredError(registry)\n        }\n      } else {\n        resolvedDependencies = resolveRegistryItemsFromRegistries(\n          item.registryDependencies,\n          config\n        )\n      }\n\n      const { items, registryNames } = await resolveDependenciesRecursively(\n        resolvedDependencies,\n        config,\n        options,\n        new Set(uniqueNames)\n      )\n      allDependencyItems.push(...items)\n      allDependencyRegistryNames.push(...registryNames)\n    }\n  }\n\n  payload.push(...allDependencyItems)\n\n  // Handle any remaining registry names that need index resolution\n  if (allDependencyRegistryNames.length > 0) {\n    // Remove duplicates from registry names\n    const uniqueRegistryNames = Array.from(new Set(allDependencyRegistryNames))\n\n    // Separate namespaced and non-namespaced items\n    const nonNamespacedItems = uniqueRegistryNames.filter(\n      (name) => !name.startsWith(\"@\")\n    )\n    const namespacedDepItems = uniqueRegistryNames.filter((name) =>\n      name.startsWith(\"@\")\n    )\n\n    // Handle namespaced dependency items\n    if (namespacedDepItems.length > 0) {\n      // This will now throw specific errors on failure\n      const depResults = await fetchRegistryItems(\n        namespacedDepItems,\n        config,\n        options\n      )\n\n      for (let i = 0; i < depResults.length; i++) {\n        const item = depResults[i]\n        const itemWithSource: z.infer<typeof registryItemWithSourceSchema> = {\n          ...item,\n          _source: namespacedDepItems[i],\n        }\n        payload.push(itemWithSource)\n      }\n    }\n\n    // For non-namespaced items, we need the index and style resolution\n    if (nonNamespacedItems.length > 0) {\n      const index = await getShadcnRegistryIndex()\n      if (!index && payload.length === 0) {\n        return null\n      }\n\n      if (index) {\n        // If we're resolving the index, we want it to go first\n        if (nonNamespacedItems.includes(\"index\")) {\n          nonNamespacedItems.unshift(\"index\")\n        }\n\n        // Resolve non-namespaced items through the existing flow\n        // Get URLs for all registry items including their dependencies\n        const registryUrls: string[] = []\n        for (const name of nonNamespacedItems) {\n          const itemDependencies = await resolveRegistryDependencies(\n            name,\n            config,\n            options\n          )\n          registryUrls.push(...itemDependencies)\n        }\n\n        // Deduplicate URLs\n        const uniqueUrls = Array.from(new Set(registryUrls))\n        let result = await fetchRegistry(uniqueUrls, options)\n        const registryPayload = z.array(registryItemSchema).parse(result)\n        payload.push(...registryPayload)\n      }\n    }\n  }\n\n  if (!payload.length) {\n    return null\n  }\n\n  // No deduplication - we want to support multiple items with the same name from different sources\n\n  // If we're resolving the index, we want to fetch\n  // the theme item if a base color is provided.\n  // We do this for index only.\n  // Other components will ship with their theme tokens.\n  if (\n    uniqueNames.includes(\"index\") ||\n    allDependencyRegistryNames.includes(\"index\")\n  ) {\n    if (config.tailwind.baseColor) {\n      const theme = await registryGetTheme(config.tailwind.baseColor, config)\n      if (theme) {\n        payload.unshift(theme)\n      }\n    }\n  }\n\n  // Build source map for topological sort\n  const sourceMap = new Map<z.infer<typeof registryItemSchema>, string>()\n  payload.forEach((item) => {\n    // Use the _source property if it was added, otherwise use the name\n    const source = item._source || item.name\n    sourceMap.set(item, source)\n  })\n\n  // Apply topological sort to ensure dependencies come before dependents\n  payload = topologicalSortRegistryItems(payload, sourceMap)\n\n  // Sort the payload so that registry:theme items come first,\n  // while maintaining the relative order of all items.\n  payload.sort((a, b) => {\n    if (a.type === \"registry:theme\" && b.type !== \"registry:theme\") {\n      return -1\n    }\n    if (a.type !== \"registry:theme\" && b.type === \"registry:theme\") {\n      return 1\n    }\n    return 0\n  })\n\n  let tailwind = {}\n  payload.forEach((item) => {\n    tailwind = deepmerge(tailwind, item.tailwind ?? {})\n  })\n\n  let cssVars = {}\n  payload.forEach((item) => {\n    cssVars = deepmerge(cssVars, item.cssVars ?? {})\n  })\n\n  let css = {}\n  payload.forEach((item) => {\n    css = deepmerge(css, item.css ?? {})\n  })\n\n  let docs = \"\"\n  payload.forEach((item) => {\n    if (item.docs) {\n      docs += `${item.docs}\\n`\n    }\n  })\n\n  let envVars = {}\n  payload.forEach((item) => {\n    envVars = deepmerge(envVars, item.envVars ?? {})\n  })\n\n  // Deduplicate files based on resolved target paths.\n  const deduplicatedFiles = await deduplicateFilesByTarget(\n    payload.map((item) => item.files ?? []),\n    config\n  )\n\n  const parsed = registryResolvedItemsTreeSchema.parse({\n    dependencies: deepmerge.all(payload.map((item) => item.dependencies ?? [])),\n    devDependencies: deepmerge.all(\n      payload.map((item) => item.devDependencies ?? [])\n    ),\n    files: deduplicatedFiles,\n    tailwind,\n    cssVars,\n    css,\n    docs,\n  })\n\n  if (Object.keys(envVars).length > 0) {\n    parsed.envVars = envVars\n  }\n\n  return parsed\n}\n\nasync function resolveDependenciesRecursively(\n  dependencies: string[],\n  config: Config,\n  options: { useCache?: boolean } = {},\n  visited: Set<string> = new Set()\n) {\n  const items: z.infer<typeof registryItemSchema>[] = []\n  const registryNames: string[] = []\n\n  for (const dep of dependencies) {\n    if (visited.has(dep)) {\n      continue\n    }\n    visited.add(dep)\n\n    // Handle URLs and local files directly.\n    if (isUrl(dep) || isLocalFile(dep)) {\n      const [item] = await fetchRegistryItems([dep], config, options)\n      if (item) {\n        items.push(item)\n        if (item.registryDependencies) {\n          // Resolve namespaced dependencies to set proper headers.\n          const resolvedDeps = config?.registries\n            ? resolveRegistryItemsFromRegistries(\n                item.registryDependencies,\n                config\n              )\n            : item.registryDependencies\n\n          const nested = await resolveDependenciesRecursively(\n            resolvedDeps,\n            config,\n            options,\n            visited\n          )\n          items.push(...nested.items)\n          registryNames.push(...nested.registryNames)\n        }\n      }\n    }\n    // Handle namespaced items (e.g., @one/foo, @two/bar).\n    else if (dep.startsWith(\"@\") && config?.registries) {\n      // Check if the registry exists.\n      const { registry } = parseRegistryAndItemFromString(dep)\n      if (registry && !(registry in config.registries)) {\n        throw new RegistryNotConfiguredError(registry)\n      }\n\n      // Let getRegistryItem handle the namespaced item with config\n      // This ensures proper authentication headers are used\n      const [item] = await fetchRegistryItems([dep], config, options)\n      if (item) {\n        items.push(item)\n        if (item.registryDependencies) {\n          // Resolve namespaced dependencies to set proper headers.\n          const resolvedDeps = config?.registries\n            ? resolveRegistryItemsFromRegistries(\n                item.registryDependencies,\n                config\n              )\n            : item.registryDependencies\n\n          const nested = await resolveDependenciesRecursively(\n            resolvedDeps,\n            config,\n            options,\n            visited\n          )\n          items.push(...nested.items)\n          registryNames.push(...nested.registryNames)\n        }\n      }\n    }\n    // Handle regular component names.\n    else {\n      registryNames.push(dep)\n\n      if (config) {\n        try {\n          const [item] = await fetchRegistryItems([dep], config, options)\n          if (item && item.registryDependencies) {\n            // Resolve namespaced dependencies to set proper headers.\n            const resolvedDeps = config?.registries\n              ? resolveRegistryItemsFromRegistries(\n                  item.registryDependencies,\n                  config\n                )\n              : item.registryDependencies\n\n            const nested = await resolveDependenciesRecursively(\n              resolvedDeps,\n              config,\n              options,\n              visited\n            )\n            items.push(...nested.items)\n            registryNames.push(...nested.registryNames)\n          }\n        } catch (error) {\n          // If we can't fetch the registry item, that's okay - we'll still\n          // include the name.\n        }\n      }\n    }\n  }\n\n  return { items, registryNames }\n}\n\nasync function resolveRegistryDependencies(\n  url: string,\n  config: Config,\n  options: { useCache?: boolean } = {}\n) {\n  if (isUrl(url)) {\n    return [url]\n  }\n\n  const { registryNames } = await resolveDependenciesRecursively(\n    [url],\n    config,\n    options,\n    new Set()\n  )\n\n  const style = config.resolvedPaths?.cwd\n    ? await getTargetStyleFromConfig(config.resolvedPaths.cwd, config.style)\n    : config.style\n\n  const urls = registryNames.map((name) =>\n    resolveRegistryUrl(isUrl(name) ? name : `styles/${style}/${name}.json`)\n  )\n\n  return Array.from(new Set(urls))\n}\n\nasync function registryGetTheme(name: string, config: Config) {\n  const [baseColor, tailwindVersion] = await Promise.all([\n    getRegistryBaseColor(name),\n    getProjectTailwindVersionFromConfig(config),\n  ])\n  if (!baseColor) {\n    return null\n  }\n\n  // TODO: Move this to the registry i.e registry:theme.\n  const theme = {\n    name,\n    type: \"registry:theme\",\n    tailwind: {\n      config: {\n        theme: {\n          extend: {\n            borderRadius: {\n              lg: \"var(--radius)\",\n              md: \"calc(var(--radius) - 2px)\",\n              sm: \"calc(var(--radius) - 4px)\",\n            },\n            colors: {},\n          },\n        },\n      },\n    },\n    cssVars: {\n      theme: {},\n      light: {\n        radius: \"0.5rem\",\n      },\n      dark: {},\n    },\n  } satisfies z.infer<typeof registryItemSchema>\n\n  if (config.tailwind.cssVariables) {\n    theme.tailwind.config.theme.extend.colors = {\n      ...theme.tailwind.config.theme.extend.colors,\n      ...buildTailwindThemeColorsFromCssVars(baseColor.cssVars.dark ?? {}),\n    }\n    theme.cssVars = {\n      theme: {\n        ...baseColor.cssVars.theme,\n        ...theme.cssVars.theme,\n      },\n      light: {\n        ...baseColor.cssVars.light,\n        ...theme.cssVars.light,\n      },\n      dark: {\n        ...baseColor.cssVars.dark,\n        ...theme.cssVars.dark,\n      },\n    }\n\n    if (tailwindVersion === \"v4\" && baseColor.cssVarsV4) {\n      theme.cssVars = {\n        theme: {\n          ...baseColor.cssVarsV4.theme,\n          ...theme.cssVars.theme,\n        },\n        light: {\n          radius: \"0.625rem\",\n          ...baseColor.cssVarsV4.light,\n        },\n        dark: {\n          ...baseColor.cssVarsV4.dark,\n        },\n      }\n    }\n  }\n\n  return theme\n}\n\nfunction computeItemHash(\n  item: Pick<z.infer<typeof registryItemSchema>, \"name\">,\n  source?: string\n) {\n  const identifier = source || item.name\n\n  const hash = createHash(\"sha256\")\n    .update(identifier)\n    .digest(\"hex\")\n    .substring(0, 8)\n\n  return `${item.name}::${hash}`\n}\n\nfunction extractItemIdentifierFromDependency(dependency: string) {\n  if (isUrl(dependency)) {\n    const url = new URL(dependency)\n    const pathname = url.pathname\n    const match = pathname.match(/\\/([^/]+)\\.json$/)\n    const name = match ? match[1] : path.basename(pathname, \".json\")\n\n    return {\n      name,\n      hash: computeItemHash({ name }, dependency),\n    }\n  }\n\n  if (isLocalFile(dependency)) {\n    const match = dependency.match(/\\/([^/]+)\\.json$/)\n    const name = match ? match[1] : path.basename(dependency, \".json\")\n\n    return {\n      name,\n      hash: computeItemHash({ name }, dependency),\n    }\n  }\n\n  const { item } = parseRegistryAndItemFromString(dependency)\n  return {\n    name: item,\n    hash: computeItemHash({ name: item }, dependency),\n  }\n}\n\nfunction topologicalSortRegistryItems(\n  items: z.infer<typeof registryItemSchema>[],\n  sourceMap: Map<z.infer<typeof registryItemSchema>, string>\n) {\n  const itemMap = new Map<string, z.infer<typeof registryItemSchema>>()\n  const hashToItem = new Map<string, z.infer<typeof registryItemSchema>>()\n  const inDegree = new Map<string, number>()\n  const adjacencyList = new Map<string, string[]>()\n\n  items.forEach((item) => {\n    const source = sourceMap.get(item) || item.name\n    const hash = computeItemHash(item, source)\n\n    itemMap.set(hash, item)\n    hashToItem.set(hash, item)\n    inDegree.set(hash, 0)\n    adjacencyList.set(hash, [])\n  })\n\n  // Build a map of dependency to possible items.\n  const depToHashes = new Map<string, string[]>()\n  items.forEach((item) => {\n    const source = sourceMap.get(item) || item.name\n    const hash = computeItemHash(item, source)\n\n    if (!depToHashes.has(item.name)) {\n      depToHashes.set(item.name, [])\n    }\n    depToHashes.get(item.name)!.push(hash)\n\n    if (source !== item.name) {\n      if (!depToHashes.has(source)) {\n        depToHashes.set(source, [])\n      }\n      depToHashes.get(source)!.push(hash)\n    }\n  })\n\n  items.forEach((item) => {\n    const itemSource = sourceMap.get(item) || item.name\n    const itemHash = computeItemHash(item, itemSource)\n\n    if (item.registryDependencies) {\n      item.registryDependencies.forEach((dep) => {\n        let depHash: string | undefined\n\n        const exactMatches = depToHashes.get(dep) || []\n        if (exactMatches.length === 1) {\n          depHash = exactMatches[0]\n        } else if (exactMatches.length > 1) {\n          // Multiple matches - try to disambiguate.\n          // For now, just use the first one and warn.\n          depHash = exactMatches[0]\n        } else {\n          const { name } = extractItemIdentifierFromDependency(dep)\n          const nameMatches = depToHashes.get(name) || []\n          if (nameMatches.length > 0) {\n            depHash = nameMatches[0]\n          }\n        }\n\n        if (depHash && itemMap.has(depHash)) {\n          adjacencyList.get(depHash)!.push(itemHash)\n          inDegree.set(itemHash, inDegree.get(itemHash)! + 1)\n        }\n      })\n    }\n  })\n\n  // Implements Kahn's algorithm.\n  const queue: string[] = []\n  const sorted: z.infer<typeof registryItemSchema>[] = []\n\n  inDegree.forEach((degree, hash) => {\n    if (degree === 0) {\n      queue.push(hash)\n    }\n  })\n\n  while (queue.length > 0) {\n    const currentHash = queue.shift()!\n    const item = itemMap.get(currentHash)!\n    sorted.push(item)\n\n    adjacencyList.get(currentHash)!.forEach((dependentHash) => {\n      const newDegree = inDegree.get(dependentHash)! - 1\n      inDegree.set(dependentHash, newDegree)\n\n      if (newDegree === 0) {\n        queue.push(dependentHash)\n      }\n    })\n  }\n\n  if (sorted.length !== items.length) {\n    console.warn(\"Circular dependency detected in registry items\")\n    // Return all items even if there are circular dependencies\n    // Items not in sorted are part of circular dependencies\n    const sortedHashes = new Set(\n      sorted.map((item) => {\n        const source = sourceMap.get(item) || item.name\n        return computeItemHash(item, source)\n      })\n    )\n\n    items.forEach((item) => {\n      const source = sourceMap.get(item) || item.name\n      const hash = computeItemHash(item, source)\n      if (!sortedHashes.has(hash)) {\n        sorted.push(item)\n      }\n    })\n  }\n\n  return sorted\n}\n", "import { RegistryError } from \"@/src/registry/errors\"\nimport { highlighter } from \"@/src/utils/highlighter\"\nimport { logger } from \"@/src/utils/logger\"\nimport { z } from \"zod\"\n\nexport function handleError(error: unknown) {\n  logger.break()\n  logger.error(\n    `Something went wrong. Please check the error below for more details.`\n  )\n  logger.error(`If the problem persists, please open an issue on GitHub.`)\n  logger.error(\"\")\n  if (typeof error === \"string\") {\n    logger.error(error)\n    logger.break()\n    process.exit(1)\n  }\n\n  if (error instanceof RegistryError) {\n    if (error.message) {\n      logger.error(error.cause ? \"Error:\" : \"Message:\")\n      logger.error(error.message)\n    }\n\n    if (error.cause) {\n      logger.error(\"\\nMessage:\")\n      logger.error(error.cause)\n    }\n\n    if (error.suggestion) {\n      logger.error(\"\\nSuggestion:\")\n      logger.error(error.suggestion)\n    }\n    logger.break()\n    process.exit(1)\n  }\n\n  if (error instanceof z.ZodError) {\n    logger.error(\"Validation failed:\")\n    for (const [key, value] of Object.entries(error.flatten().fieldErrors)) {\n      logger.error(`- ${highlighter.info(key)}: ${value}`)\n    }\n    logger.break()\n    process.exit(1)\n  }\n\n  if (error instanceof Error) {\n    logger.error(error.message)\n    logger.break()\n    process.exit(1)\n  }\n\n  logger.break()\n  process.exit(1)\n}\n", "import path from \"path\"\nimport { buildUrlAndHeadersForRegistryItem } from \"@/src/registry/builder\"\nimport { configWithDefaults } from \"@/src/registry/config\"\nimport {\n  BASE_COLORS,\n  BUILTIN_REGISTRIES,\n  REGISTRY_URL,\n} from \"@/src/registry/constants\"\nimport {\n  clearRegistryContext,\n  setRegistryHeaders,\n} from \"@/src/registry/context\"\nimport {\n  ConfigParseError,\n  RegistriesIndexParseError,\n  RegistryInvalidNamespaceError,\n  RegistryNotFoundError,\n  RegistryParseError,\n} from \"@/src/registry/errors\"\nimport { fetchRegistry } from \"@/src/registry/fetcher\"\nimport {\n  fetchRegistryItems,\n  resolveRegistryTree,\n} from \"@/src/registry/resolver\"\nimport { isUrl } from \"@/src/registry/utils\"\nimport {\n  iconsSchema,\n  registriesIndexSchema,\n  registryBaseColorSchema,\n  registryConfigSchema,\n  registryIndexSchema,\n  registryItemSchema,\n  registrySchema,\n  stylesSchema,\n} from \"@/src/schema\"\nimport { Config, explorer } from \"@/src/utils/get-config\"\nimport { handleError } from \"@/src/utils/handle-error\"\nimport { logger } from \"@/src/utils/logger\"\nimport { z } from \"zod\"\n\nexport async function getRegistry(\n  name: string,\n  options?: {\n    config?: Partial<Config>\n    useCache?: boolean\n  }\n) {\n  const { config, useCache } = options || {}\n\n  if (isUrl(name)) {\n    const [result] = await fetchRegistry([name], { useCache })\n    try {\n      return registrySchema.parse(result)\n    } catch (error) {\n      throw new RegistryParseError(name, error)\n    }\n  }\n\n  if (!name.startsWith(\"@\")) {\n    throw new RegistryInvalidNamespaceError(name)\n  }\n\n  let registryName = name\n  if (!registryName.endsWith(\"/registry\")) {\n    registryName = `${registryName}/registry`\n  }\n\n  const urlAndHeaders = buildUrlAndHeadersForRegistryItem(\n    registryName as `@${string}`,\n    configWithDefaults(config)\n  )\n\n  if (!urlAndHeaders?.url) {\n    throw new RegistryNotFoundError(registryName)\n  }\n\n  if (urlAndHeaders.headers && Object.keys(urlAndHeaders.headers).length > 0) {\n    setRegistryHeaders({\n      [urlAndHeaders.url]: urlAndHeaders.headers,\n    })\n  }\n\n  const [result] = await fetchRegistry([urlAndHeaders.url], { useCache })\n\n  try {\n    return registrySchema.parse(result)\n  } catch (error) {\n    throw new RegistryParseError(registryName, error)\n  }\n}\n\nexport async function getRegistryItems(\n  items: string[],\n  options?: {\n    config?: Partial<Config>\n    useCache?: boolean\n  }\n) {\n  const { config, useCache = false } = options || {}\n\n  clearRegistryContext()\n\n  return fetchRegistryItems(items, configWithDefaults(config), { useCache })\n}\n\nexport async function resolveRegistryItems(\n  items: string[],\n  options?: {\n    config?: Partial<Config>\n    useCache?: boolean\n  }\n) {\n  const { config, useCache = false } = options || {}\n\n  clearRegistryContext()\n  return resolveRegistryTree(items, configWithDefaults(config), { useCache })\n}\n\nexport async function getRegistriesConfig(\n  cwd: string,\n  options?: { useCache?: boolean }\n) {\n  const { useCache = true } = options || {}\n\n  // Clear cache if requested\n  if (!useCache) {\n    explorer.clearCaches()\n  }\n\n  const configResult = await explorer.search(cwd)\n\n  if (!configResult) {\n    // Do not throw an error if the config is missing.\n    // We still have access to the built-in registries.\n    return {\n      registries: BUILTIN_REGISTRIES,\n    }\n  }\n\n  // Parse just the registries field from the config\n  const registriesConfig = z\n    .object({\n      registries: registryConfigSchema.optional(),\n    })\n    .safeParse(configResult.config)\n\n  if (!registriesConfig.success) {\n    throw new ConfigParseError(cwd, registriesConfig.error)\n  }\n\n  // Merge built-in registries with user registries\n  return {\n    registries: {\n      ...BUILTIN_REGISTRIES,\n      ...(registriesConfig.data.registries || {}),\n    },\n  }\n}\n\nexport async function getShadcnRegistryIndex() {\n  try {\n    const [result] = await fetchRegistry([\"index.json\"])\n\n    return registryIndexSchema.parse(result)\n  } catch (error) {\n    logger.error(\"\\n\")\n    handleError(error)\n  }\n}\n\nexport async function getRegistryStyles() {\n  try {\n    const [result] = await fetchRegistry([\"styles/index.json\"])\n\n    return stylesSchema.parse(result)\n  } catch (error) {\n    logger.error(\"\\n\")\n    handleError(error)\n    return []\n  }\n}\n\nexport async function getRegistryIcons() {\n  try {\n    const [result] = await fetchRegistry([\"icons/index.json\"])\n    return iconsSchema.parse(result)\n  } catch (error) {\n    handleError(error)\n    return {}\n  }\n}\n\nexport async function getRegistryBaseColors() {\n  return BASE_COLORS\n}\n\nexport async function getRegistryBaseColor(baseColor: string) {\n  try {\n    const [result] = await fetchRegistry([`colors/${baseColor}.json`])\n\n    return registryBaseColorSchema.parse(result)\n  } catch (error) {\n    handleError(error)\n  }\n}\n\n/**\n * @deprecated This function is deprecated and will be removed in a future version.\n */\nexport async function resolveTree(\n  index: z.infer<typeof registryIndexSchema>,\n  names: string[]\n) {\n  const tree: z.infer<typeof registryIndexSchema> = []\n\n  for (const name of names) {\n    const entry = index.find((entry) => entry.name === name)\n\n    if (!entry) {\n      continue\n    }\n\n    tree.push(entry)\n\n    if (entry.registryDependencies) {\n      const dependencies = await resolveTree(index, entry.registryDependencies)\n      tree.push(...dependencies)\n    }\n  }\n\n  return tree.filter(\n    (component, index, self) =>\n      self.findIndex((c) => c.name === component.name) === index\n  )\n}\n\n/**\n * @deprecated This function is deprecated and will be removed in a future version.\n */\nexport async function fetchTree(\n  style: string,\n  tree: z.infer<typeof registryIndexSchema>\n) {\n  try {\n    const paths = tree.map((item) => `styles/${style}/${item.name}.json`)\n    const results = await fetchRegistry(paths)\n    return results.map((result) => registryItemSchema.parse(result))\n  } catch (error) {\n    handleError(error)\n    return []\n  }\n}\n\n/**\n * @deprecated This function is deprecated and will be removed in a future version.\n */\nexport async function getItemTargetPath(\n  config: Config,\n  item: Pick<z.infer<typeof registryItemSchema>, \"type\">,\n  override?: string\n) {\n  if (override) {\n    return override\n  }\n\n  if (item.type === \"registry:ui\") {\n    return config.resolvedPaths.ui ?? config.resolvedPaths.components\n  }\n\n  const [parent, type] = item.type?.split(\":\") ?? []\n  if (!(parent in config.resolvedPaths)) {\n    return null\n  }\n\n  return path.join(\n    config.resolvedPaths[parent as keyof typeof config.resolvedPaths],\n    type\n  )\n}\n\nexport async function getRegistriesIndex(options?: { useCache?: boolean }) {\n  options = {\n    useCache: true,\n    ...options,\n  }\n\n  const url = `${REGISTRY_URL}/registries.json`\n  const [data] = await fetchRegistry([url], {\n    useCache: options.useCache,\n  })\n\n  try {\n    return registriesIndexSchema.parse(data)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      throw new RegistriesIndexParseError(error)\n    }\n\n    throw error\n  }\n}\n", "import { searchResultItemSchema, searchResultsSchema } from \"@/src/schema\"\nimport { Config } from \"@/src/utils/get-config\"\nimport fuzzysort from \"fuzzysort\"\nimport { z } from \"zod\"\n\nimport { getRegistry } from \"./api\"\n\nexport async function searchRegistries(\n  registries: string[],\n  options?: {\n    query?: string\n    limit?: number\n    offset?: number\n    config?: Partial<Config>\n    useCache?: boolean\n  }\n) {\n  const { query, limit, offset, config, useCache } = options || {}\n\n  let allItems: z.infer<typeof searchResultItemSchema>[] = []\n\n  for (const registry of registries) {\n    const registryData = await getRegistry(registry, { config, useCache })\n\n    const itemsWithRegistry = (registryData.items || []).map((item) => ({\n      name: item.name,\n      type: item.type,\n      description: item.description,\n      registry: registry,\n      addCommandArgument: buildRegistryItemNameFromRegistry(\n        item.name,\n        registry\n      ),\n    }))\n\n    allItems = allItems.concat(itemsWithRegistry)\n  }\n\n  if (query) {\n    allItems = searchItems(allItems, {\n      query,\n      limit: allItems.length,\n      keys: [\"name\", \"description\"],\n    }) as z.infer<typeof searchResultItemSchema>[]\n  }\n\n  const paginationOffset = offset || 0\n  const paginationLimit = limit || allItems.length\n  const totalItems = allItems.length\n\n  const result: z.infer<typeof searchResultsSchema> = {\n    pagination: {\n      total: totalItems,\n      offset: paginationOffset,\n      limit: paginationLimit,\n      hasMore: paginationOffset + paginationLimit < totalItems,\n    },\n    items: allItems.slice(paginationOffset, paginationOffset + paginationLimit),\n  }\n\n  return searchResultsSchema.parse(result)\n}\n\nconst searchableItemSchema = z\n  .object({\n    name: z.string(),\n    type: z.string().optional(),\n    description: z.string().optional(),\n    registry: z.string().optional(),\n    addCommandArgument: z.string().optional(),\n  })\n  .passthrough()\n\ntype SearchableItem = z.infer<typeof searchableItemSchema>\n\nfunction searchItems<\n  T extends {\n    name: string\n    type?: string\n    description?: string\n    addCommandArgument?: string\n    [key: string]: any\n  } = SearchableItem\n>(\n  items: T[],\n  options: {\n    query: string\n  } & Pick<Parameters<typeof fuzzysort.go>[2], \"keys\" | \"threshold\" | \"limit\">\n) {\n  options = {\n    limit: 100,\n    threshold: -10000,\n    ...options,\n  }\n\n  const searchResults = fuzzysort.go(options.query, items, {\n    keys: options.keys,\n    threshold: options.threshold,\n    limit: options.limit,\n  })\n\n  const results = searchResults.map((result) => result.obj)\n\n  return z.array(searchableItemSchema).parse(results)\n}\n\nfunction isUrl(string: string): boolean {\n  try {\n    new URL(string)\n    return true\n  } catch {\n    return false\n  }\n}\n\n// Builds the registry item name for the add command.\n// For namespaced registries, returns \"registry/item\".\n// For URL registries, replaces \"registry\" with the item name in the URL.\nexport function buildRegistryItemNameFromRegistry(\n  name: string,\n  registry: string\n) {\n  // If registry is not a URL, return namespace format.\n  if (!isUrl(registry)) {\n    return `${registry}/${name}`\n  }\n\n  // Find where the host part ends in the original string.\n  const protocolEnd = registry.indexOf(\"://\") + 3\n  const hostEnd = registry.indexOf(\"/\", protocolEnd)\n\n  if (hostEnd === -1) {\n    // No path, check for query params.\n    const queryStart = registry.indexOf(\"?\", protocolEnd)\n    if (queryStart !== -1) {\n      // Has query params but no path.\n      const beforeQuery = registry.substring(0, queryStart)\n      const queryAndAfter = registry.substring(queryStart)\n      // Replace \"registry\" with itemName in query params only.\n      const updatedQuery = queryAndAfter.replace(/\\bregistry\\b/g, name)\n      return beforeQuery + updatedQuery\n    }\n    // No path or query, return as is.\n    return registry\n  }\n\n  // Split at host boundary.\n  const hostPart = registry.substring(0, hostEnd)\n  const pathAndQuery = registry.substring(hostEnd)\n\n  // Find all occurrences of \"registry\" in path and query.\n  // Replace only the last occurrence in the path segment.\n  const pathEnd =\n    pathAndQuery.indexOf(\"?\") !== -1\n      ? pathAndQuery.indexOf(\"?\")\n      : pathAndQuery.length\n  const pathOnly = pathAndQuery.substring(0, pathEnd)\n  const queryAndAfter = pathAndQuery.substring(pathEnd)\n\n  // Replace the last occurrence of \"registry\" in the path.\n  const lastIndex = pathOnly.lastIndexOf(\"registry\")\n  let updatedPath = pathOnly\n  if (lastIndex !== -1) {\n    updatedPath =\n      pathOnly.substring(0, lastIndex) +\n      name +\n      pathOnly.substring(lastIndex + \"registry\".length)\n  }\n\n  // Replace all occurrences of \"registry\" in query params.\n  const updatedQuery = queryAndAfter.replace(/\\bregistry\\b/g, name)\n\n  return hostPart + updatedPath + updatedQuery\n}\n"]}