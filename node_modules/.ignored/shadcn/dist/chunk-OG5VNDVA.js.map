{"version": 3, "sources": ["../src/registry/schema.ts"], "names": ["registryItemTypeSchema", "z", "registryItemFileSchema", "registryItemTailwindSchema", "registryItemCssVarsSchema", "cssValueSchema", "registryItemCssSchema", "registryItemEnvVarsSchema", "registryItemSchema", "registrySchema", "registryIndexSchema", "stylesSchema", "iconsSchema", "registryBaseColorSchema", "registryResolvedItemsTreeSchema", "registryConfigItemSchema", "s", "registryConfigSchema", "key", "rawConfigSchema", "configSchema", "workspaceConfigSchema", "searchResultItemSchema", "searchResultsSchema", "registriesIndexSchema"], "mappings": "oBAKO,IAAMA,EAAyBC,CAAAA,CAAE,IAAA,CAAK,CAC3C,cAAA,CACA,gBAAA,CACA,qBACA,aAAA,CACA,eAAA,CACA,gBACA,eAAA,CACA,gBAAA,CACA,iBACA,eAAA,CAGA,kBAAA,CACA,mBACF,CAAC,CAAA,CAEYC,EAAyBD,CAAAA,CAAE,kBAAA,CAAmB,OAAQ,CAEjEA,CAAAA,CAAE,OAAO,CACP,IAAA,CAAMA,EAAE,MAAA,EAAO,CACf,QAASA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC7B,KAAMA,CAAAA,CAAE,IAAA,CAAK,CAAC,eAAA,CAAiB,eAAe,CAAC,CAAA,CAC/C,MAAA,CAAQA,EAAE,MAAA,EACZ,CAAC,CAAA,CACDA,CAAAA,CAAE,OAAO,CACP,IAAA,CAAMA,EAAE,MAAA,EAAO,CACf,QAASA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC7B,KAAMD,CAAAA,CAAuB,OAAA,CAAQ,CAAC,eAAA,CAAiB,eAAe,CAAC,CAAA,CACvE,MAAA,CAAQC,EAAE,MAAA,EAAO,CAAE,UACrB,CAAC,CACH,CAAC,CAAA,CAEYE,EAA6BF,CAAAA,CAAE,MAAA,CAAO,CACjD,MAAA,CAAQA,CAAAA,CACL,MAAA,CAAO,CACN,OAAA,CAASA,CAAAA,CAAE,MAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAAE,QAAA,GAC7B,KAAA,CAAOA,CAAAA,CAAE,OAAOA,CAAAA,CAAE,MAAA,GAAUA,CAAAA,CAAE,GAAA,EAAK,CAAA,CAAE,QAAA,GACrC,OAAA,CAASA,CAAAA,CAAE,MAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAAE,QAAA,EAC/B,CAAC,CAAA,CACA,UACL,CAAC,EAEYG,CAAAA,CAA4BH,CAAAA,CAAE,OAAO,CAChD,KAAA,CAAOA,EAAE,MAAA,CAAOA,CAAAA,CAAE,QAAO,CAAGA,CAAAA,CAAE,QAAQ,CAAA,CAAE,UAAS,CACjD,KAAA,CAAOA,EAAE,MAAA,CAAOA,CAAAA,CAAE,QAAO,CAAGA,CAAAA,CAAE,QAAQ,CAAA,CAAE,UAAS,CACjD,IAAA,CAAMA,EAAE,MAAA,CAAOA,CAAAA,CAAE,QAAO,CAAGA,CAAAA,CAAE,QAAQ,CAAA,CAAE,UACzC,CAAC,EAGKI,CAAAA,CAAiCJ,CAAAA,CAAE,KAAK,IAC5CA,CAAAA,CAAE,MAAM,CAACA,CAAAA,CAAE,QAAO,CAAGA,CAAAA,CAAE,OAAOA,CAAAA,CAAE,MAAA,GAAUI,CAAc,CAAC,CAAC,CAC5D,CAAA,CAEaC,CAAAA,CAAwBL,EAAE,MAAA,CAAOA,CAAAA,CAAE,QAAO,CAAGI,CAAc,EAE3DE,CAAAA,CAA4BN,CAAAA,CAAE,OAAOA,CAAAA,CAAE,MAAA,GAAUA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAE3DO,CAAAA,CAAqBP,EAAE,MAAA,CAAO,CACzC,QAASA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC7B,QAASA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC7B,KAAMA,CAAAA,CAAE,MAAA,GACR,IAAA,CAAMD,CAAAA,CACN,MAAOC,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC3B,OAAQA,CAAAA,CAAE,MAAA,GAAS,GAAA,CAAI,CAAC,EAAE,QAAA,EAAS,CACnC,YAAaA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CACjC,aAAcA,CAAAA,CAAE,KAAA,CAAMA,EAAE,MAAA,EAAQ,EAAE,QAAA,EAAS,CAC3C,gBAAiBA,CAAAA,CAAE,KAAA,CAAMA,EAAE,MAAA,EAAQ,EAAE,QAAA,EAAS,CAC9C,qBAAsBA,CAAAA,CAAE,KAAA,CAAMA,EAAE,MAAA,EAAQ,EAAE,QAAA,EAAS,CACnD,MAAOA,CAAAA,CAAE,KAAA,CAAMC,CAAsB,CAAA,CAAE,QAAA,EAAS,CAChD,QAAA,CAAUC,CAAAA,CAA2B,QAAA,GACrC,OAAA,CAASC,CAAAA,CAA0B,UAAS,CAC5C,GAAA,CAAKE,EAAsB,QAAA,EAAS,CACpC,QAASC,CAAAA,CAA0B,QAAA,GACnC,IAAA,CAAMN,CAAAA,CAAE,OAAOA,CAAAA,CAAE,MAAA,GAAUA,CAAAA,CAAE,GAAA,EAAK,CAAA,CAAE,QAAA,GACpC,IAAA,CAAMA,CAAAA,CAAE,QAAO,CAAE,QAAA,GACjB,UAAA,CAAYA,CAAAA,CAAE,MAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAAE,QAAA,EAClC,CAAC,CAAA,CAIYQ,EAAiBR,CAAAA,CAAE,MAAA,CAAO,CACrC,IAAA,CAAMA,CAAAA,CAAE,MAAA,GACR,QAAA,CAAUA,CAAAA,CAAE,QAAO,CACnB,KAAA,CAAOA,EAAE,KAAA,CAAMO,CAAkB,CACnC,CAAC,CAAA,CAIYE,EAAsBT,CAAAA,CAAE,KAAA,CAAMO,CAAkB,CAAA,CAEhDG,CAAAA,CAAeV,EAAE,KAAA,CAC5BA,CAAAA,CAAE,OAAO,CACP,IAAA,CAAMA,EAAE,MAAA,EAAO,CACf,MAAOA,CAAAA,CAAE,MAAA,EACX,CAAC,CACH,EAEaW,CAAAA,CAAcX,CAAAA,CAAE,OAC3BA,CAAAA,CAAE,MAAA,GACFA,CAAAA,CAAE,MAAA,CAAOA,EAAE,MAAA,EAAO,CAAGA,CAAAA,CAAE,MAAA,EAAQ,CACjC,EAEaY,CAAAA,CAA0BZ,CAAAA,CAAE,OAAO,CAC9C,YAAA,CAAcA,EAAE,MAAA,CAAO,CACrB,MAAOA,CAAAA,CAAE,MAAA,CAAOA,EAAE,MAAA,EAAO,CAAGA,EAAE,MAAA,EAAQ,EACtC,IAAA,CAAMA,CAAAA,CAAE,OAAOA,CAAAA,CAAE,MAAA,GAAUA,CAAAA,CAAE,MAAA,EAAQ,CACvC,CAAC,EACD,OAAA,CAASG,CAAAA,CACT,UAAWA,CAAAA,CAA0B,QAAA,GACrC,oBAAA,CAAsBH,CAAAA,CAAE,QAAO,CAC/B,eAAA,CAAiBA,EAAE,MAAA,EACrB,CAAC,CAAA,CAEYa,CAAAA,CAAkCN,EAAmB,IAAA,CAAK,CACrE,aAAc,IAAA,CACd,eAAA,CAAiB,KACjB,KAAA,CAAO,IAAA,CACP,SAAU,IAAA,CACV,OAAA,CAAS,KACT,GAAA,CAAK,IAAA,CACL,QAAS,IAAA,CACT,IAAA,CAAM,IACR,CAAC,CAAA,CAEYO,EAA2Bd,CAAAA,CAAE,KAAA,CAAM,CAE9CA,CAAAA,CAAE,MAAA,GAAS,MAAA,CAAQe,CAAAA,EAAMA,EAAE,QAAA,CAAS,QAAQ,EAAG,CAC7C,OAAA,CAAS,8CACX,CAAC,CAAA,CAEDf,EAAE,MAAA,CAAO,CACP,IAAKA,CAAAA,CAAE,MAAA,EAAO,CAAE,MAAA,CAAQe,CAAAA,EAAMA,CAAAA,CAAE,SAAS,QAAQ,CAAA,CAAG,CAClD,OAAA,CAAS,8CACX,CAAC,CAAA,CACD,MAAA,CAAQf,EAAE,MAAA,CAAOA,CAAAA,CAAE,QAAO,CAAGA,CAAAA,CAAE,QAAQ,CAAA,CAAE,UAAS,CAClD,OAAA,CAASA,EAAE,MAAA,CAAOA,CAAAA,CAAE,QAAO,CAAGA,CAAAA,CAAE,QAAQ,CAAA,CAAE,UAC5C,CAAC,CACH,CAAC,CAAA,CAEYgB,EAAuBhB,CAAAA,CAAE,MAAA,CACpCA,EAAE,MAAA,EAAO,CAAE,OAAQiB,CAAAA,EAAQA,CAAAA,CAAI,WAAW,GAAG,CAAA,CAAG,CAC9C,OAAA,CAAS,qDACX,CAAC,CAAA,CACDH,CACF,EAEaI,CAAAA,CAAkBlB,CAAAA,CAC5B,OAAO,CACN,OAAA,CAASA,EAAE,MAAA,EAAO,CAAE,UAAS,CAC7B,KAAA,CAAOA,EAAE,MAAA,EAAO,CAChB,IAAKA,CAAAA,CAAE,MAAA,CAAO,SAAQ,CAAE,OAAA,CAAQ,KAAK,CAAA,CACrC,GAAA,CAAKA,EAAE,MAAA,CAAO,OAAA,GAAU,OAAA,CAAQ,IAAI,EACpC,QAAA,CAAUA,CAAAA,CAAE,OAAO,CACjB,MAAA,CAAQA,EAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC5B,GAAA,CAAKA,CAAAA,CAAE,QAAO,CACd,SAAA,CAAWA,EAAE,MAAA,EAAO,CACpB,aAAcA,CAAAA,CAAE,OAAA,GAAU,OAAA,CAAQ,IAAI,EACtC,MAAA,CAAQA,CAAAA,CAAE,QAAO,CAAE,OAAA,CAAQ,EAAE,CAAA,CAAE,QAAA,EACjC,CAAC,CAAA,CACD,YAAaA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CACjC,QAASA,CAAAA,CAAE,MAAA,CAAO,CAChB,UAAA,CAAYA,CAAAA,CAAE,QAAO,CACrB,KAAA,CAAOA,EAAE,MAAA,EAAO,CAChB,GAAIA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CACxB,IAAKA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CACzB,MAAOA,CAAAA,CAAE,MAAA,GAAS,QAAA,EACpB,CAAC,CAAA,CACD,UAAA,CAAYgB,EAAqB,QAAA,EACnC,CAAC,CAAA,CACA,MAAA,GAEUG,CAAAA,CAAeD,CAAAA,CAAgB,OAAO,CACjD,aAAA,CAAelB,EAAE,MAAA,CAAO,CACtB,IAAKA,CAAAA,CAAE,MAAA,GACP,cAAA,CAAgBA,CAAAA,CAAE,QAAO,CACzB,WAAA,CAAaA,EAAE,MAAA,EAAO,CACtB,MAAOA,CAAAA,CAAE,MAAA,EAAO,CAChB,UAAA,CAAYA,CAAAA,CAAE,MAAA,GACd,GAAA,CAAKA,CAAAA,CAAE,QAAO,CACd,KAAA,CAAOA,EAAE,MAAA,EAAO,CAChB,GAAIA,CAAAA,CAAE,MAAA,EACR,CAAC,CACH,CAAC,CAAA,CAIYoB,CAAAA,CAAwBpB,EAAE,MAAA,CAAOmB,CAAY,EAE7CE,CAAAA,CAAyBrB,CAAAA,CAAE,OAAO,CAC7C,IAAA,CAAMA,EAAE,MAAA,EAAO,CACf,KAAMA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAC1B,YAAaA,CAAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CACjC,SAAUA,CAAAA,CAAE,MAAA,EAAO,CACnB,kBAAA,CAAoBA,CAAAA,CAAE,MAAA,EACxB,CAAC,CAAA,CAEYsB,EAAsBtB,CAAAA,CAAE,MAAA,CAAO,CAC1C,UAAA,CAAYA,CAAAA,CAAE,OAAO,CACnB,KAAA,CAAOA,EAAE,MAAA,EAAO,CAChB,OAAQA,CAAAA,CAAE,MAAA,GACV,KAAA,CAAOA,CAAAA,CAAE,QAAO,CAChB,OAAA,CAASA,EAAE,OAAA,EACb,CAAC,CAAA,CACD,KAAA,CAAOA,EAAE,KAAA,CAAMqB,CAAsB,CACvC,CAAC,CAAA,CAEYE,EAAwBvB,CAAAA,CAAE,MAAA,CACrCA,EAAE,MAAA,EAAO,CAAE,MAAM,8BAA8B,CAAA,CAC/CA,CAAAA,CAAE,MAAA,EACJ", "file": "chunk-OG5VNDVA.js", "sourcesContent": ["import { z } from \"zod\"\n\n// Note: if you edit the schema here, you must also edit the schema in the\n// apps/www/public/schema/registry-item.json file.\n\nexport const registryItemTypeSchema = z.enum([\n  \"registry:lib\",\n  \"registry:block\",\n  \"registry:component\",\n  \"registry:ui\",\n  \"registry:hook\",\n  \"registry:page\",\n  \"registry:file\",\n  \"registry:theme\",\n  \"registry:style\",\n  \"registry:item\",\n\n  // Internal use only\n  \"registry:example\",\n  \"registry:internal\",\n])\n\nexport const registryItemFileSchema = z.discriminatedUnion(\"type\", [\n  // Target is required for registry:file and registry:page\n  z.object({\n    path: z.string(),\n    content: z.string().optional(),\n    type: z.enum([\"registry:file\", \"registry:page\"]),\n    target: z.string(),\n  }),\n  z.object({\n    path: z.string(),\n    content: z.string().optional(),\n    type: registryItemTypeSchema.exclude([\"registry:file\", \"registry:page\"]),\n    target: z.string().optional(),\n  }),\n])\n\nexport const registryItemTailwindSchema = z.object({\n  config: z\n    .object({\n      content: z.array(z.string()).optional(),\n      theme: z.record(z.string(), z.any()).optional(),\n      plugins: z.array(z.string()).optional(),\n    })\n    .optional(),\n})\n\nexport const registryItemCssVarsSchema = z.object({\n  theme: z.record(z.string(), z.string()).optional(),\n  light: z.record(z.string(), z.string()).optional(),\n  dark: z.record(z.string(), z.string()).optional(),\n})\n\n// Recursive type for CSS properties that supports empty objects at any level.\nconst cssValueSchema: z.ZodType<any> = z.lazy(() =>\n  z.union([z.string(), z.record(z.string(), cssValueSchema)])\n)\n\nexport const registryItemCssSchema = z.record(z.string(), cssValueSchema)\n\nexport const registryItemEnvVarsSchema = z.record(z.string(), z.string())\n\nexport const registryItemSchema = z.object({\n  $schema: z.string().optional(),\n  extends: z.string().optional(),\n  name: z.string(),\n  type: registryItemTypeSchema,\n  title: z.string().optional(),\n  author: z.string().min(2).optional(),\n  description: z.string().optional(),\n  dependencies: z.array(z.string()).optional(),\n  devDependencies: z.array(z.string()).optional(),\n  registryDependencies: z.array(z.string()).optional(),\n  files: z.array(registryItemFileSchema).optional(),\n  tailwind: registryItemTailwindSchema.optional(),\n  cssVars: registryItemCssVarsSchema.optional(),\n  css: registryItemCssSchema.optional(),\n  envVars: registryItemEnvVarsSchema.optional(),\n  meta: z.record(z.string(), z.any()).optional(),\n  docs: z.string().optional(),\n  categories: z.array(z.string()).optional(),\n})\n\nexport type RegistryItem = z.infer<typeof registryItemSchema>\n\nexport const registrySchema = z.object({\n  name: z.string(),\n  homepage: z.string(),\n  items: z.array(registryItemSchema),\n})\n\nexport type Registry = z.infer<typeof registrySchema>\n\nexport const registryIndexSchema = z.array(registryItemSchema)\n\nexport const stylesSchema = z.array(\n  z.object({\n    name: z.string(),\n    label: z.string(),\n  })\n)\n\nexport const iconsSchema = z.record(\n  z.string(),\n  z.record(z.string(), z.string())\n)\n\nexport const registryBaseColorSchema = z.object({\n  inlineColors: z.object({\n    light: z.record(z.string(), z.string()),\n    dark: z.record(z.string(), z.string()),\n  }),\n  cssVars: registryItemCssVarsSchema,\n  cssVarsV4: registryItemCssVarsSchema.optional(),\n  inlineColorsTemplate: z.string(),\n  cssVarsTemplate: z.string(),\n})\n\nexport const registryResolvedItemsTreeSchema = registryItemSchema.pick({\n  dependencies: true,\n  devDependencies: true,\n  files: true,\n  tailwind: true,\n  cssVars: true,\n  css: true,\n  envVars: true,\n  docs: true,\n})\n\nexport const registryConfigItemSchema = z.union([\n  // Simple string format: \"https://example.com/{name}.json\"\n  z.string().refine((s) => s.includes(\"{name}\"), {\n    message: \"Registry URL must include {name} placeholder\",\n  }),\n  // Advanced object format with auth options\n  z.object({\n    url: z.string().refine((s) => s.includes(\"{name}\"), {\n      message: \"Registry URL must include {name} placeholder\",\n    }),\n    params: z.record(z.string(), z.string()).optional(),\n    headers: z.record(z.string(), z.string()).optional(),\n  }),\n])\n\nexport const registryConfigSchema = z.record(\n  z.string().refine((key) => key.startsWith(\"@\"), {\n    message: \"Registry names must start with @ (e.g., @v0, @acme)\",\n  }),\n  registryConfigItemSchema\n)\n\nexport const rawConfigSchema = z\n  .object({\n    $schema: z.string().optional(),\n    style: z.string(),\n    rsc: z.coerce.boolean().default(false),\n    tsx: z.coerce.boolean().default(true),\n    tailwind: z.object({\n      config: z.string().optional(),\n      css: z.string(),\n      baseColor: z.string(),\n      cssVariables: z.boolean().default(true),\n      prefix: z.string().default(\"\").optional(),\n    }),\n    iconLibrary: z.string().optional(),\n    aliases: z.object({\n      components: z.string(),\n      utils: z.string(),\n      ui: z.string().optional(),\n      lib: z.string().optional(),\n      hooks: z.string().optional(),\n    }),\n    registries: registryConfigSchema.optional(),\n  })\n  .strict()\n\nexport const configSchema = rawConfigSchema.extend({\n  resolvedPaths: z.object({\n    cwd: z.string(),\n    tailwindConfig: z.string(),\n    tailwindCss: z.string(),\n    utils: z.string(),\n    components: z.string(),\n    lib: z.string(),\n    hooks: z.string(),\n    ui: z.string(),\n  }),\n})\n\n// TODO: type the key.\n// Okay for now since I don't want a breaking change.\nexport const workspaceConfigSchema = z.record(configSchema)\n\nexport const searchResultItemSchema = z.object({\n  name: z.string(),\n  type: z.string().optional(),\n  description: z.string().optional(),\n  registry: z.string(),\n  addCommandArgument: z.string(),\n})\n\nexport const searchResultsSchema = z.object({\n  pagination: z.object({\n    total: z.number(),\n    offset: z.number(),\n    limit: z.number(),\n    hasMore: z.boolean(),\n  }),\n  items: z.array(searchResultItemSchema),\n})\n\nexport const registriesIndexSchema = z.record(\n  z.string().regex(/^@[a-zA-Z0-9][a-zA-Z0-9-_]*$/),\n  z.string()\n)\n"]}