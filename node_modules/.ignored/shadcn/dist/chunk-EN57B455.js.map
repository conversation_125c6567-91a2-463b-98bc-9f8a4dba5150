{"version": 3, "sources": ["../src/utils/get-package-manager.ts", "../src/mcp/utils.ts", "../src/mcp/index.ts"], "names": ["getPackageManager", "targetDir", "<PERSON><PERSON><PERSON><PERSON>", "packageManager", "detect", "userAgent", "getPackageRunner", "cwd", "SHADCN_CLI_COMMAND", "npxShadcn", "command", "getMcpConfig", "getRegistriesConfig", "formatSearchResultsWithPagination", "results", "options", "query", "registries", "formattedItems", "item", "parts", "header", "<PERSON><PERSON><PERSON><PERSON>", "output", "formatRegistryItems", "items", "formatItemExamples", "sections", "file", "server", "Server", "ListToolsRequestSchema", "zodToJsonSchema", "z", "CallToolRequestSchema", "request", "config", "dedent", "registry", "args", "searchRegistries", "registryItems", "getRegistryItems", "itemNames", "fullItems", "error", "e", "RegistryError", "errorMessage"], "mappings": "0UAEA,eAAsBA,EACpBC,CAAAA,CACA,CAAE,YAAA,CAAAC,CAAa,CAAA,CAAgC,CAC7C,aAAc,KAChB,CAAA,CACmD,CACnD,IAAMC,CAAAA,CAAiB,MAAMC,OAAO,CAAE,YAAA,CAAc,IAAA,CAAM,GAAA,CAAKH,CAAU,CAAC,EAE1E,GAAIE,CAAAA,GAAmB,YAAA,CAAc,OAAO,MAAA,CAC5C,GAAIA,IAAmB,QAAA,CAAU,OAAO,OACxC,GAAIA,CAAAA,GAAmB,MAAO,OAAO,KAAA,CACrC,GAAIA,CAAAA,GAAmB,MAAA,CAAQ,OAAO,OACtC,GAAI,CAACD,CAAAA,CACH,OAAOC,CAAAA,EAAkB,KAAA,CAI3B,IAAME,CAAAA,CAAY,OAAA,CAAQ,GAAA,CAAI,qBAAA,EAAyB,EAAA,CAEvD,OAAIA,EAAU,UAAA,CAAW,MAAM,CAAA,CACtB,MAAA,CAGLA,CAAAA,CAAU,UAAA,CAAW,MAAM,CAAA,CACtB,MAAA,CAGLA,CAAAA,CAAU,UAAA,CAAW,KAAK,CAAA,CACrB,MAGF,KACT,CAEA,eAAsBC,CAAAA,CAAiBC,CAAAA,CAAa,CAClD,IAAMJ,CAAAA,CAAiB,MAAMH,CAAAA,CAAkBO,CAAG,CAAA,CAElD,OAAIJ,IAAmB,MAAA,CAAe,UAAA,CAElCA,IAAmB,KAAA,CAAc,MAAA,CAE9B,KACT,CCvCA,IAAMK,CAAAA,CAAqB,eAAA,CAE3B,eAAsBC,CAAAA,CAAUC,EAAiB,CAE/C,OAAO,CAAA,EADe,MAAMJ,CAAAA,CAAiB,OAAA,CAAQ,KAAK,CACnC,CAAA,CAAA,EAAIE,CAAkB,CAAA,CAAA,EAAIE,CAAO,EAC1D,CAEA,eAAsBC,CAAAA,CAAaJ,CAAAA,CAAM,OAAA,CAAQ,GAAA,GAAO,CAKtD,OAAO,CACL,UAAA,CAAA,CALa,MAAMK,EAAAA,CAAoBL,EAAK,CAC5C,QAAA,CAAU,KACZ,CAAC,CAAA,EAGoB,UACrB,CACF,CAEO,SAASM,CAAAA,CACdC,CAAAA,CACAC,CAAAA,CAIA,CACA,GAAM,CAAE,KAAA,CAAAC,EAAO,UAAA,CAAAC,CAAW,EAAIF,CAAAA,EAAW,EAAC,CAEpCG,CAAAA,CAAiBJ,CAAAA,CAAQ,KAAA,CAAM,IAAKK,CAAAA,EAAS,CACjD,IAAMC,CAAAA,CAAkB,CAAC,CAAA,EAAA,EAAKD,EAAK,IAAI,CAAA,CAAE,CAAA,CAEzC,OAAIA,CAAAA,CAAK,IAAA,EACPC,EAAM,IAAA,CAAK,CAAA,CAAA,EAAID,CAAAA,CAAK,IAAI,CAAA,CAAA,CAAG,CAAA,CAGzBA,EAAK,WAAA,EACPC,CAAAA,CAAM,IAAA,CAAK,CAAA,EAAA,EAAKD,CAAAA,CAAK,WAAW,EAAE,CAAA,CAGhCA,CAAAA,CAAK,QAAA,EACPC,CAAAA,CAAM,IAAA,CAAK,CAAA,CAAA,EAAID,EAAK,QAAQ,CAAA,CAAA,CAAG,CAAA,CAGjCC,CAAAA,CAAM,IAAA,CACJ;AAAA,iBAAA,EAAsBX,CAAAA,CAAU,CAAA,IAAA,EAAOU,CAAAA,CAAK,kBAAkB,CAAA,CAAE,CAAC,CAAA,EAAA,CACnE,CAAA,CAEOC,CAAAA,CAAM,IAAA,CAAK,GAAG,CACvB,CAAC,CAAA,CAEGC,CAAAA,CAAS,CAAA,MAAA,EAASP,CAAAA,CAAQ,UAAA,CAAW,KAAK,CAAA,MAAA,CAAA,CAC1CE,CAAAA,GACFK,CAAAA,EAAU,CAAA,WAAA,EAAcL,CAAK,CAAA,CAAA,CAAA,CAAA,CAE3BC,CAAAA,EAAcA,CAAAA,CAAW,MAAA,CAAS,CAAA,GACpCI,GAAU,CAAA,eAAA,EAAkBJ,CAAAA,CAAW,IAAA,CAAK,IAAI,CAAC,CAAA,CAAA,CAAA,CAEnDI,CAAAA,EAAU,GAAA,CAEV,IAAMC,CAAAA,CAAe,CAAA,cAAA,EACnBR,CAAAA,CAAQ,UAAA,CAAW,MAAA,CAAS,CAC9B,CAAA,CAAA,EAAI,KAAK,GAAA,CACPA,CAAAA,CAAQ,UAAA,CAAW,MAAA,CAASA,CAAAA,CAAQ,UAAA,CAAW,KAAA,CAC/CA,CAAAA,CAAQ,WAAW,KACrB,CAAC,CAAA,IAAA,EAAOA,CAAAA,CAAQ,UAAA,CAAW,KAAK,CAAA,CAAA,CAAA,CAE5BS,CAAAA,CAAS,GAAGF,CAAM;;AAAA,EAAOC,CAAY;;AAAA,EAAOJ,EAAe,IAAA,CAAK;;AAAA,CAAM,CAAC,CAAA,CAAA,CAE3E,OAAIJ,CAAAA,CAAQ,UAAA,CAAW,UACrBS,CAAAA,EAAU;;AAAA,kCAAA,EACRT,CAAAA,CAAQ,WAAW,MAAA,CAASA,CAAAA,CAAQ,WAAW,KACjD,CAAA,sBAAA,CAAA,CAAA,CAGKS,CACT,CAEO,SAASC,CAAAA,CACdC,EACA,CACA,OAAOA,CAAAA,CAAM,GAAA,CAAKN,CAAAA,EACQ,CACtB,MAAMA,CAAAA,CAAK,IAAI,CAAA,CAAA,CACfA,CAAAA,CAAK,WAAA,CAAc;AAAA,EAAKA,EAAK,WAAW;AAAA,CAAA,CAAO,EAAA,CAC/CA,CAAAA,CAAK,IAAA,CAAO,CAAA,UAAA,EAAaA,CAAAA,CAAK,IAAI,CAAA,CAAA,CAAK,EAAA,CACvCA,CAAAA,CAAK,KAAA,EAASA,CAAAA,CAAK,KAAA,CAAM,OAAS,CAAA,CAC9B,CAAA,WAAA,EAAcA,CAAAA,CAAK,KAAA,CAAM,MAAM,CAAA,QAAA,CAAA,CAC/B,GACJA,CAAAA,CAAK,YAAA,EAAgBA,CAAAA,CAAK,YAAA,CAAa,MAAA,CAAS,CAAA,CAC5C,qBAAqBA,CAAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAI,CAAC,CAAA,CAAA,CACjD,GACJA,CAAAA,CAAK,eAAA,EAAmBA,CAAAA,CAAK,eAAA,CAAgB,MAAA,CAAS,CAAA,CAClD,yBAAyBA,CAAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,IAAI,CAAC,CAAA,CAAA,CACxD,EACN,CAAA,CACa,MAAA,CAAO,OAAO,CAAA,CAAE,IAAA,CAAK;AAAA,CAAI,CACvC,CACH,CAEO,SAASO,EACdD,CAAAA,CACAT,CAAAA,CACA,CACA,IAAMW,CAAAA,CAAWF,CAAAA,CAAM,IAAKN,CAAAA,EAAS,CACnC,IAAMC,CAAAA,CAAkB,CACtB,eAAeD,CAAAA,CAAK,IAAI,CAAA,CAAA,CACxBA,CAAAA,CAAK,WAAA,CAAc;AAAA,EAAKA,EAAK,WAAW;AAAA,CAAA,CAAO,EACjD,CAAA,CAEA,OAAIA,CAAAA,CAAK,KAAA,EAAO,QACdA,CAAAA,CAAK,KAAA,CAAM,OAAA,CAASS,CAAAA,EAAS,CACvBA,CAAAA,CAAK,OAAA,GACPR,EAAM,IAAA,CAAK,CAAA,UAAA,EAAaQ,EAAK,IAAI,CAAA;AAAA,CAAM,CAAA,CACvCR,EAAM,IAAA,CAAK,QAAQ,EACnBA,CAAAA,CAAM,IAAA,CAAKQ,EAAK,OAAO,CAAA,CACvBR,EAAM,IAAA,CAAK,KAAK,GAEpB,CAAC,CAAA,CAGIA,EAAM,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK;AAAA,CAAI,CACxC,CAAC,CAAA,CAMD,OAJe,CAAA;;AAAA,MAAA,EAA6BK,CAAAA,CAAM,MAAM,CAAA,QAAA,EACtDA,CAAAA,CAAM,OAAS,CAAA,CAAI,GAAA,CAAM,EAC3B,CAAA,WAAA,EAAcT,CAAK,CAAA;AAAA,CAAA,CAEHW,EAAS,IAAA,CAAK;;AAAA;;AAAA,CAAa,CAC7C,CChHO,IAAME,CAAAA,CAAS,IAAIC,MAAAA,CACxB,CACE,IAAA,CAAM,QAAA,CACN,OAAA,CAAS,OACX,CAAA,CACA,CACE,YAAA,CAAc,CACZ,SAAA,CAAW,EAAC,CACZ,KAAA,CAAO,EACT,CACF,CACF,EAEAD,CAAAA,CAAO,iBAAA,CAAkBE,sBAAAA,CAAwB,UACxC,CACL,MAAO,CACL,CACE,IAAA,CAAM,wBAAA,CACN,WAAA,CACE,kIAAA,CACF,WAAA,CAAaC,eAAAA,CAAgBC,CAAAA,CAAE,MAAA,CAAO,EAAE,CAAC,CAC3C,CAAA,CACA,CACE,KAAM,0BAAA,CACN,WAAA,CACE,qFAAA,CACF,WAAA,CAAaD,eAAAA,CACXC,CAAAA,CAAE,MAAA,CAAO,CACP,WAAYA,CAAAA,CACT,KAAA,CAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAChB,QAAA,CACC,gEACF,EACF,KAAA,CAAOA,CAAAA,CACJ,MAAA,EAAO,CACP,QAAA,EAAS,CACT,QAAA,CAAS,mCAAmC,CAAA,CAC/C,MAAA,CAAQA,CAAAA,CACL,MAAA,EAAO,CACP,QAAA,EAAS,CACT,QAAA,CAAS,wCAAwC,CACtD,CAAC,CACH,CACF,CAAA,CACA,CACE,IAAA,CAAM,4BAAA,CACN,YACE,0KAAA,CACF,WAAA,CAAaD,eAAAA,CACXC,CAAAA,CAAE,MAAA,CAAO,CACP,UAAA,CAAYA,CAAAA,CACT,MAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAChB,QAAA,CACC,gEACF,CAAA,CACF,KAAA,CAAOA,CAAAA,CACJ,MAAA,EAAO,CACP,QAAA,CACC,4EACF,CAAA,CACF,KAAA,CAAOA,CAAAA,CACJ,QAAO,CACP,QAAA,EAAS,CACT,QAAA,CAAS,mCAAmC,CAAA,CAC/C,MAAA,CAAQA,CAAAA,CACL,QAAO,CACP,QAAA,EAAS,CACT,QAAA,CAAS,wCAAwC,CACtD,CAAC,CACH,CACF,CAAA,CACA,CACE,IAAA,CAAM,0BAAA,CACN,WAAA,CACE,qLAAA,CACF,WAAA,CAAaD,eAAAA,CACXC,CAAAA,CAAE,MAAA,CAAO,CACP,KAAA,CAAOA,CAAAA,CACJ,KAAA,CAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAChB,QAAA,CACC,qFACF,CACJ,CAAC,CACH,CACF,CAAA,CACA,CACE,IAAA,CAAM,mCAAA,CACN,WAAA,CACE,4LAAA,CACF,WAAA,CAAaD,eAAAA,CACXC,CAAAA,CAAE,MAAA,CAAO,CACP,UAAA,CAAYA,CAAAA,CACT,KAAA,CAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAChB,QAAA,CACC,gEACF,CAAA,CACF,KAAA,CAAOA,CAAAA,CACJ,MAAA,EAAO,CACP,QAAA,CACC,8NACF,CACJ,CAAC,CACH,CACF,CAAA,CACA,CACE,IAAA,CAAM,2BAAA,CACN,WAAA,CACE,qIACF,WAAA,CAAaD,eAAAA,CACXC,CAAAA,CAAE,MAAA,CAAO,CACP,KAAA,CAAOA,CAAAA,CACJ,KAAA,CAAMA,EAAE,MAAA,EAAQ,CAAA,CAChB,QAAA,CACC,sHACF,CACJ,CAAC,CACH,CACF,CAAA,CACA,CACE,IAAA,CAAM,qBAAA,CACN,WAAA,CACE,2NAAA,CACF,WAAA,CAAaD,gBAAgBC,CAAAA,CAAE,MAAA,CAAO,EAAE,CAAC,CAC3C,CACF,CACF,EACD,CAAA,CAEDJ,CAAAA,CAAO,iBAAA,CAAkBK,qBAAAA,CAAuB,MAAOC,CAAAA,EAAY,CACjE,GAAI,CACF,GAAI,CAACA,CAAAA,CAAQ,MAAA,CAAO,SAAA,CAClB,MAAM,IAAI,KAAA,CAAM,6BAA6B,CAAA,CAG/C,OAAQA,CAAAA,CAAQ,MAAA,CAAO,IAAA,EACrB,KAAK,yBAA0B,CAC7B,IAAMC,CAAAA,CAAS,MAAMzB,CAAAA,CAAa,OAAA,CAAQ,GAAA,EAAK,EAE/C,OAAKyB,CAAAA,EAAQ,UAAA,CAeN,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,OACN,IAAA,CAAMC,CAAAA,CAAAA;;AAAA,gBAAA,EAEF,MAAA,CAAO,IAAA,CAAKD,CAAAA,CAAO,UAAU,CAAA,CAC5B,GAAA,CAAKE,CAAAA,EAAa,CAAA,EAAA,EAAKA,CAAQ,CAAA,CAAE,CAAA,CACjC,IAAA,CAAK;AAAA,CAAI,CAAC;;AAAA;AAAA,kBAAA,EAGT,MAAM7B,CAAAA,CAAU,wBAAwB,CAAC,CAAA;;AAAA,+BAAA,EAE5B,MAAMA,EACrB,cACF,CAAC,WAAW,MAAMA,CAAAA,CAClB,oBACF,CAAC,CAAA;AAAA,gBAAA,CAEH,CACF,CACF,CAAA,CAnCS,CACL,QAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAM4B,CAAAA,CAAAA;;AAAA;AAAA;AAAA,+EAAA,CAKR,CACF,CACF,CAyBJ,CAEA,KAAK,4BAAA,CAA8B,CAQjC,IAAME,CAAAA,CAPcN,CAAAA,CAAE,MAAA,CAAO,CAC3B,WAAYA,CAAAA,CAAE,KAAA,CAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAC9B,KAAA,CAAOA,CAAAA,CAAE,MAAA,EAAO,CAChB,KAAA,CAAOA,CAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC3B,OAAQA,CAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EACrB,CAAC,CAAA,CAEwB,KAAA,CAAME,CAAAA,CAAQ,MAAA,CAAO,SAAS,CAAA,CACjDrB,CAAAA,CAAU,MAAM0B,EAAAA,CAAiBD,CAAAA,CAAK,WAAY,CACtD,KAAA,CAAOA,CAAAA,CAAK,KAAA,CACZ,KAAA,CAAOA,CAAAA,CAAK,KAAA,CACZ,MAAA,CAAQA,EAAK,MAAA,CACb,MAAA,CAAQ,MAAM5B,CAAAA,CAAa,OAAA,CAAQ,GAAA,EAAK,CAAA,CACxC,SAAU,CAAA,CACZ,CAAC,CAAA,CAED,OAAIG,CAAAA,CAAQ,KAAA,CAAM,MAAA,GAAW,CAAA,CACpB,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMuB,CAAAA,CAAAA,yBAAAA,EACJE,EAAK,KACP,CAAA,gBAAA,EAAmBA,CAAAA,CAAK,UAAA,CAAW,IAAA,CACjC,IACF,CAAC,CAAA,mDAAA,CACH,CACF,CACF,CAAA,CAGK,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,KAAM1B,CAAAA,CAAkCC,CAAAA,CAAS,CAC/C,KAAA,CAAOyB,CAAAA,CAAK,KAAA,CACZ,UAAA,CAAYA,CAAAA,CAAK,UACnB,CAAC,CACH,CACF,CACF,CACF,CAEA,KAAK,2BAA4B,CAQ/B,IAAMA,CAAAA,CAPcN,CAAAA,CAAE,MAAA,CAAO,CAC3B,UAAA,CAAYA,CAAAA,CAAE,MAAMA,CAAAA,CAAE,MAAA,EAAQ,CAAA,CAC9B,KAAA,CAAOA,CAAAA,CAAE,MAAA,EAAO,CAAE,UAAS,CAC3B,MAAA,CAAQA,CAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EAAS,CAC5B,GAAA,CAAKA,CAAAA,CAAE,MAAA,EAAO,CAAE,QAAA,EAClB,CAAC,CAAA,CAEwB,KAAA,CAAME,EAAQ,MAAA,CAAO,SAAS,CAAA,CACjDrB,CAAAA,CAAU,MAAM0B,EAAAA,CAAiBD,CAAAA,CAAK,UAAA,CAAY,CACtD,KAAA,CAAOA,CAAAA,CAAK,KAAA,CACZ,MAAA,CAAQA,CAAAA,CAAK,MAAA,CACb,MAAA,CAAQ,MAAM5B,EAAa,OAAA,CAAQ,GAAA,EAAK,CAAA,CACxC,QAAA,CAAU,CAAA,CACZ,CAAC,CAAA,CAED,OAAIG,CAAAA,CAAQ,KAAA,CAAM,MAAA,GAAW,CAAA,CACpB,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMuB,CAAAA,CAAAA,6BAAAA,EAAsCE,CAAAA,CAAK,UAAA,CAAW,IAAA,CAC1D,IACF,CAAC,CAAA,CAAA,CACH,CACF,CACF,CAAA,CAGK,CACL,OAAA,CAAS,CACP,CACE,KAAM,MAAA,CACN,IAAA,CAAM1B,CAAAA,CAAkCC,CAAAA,CAAS,CAC/C,UAAA,CAAYyB,CAAAA,CAAK,UACnB,CAAC,CACH,CACF,CACF,CACF,CAEA,KAAK,0BAAA,CAA4B,CAK/B,IAAMA,CAAAA,CAJcN,CAAAA,CAAE,MAAA,CAAO,CAC3B,KAAA,CAAOA,CAAAA,CAAE,KAAA,CAAMA,EAAE,MAAA,EAAQ,CAC3B,CAAC,CAAA,CAEwB,KAAA,CAAME,CAAAA,CAAQ,MAAA,CAAO,SAAS,CAAA,CACjDM,CAAAA,CAAgB,MAAMC,CAAAA,CAAiBH,CAAAA,CAAK,KAAA,CAAO,CACvD,MAAA,CAAQ,MAAM5B,CAAAA,CAAa,OAAA,CAAQ,GAAA,EAAK,CAAA,CACxC,QAAA,CAAU,CAAA,CACZ,CAAC,CAAA,CAED,GAAI8B,CAAAA,EAAe,MAAA,GAAW,CAAA,CAC5B,OAAO,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMJ,CAAAA,CAAAA,oBAAAA,EAA6BE,CAAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI,CAAC;;AAAA,4GAAA,CAG1D,CACF,CACF,CAAA,CAGF,IAAMrB,EAAiBM,CAAAA,CAAoBiB,CAAa,CAAA,CAExD,OAAO,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,OACN,IAAA,CAAMJ,CAAAA,CAAAA;;AAAA,cAAA,EAEJnB,EAAe,IAAA,CAAK;;AAAA;;AAAA,CAAa,CAAC,CAAA,CACtC,CACF,CACF,CACF,CAEA,KAAK,mCAAA,CAAqC,CAMxC,IAAMqB,CAAAA,CALcN,CAAAA,CAAE,OAAO,CAC3B,KAAA,CAAOA,CAAAA,CAAE,MAAA,EAAO,CAChB,UAAA,CAAYA,CAAAA,CAAE,KAAA,CAAMA,EAAE,MAAA,EAAQ,CAChC,CAAC,CAAA,CAEwB,KAAA,CAAME,CAAAA,CAAQ,MAAA,CAAO,SAAS,CAAA,CACjDC,CAAAA,CAAS,MAAMzB,CAAAA,EAAa,CAE5BG,CAAAA,CAAU,MAAM0B,EAAAA,CAAiBD,CAAAA,CAAK,UAAA,CAAY,CACtD,KAAA,CAAOA,CAAAA,CAAK,KAAA,CACZ,MAAA,CAAAH,CAAAA,CACA,SAAU,CAAA,CACZ,CAAC,CAAA,CAED,GAAItB,CAAAA,CAAQ,KAAA,CAAM,MAAA,GAAW,CAAA,CAC3B,OAAO,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMuB,CAAAA,CAAAA,6BAAAA,EAAsCE,EAAK,KAAK,CAAA;;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,uGAAA,CAUxD,CACF,CACF,CAAA,CAGF,IAAMI,EAAY7B,CAAAA,CAAQ,KAAA,CAAM,IAAKK,CAAAA,EAASA,CAAAA,CAAK,kBAAkB,CAAA,CAC/DyB,CAAAA,CAAY,MAAMF,CAAAA,CAAiBC,CAAAA,CAAW,CAClD,MAAA,CAAAP,CAAAA,CACA,SAAU,CAAA,CACZ,CAAC,EAED,OAAO,CACL,QAAS,CACP,CACE,KAAM,MAAA,CACN,IAAA,CAAMV,EAAmBkB,CAAAA,CAAWL,CAAAA,CAAK,KAAK,CAChD,CACF,CACF,CACF,CAEA,KAAK,2BAAA,CAA6B,CAChC,IAAMA,CAAAA,CAAON,CAAAA,CACV,OAAO,CACN,KAAA,CAAOA,EAAE,KAAA,CAAMA,CAAAA,CAAE,QAAQ,CAC3B,CAAC,CAAA,CACA,KAAA,CAAME,EAAQ,MAAA,CAAO,SAAS,EAEjC,OAAO,CACL,QAAS,CACP,CACE,KAAM,MAAA,CACN,IAAA,CAAM,MAAM1B,CAAAA,CAAU,CAAA,IAAA,EAAO8B,EAAK,KAAA,CAAM,IAAA,CAAK,GAAG,CAAC,CAAA,CAAE,CACrD,CACF,CACF,CACF,CAEA,KAAK,sBACH,OAAO,CACL,QAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMF,CAAAA,CAAAA;;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAAA,CAWR,CACF,CACF,CAAA,CAGF,QACE,MAAM,IAAI,KAAA,CAAM,CAAA,KAAA,EAAQF,CAAAA,CAAQ,MAAA,CAAO,IAAI,CAAA,UAAA,CAAY,CAC3D,CACF,CAAA,MAASU,CAAAA,CAAO,CACd,GAAIA,CAAAA,YAAiBZ,CAAAA,CAAE,QAAA,CACrB,OAAO,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMI,CAAAA,CAAAA;AAAA,cAAA,EACFQ,CAAAA,CAAM,MAAA,CACL,GAAA,CAAKC,CAAAA,EAAM,KAAKA,CAAAA,CAAE,IAAA,CAAK,IAAA,CAAK,GAAG,CAAC,CAAA,EAAA,EAAKA,CAAAA,CAAE,OAAO,CAAA,CAAE,EAChD,IAAA,CAAK;AAAA,CAAI,CAAC;AAAA,cAAA,CAEjB,CACF,CAAA,CACA,OAAA,CAAS,IACX,CAAA,CAGF,GAAID,CAAAA,YAAiBE,GAAAA,CAAe,CAClC,IAAIC,EAAeH,CAAAA,CAAM,OAAA,CAEzB,OAAIA,CAAAA,CAAM,aACRG,CAAAA,EAAgB;;AAAA,UAAA,EAAUH,CAAAA,CAAM,UAAU,CAAA,CAAA,CAAA,CAGxCA,CAAAA,CAAM,UACRG,CAAAA,EAAgB;;AAAA,SAAA,EAAgB,IAAA,CAAK,SAAA,CAAUH,CAAAA,CAAM,OAAA,CAAS,IAAA,CAAM,CAAC,CAAC,CAAA,CAAA,CAAA,CAGjE,CACL,OAAA,CAAS,CACP,CACE,KAAM,MAAA,CACN,IAAA,CAAMR,CAAAA,CAAAA,OAAAA,EAAgBQ,CAAAA,CAAM,IAAI,CAAA,GAAA,EAAMG,CAAY,CAAA,CACpD,CACF,CAAA,CACA,OAAA,CAAS,IACX,CACF,CAEA,IAAMA,CAAAA,CAAeH,CAAAA,YAAiB,KAAA,CAAQA,CAAAA,CAAM,OAAA,CAAU,OAAOA,CAAK,CAAA,CAC1E,OAAO,CACL,OAAA,CAAS,CACP,CACE,IAAA,CAAM,MAAA,CACN,IAAA,CAAMR,CAAAA,CAAAA,OAAAA,EAAgBW,CAAY,CAAA,CACpC,CACF,CAAA,CACA,OAAA,CAAS,IACX,CACF,CACF,CAAC,CAAA", "file": "chunk-EN57B455.js", "sourcesContent": ["import { detect } from \"@antfu/ni\"\n\nexport async function getPackageManager(\n  targetDir: string,\n  { withFallback }: { withFallback?: boolean } = {\n    withFallback: false,\n  }\n): Promise<\"yarn\" | \"pnpm\" | \"bun\" | \"npm\" | \"deno\"> {\n  const packageManager = await detect({ programmatic: true, cwd: targetDir })\n\n  if (packageManager === \"yarn@berry\") return \"yarn\"\n  if (packageManager === \"pnpm@6\") return \"pnpm\"\n  if (packageManager === \"bun\") return \"bun\"\n  if (packageManager === \"deno\") return \"deno\"\n  if (!withFallback) {\n    return packageManager ?? \"npm\"\n  }\n\n  // Fallback to user agent if not detected.\n  const userAgent = process.env.npm_config_user_agent || \"\"\n\n  if (userAgent.startsWith(\"yarn\")) {\n    return \"yarn\"\n  }\n\n  if (userAgent.startsWith(\"pnpm\")) {\n    return \"pnpm\"\n  }\n\n  if (userAgent.startsWith(\"bun\")) {\n    return \"bun\"\n  }\n\n  return \"npm\"\n}\n\nexport async function getPackageRunner(cwd: string) {\n  const packageManager = await getPackageManager(cwd)\n\n  if (packageManager === \"pnpm\") return \"pnpm dlx\"\n\n  if (packageManager === \"bun\") return \"bunx\"\n\n  return \"npx\"\n}\n", "import { getRegistriesConfig } from \"@/src/registry/api\"\nimport { registryItemSchema, searchResultsSchema } from \"@/src/schema\"\nimport { getPackageRunner } from \"@/src/utils/get-package-manager\"\nimport { z } from \"zod\"\n\nconst SHADCN_CLI_COMMAND = \"shadcn@latest\"\n\nexport async function npxShadcn(command: string) {\n  const packageRunner = await getPackageRunner(process.cwd())\n  return `${packageRunner} ${SHADCN_CLI_COMMAND} ${command}`\n}\n\nexport async function getMcpConfig(cwd = process.cwd()) {\n  const config = await getRegistriesConfig(cwd, {\n    useCache: false,\n  })\n\n  return {\n    registries: config.registries,\n  }\n}\n\nexport function formatSearchResultsWithPagination(\n  results: z.infer<typeof searchResultsSchema>,\n  options?: {\n    query?: string\n    registries?: string[]\n  }\n) {\n  const { query, registries } = options || {}\n\n  const formattedItems = results.items.map((item) => {\n    const parts: string[] = [`- ${item.name}`]\n\n    if (item.type) {\n      parts.push(`(${item.type})`)\n    }\n\n    if (item.description) {\n      parts.push(`- ${item.description}`)\n    }\n\n    if (item.registry) {\n      parts.push(`[${item.registry}]`)\n    }\n\n    parts.push(\n      `\\n  Add command: \\`${npxShadcn(`add ${item.addCommandArgument}`)}\\``\n    )\n\n    return parts.join(\" \")\n  })\n\n  let header = `Found ${results.pagination.total} items`\n  if (query) {\n    header += ` matching \"${query}\"`\n  }\n  if (registries && registries.length > 0) {\n    header += ` in registries ${registries.join(\", \")}`\n  }\n  header += \":\"\n\n  const showingRange = `Showing items ${\n    results.pagination.offset + 1\n  }-${Math.min(\n    results.pagination.offset + results.pagination.limit,\n    results.pagination.total\n  )} of ${results.pagination.total}:`\n\n  let output = `${header}\\n\\n${showingRange}\\n\\n${formattedItems.join(\"\\n\\n\")}`\n\n  if (results.pagination.hasMore) {\n    output += `\\n\\nMore items available. Use offset: ${\n      results.pagination.offset + results.pagination.limit\n    } to see the next page.`\n  }\n\n  return output\n}\n\nexport function formatRegistryItems(\n  items: z.infer<typeof registryItemSchema>[]\n) {\n  return items.map((item) => {\n    const parts: string[] = [\n      `## ${item.name}`,\n      item.description ? `\\n${item.description}\\n` : \"\",\n      item.type ? `**Type:** ${item.type}` : \"\",\n      item.files && item.files.length > 0\n        ? `**Files:** ${item.files.length} file(s)`\n        : \"\",\n      item.dependencies && item.dependencies.length > 0\n        ? `**Dependencies:** ${item.dependencies.join(\", \")}`\n        : \"\",\n      item.devDependencies && item.devDependencies.length > 0\n        ? `**Dev Dependencies:** ${item.devDependencies.join(\", \")}`\n        : \"\",\n    ]\n    return parts.filter(Boolean).join(\"\\n\")\n  })\n}\n\nexport function formatItemExamples(\n  items: z.infer<typeof registryItemSchema>[],\n  query: string\n) {\n  const sections = items.map((item) => {\n    const parts: string[] = [\n      `## Example: ${item.name}`,\n      item.description ? `\\n${item.description}\\n` : \"\",\n    ]\n\n    if (item.files?.length) {\n      item.files.forEach((file) => {\n        if (file.content) {\n          parts.push(`### Code (${file.path}):\\n`)\n          parts.push(\"```tsx\")\n          parts.push(file.content)\n          parts.push(\"```\")\n        }\n      })\n    }\n\n    return parts.filter(Boolean).join(\"\\n\")\n  })\n\n  const header = `# Usage Examples\\n\\nFound ${items.length} example${\n    items.length > 1 ? \"s\" : \"\"\n  } matching \"${query}\":\\n`\n\n  return header + sections.join(\"\\n\\n---\\n\\n\")\n}\n", "import { getRegistryItems, searchRegistries } from \"@/src/registry\"\nimport { RegistryError } from \"@/src/registry/errors\"\nimport { Server } from \"@modelcontextprotocol/sdk/server/index.js\"\nimport {\n  CallToolRequestSchema,\n  ListToolsRequestSchema,\n} from \"@modelcontextprotocol/sdk/types.js\"\nimport dedent from \"dedent\"\nimport { z } from \"zod\"\nimport { zodToJsonSchema } from \"zod-to-json-schema\"\n\nimport {\n  formatItemExamples,\n  formatRegistryItems,\n  formatSearchResultsWithPagination,\n  getMcpConfig,\n  npxShadcn,\n} from \"./utils\"\n\nexport const server = new Server(\n  {\n    name: \"shadcn\",\n    version: \"1.0.0\",\n  },\n  {\n    capabilities: {\n      resources: {},\n      tools: {},\n    },\n  }\n)\n\nserver.setRequestHandler(ListToolsRequestSchema, async () => {\n  return {\n    tools: [\n      {\n        name: \"get_project_registries\",\n        description:\n          \"Get configured registry names from components.json - Returns error if no components.json exists (use init_project to create one)\",\n        inputSchema: zodToJsonSchema(z.object({})),\n      },\n      {\n        name: \"list_items_in_registries\",\n        description:\n          \"List items from registries (requires components.json - use init_project if missing)\",\n        inputSchema: zodToJsonSchema(\n          z.object({\n            registries: z\n              .array(z.string())\n              .describe(\n                \"Array of registry names to search (e.g., ['@shadcn', '@acme'])\"\n              ),\n            limit: z\n              .number()\n              .optional()\n              .describe(\"Maximum number of items to return\"),\n            offset: z\n              .number()\n              .optional()\n              .describe(\"Number of items to skip for pagination\"),\n          })\n        ),\n      },\n      {\n        name: \"search_items_in_registries\",\n        description:\n          \"Search for components in registries using fuzzy matching (requires components.json). After finding an item, use get_item_examples_from_registries to see usage examples.\",\n        inputSchema: zodToJsonSchema(\n          z.object({\n            registries: z\n              .array(z.string())\n              .describe(\n                \"Array of registry names to search (e.g., ['@shadcn', '@acme'])\"\n              ),\n            query: z\n              .string()\n              .describe(\n                \"Search query string for fuzzy matching against item names and descriptions\"\n              ),\n            limit: z\n              .number()\n              .optional()\n              .describe(\"Maximum number of items to return\"),\n            offset: z\n              .number()\n              .optional()\n              .describe(\"Number of items to skip for pagination\"),\n          })\n        ),\n      },\n      {\n        name: \"view_items_in_registries\",\n        description:\n          \"View detailed information about specific registry items including the name, description, type and files content. For usage examples, use get_item_examples_from_registries instead.\",\n        inputSchema: zodToJsonSchema(\n          z.object({\n            items: z\n              .array(z.string())\n              .describe(\n                \"Array of item names with registry prefix (e.g., ['@shadcn/button', '@shadcn/card'])\"\n              ),\n          })\n        ),\n      },\n      {\n        name: \"get_item_examples_from_registries\",\n        description:\n          \"Find usage examples and demos with their complete code. Search for patterns like 'accordion-demo', 'button example', 'card-demo', etc. Returns full implementation code with dependencies.\",\n        inputSchema: zodToJsonSchema(\n          z.object({\n            registries: z\n              .array(z.string())\n              .describe(\n                \"Array of registry names to search (e.g., ['@shadcn', '@acme'])\"\n              ),\n            query: z\n              .string()\n              .describe(\n                \"Search query for examples (e.g., 'accordion-demo', 'button demo', 'card example', 'tooltip-demo', 'example-booking-form', 'example-hero'). Common patterns: '{item-name}-demo', '{item-name} example', 'example {item-name}'\"\n              ),\n          })\n        ),\n      },\n      {\n        name: \"get_add_command_for_items\",\n        description:\n          \"Get the shadcn CLI add command for specific items in a registry. This is useful for adding one or more components to your project.\",\n        inputSchema: zodToJsonSchema(\n          z.object({\n            items: z\n              .array(z.string())\n              .describe(\n                \"Array of items to get the add command for prefixed with the registry name (e.g., ['@shadcn/button', '@shadcn/card'])\"\n              ),\n          })\n        ),\n      },\n      {\n        name: \"get_audit_checklist\",\n        description:\n          \"After creating new components or generating new code files, use this tool for a quick checklist to verify that everything is working as expected. Make sure to run the tool after all required steps have been completed.\",\n        inputSchema: zodToJsonSchema(z.object({})),\n      },\n    ],\n  }\n})\n\nserver.setRequestHandler(CallToolRequestSchema, async (request) => {\n  try {\n    if (!request.params.arguments) {\n      throw new Error(\"No tool arguments provided.\")\n    }\n\n    switch (request.params.name) {\n      case \"get_project_registries\": {\n        const config = await getMcpConfig(process.cwd())\n\n        if (!config?.registries) {\n          return {\n            content: [\n              {\n                type: \"text\",\n                text: dedent`No components.json found or no registries configured.\n\n                To fix this:\n                1. Use the \\`init\\` command to create a components.json file\n                2. Or manually create components.json with a registries section`,\n              },\n            ],\n          }\n        }\n\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: dedent`The following registries are configured in the current project:\n\n                ${Object.keys(config.registries)\n                  .map((registry) => `- ${registry}`)\n                  .join(\"\\n\")}\n\n                You can view the items in a registry by running:\n                \\`${await npxShadcn(\"view @name-of-registry\")}\\`\n\n                For example: \\`${await npxShadcn(\n                  \"view @shadcn\"\n                )}\\` or \\`${await npxShadcn(\n                \"view @shadcn @acme\"\n              )}\\` to view multiple registries.\n                `,\n            },\n          ],\n        }\n      }\n\n      case \"search_items_in_registries\": {\n        const inputSchema = z.object({\n          registries: z.array(z.string()),\n          query: z.string(),\n          limit: z.number().optional(),\n          offset: z.number().optional(),\n        })\n\n        const args = inputSchema.parse(request.params.arguments)\n        const results = await searchRegistries(args.registries, {\n          query: args.query,\n          limit: args.limit,\n          offset: args.offset,\n          config: await getMcpConfig(process.cwd()),\n          useCache: false,\n        })\n\n        if (results.items.length === 0) {\n          return {\n            content: [\n              {\n                type: \"text\",\n                text: dedent`No items found matching \"${\n                  args.query\n                }\" in registries ${args.registries.join(\n                  \", \"\n                )}, Try searching with a different query or registry.`,\n              },\n            ],\n          }\n        }\n\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: formatSearchResultsWithPagination(results, {\n                query: args.query,\n                registries: args.registries,\n              }),\n            },\n          ],\n        }\n      }\n\n      case \"list_items_in_registries\": {\n        const inputSchema = z.object({\n          registries: z.array(z.string()),\n          limit: z.number().optional(),\n          offset: z.number().optional(),\n          cwd: z.string().optional(),\n        })\n\n        const args = inputSchema.parse(request.params.arguments)\n        const results = await searchRegistries(args.registries, {\n          limit: args.limit,\n          offset: args.offset,\n          config: await getMcpConfig(process.cwd()),\n          useCache: false,\n        })\n\n        if (results.items.length === 0) {\n          return {\n            content: [\n              {\n                type: \"text\",\n                text: dedent`No items found in registries ${args.registries.join(\n                  \", \"\n                )}.`,\n              },\n            ],\n          }\n        }\n\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: formatSearchResultsWithPagination(results, {\n                registries: args.registries,\n              }),\n            },\n          ],\n        }\n      }\n\n      case \"view_items_in_registries\": {\n        const inputSchema = z.object({\n          items: z.array(z.string()),\n        })\n\n        const args = inputSchema.parse(request.params.arguments)\n        const registryItems = await getRegistryItems(args.items, {\n          config: await getMcpConfig(process.cwd()),\n          useCache: false,\n        })\n\n        if (registryItems?.length === 0) {\n          return {\n            content: [\n              {\n                type: \"text\",\n                text: dedent`No items found for: ${args.items.join(\", \")}\n\n                Make sure the item names are correct and include the registry prefix (e.g., @shadcn/button).`,\n              },\n            ],\n          }\n        }\n\n        const formattedItems = formatRegistryItems(registryItems)\n\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: dedent`Item Details:\n\n              ${formattedItems.join(\"\\n\\n---\\n\\n\")}`,\n            },\n          ],\n        }\n      }\n\n      case \"get_item_examples_from_registries\": {\n        const inputSchema = z.object({\n          query: z.string(),\n          registries: z.array(z.string()),\n        })\n\n        const args = inputSchema.parse(request.params.arguments)\n        const config = await getMcpConfig()\n\n        const results = await searchRegistries(args.registries, {\n          query: args.query,\n          config,\n          useCache: false,\n        })\n\n        if (results.items.length === 0) {\n          return {\n            content: [\n              {\n                type: \"text\",\n                text: dedent`No examples found for query \"${args.query}\".\n\n                Try searching with patterns like:\n                - \"accordion-demo\" for accordion examples\n                - \"button demo\" or \"button example\"\n                - Component name followed by \"-demo\" or \"example\"\n\n                You can also:\n                1. Use search_items_in_registries to find all items matching your query\n                2. View the main component with view_items_in_registries for inline usage documentation`,\n              },\n            ],\n          }\n        }\n\n        const itemNames = results.items.map((item) => item.addCommandArgument)\n        const fullItems = await getRegistryItems(itemNames, {\n          config,\n          useCache: false,\n        })\n\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: formatItemExamples(fullItems, args.query),\n            },\n          ],\n        }\n      }\n\n      case \"get_add_command_for_items\": {\n        const args = z\n          .object({\n            items: z.array(z.string()),\n          })\n          .parse(request.params.arguments)\n\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: await npxShadcn(`add ${args.items.join(\" \")}`),\n            },\n          ],\n        }\n      }\n\n      case \"get_audit_checklist\": {\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: dedent`## Component Audit Checklist\n\n              After adding or generating components, check the following common issues:\n\n              - [ ] Ensure imports are correct i.e named vs default imports\n              - [ ] If using next/image, ensure images.remotePatterns next.config.js is configured correctly.\n              - [ ] Ensure all dependencies are installed.\n              - [ ] Check for linting errors or warnings\n              - [ ] Check for TypeScript errors\n              - [ ] Use the Playwright MCP if available.\n              `,\n            },\n          ],\n        }\n      }\n\n      default:\n        throw new Error(`Tool ${request.params.name} not found`)\n    }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return {\n        content: [\n          {\n            type: \"text\",\n            text: dedent`Invalid input parameters:\n              ${error.errors\n                .map((e) => `- ${e.path.join(\".\")}: ${e.message}`)\n                .join(\"\\n\")}\n              `,\n          },\n        ],\n        isError: true,\n      }\n    }\n\n    if (error instanceof RegistryError) {\n      let errorMessage = error.message\n\n      if (error.suggestion) {\n        errorMessage += `\\n\\n💡 ${error.suggestion}`\n      }\n\n      if (error.context) {\n        errorMessage += `\\n\\nContext: ${JSON.stringify(error.context, null, 2)}`\n      }\n\n      return {\n        content: [\n          {\n            type: \"text\",\n            text: dedent`Error (${error.code}): ${errorMessage}`,\n          },\n        ],\n        isError: true,\n      }\n    }\n\n    const errorMessage = error instanceof Error ? error.message : String(error)\n    return {\n      content: [\n        {\n          type: \"text\",\n          text: dedent`Error: ${errorMessage}`,\n        },\n      ],\n      isError: true,\n    }\n  }\n})\n"]}