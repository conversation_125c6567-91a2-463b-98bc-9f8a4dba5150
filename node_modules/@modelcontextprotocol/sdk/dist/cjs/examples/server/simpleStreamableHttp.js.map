{"version": 3, "file": "simpleStreamableHttp.js", "sourceRoot": "", "sources": ["../../../../src/examples/server/simpleStreamableHttp.ts"], "names": [], "mappings": ";;;;;AAAA,sDAAqD;AACrD,6CAAyC;AACzC,6BAAwB;AACxB,gDAAgD;AAChD,sEAA+E;AAC/E,2DAA0G;AAC1G,8EAA+E;AAC/E,6CAOwB;AACxB,2EAAqE;AACrE,iFAAiE;AAEjE,4DAAgE;AAEhE,gDAAwB;AAExB,uBAAuB;AACvB,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAClD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AAE5D,mDAAmD;AACnD,MAAM,SAAS,GAAG,GAAG,EAAE;IACnB,MAAM,MAAM,GAAG,IAAI,kBAAS,CACxB;QACI,IAAI,EAAE,+BAA+B;QACrC,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;QAC5E,UAAU,EAAE,wDAAwD;KACvE,EACD,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,CACpC,CAAC;IAEF,iDAAiD;IACjD,MAAM,CAAC,YAAY,CACf,OAAO,EACP;QACI,KAAK,EAAE,eAAe,EAAE,sBAAsB;QAC9C,WAAW,EAAE,wBAAwB;QACrC,WAAW,EAAE;YACT,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;SAC7C;KACJ,EACD,KAAK,EAAE,EAAE,IAAI,EAAE,EAA2B,EAAE;QACxC,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU,IAAI,GAAG;iBAC1B;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,sFAAsF;IACtF,MAAM,CAAC,IAAI,CACP,aAAa,EACb,gEAAgE,EAChE;QACI,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;KAC7C,EACD;QACI,KAAK,EAAE,wBAAwB;QAC/B,YAAY,EAAE,IAAI;QAClB,aAAa,EAAE,KAAK;KACvB,EACD,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAA2B,EAAE;QAC/C,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAE9E,MAAM,MAAM,CAAC,kBAAkB,CAC3B;YACI,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,4BAA4B,IAAI,EAAE;SAC3C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;QAEF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,sCAAsC;QAEzD,MAAM,MAAM,CAAC,kBAAkB,CAC3B;YACI,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,6BAA6B,IAAI,EAAE;SAC5C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;QAEF,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,6CAA6C;QAEhE,MAAM,MAAM,CAAC,kBAAkB,CAC3B;YACI,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,8BAA8B,IAAI,EAAE;SAC7C,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;QAEF,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,iBAAiB,IAAI,GAAG;iBACjC;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IACF,wEAAwE;IACxE,2DAA2D;IAC3D,MAAM,CAAC,IAAI,CACP,mBAAmB,EACnB,2DAA2D,EAC3D;QACI,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,gCAAgC,CAAC;KACtG,EACD,KAAK,EAAE,EAAE,QAAQ,EAAE,EAA2B,EAAE;QAC5C,IAAI,OAAe,CAAC;QACpB,IAAI,eAIH,CAAC;QAEF,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,SAAS;gBACV,OAAO,GAAG,yCAAyC,CAAC;gBACpD,eAAe,GAAG;oBACd,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,IAAI,EAAE;4BACF,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,WAAW;4BAClB,WAAW,EAAE,gBAAgB;yBAChC;wBACD,KAAK,EAAE;4BACH,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,eAAe;4BACtB,WAAW,EAAE,oBAAoB;4BACjC,MAAM,EAAE,OAAO;yBAClB;wBACD,KAAK,EAAE;4BACH,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,cAAc;4BACrB,WAAW,EAAE,8BAA8B;yBAC9C;qBACJ;oBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;iBAC9B,CAAC;gBACF,MAAM;YACV,KAAK,aAAa;gBACd,OAAO,GAAG,6BAA6B,CAAC;gBACxC,eAAe,GAAG;oBACd,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,KAAK,EAAE;4BACH,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,OAAO;4BACd,WAAW,EAAE,6BAA6B;4BAC1C,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;4BAC/B,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;yBACvC;wBACD,aAAa,EAAE;4BACX,IAAI,EAAE,SAAS;4BACf,KAAK,EAAE,sBAAsB;4BAC7B,WAAW,EAAE,0CAA0C;4BACvD,OAAO,EAAE,IAAI;yBAChB;wBACD,SAAS,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,wBAAwB;4BAC/B,WAAW,EAAE,yCAAyC;4BACtD,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;4BACpC,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;yBAC5C;qBACJ;oBACD,QAAQ,EAAE,CAAC,OAAO,CAAC;iBACtB,CAAC;gBACF,MAAM;YACV,KAAK,UAAU;gBACX,OAAO,GAAG,8BAA8B,CAAC;gBACzC,eAAe,GAAG;oBACd,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACR,MAAM,EAAE;4BACJ,IAAI,EAAE,SAAS;4BACf,KAAK,EAAE,QAAQ;4BACf,WAAW,EAAE,4BAA4B;4BACzC,OAAO,EAAE,CAAC;4BACV,OAAO,EAAE,CAAC;yBACb;wBACD,QAAQ,EAAE;4BACN,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,UAAU;4BACjB,WAAW,EAAE,gCAAgC;4BAC7C,SAAS,EAAE,GAAG;yBACjB;wBACD,SAAS,EAAE;4BACP,IAAI,EAAE,SAAS;4BACf,KAAK,EAAE,2BAA2B;4BAClC,WAAW,EAAE,qCAAqC;yBACrD;qBACJ;oBACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;iBACpC,CAAC;gBACF,MAAM;YACV;gBACI,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC;YACD,qEAAqE;YACrE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;gBAC3C,OAAO;gBACP,eAAe;aAClB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC7B,OAAO;oBACH,OAAO,EAAE;wBACL;4BACI,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,wBAAwB,QAAQ,iBAAiB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;yBACnG;qBACJ;iBACJ,CAAC;YACN,CAAC;iBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,OAAO;oBACH,OAAO,EAAE;wBACL;4BACI,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,+CAA+C,QAAQ,uBAAuB;yBACvF;qBACJ;iBACJ,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,OAAO;oBACH,OAAO,EAAE;wBACL;4BACI,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,mDAAmD;yBAC5D;qBACJ;iBACJ,CAAC;YACN,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,oBAAoB,QAAQ,iBAAiB,KAAK,EAAE;qBAC7D;iBACJ;aACJ,CAAC;QACN,CAAC;IACL,CAAC,CACJ,CAAC;IAEF,sCAAsC;IACtC,MAAM,CAAC,cAAc,CACjB,mBAAmB,EACnB;QACI,KAAK,EAAE,mBAAmB,EAAE,sBAAsB;QAClD,WAAW,EAAE,mCAAmC;QAChD,UAAU,EAAE;YACR,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;SAC3D;KACJ,EACD,KAAK,EAAE,EAAE,IAAI,EAAE,EAA4B,EAAE;QACzC,OAAO;YACH,QAAQ,EAAE;gBACN;oBACI,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACL,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,gBAAgB,IAAI,wBAAwB;qBACrD;iBACJ;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,wDAAwD;IACxD,MAAM,CAAC,IAAI,CACP,2BAA2B,EAC3B,gEAAgE,EAChE;QACI,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gDAAgD,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;QAC5F,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6CAA6C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;KACxF,EACD,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,KAAK,EAA2B,EAAE;QAC1D,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9E,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,GAAG,KAAK,EAAE,CAAC;YACpC,OAAO,EAAE,CAAC;YACV,IAAI,CAAC;gBACD,MAAM,MAAM,CAAC,kBAAkB,CAC3B;oBACI,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE,0BAA0B,OAAO,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;iBAC3E,EACD,KAAK,CAAC,SAAS,CAClB,CAAC;YACN,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;YACD,kCAAkC;YAClC,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,gDAAgD,QAAQ,IAAI;iBACrE;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,0CAA0C;IAC1C,MAAM,CAAC,gBAAgB,CACnB,mBAAmB,EACnB,uCAAuC,EACvC;QACI,KAAK,EAAE,kBAAkB,EAAE,sBAAsB;QACjD,WAAW,EAAE,4BAA4B;QACzC,QAAQ,EAAE,YAAY;KACzB,EACD,KAAK,IAAiC,EAAE;QACpC,OAAO;YACH,QAAQ,EAAE;gBACN;oBACI,GAAG,EAAE,uCAAuC;oBAC5C,IAAI,EAAE,eAAe;iBACxB;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,6DAA6D;IAC7D,MAAM,CAAC,gBAAgB,CACnB,gBAAgB,EAChB,2BAA2B,EAC3B;QACI,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,mDAAmD;QAChE,QAAQ,EAAE,YAAY;KACzB,EACD,KAAK,IAAiC,EAAE;QACpC,OAAO;YACH,QAAQ,EAAE;gBACN;oBACI,GAAG,EAAE,2BAA2B;oBAChC,IAAI,EAAE,+BAA+B;iBACxC;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,MAAM,CAAC,gBAAgB,CACnB,gBAAgB,EAChB,2BAA2B,EAC3B;QACI,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,oDAAoD;QACjE,QAAQ,EAAE,YAAY;KACzB,EACD,KAAK,IAAiC,EAAE;QACpC,OAAO;YACH,QAAQ,EAAE;gBACN;oBACI,GAAG,EAAE,2BAA2B;oBAChC,IAAI,EAAE,+BAA+B;iBACxC;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,6CAA6C;IAC7C,MAAM,CAAC,YAAY,CACf,YAAY,EACZ;QACI,KAAK,EAAE,+BAA+B;QACtC,WAAW,EAAE,0EAA0E;QACvF,WAAW,EAAE;YACT,mBAAmB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uDAAuD,CAAC;SAChH;KACJ,EACD,KAAK,EAAE,EAAE,mBAAmB,GAAG,IAAI,EAAE,EAA2B,EAAE;QAC9D,MAAM,aAAa,GAAmB;YAClC;gBACI,IAAI,EAAE,eAAe;gBACrB,GAAG,EAAE,uCAAuC;gBAC5C,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,YAAY;gBACtB,GAAG,CAAC,mBAAmB,IAAI,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;aAC5E;YACD;gBACI,IAAI,EAAE,eAAe;gBACrB,GAAG,EAAE,2BAA2B;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,YAAY;gBACtB,GAAG,CAAC,mBAAmB,IAAI,EAAE,WAAW,EAAE,mDAAmD,EAAE,CAAC;aACnG;YACD;gBACI,IAAI,EAAE,eAAe;gBACrB,GAAG,EAAE,2BAA2B;gBAChC,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,YAAY;gBACtB,GAAG,CAAC,mBAAmB,IAAI,EAAE,WAAW,EAAE,oDAAoD,EAAE,CAAC;aACpG;SACJ,CAAC;QAEF,OAAO;YACH,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,iDAAiD;iBAC1D;gBACD,GAAG,aAAa;gBAChB;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,wDAAwD;iBACjE;aACJ;SACJ,CAAC;IACN,CAAC,CACJ,CAAC;IAEF,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAClF,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAE7F,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AACtB,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAExB,2DAA2D;AAC3D,GAAG,CAAC,GAAG,CACH,IAAA,cAAI,EAAC;IACD,MAAM,EAAE,GAAG,EAAE,oBAAoB;IACjC,cAAc,EAAE,CAAC,gBAAgB,CAAC;CACrC,CAAC,CACL,CAAC;AAEF,0BAA0B;AAC1B,IAAI,cAAc,GAAG,IAAI,CAAC;AAC1B,IAAI,QAAQ,EAAE,CAAC;IACX,2CAA2C;IAC3C,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,oBAAoB,QAAQ,MAAM,CAAC,CAAC;IACjE,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,oBAAoB,SAAS,EAAE,CAAC,CAAC;IAE/D,MAAM,aAAa,GAAkB,IAAA,8CAAe,EAAC,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC;IAEnH,MAAM,aAAa,GAAG;QAClB,iBAAiB,EAAE,KAAK,EAAE,KAAa,EAAE,EAAE;YACvC,MAAM,QAAQ,GAAG,aAAa,CAAC,sBAAsB,CAAC;YAEtD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE;gBACnC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,cAAc,EAAE,mCAAmC;iBACtD;gBACD,IAAI,EAAE,IAAI,eAAe,CAAC;oBACtB,KAAK,EAAE,KAAK;iBACf,CAAC,CAAC,QAAQ,EAAE;aAChB,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,IAAI,WAAW,EAAE,CAAC;gBACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACZ,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,CAAC,IAAA,oCAAoB,EAAC,EAAE,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,kBAAkB,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;oBAC3F,MAAM,IAAI,KAAK,CAAC,+BAA+B,YAAY,UAAU,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBACrF,CAAC;YACL,CAAC;YAED,0CAA0C;YAC1C,OAAO;gBACH,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC/C,SAAS,EAAE,IAAI,CAAC,GAAG;aACtB,CAAC;QACN,CAAC;KACJ,CAAC;IACF,6CAA6C;IAC7C,GAAG,CAAC,GAAG,CACH,IAAA,iCAAqB,EAAC;QAClB,aAAa;QACb,iBAAiB,EAAE,YAAY;QAC/B,eAAe,EAAE,CAAC,WAAW,CAAC;QAC9B,YAAY,EAAE,iBAAiB;KAClC,CAAC,CACL,CAAC;IAEF,cAAc,GAAG,IAAA,iCAAiB,EAAC;QAC/B,QAAQ,EAAE,aAAa;QACvB,cAAc,EAAE,EAAE;QAClB,mBAAmB,EAAE,IAAA,gDAAoC,EAAC,YAAY,CAAC;KAC1E,CAAC,CAAC;AACP,CAAC;AAED,wCAAwC;AACxC,MAAM,UAAU,GAA2D,EAAE,CAAC;AAE9E,uCAAuC;AACvC,MAAM,cAAc,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACzD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;IACtE,IAAI,SAAS,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,CAAC,qCAAqC,SAAS,EAAE,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACJ,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IACD,IAAI,CAAC;QACD,IAAI,SAAwC,CAAC;QAC7C,IAAI,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,2BAA2B;YAC3B,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,CAAC,SAAS,IAAI,IAAA,8BAAmB,EAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,6BAA6B;YAC7B,MAAM,UAAU,GAAG,IAAI,0CAAkB,EAAE,CAAC;YAC5C,SAAS,GAAG,IAAI,iDAA6B,CAAC;gBAC1C,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAU,GAAE;gBACtC,UAAU,EAAE,sBAAsB;gBAClC,oBAAoB,EAAE,SAAS,CAAC,EAAE;oBAC9B,gEAAgE;oBAChE,wFAAwF;oBACxF,OAAO,CAAC,GAAG,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;oBACzD,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;gBACtC,CAAC;aACJ,CAAC,CAAC;YAEH,2DAA2D;YAC3D,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;gBACrB,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC;gBAChC,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACzB,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,gCAAgC,CAAC,CAAC;oBACjF,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC;YACL,CAAC,CAAC;YAEF,sEAAsE;YACtE,wDAAwD;YACxD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YAClD,OAAO,CAAC,kBAAkB;QAC9B,CAAC;aAAM,CAAC;YACJ,gEAAgE;YAChE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,2CAA2C;iBACvD;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;YACH,OAAO;QACX,CAAC;QAED,oEAAoE;QACpE,4DAA4D;QAC5D,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACH,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,uBAAuB;iBACnC;gBACD,EAAE,EAAE,IAAI;aACX,CAAC,CAAC;QACP,CAAC;IACL,CAAC;AACL,CAAC,CAAC;AAEF,iDAAiD;AACjD,IAAI,QAAQ,IAAI,cAAc,EAAE,CAAC;IAC7B,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;AACrD,CAAC;KAAM,CAAC;IACJ,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AACrC,CAAC;AAED,mFAAmF;AACnF,MAAM,aAAa,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACxD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;IACtE,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACvC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACtD,OAAO;IACX,CAAC;IAED,IAAI,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC;IAED,kDAAkD;IAClD,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAuB,CAAC;IACvE,IAAI,WAAW,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,2CAA2C,WAAW,EAAE,CAAC,CAAC;IAC1E,CAAC;SAAM,CAAC;QACJ,OAAO,CAAC,GAAG,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;IACxC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEF,oDAAoD;AACpD,IAAI,QAAQ,IAAI,cAAc,EAAE,CAAC;IAC7B,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;AACnD,CAAC;KAAM,CAAC;IACJ,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AACnC,CAAC;AAED,yEAAyE;AACzE,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC3D,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;IACtE,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACvC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACtD,OAAO;IACX,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,oDAAoD,SAAS,EAAE,CAAC,CAAC;IAE7E,IAAI,CAAC;QACD,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QACxC,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;AACL,CAAC,CAAC;AAEF,uDAAuD;AACvD,IAAI,QAAQ,IAAI,cAAc,EAAE,CAAC;IAC7B,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;AACzD,CAAC;KAAM,CAAC;IACJ,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;AACzC,CAAC;AAED,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;IACzB,IAAI,KAAK,EAAE,CAAC;QACR,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,gDAAgD,QAAQ,EAAE,CAAC,CAAC;AAC5E,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACzB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;IAC5B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAEvC,6DAA6D;IAC7D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;YAC1D,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;YACpC,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uCAAuC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IACxC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC"}