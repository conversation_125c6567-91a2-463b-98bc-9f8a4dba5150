export declare const mainSymbols: {
    tick: string;
    info: string;
    warning: string;
    cross: string;
    squareSmall: string;
    squareSmallFilled: string;
    circle: string;
    circleFilled: string;
    circleDotted: string;
    circleDouble: string;
    circleCircle: string;
    circleCross: string;
    circlePipe: string;
    radioOn: string;
    radioOff: string;
    checkboxOn: string;
    checkboxOff: string;
    checkboxCircleOn: string;
    checkboxCircleOff: string;
    pointer: string;
    triangleUpOutline: string;
    triangleLeft: string;
    triangleRight: string;
    lozenge: string;
    lozengeOutline: string;
    hamburger: string;
    smiley: string;
    mustache: string;
    star: string;
    play: string;
    nodejs: string;
    oneSeventh: string;
    oneNinth: string;
    oneTenth: string;
    circleQuestionMark: string;
    questionMarkPrefix: string;
    square: string;
    squareDarkShade: string;
    squareMediumShade: string;
    squareLightShade: string;
    squareTop: string;
    squareBottom: string;
    squareLeft: string;
    squareRight: string;
    squareCenter: string;
    bullet: string;
    dot: string;
    ellipsis: string;
    pointerSmall: string;
    triangleUp: string;
    triangleUpSmall: string;
    triangleDown: string;
    triangleDownSmall: string;
    triangleLeftSmall: string;
    triangleRightSmall: string;
    home: string;
    heart: string;
    musicNote: string;
    musicNoteBeamed: string;
    arrowUp: string;
    arrowDown: string;
    arrowLeft: string;
    arrowRight: string;
    arrowLeftRight: string;
    arrowUpDown: string;
    almostEqual: string;
    notEqual: string;
    lessOrEqual: string;
    greaterOrEqual: string;
    identical: string;
    infinity: string;
    subscriptZero: string;
    subscriptOne: string;
    subscriptTwo: string;
    subscriptThree: string;
    subscriptFour: string;
    subscriptFive: string;
    subscriptSix: string;
    subscriptSeven: string;
    subscriptEight: string;
    subscriptNine: string;
    oneHalf: string;
    oneThird: string;
    oneQuarter: string;
    oneFifth: string;
    oneSixth: string;
    oneEighth: string;
    twoThirds: string;
    twoFifths: string;
    threeQuarters: string;
    threeFifths: string;
    threeEighths: string;
    fourFifths: string;
    fiveSixths: string;
    fiveEighths: string;
    sevenEighths: string;
    line: string;
    lineBold: string;
    lineDouble: string;
    lineDashed0: string;
    lineDashed1: string;
    lineDashed2: string;
    lineDashed3: string;
    lineDashed4: string;
    lineDashed5: string;
    lineDashed6: string;
    lineDashed7: string;
    lineDashed8: string;
    lineDashed9: string;
    lineDashed10: string;
    lineDashed11: string;
    lineDashed12: string;
    lineDashed13: string;
    lineDashed14: string;
    lineDashed15: string;
    lineVertical: string;
    lineVerticalBold: string;
    lineVerticalDouble: string;
    lineVerticalDashed0: string;
    lineVerticalDashed1: string;
    lineVerticalDashed2: string;
    lineVerticalDashed3: string;
    lineVerticalDashed4: string;
    lineVerticalDashed5: string;
    lineVerticalDashed6: string;
    lineVerticalDashed7: string;
    lineVerticalDashed8: string;
    lineVerticalDashed9: string;
    lineVerticalDashed10: string;
    lineVerticalDashed11: string;
    lineDownLeft: string;
    lineDownLeftArc: string;
    lineDownBoldLeftBold: string;
    lineDownBoldLeft: string;
    lineDownLeftBold: string;
    lineDownDoubleLeftDouble: string;
    lineDownDoubleLeft: string;
    lineDownLeftDouble: string;
    lineDownRight: string;
    lineDownRightArc: string;
    lineDownBoldRightBold: string;
    lineDownBoldRight: string;
    lineDownRightBold: string;
    lineDownDoubleRightDouble: string;
    lineDownDoubleRight: string;
    lineDownRightDouble: string;
    lineUpLeft: string;
    lineUpLeftArc: string;
    lineUpBoldLeftBold: string;
    lineUpBoldLeft: string;
    lineUpLeftBold: string;
    lineUpDoubleLeftDouble: string;
    lineUpDoubleLeft: string;
    lineUpLeftDouble: string;
    lineUpRight: string;
    lineUpRightArc: string;
    lineUpBoldRightBold: string;
    lineUpBoldRight: string;
    lineUpRightBold: string;
    lineUpDoubleRightDouble: string;
    lineUpDoubleRight: string;
    lineUpRightDouble: string;
    lineUpDownLeft: string;
    lineUpBoldDownBoldLeftBold: string;
    lineUpBoldDownBoldLeft: string;
    lineUpDownLeftBold: string;
    lineUpBoldDownLeftBold: string;
    lineUpDownBoldLeftBold: string;
    lineUpDownBoldLeft: string;
    lineUpBoldDownLeft: string;
    lineUpDoubleDownDoubleLeftDouble: string;
    lineUpDoubleDownDoubleLeft: string;
    lineUpDownLeftDouble: string;
    lineUpDownRight: string;
    lineUpBoldDownBoldRightBold: string;
    lineUpBoldDownBoldRight: string;
    lineUpDownRightBold: string;
    lineUpBoldDownRightBold: string;
    lineUpDownBoldRightBold: string;
    lineUpDownBoldRight: string;
    lineUpBoldDownRight: string;
    lineUpDoubleDownDoubleRightDouble: string;
    lineUpDoubleDownDoubleRight: string;
    lineUpDownRightDouble: string;
    lineDownLeftRight: string;
    lineDownBoldLeftBoldRightBold: string;
    lineDownLeftBoldRightBold: string;
    lineDownBoldLeftRight: string;
    lineDownBoldLeftBoldRight: string;
    lineDownBoldLeftRightBold: string;
    lineDownLeftRightBold: string;
    lineDownLeftBoldRight: string;
    lineDownDoubleLeftDoubleRightDouble: string;
    lineDownDoubleLeftRight: string;
    lineDownLeftDoubleRightDouble: string;
    lineUpLeftRight: string;
    lineUpBoldLeftBoldRightBold: string;
    lineUpLeftBoldRightBold: string;
    lineUpBoldLeftRight: string;
    lineUpBoldLeftBoldRight: string;
    lineUpBoldLeftRightBold: string;
    lineUpLeftRightBold: string;
    lineUpLeftBoldRight: string;
    lineUpDoubleLeftDoubleRightDouble: string;
    lineUpDoubleLeftRight: string;
    lineUpLeftDoubleRightDouble: string;
    lineUpDownLeftRight: string;
    lineUpBoldDownBoldLeftBoldRightBold: string;
    lineUpDownBoldLeftBoldRightBold: string;
    lineUpBoldDownLeftBoldRightBold: string;
    lineUpBoldDownBoldLeftRightBold: string;
    lineUpBoldDownBoldLeftBoldRight: string;
    lineUpBoldDownLeftRight: string;
    lineUpDownBoldLeftRight: string;
    lineUpDownLeftBoldRight: string;
    lineUpDownLeftRightBold: string;
    lineUpBoldDownBoldLeftRight: string;
    lineUpDownLeftBoldRightBold: string;
    lineUpBoldDownLeftBoldRight: string;
    lineUpBoldDownLeftRightBold: string;
    lineUpDownBoldLeftBoldRight: string;
    lineUpDownBoldLeftRightBold: string;
    lineUpDoubleDownDoubleLeftDoubleRightDouble: string;
    lineUpDoubleDownDoubleLeftRight: string;
    lineUpDownLeftDoubleRightDouble: string;
    lineCross: string;
    lineBackslash: string;
    lineSlash: string;
};
export declare const fallbackSymbols: Record<string, string>;
declare const figures: {
    tick: string;
    info: string;
    warning: string;
    cross: string;
    squareSmall: string;
    squareSmallFilled: string;
    circle: string;
    circleFilled: string;
    circleDotted: string;
    circleDouble: string;
    circleCircle: string;
    circleCross: string;
    circlePipe: string;
    radioOn: string;
    radioOff: string;
    checkboxOn: string;
    checkboxOff: string;
    checkboxCircleOn: string;
    checkboxCircleOff: string;
    pointer: string;
    triangleUpOutline: string;
    triangleLeft: string;
    triangleRight: string;
    lozenge: string;
    lozengeOutline: string;
    hamburger: string;
    smiley: string;
    mustache: string;
    star: string;
    play: string;
    nodejs: string;
    oneSeventh: string;
    oneNinth: string;
    oneTenth: string;
    circleQuestionMark: string;
    questionMarkPrefix: string;
    square: string;
    squareDarkShade: string;
    squareMediumShade: string;
    squareLightShade: string;
    squareTop: string;
    squareBottom: string;
    squareLeft: string;
    squareRight: string;
    squareCenter: string;
    bullet: string;
    dot: string;
    ellipsis: string;
    pointerSmall: string;
    triangleUp: string;
    triangleUpSmall: string;
    triangleDown: string;
    triangleDownSmall: string;
    triangleLeftSmall: string;
    triangleRightSmall: string;
    home: string;
    heart: string;
    musicNote: string;
    musicNoteBeamed: string;
    arrowUp: string;
    arrowDown: string;
    arrowLeft: string;
    arrowRight: string;
    arrowLeftRight: string;
    arrowUpDown: string;
    almostEqual: string;
    notEqual: string;
    lessOrEqual: string;
    greaterOrEqual: string;
    identical: string;
    infinity: string;
    subscriptZero: string;
    subscriptOne: string;
    subscriptTwo: string;
    subscriptThree: string;
    subscriptFour: string;
    subscriptFive: string;
    subscriptSix: string;
    subscriptSeven: string;
    subscriptEight: string;
    subscriptNine: string;
    oneHalf: string;
    oneThird: string;
    oneQuarter: string;
    oneFifth: string;
    oneSixth: string;
    oneEighth: string;
    twoThirds: string;
    twoFifths: string;
    threeQuarters: string;
    threeFifths: string;
    threeEighths: string;
    fourFifths: string;
    fiveSixths: string;
    fiveEighths: string;
    sevenEighths: string;
    line: string;
    lineBold: string;
    lineDouble: string;
    lineDashed0: string;
    lineDashed1: string;
    lineDashed2: string;
    lineDashed3: string;
    lineDashed4: string;
    lineDashed5: string;
    lineDashed6: string;
    lineDashed7: string;
    lineDashed8: string;
    lineDashed9: string;
    lineDashed10: string;
    lineDashed11: string;
    lineDashed12: string;
    lineDashed13: string;
    lineDashed14: string;
    lineDashed15: string;
    lineVertical: string;
    lineVerticalBold: string;
    lineVerticalDouble: string;
    lineVerticalDashed0: string;
    lineVerticalDashed1: string;
    lineVerticalDashed2: string;
    lineVerticalDashed3: string;
    lineVerticalDashed4: string;
    lineVerticalDashed5: string;
    lineVerticalDashed6: string;
    lineVerticalDashed7: string;
    lineVerticalDashed8: string;
    lineVerticalDashed9: string;
    lineVerticalDashed10: string;
    lineVerticalDashed11: string;
    lineDownLeft: string;
    lineDownLeftArc: string;
    lineDownBoldLeftBold: string;
    lineDownBoldLeft: string;
    lineDownLeftBold: string;
    lineDownDoubleLeftDouble: string;
    lineDownDoubleLeft: string;
    lineDownLeftDouble: string;
    lineDownRight: string;
    lineDownRightArc: string;
    lineDownBoldRightBold: string;
    lineDownBoldRight: string;
    lineDownRightBold: string;
    lineDownDoubleRightDouble: string;
    lineDownDoubleRight: string;
    lineDownRightDouble: string;
    lineUpLeft: string;
    lineUpLeftArc: string;
    lineUpBoldLeftBold: string;
    lineUpBoldLeft: string;
    lineUpLeftBold: string;
    lineUpDoubleLeftDouble: string;
    lineUpDoubleLeft: string;
    lineUpLeftDouble: string;
    lineUpRight: string;
    lineUpRightArc: string;
    lineUpBoldRightBold: string;
    lineUpBoldRight: string;
    lineUpRightBold: string;
    lineUpDoubleRightDouble: string;
    lineUpDoubleRight: string;
    lineUpRightDouble: string;
    lineUpDownLeft: string;
    lineUpBoldDownBoldLeftBold: string;
    lineUpBoldDownBoldLeft: string;
    lineUpDownLeftBold: string;
    lineUpBoldDownLeftBold: string;
    lineUpDownBoldLeftBold: string;
    lineUpDownBoldLeft: string;
    lineUpBoldDownLeft: string;
    lineUpDoubleDownDoubleLeftDouble: string;
    lineUpDoubleDownDoubleLeft: string;
    lineUpDownLeftDouble: string;
    lineUpDownRight: string;
    lineUpBoldDownBoldRightBold: string;
    lineUpBoldDownBoldRight: string;
    lineUpDownRightBold: string;
    lineUpBoldDownRightBold: string;
    lineUpDownBoldRightBold: string;
    lineUpDownBoldRight: string;
    lineUpBoldDownRight: string;
    lineUpDoubleDownDoubleRightDouble: string;
    lineUpDoubleDownDoubleRight: string;
    lineUpDownRightDouble: string;
    lineDownLeftRight: string;
    lineDownBoldLeftBoldRightBold: string;
    lineDownLeftBoldRightBold: string;
    lineDownBoldLeftRight: string;
    lineDownBoldLeftBoldRight: string;
    lineDownBoldLeftRightBold: string;
    lineDownLeftRightBold: string;
    lineDownLeftBoldRight: string;
    lineDownDoubleLeftDoubleRightDouble: string;
    lineDownDoubleLeftRight: string;
    lineDownLeftDoubleRightDouble: string;
    lineUpLeftRight: string;
    lineUpBoldLeftBoldRightBold: string;
    lineUpLeftBoldRightBold: string;
    lineUpBoldLeftRight: string;
    lineUpBoldLeftBoldRight: string;
    lineUpBoldLeftRightBold: string;
    lineUpLeftRightBold: string;
    lineUpLeftBoldRight: string;
    lineUpDoubleLeftDoubleRightDouble: string;
    lineUpDoubleLeftRight: string;
    lineUpLeftDoubleRightDouble: string;
    lineUpDownLeftRight: string;
    lineUpBoldDownBoldLeftBoldRightBold: string;
    lineUpDownBoldLeftBoldRightBold: string;
    lineUpBoldDownLeftBoldRightBold: string;
    lineUpBoldDownBoldLeftRightBold: string;
    lineUpBoldDownBoldLeftBoldRight: string;
    lineUpBoldDownLeftRight: string;
    lineUpDownBoldLeftRight: string;
    lineUpDownLeftBoldRight: string;
    lineUpDownLeftRightBold: string;
    lineUpBoldDownBoldLeftRight: string;
    lineUpDownLeftBoldRightBold: string;
    lineUpBoldDownLeftBoldRight: string;
    lineUpBoldDownLeftRightBold: string;
    lineUpDownBoldLeftBoldRight: string;
    lineUpDownBoldLeftRightBold: string;
    lineUpDoubleDownDoubleLeftDoubleRightDouble: string;
    lineUpDoubleDownDoubleLeftRight: string;
    lineUpDownLeftDoubleRightDouble: string;
    lineCross: string;
    lineBackslash: string;
    lineSlash: string;
} | Record<string, string>;
export default figures;
export declare const replaceSymbols: (string: string, { useFallback }?: {
    useFallback?: boolean | undefined;
}) => string;
