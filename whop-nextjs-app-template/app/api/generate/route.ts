import { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { agentType, dnaProfile, generalInput } = await request.json();

    console.log('API Request:', { agentType, dnaProfile, generalInput });

    if (!agentType || !dnaProfile) {
      return new Response(
        JSON.stringify({ error: 'Agent type and DNA profile are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const geminiApiKey = process.env.GEMINI_API_KEY;
    if (!geminiApiKey) {
      console.error('Gemini API key not found');
      return new Response(
        JSON.stringify({ error: 'Gemini API key not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Create a prompt based on the agent type and DNA profile
    let systemInstruction = '';
    let userPrompt = '';

    switch (agentType) {
      case 'ideal-client-profile':
        systemInstruction = `You are an expert marketing strategist specializing in creating detailed Ideal Client Profiles (ICPs). Create a comprehensive ICP based on the provided DNA profile and any additional context.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate a detailed Ideal Client Profile including demographics, main problems, emotions, fears, and secret desires.`;
        break;
      case 'irresistible-offer':
        systemInstruction = `You are a copywriting expert specializing in creating irresistible offers. Create compelling offer structures based on the provided DNA profile.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate an irresistible offer structure with problem, promise, solution, delivery, and enhancements.`;
        break;
      case 'vsl-maker':
        systemInstruction = `You are a video sales letter expert. Create structured VSL scripts based on the DNA profile.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate a video sales letter script with introduction, problem, solution, mechanism, product presentation, offer, and CTA.`;
        break;
      default:
        systemInstruction = `You are an expert copywriter. Create high-converting content based on the provided DNA profile.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate compelling content based on this DNA profile.`;
    }

    console.log('Sending request to Gemini API...');

    // Use Gemini API with streaming
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:streamGenerateContent?key=${geminiApiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: userPrompt
            }]
          }],
          systemInstruction: {
            parts: [{ text: systemInstruction }]
          },
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2000,
          }
        })
      }
    );

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch {
        errorData = await response.text();
      }
      console.error('Gemini API Error:', errorData);
      throw new Error(`Gemini API error: ${response.status} - ${JSON.stringify(errorData)}`);
    }

    // Check if response is actually streaming
    const contentType = response.headers.get('content-type');
    if (!contentType?.includes('text/event-stream') && !contentType?.includes('application/x-ndjson')) {
      // Non-streaming response - handle as JSON
      const data = await response.json();
      const text = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
      return new Response(`data: ${JSON.stringify({ text })}\n\n`, {
        headers: {
          'Content-Type': 'text/event-stream',
        },
      });
    }

    // Create a readable stream for the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          controller.close();
          return;
        }

        try {
          let buffer = '';
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
              controller.close();
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim() === '') continue;
              
              try {
                // Gemini returns ndjson format, each line is a JSON object
                const data = JSON.parse(line);
                const textPart = data.candidates?.[0]?.content?.parts?.[0]?.text;
                if (textPart) {
                  controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ text: textPart })}\n\n`));
                }
              } catch (e) {
                // Try parsing as SSE format (data: prefix)
                if (line.startsWith('data: ')) {
                  try {
                    const data = JSON.parse(line.slice(6));
                    const textPart = data.candidates?.[0]?.content?.parts?.[0]?.text;
                    if (textPart) {
                      controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ text: textPart })}\n\n`));
                    }
                  } catch (e2) {
                    // Skip invalid JSON
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream error:', error);
          controller.error(error);
        } finally {
          reader.releaseLock();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Gemini API Error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate content',
        details: errorMessage,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
