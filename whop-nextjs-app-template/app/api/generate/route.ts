import { NextRequest } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

export async function POST(request: NextRequest) {
  try {
    const { agentType, dnaProfile, generalInput } = await request.json();

    console.log('API Request:', { agentType, dnaProfile, generalInput });

    if (!agentType || !dnaProfile) {
      return new Response(
        JSON.stringify({ error: 'Agent type and DNA profile are required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const geminiApiKey = process.env.GEMINI_API_KEY;
    if (!geminiApiKey) {
      console.error('Gemini API key not found');
      return new Response(
        JSON.stringify({ error: 'Gemini API key not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Create a prompt based on the agent type and DNA profile
    let systemInstruction = '';
    let userPrompt = '';

    switch (agentType) {
      case 'ideal-client-profile':
        systemInstruction = `You are an expert marketing strategist specializing in creating detailed Ideal Client Profiles (ICPs). Create a comprehensive ICP based on the provided DNA profile and any additional context.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate a detailed Ideal Client Profile including demographics, main problems, emotions, fears, and secret desires.`;
        break;
      case 'irresistible-offer':
        systemInstruction = `You are a copywriting expert specializing in creating irresistible offers. Create compelling offer structures based on the provided DNA profile.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate an irresistible offer structure with problem, promise, solution, delivery, and enhancements.`;
        break;
      case 'vsl-maker':
        systemInstruction = `You are a video sales letter expert. Create structured VSL scripts based on the DNA profile.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate a video sales letter script with introduction, problem, solution, mechanism, product presentation, offer, and CTA.`;
        break;
      default:
        systemInstruction = `You are an expert copywriter. Create high-converting content based on the provided DNA profile.`;
        userPrompt = `DNA Profile: ${dnaProfile}\n\nAdditional Context: ${generalInput || 'None'}\n\nCreate compelling content based on this DNA profile.`;
    }

    console.log('Sending request to Gemini API...');

    // Initialize the Google Generative AI client
    const genAI = new GoogleGenerativeAI(geminiApiKey);

    // Get the generative model - using the correct model name
    // Try gemini-pro first (most commonly available)
    const model = genAI.getGenerativeModel({
      model: "gemini-pro",
      systemInstruction: systemInstruction
    });

    // Generate content with streaming
    const result = await model.generateContentStream(userPrompt);
    
    // Create a readable stream for the response
    const stream = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of result.stream) {
            const chunkText = chunk.text();
            if (chunkText) {
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ text: chunkText })}\n\n`));
            }
          }
          controller.close();
        } catch (error) {
          console.error('Stream error:', error);
          controller.error(error);
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });



  } catch (error) {
    console.error('Gemini API Error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate content',
        details: errorMessage,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
