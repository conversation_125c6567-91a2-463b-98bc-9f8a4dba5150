"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Field,
  FieldDescription,
  FieldGroup,
  FieldLabel,
  FieldLegend,
  FieldSeparator,
  FieldSet,
} from "@/components/ui/field";
import { 
  ArrowLeft, 
  Save, 
  Target, 
  Users, 
  Zap, 
  CheckCircle,
  Plus,
  Trash2,
  ChevronRight,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  Mic
} from "lucide-react";
import Link from "next/link";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

export default function DNABuilder() {
  const [expandedSections, setExpandedSections] = useState<number[]>([1]);
  const [showInstructions, setShowInstructions] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    company: "",
    role: "",
    biography: "",
    achievements: "",
    idealClient: "",
    targetDemographics: "",
    painPoints: "",
    desiredOutcomes: "",
    tone: "",
    buyingProfiles: [
      { label: "", motivations: "", fears: "", objections: "" },
      { label: "", motivations: "", fears: "", objections: "" },
      { label: "", motivations: "", fears: "", objections: "" }
    ]
  });

  const toneOptions = [
    "Data-driven", "Strategic", "Direct", "Friendly", "Authoritative", 
    "Story-driven", "Hype", "Professional", "Witty", "Analytical"
  ];

  const sections = [
    {
      id: 1,
      title: "Author Biography",
      description: "Fill in below with basic information about you, your company, and at least 3 main achievements related to your work (number of clients, achievements, awards, TV appearances, etc.).",
      fields: [
        { id: "name", label: "Your Name", type: "input", placeholder: "e.g., Felix" },
        { id: "company", label: "Company Name", type: "input", placeholder: "e.g., Squadrax" },
        { id: "role", label: "Your Role", type: "input", placeholder: "e.g., Founder, CEO" },
        { id: "biography", label: "Biography / Context", type: "textarea", placeholder: "Describe your background, what you do, and your expertise..." },
        { id: "achievements", label: "Achievements / Authority Points", type: "textarea", placeholder: "List your key achievements, certifications, results, etc..." }
      ]
    },
    {
      id: 2,
      title: "Ideal Client Profile",
      description: "Here is where you describe your ideal customer in detail. This information will feed almost all the agents. Remember to first use the High-Value Customer Compass to find better customers for your business... and then understand them better using the Ideal Customer Profile agent.",
      fields: [
        { id: "idealClient", label: "Ideal Client Profile", type: "textarea", placeholder: "e.g., Shopify brand founders with 1M+ annual revenue..." },
        { id: "targetDemographics", label: "Target Demographics", type: "textarea", placeholder: "Age, location, income level, industry, etc..." }
      ]
    },
    {
      id: 3,
      title: "Author/Brand Voice",
      description: "Define your brand's unique voice and tone for AI-powered content generation.",
      fields: [
        { id: "tone", label: "Brand Voice Settings", type: "select", options: toneOptions, placeholder: "" }
      ]
    },
    {
      id: 4,
      title: "Buying Profile",
      description: "Define the different types of customers you target with their motivations, fears, and objections.",
      fields: [
        { id: "buyingProfiles", label: "Buying Profiles (3 Archetypes)", type: "profiles", placeholder: "" }
      ]
    },
    {
      id: 5,
      title: "The Persuasive Premise",
      description: "Define your core persuasive premise and messaging framework.",
      fields: [
        { id: "painPoints", label: "Problems / Pain Points", type: "textarea", placeholder: "What challenges does your ideal client face?" },
        { id: "desiredOutcomes", label: "Desired Outcomes", type: "textarea", placeholder: "What results do they want to achieve?" }
      ]
    },
    {
      id: 6,
      title: "False beliefs",
      description: "Identify and address common false beliefs your audience might have.",
      fields: [
        { id: "falseBeliefs", label: "False Beliefs", type: "textarea", placeholder: "What misconceptions do your ideal clients have?" }
      ]
    }
  ];

  const toggleSection = (sectionId: number) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleInputChange = (fieldId: string, value: string) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
  };

  const handleBuyingProfileChange = (index: number, field: string, value: string) => {
    const newProfiles = [...formData.buyingProfiles];
    newProfiles[index] = { ...newProfiles[index], [field]: value };
    setFormData(prev => ({ ...prev, buyingProfiles: newProfiles }));
  };

  const getCharacterCount = (fieldId: string) => {
    const value = formData[fieldId as keyof typeof formData] as string;
    return value ? value.length : 0;
  };

  const isSectionComplete = (section: any) => {
    return section.fields.every((field: any) => {
      if (field.type === "profiles") {
        return formData.buyingProfiles.some(profile => profile.label && profile.motivations);
      }
      const value = formData[field.id as keyof typeof formData] as string;
      return value && value.trim().length > 0;
    });
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="min-h-screen bg-background">
          {/* Header */}
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/experiences/exp_YyvRsa1dKa0EAl" className="text-foreground hover:text-foreground">
                      GhostWriter OS
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage className="text-foreground">DNA Builder</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            <div className="ml-auto flex items-center gap-2 px-4">
              <Link href="/experiences/exp_YyvRsa1dKa0EAl">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <Button size="sm">
                <Save className="w-4 h-4 mr-2" />
                Save DNA
              </Button>
            </div>
          </header>

          {/* Main Content */}
          <main className="max-w-4xl mx-auto p-6 bg-background rounded-lg">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2 text-foreground">Campaign DNAs</h1>
              <p className="text-muted-foreground">Create or edit your Campaign DNAs</p>
            </div>

            {/* Instructions */}
            <Card className="mb-6 border border-gray-200 rounded-lg">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg text-foreground">Instructions</CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setShowInstructions(!showInstructions)}
                  >
                    {showInstructions ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              {showInstructions && (
                <CardContent>
                  <div className="space-y-4 text-sm text-muted-foreground">
                    <p>Follow these steps to create your first high-converting copy:</p>
                    <ol className="list-decimal list-inside space-y-2">
                      <li>Fill in your author biography with your name, company, role, and achievements</li>
                      <li>Define your ideal client profile and target demographics</li>
                      <li>Set your brand voice and tone preferences</li>
                      <li>Create buying profiles for different customer archetypes</li>
                      <li>Define your persuasive premise and messaging framework</li>
                      <li>Address common false beliefs your audience might have</li>
                    </ol>
                    <p>Once completed, your DNA will be saved and can be used with any agent to generate personalized content.</p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* DNA Sections */}
            <div className="space-y-4">
              {sections.map((section) => (
                <Card key={section.id} className="border border-gray-200 rounded-lg">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{section.id}.</span>
                          <CardTitle className="text-base">{section.title}</CardTitle>
                        </div>
                        <CheckCircle className={`w-4 h-4 ${
                          isSectionComplete(section) ? 'text-green-600' : 'text-muted-foreground'
                        }`} />
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          Last edit: {new Date().toLocaleDateString()}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleSection(section.id)}
                        >
                          {expandedSections.includes(section.id) ? (
                            <ChevronUp className="w-4 h-4" />
                          ) : (
                            <ChevronDown className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                    <CardDescription className="text-sm">
                      {section.description}
                    </CardDescription>
                  </CardHeader>
                  
                  {expandedSections.includes(section.id) && (
                    <CardContent className="p-4">
                      <FieldGroup>
                        {section.fields.map((field) => (
                          <Field key={field.id}>
                            <FieldLabel htmlFor={field.id}>{field.label}</FieldLabel>
                            
                            {field.type === "input" && (
                              <div className="relative">
                                <Input
                                  id={field.id}
                                  placeholder={field.placeholder}
                                  value={formData[field.id as keyof typeof formData] as string || ""}
                                  onChange={(e) => handleInputChange(field.id, e.target.value)}
                                  maxLength={500}
                                  className="pr-16"
                                />
                                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                                  <span className="text-xs text-muted-foreground">
                                    {getCharacterCount(field.id)} / 500
                                  </span>
                                </div>
                              </div>
                            )}
                            
                            {field.type === "textarea" && (
                              <div className="relative">
                                <Textarea
                                  id={field.id}
                                  placeholder={field.placeholder}
                                  value={formData[field.id as keyof typeof formData] as string || ""}
                                  onChange={(e) => handleInputChange(field.id, e.target.value)}
                                  rows={4}
                                  maxLength={500}
                                  className="pr-16"
                                />
                                <div className="absolute right-2 bottom-2">
                                  <span className="text-xs text-muted-foreground">
                                    {getCharacterCount(field.id)} / 500
                                  </span>
                                </div>
                              </div>
                            )}
                            
                            {field.type === "select" && "options" in field && (
                              <div className="grid grid-cols-2 gap-3">
                                {field.options.map((option) => (
                                  <Button
                                    key={option}
                                    variant={formData.tone === option ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => handleInputChange(field.id, option)}
                                    className="justify-start h-10"
                                  >
                                    {option}
                                  </Button>
                                ))}
                              </div>
                            )}
                            
                            {field.type === "profiles" && (
                              <div className="space-y-4">
                                {formData.buyingProfiles.map((profile, index) => (
                                  <Card key={index} className="border border-gray-200 rounded-lg">
                                    <CardHeader className="pb-4">
                                      <div className="flex items-center justify-between">
                                        <CardTitle className="text-base">Profile {index + 1}</CardTitle>
                                        {index > 0 && (
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="hover:text-destructive hover:bg-destructive/10"
                                            onClick={() => {
                                              const newProfiles = formData.buyingProfiles.filter((_, i) => i !== index);
                                              setFormData({...formData, buyingProfiles: newProfiles});
                                            }}
                                          >
                                            <Trash2 className="w-4 h-4" />
                                          </Button>
                                        )}
                                      </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Field>
                                          <FieldLabel>Label</FieldLabel>
                                          <Input
                                            placeholder="e.g., Data Dominant"
                                            value={profile.label}
                                            onChange={(e) => handleBuyingProfileChange(index, "label", e.target.value)}
                                            maxLength={500}
                                          />
                                        </Field>
                                        <Field>
                                          <FieldLabel>Motivations</FieldLabel>
                                          <Textarea
                                            placeholder="What drives this profile?"
                                            value={profile.motivations}
                                            onChange={(e) => handleBuyingProfileChange(index, "motivations", e.target.value)}
                                            rows={2}
                                            maxLength={500}
                                          />
                                        </Field>
                                      </div>
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Field>
                                          <FieldLabel>Fears / False Beliefs</FieldLabel>
                                          <Textarea
                                            placeholder="What are their concerns or misconceptions?"
                                            value={profile.fears}
                                            onChange={(e) => handleBuyingProfileChange(index, "fears", e.target.value)}
                                            rows={2}
                                            maxLength={500}
                                          />
                                        </Field>
                                        <Field>
                                          <FieldLabel>Common Objections</FieldLabel>
                                          <Textarea
                                            placeholder="What objections do they typically have?"
                                            value={profile.objections}
                                            onChange={(e) => handleBuyingProfileChange(index, "objections", e.target.value)}
                                            rows={2}
                                            maxLength={500}
                                          />
                                        </Field>
                                      </div>
                                    </CardContent>
                                  </Card>
                                ))}
                              </div>
                            )}
                          </Field>
                        ))}
                      </FieldGroup>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          </main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}