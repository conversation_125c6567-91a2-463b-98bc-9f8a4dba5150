"use client"

import * as React from "react"
import Link from "next/link"
import {
  LayoutDashboard,
  Dna,
  Bot,
  History,
  FolderOpen,
  Settings,
  HelpCircle,
  Plus,
  Sparkles,
  MessageSquare,
  PenTool,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "cale",
    email: "<EMAIL>",
    avatar: "/avatars/cale.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/experiences/exp_YyvRsa1dKa0EAl",
      icon: LayoutDashboard,
      isActive: true,
      shortcut: "Ctrl+D",
    },
    {
      title: "My DNAs",
      url: "/dna-builder",
      icon: Dna,
      shortcut: "Ctrl+N",
      items: [
        {
          title: "All DNAs",
          url: "/dna-builder",
          shortcut: "Ctrl+A",
        },
        {
          title: "Recent",
          url: "/dna-builder",
          shortcut: "Ctrl+R",
        },
        {
          title: "Favorites",
          url: "/dna-builder",
          shortcut: "Ctrl+F",
        },
      ],
    },
    {
      title: "Agents",
      url: "/agents",
      icon: Bot,
      shortcut: "Ctrl+G",
      items: [
        {
          title: "Offer Creation",
          url: "/agents",
          shortcut: "Ctrl+O",
        },
        {
          title: "VSL Maker",
          url: "/agents",
          shortcut: "Ctrl+V",
        },
        {
          title: "Email Sequences",
          url: "/agents",
          shortcut: "Ctrl+E",
        },
        {
          title: "Ad Copy",
          url: "/agents",
          shortcut: "Ctrl+A",
        },
      ],
    },
    {
      title: "History",
      url: "/history",
      icon: History,
      shortcut: "Ctrl+H",
      items: [
        {
          title: "Recent Generations",
          url: "/history",
          shortcut: "Ctrl+R",
        },
        {
          title: "Saved Copies",
          url: "/history",
          shortcut: "Ctrl+S",
        },
        {
          title: "Favorites",
          url: "/history",
          shortcut: "Ctrl+F",
        },
      ],
    },
    {
      title: "Folders",
      url: "/folders",
      icon: FolderOpen,
      shortcut: "Ctrl+F",
      items: [
        {
          title: "Campaigns",
          url: "/folders",
          shortcut: "Ctrl+C",
        },
        {
          title: "Templates",
          url: "/folders",
          shortcut: "Ctrl+T",
        },
        {
          title: "Archives",
          url: "/folders",
          shortcut: "Ctrl+A",
        },
      ],
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      shortcut: "Ctrl+,",
      items: [
        {
          title: "Account",
          url: "/settings",
          shortcut: "Ctrl+A",
        },
        {
          title: "Preferences",
          url: "/settings",
          shortcut: "Ctrl+P",
        },
        {
          title: "API Keys",
          url: "/settings",
          shortcut: "Ctrl+K",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Help Center",
      url: "/help",
      icon: HelpCircle,
      shortcut: "Ctrl+?",
    },
    {
      title: "Feedback",
      url: "/feedback",
      icon: MessageSquare,
      shortcut: "Ctrl+F",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/experiences/exp_YyvRsa1dKa0EAl">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                  <PenTool className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-xs leading-tight">
                  <span className="truncate font-semibold text-xs">GhostWriter OS</span>
                  <span className="truncate text-xs text-muted-foreground">AI Copywriting Suite</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
        
        {/* Quick Create Button */}
        <div className="px-3 pb-4 pt-2">
          <Link href="/dna-builder">
            <SidebarMenuButton className="w-full justify-center gap-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md py-2.5 rounded-lg">
              <Plus className="w-4 h-4" />
              <span className="text-sm">Create New DNA</span>
            </SidebarMenuButton>
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
