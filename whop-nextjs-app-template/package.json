{"name": "whop-nextjs-app-template", "version": "0.2.0", "private": true, "scripts": {"dev": "whop-proxy --command 'next dev --turbopack'", "build": "next build", "start": "next start", "lint": "biome lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@vercel/functions": "^3.1.4", "@whop/react": "0.3.0", "@whop/sdk": "0.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.548.0", "next": "16.0.0", "openai": "^6.7.0", "react": "19.2.0", "react-dom": "19.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "2.2.6", "@types/node": "^20.19.21", "@types/react": "19.2.2", "@types/react-dom": "19.2.2", "@whop-apps/dev-proxy": "0.0.1-canary.117", "autoprefixer": "^10.4.21", "dotenv-cli": "^10.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.9.3"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531", "pnpm": {"overrides": {"@types/react": "19.2.2", "@types/react-dom": "19.2.2"}}}